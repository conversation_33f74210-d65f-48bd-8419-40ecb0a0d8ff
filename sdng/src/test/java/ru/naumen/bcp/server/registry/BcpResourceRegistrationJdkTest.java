package ru.naumen.bcp.server.registry;

import static org.mockito.Mockito.mock;

import java.io.ByteArrayInputStream;
import java.io.StringReader;
import java.util.Arrays;
import java.util.List;

import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.springframework.core.io.Resource;

import ru.naumen.bcp.server.operations.OperationStub;
import ru.naumen.bcp.server.registry.IBusinessOperationsRegistry.RelationOp;
import ru.naumen.commons.server.utils.ResourceUtils;
import ru.naumen.core.server.util.MessageFacade;

/**
 * Тестирование правильности работы парсера (распознавание граматики и т.п.)
 * <AUTHOR>
 * @since 29.10.2010
 */
public class BcpResourceRegistrationJdkTest
{
    private static final String COMMENT_AT_BEGIN = "/* Comment at begin */";

    private static final String SL_COMMENT = "// single line comment";

    private static final String ML_COMMENT = "/* Multi\n line\n comment */\n";

    private static final String SOME_WHITESPACE = """
               \r\r\r\r\r   \r
            \r
            \s
            \r
            \r     \t           \u000C           \r
            \t
            \r
            \r// any white space""";

    private static final String OPERATION =
            """
                        operation: OPERATION:10,
                        class: ru.naumen.bcp.server.operations.OperationStub,
                        domain: someDomain;
                    """;

    private static final String DEFAULT_OPERATION =
            """
                    defaultOperation: DEFAULT_OPERATION:7,
                        class: ru.naumen.bcp.server.operations.OperationStub,
                        domain: someDomain;
                    """;

    private static final String OPERATION_NO_PRIO =
            """
                    operation: OPERATION_NO_PRIO,
                        class: ru.naumen.bcp.server.operations.OperationStub,
                        domain: someDomain;
                    """;

    private static final String OPERATION_NO_DOMAIN =
            """
                    operation: OPERATION_NO_DOMAIN:1,
                        class: ru.naumen.bcp.server.operations.OperationStub;
                    """;

    private static final String OPERATION_NO_PRIO_DOMAIN =
            "operation: OPERATION_NO_PRIO_DOMAIN,\n" +
            "    class: ru.naumen.bcp.server.operations.OperationStub;";

    private static final String COMMENTED_OPERATION =
            """
                    /*\s
                    
                    operation: COMMENTED_OPERATION,
                        class: ru.naumen.bcp.server.operations.OperationStub,
                        domain: someDomain;
                    
                    */
                    
                    """;

    private static final String NOWITESPACE_OPERATION = "operation:NOWITESPACE_OPERATION:1," +
                                                        "class:ru.naumen.bcp.server.operations.OperationStub,"
                                                        + "domain:domaind;";

    private static final String THROUGH_COMMENTS_OPERATION =
            """
                    /* comments through description */
                    operation: /*operation name */ THROUGH_COMMENTS_OPERATION:0\s
                     , // some comment
                    class: // operation class name
                    
                    
                    ru.naumen.bcp.server.operations.OperationStub,
                    /*operation Domain*/ domain
                     : domainValue\s
                    ; // operation description end""";

    private BcpResourceRegistration registration;

    private IBusinessOperationsRegistry mockRegistry;

    private ResourceUtils resourceUtils;

    @Test
    public void comments()
    {
        for (String bcp : List.of(COMMENT_AT_BEGIN, SL_COMMENT, ML_COMMENT, SOME_WHITESPACE, COMMENTED_OPERATION))
        {
            registration.registerResource(bcp);
        }
        Mockito.verifyNoMoreInteractions(this.mockRegistry);
    }

    @Test
    public void init() throws Exception
    {
        // настройка системы
        Resource res1 = mock(Resource.class);
        Mockito.when(res1.getInputStream()).thenReturn(new ByteArrayInputStream(
                "operation: opname,\n class: ru.naumen.bcp.server.operations.OperationStub;\n".getBytes()));
        Resource res2 = mock(Resource.class);
        Mockito.when(res2.getInputStream()).thenReturn(new ByteArrayInputStream("op1 >> op2;".getBytes()));
        Mockito.when(this.resourceUtils.findFilesWithExtension(Mockito.anyString()))
                .thenReturn(Arrays.asList(res1, res2));
        // вызов системы
        registration.initInTx();
        // проверка утверджений
        Mockito.verify(this.mockRegistry).registerOperation("opname", OperationStub.class, null, null);
        Mockito.verify(this.mockRegistry).addRelations(List.of("op1"), RelationOp.runBefore, List.of("op2"),
                null, null, null);
    }

    @Test
    public void registerOperations() throws Exception
    {
        registration.registerResource(OPERATION);
        Mockito.verify(this.mockRegistry).registerOperation("OPERATION", OperationStub.class, "someDomain", 10);
        Mockito.reset(this.mockRegistry);

        registration.registerResource(new StringReader(OPERATION_NO_PRIO));
        Mockito.verify(this.mockRegistry).registerOperation("OPERATION_NO_PRIO", OperationStub.class, "someDomain",
                null);
        Mockito.reset(this.mockRegistry);

        registration.registerResource(new ByteArrayInputStream(OPERATION_NO_DOMAIN.getBytes()));
        Mockito.verify(this.mockRegistry).registerOperation("OPERATION_NO_DOMAIN", OperationStub.class, null, 1);
        Mockito.reset(this.mockRegistry);

        registration.registerResource(OPERATION_NO_PRIO_DOMAIN);
        Mockito.verify(this.mockRegistry).registerOperation("OPERATION_NO_PRIO_DOMAIN", OperationStub.class, null,
                null);
        Mockito.reset(this.mockRegistry);

        registration.registerResource(new StringReader(NOWITESPACE_OPERATION));
        Mockito.verify(this.mockRegistry).registerOperation("NOWITESPACE_OPERATION", OperationStub.class, "domaind", 1);
        Mockito.reset(this.mockRegistry);

        registration.registerResource(new ByteArrayInputStream(THROUGH_COMMENTS_OPERATION.getBytes()));
        Mockito.verify(this.mockRegistry).registerOperation("THROUGH_COMMENTS_OPERATION", OperationStub.class,
                "domainValue", 0);
        Mockito.reset(this.mockRegistry);

        registration.registerResource(DEFAULT_OPERATION);
        Mockito.verify(this.mockRegistry).registerDefaultOperation("DEFAULT_OPERATION", OperationStub.class, null,
                "someDomain", 7);
        Mockito.reset(this.mockRegistry);

    }

    @Before
    public void setUp()
    {
        mockRegistry = Mockito.mock(IBusinessOperationsRegistry.class);
        resourceUtils = mock(ResourceUtils.class);
        registration = new BcpResourceRegistration(mockRegistry, resourceUtils, mock(
                MessageFacade.class));
    }
}
