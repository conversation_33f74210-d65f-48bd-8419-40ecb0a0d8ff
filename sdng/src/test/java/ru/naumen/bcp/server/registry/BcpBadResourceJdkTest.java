package ru.naumen.bcp.server.registry;

import static org.mockito.Mockito.mock;

import java.util.Arrays;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.junit.runners.Parameterized;
import org.junit.runners.Parameterized.Parameters;

import ru.naumen.commons.server.utils.ResourceUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.util.MessageFacade;

/**
 *
 * <AUTHOR>
 * @since 01.11.2010
 */
@RunWith(Parameterized.class)
public class BcpBadResourceJdkTest
{
    private static final String SOME_TEXT = "some text";

    private static final String OP_TEXT = "operation";

    private static final String OP_ONLY = "operation: opname;";

    private static final String BADOP_EMPTYNAME = """
            operation:      ,
                class: ru.naumen.bcp.server.operations.OperationStub,
                domain: someDomain,
                priority: 10;
            """;

    private static final String BADOP_EMPTYCLASS = """
            operation: BADOP_EMPTYCLASS ,
                class:  ,
                domain: someDomain,
                priority: 10;
            """;

    private static final String BADOP_EMPTYDOMAIN = """
            operation: BADOP_EMPTYDOMAIN ,
                class: ru.naumen.bcp.server.operations.OperationStub,
                domain:  ,
                priority: 10;
            """;

    private static final String BADOP_EMPTYPRIO = """
            operation: BADOP_EMPTYPRIO ,
                class: ru.naumen.bcp.server.operations.OperationStub,
                domain: someDomain,
                priority:   ;
            """;

    /**
     * Исключение на таком контенте не выбрасывается, а по идее должно
     * при этом ничего не регистрируется.
     * TODO40 надо разобраться
     */
    public static final String BADOP_NONAME = """
                class: ru.naumen.bcp.server.operations.OperationStub,
                domain: someDomain,
                priority: 10;
            """;

    private static final String BADOP_NOCLASS = """
            operation: BADOP_NOCLASS ,
                domain: someDomain,
                priority: 10;
            """;

    private static final String BADOP_KEYWORDS = """
            operation: class ,
                class: domain,
                domain: operation,
                priority: 0;
            """;

    private static final String BADOP_PRIO_NOTNUMBER = """
            operation: BADOP_PRIO_NOTNUMBER ,
                class: ru.naumen.bcp.server.operations.OperationStub,
                domain: domainName ,
                priority: seven;
            """;

    public static final String BADOP_NO_COMMAS = """
            operation: BADOP_NO_COMMAS\s
                class: ru.naumen.bcp.server.operations.OperationStub
                domain: domainName\s
                priority: 1;
            """;

    private static final String BADOP_NO_END = """
            operation: BADOP_NO_END,
                class: ru.naumen.bcp.server.operations.OperationStub,
                domain: domainName,
                priority: 1\s
            """;

    private static final String BADRULE = """
            opId |> otherOpId,
                processes: [ff];
            """;

    @Parameters
    public static List<String[]> badGramatics()
    {
        return Arrays.asList(new String[][] { { SOME_TEXT }, { OP_TEXT }, { OP_ONLY }, { BADOP_EMPTYNAME },
                { BADOP_EMPTYCLASS }, { BADOP_EMPTYDOMAIN }, { BADOP_EMPTYPRIO }, { BADOP_NOCLASS },
                { BADOP_KEYWORDS }, { BADOP_PRIO_NOTNUMBER }, { BADOP_NO_END }, { BADRULE } });
    }

    private final String content;

    public BcpBadResourceJdkTest(String content)
    {
        this.content = content;
    }

    @Test(expected = FxException.class)
    public void check()
    {
        BcpResourceRegistration registration =
                new BcpResourceRegistration(mock(IBusinessOperationsRegistry.class), mock(ResourceUtils.class), mock(
                        MessageFacade.class));
        registration.registerResource(this.content);
    }
}
