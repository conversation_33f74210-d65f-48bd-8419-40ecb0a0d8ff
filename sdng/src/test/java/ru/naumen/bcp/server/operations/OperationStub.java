package ru.naumen.bcp.server.operations;

import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.core.server.bo.bop.OperationValidator;
import ru.naumen.core.server.bo.bop.PlannedVersionAttrOperationValidator;
import ru.naumen.core.server.bo.bop.SetDirectLinkAttrValueOperation;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.RelationMetaInfoService;
import ru.naumen.metainfo.shared.MetainfoUtils;

/**
 *
 * <AUTHOR>
 * @since 29.10.2010
 */
@SuppressWarnings("rawtypes")
public class OperationStub extends AtomOperationBase
{

    @SuppressWarnings("unchecked")
    public static void inject(AtomOperationBase op, MetainfoService metainfoService, MetainfoUtils metainfoUtils,
            OperationValidator validator)
    {
        op.metainfoService = metainfoService;
        op.metainfoUtils = metainfoUtils;
        op.validator = validator;
    }

    public static void inject(SetAttrValueOperation<?, ?> op, AccessorHelper accessorHelper,
            RelationMetaInfoService relationService)
    {
        op.accessorHelper = accessorHelper;
        op.relationService = relationService;
    }

    public static void inject(SetAttrValueOperation<?, ?> op, MetainfoService metainfoService,
            MetainfoUtils metainfoUtils, OperationValidator validator, IPrefixObjectLoaderService service,
            OperationHelper operationHelper, AuthorizationRunnerService authorizeRunner, AuthorizationService authorize)
    {
        inject(op, metainfoService, metainfoUtils, validator);
        op.service = service;
        op.operationHelper = operationHelper;
        op.authorizeRunner = authorizeRunner;
        op.authorize = authorize;
    }

    public static void inject(SetAttrValueOperation<?, ?> op, ResolverUtils resolverUtils)
    {
        op.resolverUtils = resolverUtils;
    }

    public static void inject(SetDirectLinkAttrValueOperation<?, ?> op, PlannedVersionAttrOperationValidator validator)
    {
        op.setPlannedVersionAttrOperationValidator(validator);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void perform(AtomOperationContext ctx) throws OperationException
    {

    }

}
