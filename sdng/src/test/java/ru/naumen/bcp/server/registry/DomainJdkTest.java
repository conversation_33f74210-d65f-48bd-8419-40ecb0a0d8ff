package ru.naumen.bcp.server.registry;

import static ru.naumen.bcp.server.registry.IBusinessOperationsRegistry.RelationOp.no;
import static ru.naumen.bcp.server.registry.IBusinessOperationsRegistry.RelationOp.orderAfter;
import static ru.naumen.bcp.server.registry.IBusinessOperationsRegistry.RelationOp.orderBefore;
import static ru.naumen.bcp.server.registry.IBusinessOperationsRegistry.RelationOp.runAfter;
import static ru.naumen.bcp.server.registry.IBusinessOperationsRegistry.RelationOp.runBefore;

import java.util.Arrays;
import java.util.Collections;
import java.util.Deque;
import java.util.Set;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import ru.naumen.NauAssert;
import ru.naumen.bcp.server.operations.AtomOperationBase;
import ru.naumen.bcp.server.operations.IAtomOperation;
import ru.naumen.bcp.server.operations.OperationStub;
import ru.naumen.bcp.server.operations.SetAttrValueOperationWithCheck;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.core.shared.utils.UUIDGenerator;

import com.google.common.collect.Iterables;

import java.util.HashSet;

/**
 * Тестирование функциональности класса {@link Domain}
 * <AUTHOR>
 * @since 08.11.2010
 */
public class DomainJdkTest
{
    static final int DEF_PRIO = IBusinessOperationsRegistry.DEFAULT_PRIORITY;

    static final String[] EMPTY = new String[0];

    /**
     * Тестовая Иерархия:<br>
     * <pre>
     *         global
     *              |
     *         parent
     *          /       \
     *       left   domain
     *                     \
     *                   child
     * </pre>
     */
    Domain global, parent, domain, child, left;

    String defOpId;
    @SuppressWarnings("rawtypes")
    Class<? extends IAtomOperation> defOpClass;

    @Mock
    BOProcess defprocess, process;

    /**
     * Проверка правильности обхода доменов по иерархии в направлениях
     * от текущего к глобальному и наоборот
     */
    @Test
    public void checkDomainSequence()
    {
        // настройка системы
        // вызов системы
        Iterable<Domain> d2g = domain.fromThisToGlobal();
        Deque<Domain> g2c = child.fromGlobalToThis();
        // проверка утверджений
        org.junit.Assert.assertTrue("Последовательность доменов не соответствует действительной",
                Iterables.elementsEqual(Arrays.asList(domain, parent, global), d2g));
        org.junit.Assert.assertTrue("Последовательность доменов не соответствует действительной",
                Iterables.elementsEqual(Arrays.asList(global, parent, domain, child), g2c));
    }

    @Test
    public void collectDefaultOpIds()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        String opId2 = UUIDGenerator.get().nextUUID();
        // проверка утверджений
        NauAssert.assertContentEquals(domain.collectDefaultOpIds(), this.defOpId);
        // настройка системы
        parent.registerDefaultOperation(opId, OperationStub.class);
        // проверка утверджений
        NauAssert.assertContentEquals(domain.collectDefaultOpIds(), this.defOpId, opId);
        // настройка системы
        domain.registerOperation(opId2, AtomOperationBase.class);
        left.registerDefaultOperation(opId2, OperationStub.class);
        // проверка утверджений
        NauAssert.assertContentEquals(child.collectDefaultOpIds(), this.defOpId, opId);
    }

    @Test
    public void doubleOperationId_noIntersectDomains()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        left.registerDefaultOperation(opId, AtomOperationBase.class);
        // вызов системы
        child.registerOperation(opId, OperationStub.class);
        // проверка утверджений
    }

    @Test(expected = IllegalArgumentException.class)
    public void doubleOperationId_outerDomain()
    {
        // настройка системы
        // вызов системы
        child.registerOperation(this.defOpId, OperationStub.class);
        // проверка утверджений
    }

    @Test(expected = IllegalArgumentException.class)
    public void doubleOperationId_sameDomain()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        domain.registerOperation(opId, OperationStub.class);
        // вызов системы
        domain.registerDefaultOperation(opId, OperationStub.class);
        // проверка утверджений
    }

    @Test
    public void getDefaultOperation()
    {
        // настройка системы
        // вызов системы
        Operation op = global.getDefaultOperation(null);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Не зарегистрирована операция по умолчанию из глобального домена", op);
        org.junit.Assert.assertEquals(this.defOpId, op.getId());
        org.junit.Assert.assertSame(op, child.getDefaultOperation(null));
        org.junit.Assert.assertSame(op, left.getDefaultOperation(null));

        // настройка системы
        String newDefOpId = UUIDGenerator.get().nextUUID();
        global.registerDefaultOperation(newDefOpId, OperationStub.class, null, DEF_PRIO + 1);
        // вызов системы
        op = global.getDefaultOperation(null);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Не зарегистрирована операция по умолчанию из глобального домена", op);
        org.junit.Assert.assertFalse("Не удалось переопределить операцию по умолчанию",
                ObjectUtils.equals(this.defOpId, op.getId()));
        org.junit.Assert.assertSame(op, left.getDefaultOperation(null));

        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        domain.registerDefaultOperation(opId, OperationStub.class);
        // вызов системы
        op = global.getDefaultOperation(null);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Не зарегистрирована операция по умолчанию из глобального домена", op);
        org.junit.Assert.assertEquals(newDefOpId, op.getId());
        org.junit.Assert.assertSame(op, left.getDefaultOperation(null));

        Operation op2 = child.getDefaultOperation(null);
        org.junit.Assert.assertFalse(ObjectUtils.equals(op.getId(), op2.getId()));
        org.junit.Assert.assertSame(op2, child.getDefaultOperation(null));
    }

    @Test
    public void getNonDefaultOperation()
    {
        // настройка системы
        // вызов системы
        Operation op = domain.getNonDefaultOperation(this.defOpId);
        // проверка утверджений
        org.junit.Assert.assertNull("Не верно определена именованая операция", op);

        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        domain.registerOperation(opId, OperationStub.class, DEF_PRIO);

        // вызов системы
        op = parent.getNonDefaultOperation(opId);
        // проверка утверджений
        org.junit.Assert.assertNull("Операция зарегистрированная во внутреннем домене - доступна во внешнем", op);
        // вызов системы
        op = left.getNonDefaultOperation(opId);
        // проверка утверджений
        org.junit.Assert.assertNull("Операция доступна в непересекающемся домене", op);
        // вызов системы
        op = child.getNonDefaultOperation(opId);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция зарегистрированная во внешнем домене - недоступна во внутреннем", op);
        org.junit.Assert.assertEquals("Неправильный идентификатор операции", opId, op.getId());
        org.junit.Assert.assertSame("Неправильный класс операции", OperationStub.class.getName(), op.opClassName);

        // настройка системы
        domain.registerOperation(opId, AtomOperationBase.class, DEF_PRIO + 1);
        // вызов системы
        op = child.getNonDefaultOperation(opId);
        // проверка утверджений
        org.junit.Assert.assertSame("Не удалось переопределить операцию с помощью повышения приоритета",
                AtomOperationBase.class.getName(), op.opClassName);
    }

    @Test
    public void hasOuterDomain()
    {
        // настройка системы
        String mess = "Ошибка в поиске внешнего домена";
        // вызов системы
        // проверка утверджений
        org.junit.Assert.assertFalse(mess, global.hasOuterDomain(parent.getId()));
        org.junit.Assert.assertFalse(mess, domain.hasOuterDomain(left.getId()));
        org.junit.Assert.assertFalse(mess, left.hasOuterDomain(child.getId()));
        org.junit.Assert.assertFalse(mess, domain.hasOuterDomain(domain.getId()));

        org.junit.Assert.assertTrue(mess, child.hasOuterDomain(parent.getId()));
        org.junit.Assert.assertTrue(mess, left.hasOuterDomain(global.getId()));
    }

    @Test
    public void isRegisteredOperation()
    {
        // настройка системы
        // вызов системы
        // проверка утверджений
        org.junit.Assert.assertFalse("Не верно определена именованая операция",
                domain.isRegisteredOperation(this.defOpId));

        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        domain.registerOperation(opId, OperationStub.class, DEF_PRIO);

        // проверка утверджений
        org.junit.Assert.assertFalse("Операция зарегистрированная во внутреннем домене - доступна во внешнем",
                parent.isRegisteredOperation(opId));
        org.junit.Assert.assertFalse("Операция доступна в непересекающемся домене", left.isRegisteredOperation(opId));
        org.junit.Assert.assertTrue("Операция зарегистрированная во внешнем домене - недоступна во внутреннем",
                child.isRegisteredOperation(opId));
    }

    @Test
    public void queryLeftOpIds()
    {
        // настройка системы
        Set<String> set = new HashSet<>();

        String op1 = UUIDGenerator.get().nextUUID();
        String op2 = UUIDGenerator.get().nextUUID();
        String op3 = UUIDGenerator.get().nextUUID();
        String op4 = UUIDGenerator.get().nextUUID();

        global.registerRelations(Arrays.asList(op1, op2), runAfter, Arrays.asList(op3, op4), process.getId());
        parent.registerRelations(Arrays.asList(op1, op4), orderBefore, Arrays.asList(op2, op3), process.getId());
        left.registerRelations(Arrays.asList(op2, op4), runBefore, Arrays.asList(op1, op3), process.getId());
        domain.registerRelations(Arrays.asList(op2, op3), orderAfter, Arrays.asList(op1, op4), process.getId());

        global.registerRelations(Arrays.asList(op3, op4), runBefore, Arrays.asList(op1, op2), defprocess.getId());
        parent.registerRelations(Arrays.asList(op3, op2), orderAfter, Arrays.asList(op1, op4), defprocess.getId());
        left.registerRelations(Arrays.asList(op4, op2), runAfter, Arrays.asList(op1, op3), defprocess.getId());
        domain.registerRelations(Arrays.asList(op4, op1), orderBefore, Arrays.asList(op3, op2), defprocess.getId());

        // вызов системы
        global.queryLeftOpIds(set, process, op1, no, orderBefore, orderAfter, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        global.queryLeftOpIds(set, process, op3, no, orderBefore, orderAfter, runBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        global.queryLeftOpIds(set, process, op4, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2);
        set.clear();
        // вызов системы
        parent.queryLeftOpIds(set, process, op1, no, orderBefore, orderAfter, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        parent.queryLeftOpIds(set, process, op3, orderBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2, op4);
        set.clear();
        // вызов системы
        parent.queryLeftOpIds(set, process, op3, orderBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op4);
        set.clear();
        // вызов системы
        parent.queryLeftOpIds(set, process, op3, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op2);
        set.clear();
        // вызов системы
        left.queryLeftOpIds(set, process, op1, no, orderBefore, orderAfter, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        left.queryLeftOpIds(set, process, op4, no, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2);
        set.clear();
        // вызов системы
        left.queryLeftOpIds(set, process, op3, runBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op2, op4);
        set.clear();
        // вызов системы
        left.queryLeftOpIds(set, process, op3, orderBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1);
        set.clear();
        // вызов системы
        left.queryLeftOpIds(set, process, op3, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        child.queryLeftOpIds(set, process, op1, no, orderBefore, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        child.queryLeftOpIds(set, process, op3, runBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
    }

    @Test
    public void queryLeftOpIdsWithDefaultRight()
    {
        // настройка системы
        Set<String> set = new HashSet<>();
        String def1 = "def1";
        String def2 = "def2";
        String op1 = "op1";
        String op2 = "op2";
        String op3 = "op3";
        String op4 = "op4";

        domain.registerDefaultOperation(def1, OperationStub.class);
        left.registerDefaultOperation(def2, OperationStub.class);

        global.registerRelations(Arrays.asList(op1, op2), orderAfter, Arrays.asList(defOpId), process.getId());
        parent.registerRelations(Arrays.asList(op3, op4), orderBefore, Arrays.asList(def1, def2), process.getId());
        left.registerRelations(Arrays.asList(op1, op3), orderAfter, Arrays.asList(def1, def2), process.getId());
        domain.registerRelations(Arrays.asList(op2, op4), orderBefore, Arrays.asList(def1, def2, defOpId),
                process.getId());

        global.registerRelations(Arrays.asList(op3, op4), orderBefore, Arrays.asList(defOpId), defprocess.getId());
        parent.registerRelations(Arrays.asList(op1, op2), orderAfter, Arrays.asList(def1, def2), defprocess.getId());
        left.registerRelations(Arrays.asList(op1, op4), orderBefore, Arrays.asList(def1), defprocess.getId());
        domain.registerRelations(Arrays.asList(op2, op3), orderAfter, Arrays.asList(def2), defprocess.getId());
        // вызов системы
        global.queryLeftOpIdsWithDefaultRight(set, process, def1, no, orderBefore, orderAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2);
        set.clear();
        // вызов системы
        global.queryLeftOpIdsWithDefaultRight(set, process, def1, no, orderBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        parent.queryLeftOpIdsWithDefaultRight(set, process, def1, no, orderBefore, orderAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2);
        set.clear();
        // вызов системы
        parent.queryLeftOpIdsWithDefaultRight(set, process, def1, no, orderBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        left.queryLeftOpIdsWithDefaultRight(set, process, def2, no, orderAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2, op3);
        set.clear();
        // вызов системы
        left.queryLeftOpIdsWithDefaultRight(set, process, def2, orderBefore);
        // проверка утверджений
        System.out.println(set);
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        child.queryLeftOpIdsWithDefaultRight(set, process, def1, no, orderBefore, orderAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2, op4);
        set.clear();
        // вызов системы
        //child.queryLeftOpIdsWithDefaultRight(set, process, def1, orderAfter);
        // проверка утверджений
        //Assert.assertContentEquals(set, op1);
        set.clear();
        // вызов системы
        child.queryLeftOpIdsWithDefaultRight(set, process, def1, orderBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op2, op4);
        set.clear();
    }

    @Test
    public void queryRightOpIds()
    {
        // настройка системы
        Set<String> set = new HashSet<>();

        String op1 = UUIDGenerator.get().nextUUID();
        String op2 = UUIDGenerator.get().nextUUID();
        String op3 = UUIDGenerator.get().nextUUID();
        String op4 = UUIDGenerator.get().nextUUID();

        global.registerRelations(Arrays.asList(op1, op2), runAfter, Arrays.asList(op3, op4), process.getId());
        parent.registerRelations(Arrays.asList(op1, op4), orderBefore, Arrays.asList(op2, op3), process.getId());
        left.registerRelations(Arrays.asList(op2, op4), runBefore, Arrays.asList(op1, op3), process.getId());
        domain.registerRelations(Arrays.asList(op2, op3), orderAfter, Arrays.asList(op1, op4), process.getId());

        global.registerRelations(Arrays.asList(op3, op4), runBefore, Arrays.asList(op1, op2), defprocess.getId());
        parent.registerRelations(Arrays.asList(op3, op2), orderAfter, Arrays.asList(op1, op4), defprocess.getId());
        left.registerRelations(Arrays.asList(op4, op2), runAfter, Arrays.asList(op1, op3), defprocess.getId());
        domain.registerRelations(Arrays.asList(op4, op1), orderBefore, Arrays.asList(op3, op2), defprocess.getId());

        // вызов системы
        global.queryRightOpIds(set, process, op3, no, orderBefore, orderAfter, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        global.queryRightOpIds(set, process, op1, no, orderBefore, orderAfter, runBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        global.queryRightOpIds(set, process, op2, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op3, op4);
        set.clear();
        // вызов системы
        parent.queryRightOpIds(set, process, op3, no, orderBefore, orderAfter, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        parent.queryRightOpIds(set, process, op1, orderBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op2, op3, op4);
        set.clear();
        // вызов системы
        parent.queryRightOpIds(set, process, op1, orderBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op2, op3);
        set.clear();
        // вызов системы
        parent.queryRightOpIds(set, process, op1, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op4);
        set.clear();
        // вызов системы
        left.queryRightOpIds(set, process, op3, no, orderBefore, orderAfter, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        left.queryRightOpIds(set, process, op2, no, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op4);
        set.clear();
        // вызов системы
        left.queryRightOpIds(set, process, op1, runBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);

        // вызов системы
        child.queryRightOpIds(set, process, op3, no, orderBefore, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        child.queryRightOpIds(set, process, op2, orderAfter, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op3, op4);
        set.clear();
        // вызов системы
        child.queryRightOpIds(set, process, op2, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op3);
        set.clear();
    }

    @Test
    public void queryRightOpIdsWithDefaultLeft()
    {
        // настройка системы
        Set<String> set = new HashSet<>();
        String def1 = UUIDGenerator.get().nextUUID();
        String def2 = UUIDGenerator.get().nextUUID();
        String op1 = UUIDGenerator.get().nextUUID();
        String op2 = UUIDGenerator.get().nextUUID();
        String op3 = UUIDGenerator.get().nextUUID();
        String op4 = UUIDGenerator.get().nextUUID();

        domain.registerDefaultOperation(def1, OperationStub.class);
        left.registerDefaultOperation(def2, OperationStub.class);

        global.registerRelations(Arrays.asList(defOpId), orderAfter, Arrays.asList(op1, op2), process.getId());
        parent.registerRelations(Arrays.asList(def1, def2), runBefore, Arrays.asList(op3, op4), process.getId());
        left.registerRelations(Arrays.asList(def1, def2), orderBefore, Arrays.asList(op1, op3), process.getId());
        domain.registerRelations(Arrays.asList(def1, def2, defOpId), runAfter, Arrays.asList(op2, op4),
                process.getId());

        global.registerRelations(Arrays.asList(defOpId), runBefore, Arrays.asList(op3, op4), defprocess.getId());
        parent.registerRelations(Arrays.asList(def1, def2), orderAfter, Arrays.asList(op1, op2), defprocess.getId());
        left.registerRelations(Arrays.asList(def1), runAfter, Arrays.asList(op1, op4), defprocess.getId());
        domain.registerRelations(Arrays.asList(def2), orderBefore, Arrays.asList(op2, op3), defprocess.getId());

        // вызов системы
        global.queryRightOpIdsWithDefaultLeft(set, process, def1, no, orderBefore, orderAfter, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2);
        set.clear();
        // вызов системы
        global.queryRightOpIdsWithDefaultLeft(set, process, def1, no, orderBefore, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        parent.queryRightOpIdsWithDefaultLeft(set, process, def1, no, orderBefore, orderAfter, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2);
        set.clear();
        // вызов системы
        left.queryRightOpIdsWithDefaultLeft(set, process, def1, no, orderBefore, orderAfter, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2, op3);
        set.clear();
        // вызов системы
        left.queryRightOpIdsWithDefaultLeft(set, process, def1, orderAfter, runBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2);
        set.clear();
        // вызов системы
        left.queryRightOpIdsWithDefaultLeft(set, process, def1, orderBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op3);
        set.clear();
        // вызов системы
        child.queryRightOpIdsWithDefaultLeft(set, process, def1, no, orderBefore, orderAfter, runBefore, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2, op4);
        set.clear();
        // вызов системы
        child.queryRightOpIdsWithDefaultLeft(set, process, def1, orderAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1);
        set.clear();
        // вызов системы
        child.queryRightOpIdsWithDefaultLeft(set, process, def1, runBefore, orderBefore);
        // проверка утверджений
        NauAssert.assertContentEquals(set, EMPTY);
        // вызов системы
        child.queryRightOpIdsWithDefaultLeft(set, process, def1, runAfter);
        // проверка утверджений
        NauAssert.assertContentEquals(set, op2, op4);
        set.clear();
    }

    @Test(expected = IllegalArgumentException.class)
    public void registerRelation_EmptyOpId()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        // вызов системы
        domain.registerRelation("", orderAfter, opId);
        // проверка утверджений
    }

    @Test(expected = IllegalArgumentException.class)
    public void registerRelation_EqualOpId()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        // вызов системы
        domain.registerRelation(opId, orderAfter, opId, defprocess.getId());
        // проверка утверджений
    }

    @Test(expected = IllegalArgumentException.class)
    public void registerRelation_NullOp()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        // вызов системы
        domain.registerRelation(opId, null, defOpId);
        // проверка утверджений
    }

    @Test(expected = IllegalArgumentException.class)
    public void registerRelation_NullOpId()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        // вызов системы
        domain.registerRelation(opId, orderAfter, null, process.getId());
        // проверка утверджений
    }

    @Test
    public void registerRelation_Override()
    {
        // настройка системы
        String left = UUIDGenerator.get().nextUUID();
        String right = UUIDGenerator.get().nextUUID();
        domain.registerRelation(left, orderAfter, right, process.getId(), DEF_PRIO);
        Relation original = domain.getRelation(left, right, process.getId());
        // проверка утверджений
        org.junit.Assert.assertNotNull("Не удалось зарегистрировать правило", original);
        // вызов системы
        domain.registerRelation(left, no, right, process.getId(), DEF_PRIO);
        Relation r = domain.getRelation(left, right, process.getId());
        // проверка утверджений
        org.junit.Assert.assertSame("Переопределено правило с равным приоритетом", original, r);
        // вызов системы
        domain.registerRelation(left, runBefore, right, process.getId(), DEF_PRIO + 1);
        r = domain.getRelation(left, right, process.getId());
        // проверка утверджений
        org.junit.Assert.assertNotNull("Не удалось зарегистрировать правило", r);
        org.junit.Assert.assertNotSame("Не удалось переопределить правило", original, r);
        org.junit.Assert.assertEquals("Не удалось переопределить правило", runBefore, r.op);
    }

    @Test
    public void searchOperation()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        domain.registerOperation(opId, OperationStub.class);
        // вызов системы
        Operation op = domain.searchOperation(null, this.defOpId);
        // проверка утверджений
        org.junit.Assert.assertNull("Метод должен искать операции только в своём объекте домена", op);
        // настройка системы
        domain.registerDefaultOperation(this.defOpId, OperationStub.class);
        // вызов системы
        op = domain.searchOperation(false, this.defOpId);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
        org.junit.Assert.assertSame("Не удалось переопределить операцию по умолчанию", OperationStub.class.getName(),
                op.opClassName);
        // вызов системы
        op = domain.searchOperation(true, opId);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
    }

    @Test
    public void searchOperationInBranch()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        domain.registerOperation(opId, OperationStub.class);
        String opId2 = UUIDGenerator.get().nextUUID();
        left.registerDefaultOperation(opId2, AtomOperationBase.class);

        // вызов системы
        Operation op = parent.searchOperationInBranch(null, this.defOpId);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
        org.junit.Assert.assertEquals(this.defOpId, op.getId());
        // вызов системы
        op = parent.searchOperationInBranch(true, opId);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
        org.junit.Assert.assertEquals(opId, op.getId());
        // вызов системы
        op = parent.searchOperationInBranch(false, opId2);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
        org.junit.Assert.assertEquals(opId2, op.getId());

        // вызов системы
        op = left.searchOperationInBranch(null, opId);
        // проверка утверджений
        org.junit.Assert.assertNull("Операция не должна быть найдена", op);
        // вызов системы
        op = child.searchOperationInBranch(null, opId2);
        // проверка утверджений
        org.junit.Assert.assertNull("Операция не должна быть найдена", op);
    }

    @Test
    public void searchOperationInInners()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        domain.registerOperation(opId, OperationStub.class);
        String opId2 = UUIDGenerator.get().nextUUID();
        left.registerDefaultOperation(opId2, AtomOperationBase.class);
        // вызов системы
        Operation op = global.searchOperationInInners(false, this.defOpId);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
        org.junit.Assert.assertEquals(this.defOpId, op.getId());
        // вызов системы
        op = parent.searchOperationInInners(true, opId);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
        org.junit.Assert.assertEquals(opId, op.getId());
        // вызов системы
        op = parent.searchOperationInInners(null, opId2);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
        org.junit.Assert.assertEquals(opId2, op.getId());
        // вызов системы
        op = child.searchOperationInInners(null, opId);
        // проверка утверджений
        org.junit.Assert.assertNull("Операция не должна быть найдена", op);
        // вызов системы
        op = child.searchOperationInInners(null, opId2);
        // проверка утверджений
        org.junit.Assert.assertNull("Операция не должна быть найдена", op);
    }

    @Test
    public void searchOperationInOuters()
    {
        // настройка системы
        String opId = UUIDGenerator.get().nextUUID();
        domain.registerOperation(opId, OperationStub.class);
        String opId2 = UUIDGenerator.get().nextUUID();
        left.registerDefaultOperation(opId2, AtomOperationBase.class);
        // вызов системы
        Operation op = global.searchOperationInOuters(false, this.defOpId);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
        org.junit.Assert.assertEquals(this.defOpId, op.getId());
        // вызов системы
        op = child.searchOperationInOuters(true, opId);
        // проверка утверджений
        org.junit.Assert.assertNotNull("Операция не найдена", op);
        org.junit.Assert.assertEquals(opId, op.getId());
        // вызов системы
        op = parent.searchOperationInOuters(null, opId2);
        // проверка утверджений
        org.junit.Assert.assertNull("Операция не должна быть найдена", op);
        // вызов системы
        op = child.searchOperationInOuters(null, opId2);
        // проверка утверджений
        org.junit.Assert.assertNull("Операция не должна быть найдена", op);
    }

    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        global = new Domain(IBusinessOperationsRegistry.GLOBAL_DOMAIN_ID);

        parent = new Domain("parentD");
        parent.outer = global;
        global.inners.add(parent);

        domain = new Domain("domainD");
        domain.outer = parent;
        parent.inners.add(domain);

        child = new Domain("childD");
        child.outer = domain;
        domain.inners.add(child);

        left = new Domain("leftD");
        left.outer = parent;
        parent.inners.add(left);

        this.defOpId = "defOpId";
        this.defOpClass = SetAttrValueOperationWithCheck.class;
        global.registerDefaultOperation(this.defOpId, this.defOpClass);

        Mockito.when(process.getId()).thenReturn("process1");
        Mockito.when(process.getRelation(Mockito.<RelationKey> any(), Mockito.<Domain> any())).thenReturn(
                Collections.<Relation> emptySet());
        Mockito.when(defprocess.getId()).thenReturn(IBusinessOperationsRegistry.DEFAULT_PROCESS_ID);
    }

    @After
    public void tearDown()
    {
        global = parent = domain = child = left = null;
    }

}
