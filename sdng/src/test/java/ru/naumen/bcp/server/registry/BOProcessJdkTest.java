package ru.naumen.bcp.server.registry;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Answers;
import org.mockito.ArgumentMatcher;
import org.mockito.Mockito;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;
import org.springframework.context.ApplicationEventPublisher;

import ru.naumen.NauAssert;
import ru.naumen.bcp.server.operations.IAtomOperation;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IBOContext;
import ru.naumen.bcp.server.registry.IBusinessOperationsRegistry.RelationOp;
import ru.naumen.core.shared.utils.UUIDGenerator;

/**
 * Тестирование класса {@link BOProcess}
 *
 * <AUTHOR>
 * @since 11.11.2010
 */
public class BOProcessJdkTest
{
    static class Contains<T> implements ArgumentMatcher<T>
    {

        Collection<T> collection;

        public Contains(Collection<T> collection)
        {
            this.collection = collection;
        }

        @Override
        public boolean matches(Object actual)
        {
            return this.collection.contains(actual);
        }
    }

    static class QueryAnswer implements Answer<Object>
    {
        static final int ROSIZE = RelationOp.values().length;

        Map<String, String[][][]> rules = new HashMap<String, String[][][]>();
        String[][][] defaults = new String[2][ROSIZE][];

        @SuppressWarnings("unchecked")
        @Override
        public Object answer(InvocationOnMock invocation) throws Throwable
        {
            String mName = invocation.getMethod().getName();
            Object[] args = invocation.getArguments();
            Collection<String> result = (Collection<String>)args[0];
            int idx0 = mName.startsWith("queryLeft") ? 0 : 1;
            String opId = mName.contains("Default") ? null : (String)args[2];
            RelationOp[] ops = Arrays.copyOfRange(args, 3, args.length, RelationOp[].class);
            for (RelationOp op : ops)
            {
                String[][][] arr = opId == null ? defaults : rules.get(opId);
                String[] res = arr != null ? arr[idx0][op.ordinal()] : null;
                if (res != null)
                {
                    result.addAll(Arrays.asList(res));
                }
            }
            return null;
        }

        QueryAnswer forQueryLeftOpIds(String opId, RelationOp op, String... results)
        {
            return put(opId, 0, op.ordinal(), results);
        }

        QueryAnswer forQueryLeftOpIdsWithDefaultRight(RelationOp op, String... results)
        {
            return put(null, 0, op.ordinal(), results);
        }

        QueryAnswer forQueryRightOpIds(String opId, RelationOp op, String... results)
        {
            return put(opId, 1, op.ordinal(), results);
        }

        QueryAnswer forQueryRightOpIdsWithDefaultLeft(RelationOp op, String... results)
        {
            return put(null, 1, op.ordinal(), results);
        }

        private QueryAnswer put(String opId, int idx0, int idx1, String[] values)
        {
            String[][][] arr = opId == null ? defaults : rules.get(opId);
            if (arr == null)
            {
                arr = new String[2][ROSIZE][];
                rules.put(opId, arr);
            }
            arr[idx0][idx1] = values;
            return this;
        }
    }

    QueryAnswer qAnswer;

    @Mock(answer = Answers.RETURNS_MOCKS)
    BusinessOperationsRegistry registry;

    BOProcess process;

    @Mock
    Domain domain;

    @Mock
    IBOContext context;
    @Mock
    ApplicationEventPublisher eventPublisher;

    /**
     * Идентификаторы операций
     */
    String def, // по умолчанию
            op1, op2, op3, op4, op5, // именованные (зарегистрированы)
            op6;

    /**
     * При добавлении операции (op7)внутри выполнения другой операции (op5) - она будет добавлена в общий маршрут и
     * выполнена.
     * Порядок в данных тестах не учитывается.
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    @Test
    public void addOperationDuringPerform()
    {
        String processId = UUIDGenerator.get().nextUUID();

        final String op7 = UUIDGenerator.get().nextUUID();
        //qAnswer.forQueryRightOpIds(op7, RelationOp.orderBefore, op5);

        Mockito.when(registry.getDomain(Mockito.any())).thenReturn(domain);
        IAtomOperation operation = Mockito.mock(IAtomOperation.class);
        Mockito.when(registry.getOperation(Mockito.any(), Mockito.eq(op6))).thenReturn(operation);

        Mockito.doAnswer(new Answer<Void>()
        {
            @Override
            public Void answer(InvocationOnMock invocation) throws Throwable
            {
                process.addOperations(Arrays.asList(op7));
                return null;
            }

        }).when(operation).perform(Mockito.<AtomOperationContext> any());

        // вызов системы
        process = new BOProcess(processId, registry, Arrays.asList(op1, op6));
        process.init(context);
        process.setEventPublisher(eventPublisher);
        process.perform();

        // проверка утверджений
        NauAssert.assertEmpty(process.operationsToUpdate);
        org.junit.Assert.assertFalse(process.needUpdate);
        NauAssert.assertContentEquals(process.route, op1, op2, op3, op4, op5, op6, op7);
    }

    @Test
    public void addRequiredOps()
    {
        String processId = UUIDGenerator.get().nextUUID();
        process = new BOProcess(processId, registry, Collections.<String> emptySet());
        // вызов системы
        Set<String> set = process.addRequiredOps(domain, Arrays.asList(op1, op6));
        // проверка утверджений
        NauAssert.assertContentEquals(set, op1, op2, op3, op4, op5, op6);
    }

    /**
     * При вызове метода {#updateProcessIfNeeded} происходит обновление маршрута.
     * В итоговый маршрут попадают все правила - и добавленные через конструктор, и добавленные методом {#addOperation}
     */
    @Test
    public void lazyOperationsSorting()
    {
        String processId = UUIDGenerator.get().nextUUID();

        // вызов системы
        Mockito.when(registry.getDomain(Mockito.any())).thenReturn(domain);

        process = new BOProcess(processId, registry, Arrays.asList(op1, op6));
        process.init(context);
        String op7 = UUIDGenerator.get().nextUUID();
        process.addOperations(Arrays.asList(op7));
        process.updateProcessIfNeeded();

        // проверка утверджений
        NauAssert.assertEmpty(process.operationsToUpdate);
        org.junit.Assert.assertFalse(process.needUpdate);
        NauAssert.assertContentEquals(process.route, op1, op2, op3, op4, op5, op6, op7);
    }

    /**
     * При вызове метода {#perform} происходит обновление маршрута.
     * В итоговый маршрут попадают все правила - и добавленные через конструктор, и добавленные методом {#addOperation}
     */
    @Test
    public void lazyOperationsSortingAfterPerform()
    {
        String processId = UUIDGenerator.get().nextUUID();

        // вызов системы
        Mockito.when(registry.getDomain(Mockito.any())).thenReturn(domain);

        process = new BOProcess(processId, registry, Arrays.asList(op1, op6));
        process.init(context);
        process.setEventPublisher(eventPublisher);
        process.perform();

        // проверка утверджений
        NauAssert.assertEmpty(process.operationsToUpdate);
        org.junit.Assert.assertFalse(process.needUpdate);
        NauAssert.assertContentEquals(process.route, op1, op2, op3, op4, op5, op6);
    }

    /**
     * При вызове метода {#step} происходит обновление маршрута.
     * В итоговый маршрут попадают все правила - и добавленные через конструктор, и добавленные методом {#addOperation}
     */
    @Test
    public void lazyOperationsSortingAfterStep()
    {
        String processId = UUIDGenerator.get().nextUUID();

        // вызов системы
        Mockito.when(registry.getDomain(Mockito.any())).thenReturn(domain);

        process = new BOProcess(processId, registry, Arrays.asList(op1, op6));
        process.init(context);
        process.step();

        // проверка утверджений
        NauAssert.assertEmpty(process.operationsToUpdate);
        org.junit.Assert.assertFalse(process.needUpdate);
        NauAssert.assertContentEquals(process.route, op1, op2, op3, op4, op5, op6);
    }

    /**
     * После создания и инициализации процесс не содержит правильно отсортированный маршрут - сортировка произойдёт
     * только при выполнении процесса.
     * Все добавленные операции после создания процесса и до его выполнения помещаются в список "необходимо обновить".
     */
    @Test
    public void operationsToUpdateNotEmptyAfterAdd()
    {
        String processId = UUIDGenerator.get().nextUUID();

        // вызов системы
        process = new BOProcess(processId, registry, Arrays.asList(op1, op6));
        process.init(context);
        //
        NauAssert.assertEmpty(process.route);

        String op7 = UUIDGenerator.get().nextUUID();
        process.addOperations(Arrays.asList(op7));

        // проверка утверджений
        org.junit.Assert.assertTrue(process.needUpdate); // флаг "необходимо обновление"
        NauAssert.assertContentEquals(process.operationsToUpdate, op1, op6,
                op7); // все операции добавлены в список "необходимо обновить"
    }

    /**
     * После выполнения метода {#addOperation} все добавленные операции помещаются в список "к обновлению" и меняется
     * состояние флага на "необходимо обновить".
     */
    @Test
    public void operationsToUpdateNotEmptyAfterAddOperation()
    {
        String processId = UUIDGenerator.get().nextUUID();

        // вызов системы
        Mockito.when(registry.getDomain(Mockito.any())).thenReturn(domain);

        process = new BOProcess(processId, registry, Arrays.asList(op1, op6));
        process.init(context);
        process.updateProcessIfNeeded();

        String op7 = UUIDGenerator.get().nextUUID();
        process.addOperations(Arrays.asList(op7));

        // проверка утверджений
        NauAssert.assertContentEquals(process.operationsToUpdate, op7);
        org.junit.Assert.assertTrue(process.needUpdate);
        NauAssert.assertContentEquals(process.route, op1, op2, op3, op4, op5, op6);
    }

    /**
     * После создания и инициализации процесс не содержит правильно отсортированный маршрут - сортировка произойдёт
     * только при выполнении процесса.
     */
    @Test
    public void operationsToUpdateNotEmptyAfterInit()
    {
        String processId = UUIDGenerator.get().nextUUID();

        // вызов системы
        process = new BOProcess(processId, registry, Arrays.asList(op1, op6));
        process.init(context);

        // проверка утверджений
        org.junit.Assert.assertTrue(process.needUpdate); // флаг "необходимо обновление"
        NauAssert.assertContentEquals(process.operationsToUpdate, op1,
                op6); // все операции добавлены в список "необходимо обновить"
    }

    @SuppressWarnings("unchecked")
    @Before
    public void setUp()
    {
        MockitoAnnotations.initMocks(this);

        def = UUIDGenerator.get().nextUUID();
        op1 = UUIDGenerator.get().nextUUID();
        op2 = UUIDGenerator.get().nextUUID();
        op3 = UUIDGenerator.get().nextUUID();
        op4 = UUIDGenerator.get().nextUUID();
        op5 = UUIDGenerator.get().nextUUID();
        op6 = UUIDGenerator.get().nextUUID();

        qAnswer = new QueryAnswer();

        Mockito.when(domain.getId()).thenReturn("defaultDomain");
        Mockito.doAnswer(qAnswer)
                .when(domain)
                .queryRightOpIds(Mockito.anyCollection(), Mockito.any(BOProcess.class), Mockito.anyString(),
                        Mockito.any(RelationOp[].class));
        Mockito.doAnswer(qAnswer)
                .when(domain)
                .queryRightOpIdsWithDefaultLeft(Mockito.anyCollection(), Mockito.any(BOProcess.class),
                        Mockito.anyString(), Mockito.any(RelationOp[].class));

        Mockito.doAnswer(qAnswer)
                .when(domain)
                .queryLeftOpIds(Mockito.anyCollection(), Mockito.any(BOProcess.class), Mockito.anyString(),
                        Mockito.any(RelationOp[].class));
        Mockito.doAnswer(qAnswer)
                .when(domain)
                .queryLeftOpIdsWithDefaultRight(Mockito.anyCollection(), Mockito.any(BOProcess.class),
                        Mockito.anyString(), Mockito.any(RelationOp[].class));

        Mockito.when(
                domain.isRegisteredOperation(Mockito.argThat(new Contains<String>(Arrays.asList(op1, op2, op3, op4,
                        op5))))).thenReturn(true);

        qAnswer.forQueryRightOpIds(op1, RelationOp.runBefore, op2, op3); // op1 >> op2,op3
        qAnswer.forQueryRightOpIds(op2, RelationOp.orderAfter, op3); // op2 > op3
        qAnswer.forQueryRightOpIds(op3, RelationOp.runAfter, op4); // op3 << op4
        qAnswer.forQueryRightOpIdsWithDefaultLeft(RelationOp.runAfter, op5); // default << op5
    }

    @After
    public void tearDown()
    {
        process = null;
    }
}
