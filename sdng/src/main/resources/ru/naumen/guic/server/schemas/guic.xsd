<?xml version="1.0" encoding="UTF-8"?>
<xs:schema version="1.0" xmlns:xs="http://www.w3.org/2001/XMLSchema">
    <xs:element name="card" type="card"/>
    <xs:element name="form" type="form"/>
    <xs:element name="publisher" type="publisher"/>
    <xs:complexType name="field">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence/>
                <xs:attribute name="elements" type="xs:string"/>
                <xs:attribute name="enabled" type="xs:boolean"/>
                <xs:attribute name="on-change" type="fieldOnChange"/>
                <xs:attribute name="presentation" type="xs:string"/>
                <xs:attribute name="required" type="xs:boolean"/>
                <xs:attribute name="validation">
                    <xs:simpleType>
                        <xs:list itemType="fieldValidation"/>
                    </xs:simpleType>
                </xs:attribute>
                <xs:attribute name="value" type="xs:string"/>
                <xs:attribute name="description" type="xs:string"/>
                <xs:attribute name="max-length" type="xs:int"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="componentBase">
        <xs:sequence/>
        <xs:attribute name="client-controller" type="xs:string"/>
        <xs:attribute name="controller" type="xs:string"/>
        <xs:attribute name="debug-id" type="xs:string"/>
        <xs:attribute name="name" type="xs:string" use="required"/>
        <xs:attribute name="title" type="xs:string"/>
        <xs:attribute name="visible" type="xs:boolean"/>
        <xs:attribute name="permissionVisibleCode" type="xs:string"/>
    </xs:complexType>
    <xs:complexType name="button">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence/>
                <xs:attribute name="dialog-caption" type="xs:string"/>
                <xs:attribute name="dialog-message" type="xs:string"/>
                <xs:attribute name="form" type="xs:string"/>
                <xs:attribute name="style-code" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="property-list">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0"
                        name="button" type="button"/>
                    <xs:element maxOccurs="unbounded" minOccurs="0"
                        name="property" type="property"/>
                </xs:sequence>
                <xs:attribute name="attention" type="xs:string"/>
                <xs:attribute name="show-title" type="xs:boolean"/>
                <xs:attribute name="show-captions" type="xs:boolean"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="property">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence/>
                <xs:attribute name="presentation" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="card">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0"
                        name="tabs" nillable="true" type="tab"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="tab">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0"
                        name="property-list" type="property-list"/>
                    <xs:element maxOccurs="unbounded" minOccurs="0"
                        name="table-list" type="table-list"/>
                </xs:sequence>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="table-list">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0"
                        name="button" type="button"/>
                    <xs:choice maxOccurs="unbounded" minOccurs="0">
                        <xs:element name="column" type="column"/>
                        <xs:element name="action-column" type="action-column"/>
                        <xs:element name="form-column" type="form-column"/>
                    </xs:choice>
                    <xs:element maxOccurs="unbounded" minOccurs="0"
                        name="mass-operation" type="mass-operation"/>
                </xs:sequence>
                <xs:attribute name="filtering-enabled" type="xs:boolean"/>
                <xs:attribute name="complex-sort-enabled" type="xs:boolean"/>
                <xs:attribute name="paging-enabled" type="xs:boolean"/>
                <xs:attribute name="selection-enabled" type="xs:boolean"/>
                <xs:attribute name="show-title" type="xs:boolean"/>
                <xs:attribute name="default-page-size" type="xs:int"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="column">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence/>
                <xs:attribute name="filter-conditions">
                    <xs:simpleType>
                        <xs:list itemType="filterCondition"/>
                    </xs:simpleType>
                </xs:attribute>
                <xs:attribute name="filtered" type="xs:boolean"/>
                <xs:attribute name="icon" type="xs:string"/>
                <xs:attribute name="max-line-length" type="xs:int"/>
                <xs:attribute name="sortable" type="xs:boolean"/>
                <xs:attribute name="title-hint" type="xs:string"/>
                <xs:attribute name="width" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="action-column">
        <xs:complexContent>
            <xs:extension base="column">
                <xs:sequence/>
                <xs:attribute name="caption" type="xs:string"/>
                <xs:attribute name="caption-builder" type="xs:string"/>
                <xs:attribute name="message" type="xs:string"/>
                <xs:attribute name="message-builder" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="form-column">
        <xs:complexContent>
            <xs:extension base="column">
                <xs:sequence/>
                <xs:attribute name="form" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="mass-operation">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence/>
                <xs:attribute name="dialog-caption" type="xs:string"/>
                <xs:attribute name="dialog-message" type="xs:string"/>
                <xs:attribute name="dialog-caption-builder" type="xs:string"/>
                <xs:attribute name="dialog-message-builder" type="xs:string"/>
                <xs:attribute name="form" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="form">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence>
                    <xs:choice maxOccurs="unbounded" minOccurs="0">
                        <xs:element name="field" type="field"/>
                        <xs:element name="check-box-field" type="check-box-field"/>
                        <xs:element name="script-field" type="script-field"/>
                    </xs:choice>
                </xs:sequence>
                <xs:attribute name="fixed" type="xs:boolean"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="check-box-field">
        <xs:complexContent>
            <xs:extension base="field">
                <xs:sequence/>
                <xs:attribute name="control-visibility" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="script-field">
        <xs:complexContent>
            <xs:extension base="field">
                <xs:sequence/>
                <xs:attribute name="script-category" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:complexType name="publisher">
        <xs:complexContent>
            <xs:extension base="componentBase">
                <xs:sequence>
                    <xs:element maxOccurs="unbounded" minOccurs="0"
                        name="tab" type="tab"/>
                </xs:sequence>
                <xs:attribute name="place-class" type="xs:string"/>
            </xs:extension>
        </xs:complexContent>
    </xs:complexType>
    <xs:simpleType name="fieldOnChange">
        <xs:restriction base="xs:string">
            <xs:enumeration value="ServerRefresh"/>
            <xs:enumeration value="ClientRefreshSync"/>
            <xs:enumeration value="ClientRefreshAsync"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="fieldValidation">
        <xs:restriction base="xs:string">
            <xs:enumeration value="Code"/>
            <xs:enumeration value="Code64"/>
            <xs:enumeration value="Port"/>
            <xs:enumeration value="MetainfoKey"/>
            <xs:enumeration value="StringLength1000"/>
            <xs:enumeration value="StringLength100000"/>
            <xs:enumeration value="XmlChars"/>
            <xs:enumeration value="CodeScriptModule"/>
        </xs:restriction>
    </xs:simpleType>
    <xs:simpleType name="filterCondition">
        <xs:restriction base="xs:string">
            <xs:enumeration value="StringContains"/>
            <xs:enumeration value="StringNotContains"/>
            <xs:enumeration value="Empty"/>
            <xs:enumeration value="NotEmpty"/>
            <xs:enumeration value="ContainsYesNo"/>
            <xs:enumeration value="IntegerGreater"/>
            <xs:enumeration value="IntegerLess"/>
            <xs:enumeration value="IntegerContains"/>
            <xs:enumeration value="IntegerNotContains"/>
            <xs:enumeration value="SelectListContains"/>
            <xs:enumeration value="SelectListNotContains"/>
            <xs:enumeration value="DateFromTo"/>
            <xs:enumeration value="LastNDays"/>
            <xs:enumeration value="Today"/>
        </xs:restriction>
    </xs:simpleType>
</xs:schema>
