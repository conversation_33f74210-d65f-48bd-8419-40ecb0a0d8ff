afterSubjectAttribute=позже атрибута текущего объекта
afterUserAttribute=позже атрибута текущего пользователя
backTimerDeadLineContains=просроченность содержит
backTimerDeadLineFromTo=время окончания с ... по
beforeSubjectAttribute=раньше атрибута текущего объекта
beforeUserAttribute=раньше атрибута текущего пользователя
conditionTitles=contains, notContains, containsInSet, notContainsInSet,  null, notNull, greater, less, fromTo, lastN, nextN, lastNHours, nextNHours, startingFrom, finishingUpTo, today, timerStatusContains, timerStatusNotContains, backTimerDeadLineFromTo, backTimerDeadLineContains, titleContains, titleNotContains, containsWithRemoved, notContainsWithRemoved, containsWithNested, equalsToUser, notEqualsToUser, equalsToUserAttribute, notEqualsToUserAttribute, equalsToSubjectAttribute, notEqualsToSubjectAttribute, containsUser, notContainsUser, containsUserAttribute, notContainsUserAttribute, containsSubjectAttribute, notContainsSubjectAttribute, equalsToSubject, notEqualsToSubject, containsSubject, notContainsSubject, beforeSubjectAttribute, afterSubjectAttribute, beforeUserAttribute, afterUserAttribute, fullTextSearch_contains, fullTextSearch_notContains, fullTextSearch_null, fullTextSearch_equal, containsSubjectWithNested, containsSubjectAttributeWithNested, containsUserAttributeWithNested, equalsToSubjectWithNested, equalsToSubjectAttributeWithNested, equalsToUserAttributeWithNested
contains=содержит
containsInSet=содержит любое из значений
notContainsInSet=не содержит любое из значений
containsSubject=содержит текущий объект
containsSubjectWithNested=содержит текущий объект (включая вложенные)
notContainsSubject=не содержит текущий объект
containsSubjectAttribute=содержит атрибут текущего объекта
containsSubjectAttributeWithNested=содержит атрибут текущего объекта (включая вложенные)
notContainsSubjectAttribute=не содержит атрибут текущего объекта
containsUser=содержит текущего пользователя
notContainsUser=не содержит текущего пользователя
containsUserAttribute=содержит атрибут текущего пользователя
containsUserAttributeWithNested=содержит атрибут текущего пользователя (включая вложенные)
notContainsUserAttribute=не содержит атрибут текущего пользователя
containsWithNested=содержит (включая вложенные)
containsWithRemoved=содержит (включая архивные)
equalsTo=равен
equalsToSubject=равно текущему объекту
equalsToSubjectWithNested=равно текущему объекту (включая вложенные)
notEqualsToSubject=не равно текущему объекту
equalsToSubjectAttribute=равно атрибуту текущего объекта
equalsToSubjectAttributeWithNested=равно атрибуту текущего объекта (включая вложенные)
notEqualsToSubjectAttribute=не равно атрибуту текущего объекта
equalsToUser=равно текущему пользователю
notEqualsToUser=не равно текущему пользователю
equalsToUserAttribute=равно атрибуту текущего пользователя
equalsToUserAttributeWithNested=равно атрибуту текущего пользователя (включая вложенные)
notEqualsToUserAttribute=не равно атрибуту текущего пользователя
finishingUpTo=заканчивая до
fromTo=с ... по
fullTextSearch_contains=содержит
fullTextSearch_equal=равно
fullTextSearch_notContains=не содержит
fullTextSearch_null=пусто
greater=больше
inFuture=вперед
inPast=назад
lastN=за последние "n" дней
lastNHours=за последние "n" часов
less=меньше
myself=я сам (сама)
notMyself=не я
nextN=в ближайшие "n" дней
nextNHours=в ближайшие "n" часов
#
#Tue Jun 04 12:03:02 YEKT 2013
notContains=не содержит
notContainsAndNotEmptyTo=не содержит (и не пусто)
notContainsIncludeEmptyTo=не содержит (включая пустые)
notContainsWithRemoved=не содержит (включая архивные)
notEqualsAndNotEmptyTo=не равно (и не пусто)
notEqualsIncludeEmptyTo=не равно (включая пустые)
notEqualsTo=не равен
notNull=не пусто
null=пусто
numberEqualsTo=равно
numberNotEqualsTo=не равно
startingFrom=начиная с
timerStatusContains=статус содержит
timerStatusNotContains=статус не содержит
titleContains=название содержит
titleNotContains=название не содержит
today=сегодня
