afterSubjectAttribute=after current object attribute
afterUserAttribute=after current user attribute
backTimerDeadLineContains=delay contains
backTimerDeadLineFromTo=end time from ... to
beforeSubjectAttribute=before current object attribute
beforeUserAttribute=before current user attribute
conditionTitles=contains, notContains, containsInSet, notContainsInSet, null, notNull, greater, less, fromTo, lastN, nextN, lastNHours, nextNHours, startingFrom, finishingUpTo, today, timerStatusContains, timerStatusNotContains, backTimerDeadLineFromTo, backTimerDeadLineContains, titleContains, titleNotContains, containsWithRemoved, notContainsWithRemoved, containsWithNested, equalsToUser, notEqualsToUser, equalsToUserAttribute, notEqualsToUserAttribute, equalsToSubjectAttribute, notEqualsToSubjectAttribute, containsUser, notContainsUser, containsUserAttribute, notContainsUserAttribute, containsSubjectAttribute, notContainsSubjectAttribute, containsSubject, notContainsSubject, equalsToSubject, notEqualsToSubject, beforeSubjectAttribute, afterSubjectAttribute, beforeUserAttribute, afterUserAttribute, fullTextSearch_contains, fullTextSearch_notContains, fullTextSearch_null, fullTextSearch_equal, containsSubjectWithNested, containsSubjectAttributeWithNested, containsUserAttributeWithNested, equalsToSubjectWithNested, equalsToSubjectAttributeWithNested, equalsToUserAttributeWithNested
contains=contains
containsInSet=contains any of the values
notContainsInSet=not contains any of the values
containsSubject=contains current object
containsSubjectWithNested=contains the current object (including nested)
notContainsSubject=not contains current object
containsSubjectAttribute=contains current object attribute
containsSubjectAttributeWithNested=contains the attribute of the current object (including nested)
notContainsSubjectAttribute=not contains current object attribute
containsUser=contains current user
notContainsUser=not contains current user
containsUserAttribute=contains current user attribute
containsUserAttributeWithNested=contains the attribute of the current user (including nested)
notContainsUserAttribute=not contains current user attribute
containsWithNested=contains (including nested)
containsWithRemoved=contains (including archive)
equalsTo=equal
equalsToSubject=equal current object
equalsToSubjectWithNested=equal to the current object (including nested)
notEqualsToSubject=not equal current object
equalsToSubjectAttribute=equal current object attribute
equalsToSubjectAttributeWithNested=equal to the attribute of the current object (including nested)
notEqualsToSubjectAttribute=not equal current object attribute
equalsToUser=equal current user
notEqualsToUser=not equal current user
equalsToUserAttribute=equal current user attribute
equalsToUserAttributeWithNested=equal to the attribute of the current user (including nested)
notEqualsToUserAttribute=not equal current user attribute
finishingUpTo=finishing up to
fromTo=from ... to
fullTextSearch_contains=contains
fullTextSearch_equal=equal
fullTextSearch_notContains=does not contain
fullTextSearch_null=null
greater=more
inFuture=in future
inPast=in past
lastN=for last "n" days
lastNHours=for last "n" hours
less=less than
myself=myself
notMyself=not myself
nextN=in the next "n" days
nextNHours=in the next "n" hours
#
#Tue Jun 04 12:03:02 YEKT 2013
notContains=does not contain
notContainsAndNotEmptyTo=does not contain (and not empty)
notContainsIncludeEmptyTo=does not contain (including empty)
notContainsWithRemoved=does not contain (including archive)
notEqualsAndNotEmptyTo=not equal (and not empty)
notEqualsIncludeEmptyTo=not equal (including empty)
notEqualsTo=not equal
notNull=not null
null=null
numberEqualsTo=equal
numberNotEqualsTo=not equal
startingFrom=starting from
timerStatusContains=state contains
timerStatusNotContains=state does not contain
titleContains=title contains
titleNotContains=title does not contain
today=today
