@external filterButton, modal-form, g-button, buttonText, gwt-Label, radiobuttons, compactMode, internalScroll;
@external formdate, formtextfield, formselect, formselect__selected, close;
@external formTextHyperlinkText, formTextHyperlinkURL;
@external formselectTriangle; 
@external filterCondition;
@external fontIcon;
@external modal-form-scroll-content;

@eval baseFont ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().baseFont();
@eval panelBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().panelBackground();
@eval inputHeight ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputHeight();
@eval inputBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().inputBackground();
@eval textColor ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().textColor();
@eval badgeHoverBackground ru.naumen.core.client.widgets.ThemeService.INSTANCE.getValue().badgeHoverBackground();


.filterAndElement,
.filterOrElement {
	position: relative;
	}

.filterElementClose {
	text-decoration: none;
	position: absolute;
	z-index: 1;
	cursor: pointer;
	}

.filterAndButton,
.filterOrButton {
	height: 16px;
	line-height: 16px;
	min-width: 48px;
	text-align: center;
	border-radius: 2px;
	font-size: 12px;
	}

.filterOrLabel,
.filterAndLabel {
	cursor: default;
	display: inline-block;
    background: badgeHoverBackground;
    font-weight: 600;
    color: textColor;
    box-sizing: border-box;
    padding: 0 12px;
	}

.filterCondSelect input {
	width: 200px;
	}
	.modal-form .filterCondSelect input {
		width: 220px;
		}

.filterValueWidget {
	min-width: 180px;
	width: 100%;
	white-space: nowrap;
	}

.filterPanel {
	margin: 8px 0 0;
	}
	.filterPanel .filterHorizontalBooleanRadioButton {
		display: inline-block;
		}
		.filterPanel .filterHorizontalBooleanRadioButton span {
			display: inline;
			}
		.filterPanel .filterHorizontalBooleanRadioButton input {
			float: inherit;
			position: relative;
			top: 2px;
			}
	.filterPanel .filterButton {
		float: left;
		cursor: pointer;
		z-index: 0; /*button must be under contentHeader*/
		}

.filterInnerPanel {
	width: 100%;
	}

.filterApplyButton,
.filterShowButton,
.filterCancelButton,
.filterResetButton {}

.filterValueWidget > .gwt-Label + div {
	display: inline-block;
	vertical-align: top;
	padding-right: 8px;
	}
.filterValueWidget > .gwt-Label {
	font-size: 12px;
	top: auto;
	padding: 0;
	}

.filterCondSelect,
.filterAttrSelect {
	margin: 0 8px 8px 0;
	}

.filterAttrSelect input {
	width: 300px;
	}
	.modal-form .filterAttrSelect input {
		width: 220px;
		}

.filterAndButton .buttonText,
.filterOrButton .buttonText {
	font-size: 10px;
	line-height: inherit;
	text-transform: capitalize;
	}


	.filterPanel {
		padding: 9px;
		background: panelBackground;
		}
		.filterPanel #gwt-debug-scrollableArea {
			padding-bottom: 8px;
			}
	.filterAndElement,
	.filterOrElement {
		border-radius: 4px;
		padding: 8px 16px;
		}
	.filterAndElement {
		background: inputBackground;
		}
	.filterOrElement {
		margin: 0 0 10px;
		background: panelBackground;
		}
		.filterOrElement:last-child {
			margin: 0 0 5px;
			}
	.filterElementClose {
		background: inputBackground;
		border-radius: 2px;
		top: 0;
		left: 0;
		}
		.filterElementClose .close {
			display: block;
            width: 12px;
            height: 14px;
			}
			.filterElementClose .close:before {
				display: block;
				margin: -2px 0 0 -2px;
				}
	.filterAndButton {
		margin: 5px 0;
		}
		.filterAndButton:last-child {
			margin: 5px 0 0;
			}
		.filterToolPanel {
			padding-top: 10px;
			}
	.filterOrButton {
		position: absolute;
		bottom: 8px;
		margin: 0 0 -12px 17px;
		}
	.filterOrContainer {
		position: relative;
		margin: -20px 0 10px;
		height: 10px;
		}
		.filterOrContainer:last-child {
			margin-bottom: 0;
			}
	.filterCancelButton {
		margin-left: 90px;
		}
	.filterCondSelect {
		clear: none;
		padding-bottom: 0;
		z-index: 10;
		height: 24px;
		white-space: nowrap;
		}
		
	.filterPanel .filterHorizontalBooleanRadioButton span {
		padding-right: 20px;
		}
	.filterValueWidget > .gwt-Label {
		line-height: 26px;
		height: 26px;
		}
	.filterMinimizedCondition {}
		.filterMinimizedCondition > span {
			color: black;
			line-height: 1;
			font-family: baseFont;
			}
		.filterMinimizedCondition .disabledSelectableItem {
			color: #999;
			font-style: italic;
			}
	.filterValueWidget .formTextHyperlinkText,
	.filterValueWidget .formTextHyperlinkURL {
		width: 160px;
		}

.internalScroll .filterPanel #gwt-debug-scrollableArea > div {
	display: table;
	min-width: 100%;
	}

.internalScroll .filterPanel #gwt-debug-scrollableArea {
	padding-top: 8px;
	}

.filterResetButton,
.filterShowButton,
.filterApplyButton {}

.disabledSelectableItem {
    color: #999 !important;
    font-style: italic;
    }