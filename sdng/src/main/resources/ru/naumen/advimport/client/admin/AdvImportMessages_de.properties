addConnection=Verbindung hinzufügen
confirmDeleteAllLogs=Möchten Sie wirklich den gesamten Importverlauf löschen?
toConnectionsList=zur Liste der Verbindungen
viewLogSettings=Einrichten der Log-Anzeige
hideOutDaysPeriod=Protokolle ausblenden, die älter sind als (Tage)
configurationIp=Konfiguration
synchronizationHistory=Synchronization history
importConfirmation=Startbestätigung importieren
toConfigList=zur Konfigurationsliste
creationDate=Erstelldatum
authenticationType=Identifikationstyp
configurations=Konfigurationen
connection=Verbindung
displayHistorySettings=Optionen für die Anzeige der Historie
securityProtocol=Sicherheitsprotokoll
jdbcProtocolDriver=jdbc-Protokollimplementierungsklasse
confirmRun=Sind Sie sicher, dass Sie den Import ''{0}'' ausführen möchten?
configuration=Konfiguration
connections=Verbindungen
version=Version
connectionString=Verbindungsstring
deleteLog=Historie löschen
runThis=Import ''{0}'' ausführen
runCaption=Start des Imports ''{0}''
synchronization=Synchronisation
connectionType=Typ der Verbindung
editingConnectionWith=Bearbeiten der Verbindung zu {0}
addingConnectionTo=Hinzufügen einer Verbindung zu {0}
addConfiguration=Hinzufügen einer Konfiguration
warningOnSkipCertVerification=Verbindung ist nicht sicher.
connectionTimeout=Verbindungs-Timeout, min
connectionsGenitive=ausgewählte Verbindungen
skipCertVerification=Zertifikatsüberprüfung ignorieren
connectionGenitive=Verbindung
versions=Historie der Konfigurationsänderungen.
authorLogin=Autor
authorIP=IP Adresse
configurationsSelected=ausgewählte Konfigurationen
