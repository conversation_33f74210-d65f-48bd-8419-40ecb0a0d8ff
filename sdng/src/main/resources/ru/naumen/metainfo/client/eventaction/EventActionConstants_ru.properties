#
#Wed Jun 05 12:08:02 YEKT 2013

actionConditionsTypes=SCRIPT
actionTypes=ScriptEventAction, NotificationEventAction, PushEventAction, PushMobileEventAction, PushPortalEventAction, IntegrationEventAction, ChangeTrackingEventAction
eventTypes=add, edit, delete, addComment, editComment, addFile, openEditForm, mail, changeState, onsetTimeOfAttr, changeResponsible, escalation, changeMassAttr, bindToMassMaster, bindMassSlave, userEvent, loginSuccessful, logout, alertActivated, alertDeactivated, alertChanged, insertMention, arriveMessageOnQueue, ndapMessage
plannedEventCategory=BEFORE_TIME, IN_TIME, AFTER_TIME
trackingMessageStyles=Information, Warning
trackingRefreshAreas=ChangedContents, WholePage, AttrValues
trackingUiActions=Message, MessageWithRefresh, Autoreload

ChangeTrackingEventAction=Отслеживание изменений
ScriptEventAction=Скрипт
PushEventAction=Уведомление в интерфейсе
NotificationEventAction=Оповещение
PushMobileEventAction=Уведомление в мобильном приложении
PushPortalEventAction=Уведомление на портал
IntegrationEventAction=Отправка во внешнюю очередь

bindMassSlave=Связь с подчиненными объектами
changeState=Смена статуса
mail=Поступление письма
addComment=Добавление комментария к объекту
editComment=Редактирование комментария
edit=Изменение объекта
addFile=Прикрепление файла к объекту
changeMassAttr=Изменение признака массовости
add=Добавление объекта
escalation=Эскалация
delete=Удаление объекта
onsetTimeOfAttr=Наступление времени атрибута
openEditForm=Открытие формы редактирования
changeResponsible=Смена ответственного
bindToMassMaster=Связь с массовым объектом
userEvent=[Пользовательское событие]
loginSuccessful=Вход в систему
logout=Выход из системы
alertActivated=Активация тревоги
alertDeactivated=Деактивация тревоги
alertChanged=Изменение активности тревоги
insertMention=Упоминание в рамках выбранных объектов
arriveMessageOnQueue=Поступление сообщения в очередь
ndapMessage=Сообщение NDAP

ChangedContents=Контенты с изменениями
WholePage=Страница
AttrValues=Значения атрибутов

Message=Сообщение
MessageWithRefresh=Сообщение об изменении с кнопкой Обновить
Autoreload=Автообновление

Information=Информационное
Warning=Предупреждение

BEFORE_TIME=За
AFTER_TIME=Спустя
IN_TIME=В одно время

SCRIPT=Скрипт
