action=Action
actionConditionGenitive=condition
actionConditions=Conditions for action execution
actionInWeb=Action in the web interface
actionParams=Action parameters
actionTypeError=Type of action is not specified
add=Add action
addCommentEvent=Add comment to object
addCondition=Add condition
addEvent=Add object
addFileEvent=Attach file to object
addFormCaption=Add event action
addParameter=Add parameter
addingCondition=Add condition
afterTime=After
alwaysInTheInterface=Always in the interface. Additionally, a browser notice if tab is inactive
asyncActionsHint=The action for an event and its initiation operation will be executed in parallel transactions. If the action for an event cannot be executed, it will not affect execution of the operation. User interface will not be blocked during execution of the action for an event.
attributesTransferredToContext=Attributes transferred to context
attributesTransferredToContextDescription=If the field is filled, then only the selected attributes will be queued for event action and check the asynchronous condition, otherwise the entire object will be queued. It is recommended to fill in the field to reduce the risk of problems with high system load.
beforeTime=Before
changeResponsibleEvent=Change responsible
changeStateEvent=Change state
changeTracking.autoReload=Updated automatically.
changeTrackingDefaultMessageExample=Example.
changeTrackingDefaultMessageExample[addComment]=Example. <PERSON> has added comment: "Hi! Please attach application logs of...".
changeTrackingDefaultMessageExample[addFile]=Example. John Doe has added file "Error.png".
changeTrackingDefaultMessageExample[changeResponsible]=Example. John Doe has changed responsible to Bob Smith.
changeTrackingDefaultMessageExample[changeState]=Example. John Doe has changed state to "Work in progress".
changeTrackingDefaultMessageExample[edit]=Example. John Doe has changed: Priority, Severity, State, Service.
changeTrackingDefaultMessageExample[editComment]=Example. John Doe has edited comment: "Hi! Please attach application logs of...".
changeTrackingDefaultMessageExample[openEditForm]=Example. John Doe is editing: Responsible, State.
closePreview=close preview
commonProperties=Common properties
conditionType=Condition type
currentRecipientTogetherMethodCc=Unable to use parameter "currentRecipient" at the same time with the method "notification.cc" or "notification.ccEmployee".
deleteEvent=Delete object
editCommentEvent=Edit comment
editEvent=Edit object
editFormCaption=Edit event action
editingCondition=Edit condition
emails=E-mail addresses
employees=Employees
emptyEventActionInfo=This block will contain parameters of selected action
errorInExecuteResultAction=You cannot run this action in the web interface.
errorOnlyOneType=The "Attribute time occurrence" event is available for objects of only one class
errorTimeValue=The field must contain non-negative integer value
event=Event
eventActionGenitive=Event action
eventActionTime=Action delay
eventActionsGenitive=selected event actions
eventProperty=Attribute time occurrence
eventTypeError=Event type is not specified
excludeAuthor=Exclude the action author from the list of recipients
externalInteraction=External interaction
format=Format
from=From
globalRoles=Absolute roles
goToEventAction=to event action ''{0}''
goToEventActions=to event actions list
howToDisplayNotice=How to display a notification
htmlFormat=HTML
inHtmlFormat=in HTML format
inTime=At the same time
interfaceAndBrowserNotices=The interface and browser notices
jmsQueue=Action processing queue
mailEvent=Mail receipt
noTemplate=without template
notificationAction=Notification
notificationContent=Notification text
onlyExternalPush=Only browser notice
onlyInTheInterface=Only in the interface
onsetTimeOfAttr=Attribute time occurrence
openPreview=open preview
parameters=Parameters
parametersHandlingIsNotSupported=Parameters handling is not supported when event is called from mobile application.
performActionWhenChangingAttributes=Perform action when changing attributes
performActionWhenChangingAttributesOnAddDescription=If the field is filled, then the action will be performed when an event occurs and at least one attribute selected in the field is filled, otherwise additional checks of the attributes for filling will not be performed. It is recommended to fill in the field to improve system performance.
performActionWhenChangingAttributesOnEditDescription=If the field is filled, then the action will be performed when the event occurs and at least one attribute selected in the field changes, otherwise there will be no additional checks of the attributes for the change. It is recommended to fill in the field to improve system performance.
performActionWhenChangingCommentAttributes=Perform action when changing comment attributes
performSynchronously=Execute synchronously
positionBottomLeft=Bottom left
positionBottomRight=Bottom right
positionSystem=System (wide on bottom of screen)
positionTopLeft=Top left
positionTopRight=Top right
priority=Priority
priorityHigh=High
priorityNormal=Normal
pushContent=Notice text
pushHeaderFormat=Notification header format
pushPosition=Position on the screen (for notice in interface)
roles=Roles
scriptAction=Script
showPreviewRender=switch to HTML mode
showPreviewSource=switch to source mode
showWithTemplate=show with template
showWithoutTemplate=show without template
singleTransactionHint=Event action and its initiating operation will be executed in a single transaction. If the action cannot be executed, the operation will also be canceled. User interface will be blocked during the event action execution.
skipIfUserHasActiveSession=Do not execute if user has active session
style=Style
subject=Subject
syncVerificationHint=Check of synchronous condition and its initiating operation will be executed in a single transaction. If the check fails, the operation will be executed, but the action for this operation will not start.
systemNameInMobileApp=The name of the system in the mobile application
teams=Teams
template=Template
templatePreview=Preview with template
textFormat=Text
to=To
toggleOff=Disable
toggleOn=Enable
updateArea=Update area
usagePlaces=Usage places
useDefaultMessage=Use default message text