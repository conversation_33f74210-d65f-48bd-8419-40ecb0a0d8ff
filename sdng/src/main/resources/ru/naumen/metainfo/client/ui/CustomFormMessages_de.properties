attributeGroup=Attribut-Gruppe
deleteChangeCaseFormConfirmation=Möchten Sie das benutzerdefinierte Formular zur Änderung des Objekttyps wirklich entfernen? Wenn die Transaktion bestätigt wird, wird das Formular nach der Standardlogik erstellt
deleteChangeResponsibleFormConfirmation=Wollen Sie das benutzerdefinierte Formular für den Wechsel der verantwortlichen Person wirklich löschen? Wenn der Vorgang bestätigt wird, wird das Formular nach der Standardlogik erstellt
deleteTitledFormConfirmation=Möchten Sie das Formular ''{0}'' wirklich löschen?
forTransitionsInTypes=Zu den Typen gehen
massDeleteCustomForms=Möchten Sie Benutzerformulare wirklich löschen? Wenn der Vorgang bestätigt wird, werden die Formulare gemäß der Standardlogik generiert.
forTypes=Für Typen
typeOfForm=Formulartyp
addForm=Formular hinzufügen
addingForm=Hinzufügen eines Formulars
editingForm=Formularbearbeitung
changeCaseForm=Formular für die Typänderung
changeResponsibleForm=Formular zum Wechseln der Verantwortung
selectedCasesCount=Ausgewählt: {0} (Klassentypen "{1}")
formTitle=Liste der Formen für "{0}" und seine untergeordneten Typen
commentOnForm=Kommentar auf dem Formular
notFill=Nicht füllen
fill=Füllen
mustFill=Ausfüllen ist obligatorisch
quickAddAndEditForm=Formular zum schnellen Hinzufügen und Bearbeiten
massEditForm=Formular für Massenbearbeitung
attributeDescription=Attributbeschreibung anzeigen
useAsDefault=Als Standardformular verwenden
defaultMassEditFormExist=Ein Standardformular existiert bereits in der Klasse {0}. Nach dem Speichern der Änderungen werden die Schaltflächen für die Massenbearbeitung mit dem Standardformular das aktuelle Formular aufrufen.
defaultMassEditFormDoesNotExist=Dieses Formular wird über die Klasse {0} mit den Massenbearbeitungsschaltflächen mit dem Standardformular in den Listen verknüpft.
toolsWillBeDeleted=Alle zugehörigen Schaltflächen werden zusammen mit dem Formular gelöscht.
useStandardAttributesSet=Systemattributsatz verwenden
immediateObjectSaving=Hinzufügen und Bearbeiten von Objekten beim Speichern eines Schnellformulars
commentOnFormAttributeGroup=Gruppe von Attributen für den Block des Hinzufügens von Kommentaren
