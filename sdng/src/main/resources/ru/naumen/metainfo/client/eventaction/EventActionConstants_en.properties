actionConditionsTypes=SCRIPT
actionTypes=ScriptEventAction, NotificationEventAction, PushEventAction, PushMobileEventAction, PushPortalEventAction, IntegrationEventAction, ChangeTrackingEventAction
eventTypes=add, edit, delete, addComment, editComment, addFile, openEditForm, mail, changeState, onsetTimeOfAttr, changeResponsible, escalation, changeMassAttr, bindToMassMaster, bindMassSlave, userEvent, loginSuccessful, logout, alertActivated, alertDeactivated, alertChanged, insertMention, arriveMessageOnQueue, ndapMessage
plannedEventCategory=BEFORE_TIME, IN_TIME, AFTER_TIME
trackingMessageStyles=Information, Warning
trackingRefreshAreas=ChangedContents, WholePage, AttrValues
trackingUiActions=Message, MessageWithRefresh, Autoreload

ChangeTrackingEventAction=Change tracking
ScriptEventAction=Script
NotificationEventAction=Notification
PushEventAction=Notice in interface
PushMobileEventAction=Notice in mobile client
PushPortalEventAction=Notice in portal
IntegrationEventAction=Send to external queue

bindMassSlave=Link to child object
changeState=Change status
mail=Mail receipt
addComment=Add comment to object
editComment=Edit comment
edit=Edit object
addFile=Attach file to object
changeMassAttr=Mass attribute change
add=Add object
escalation=Escalation
delete=Delete object
onsetTimeOfAttr=Attribute time occurrence
openEditForm=Open edit form
changeResponsible=Change responsible
bindToMassMaster=Link to mass object
userEvent=[User event]
loginSuccessful=Log in
logout=Logout
alertActivated=Alert activated
alertDeactivated=Alert deactivated
alertChanged=Alert changed
insertMention=Mention within selected objects
arriveMessageOnQueue=Message arrival in the queue
ndapMessage=NDAP message

ChangedContents=Contents with changes
WholePage=Whole page
Autoreload=Autoreload

Message=Message
MessageWithRefresh=Message about a change with the Update button
AttrValues=Attribute values

Information=Information
Warning=Warning

BEFORE_TIME=Before
AFTER_TIME=After
IN_TIME=At the same time

SCRIPT=Script
