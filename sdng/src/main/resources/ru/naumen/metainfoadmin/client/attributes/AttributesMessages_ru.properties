addUserAttribute=Добавление пользовательского атрибута
advlistSemanticFiltering=Фильтрация в сложных списках с учётом морфологии
aggregateClasses=Агрегировать классы
aggregatingAttributes=Агрегируемые атрибуты
attibuteOfRelatedClass=Атрибут связанного класса
attribute=Атрибут
attributeParameters=Параметры атрибута
attributeParamsChanging=Изменение параметров атрибута
attributeUsageRestrictionPlace=Настройки ограничения значения атрибута "{0}".
attributeUsedInOtherRestriction=Для текущего атрибута не доступна настройка ограничения на ввод даты, так как данный атрибут используется в настройках ограничения атрибута "{0}".
attributeValue=Значение атрибута
buildHierarchyFrom=Построить иерархию начиная от
calculatingByScript=Вычисляется скриптом
calculatingOnEdit=Вычисляется при редактировании
caseAtributes=Атрибуты типа
caseProperties=Свойства типа
catalog=Справочник
checkBoxDefValue=Значение по умолчанию "{0}"
classAtributes=Атрибуты класса
classProperties=Свойства класса
clazz=Класс
complexAttrGroup=Группа атрибутов в списке
complexEmplAttrGroup=Группа атрибутов в списке для класса Сотрудник
complexFormEmplAttrGroup=Расширенное редактирование связи для класса Сотрудник
complexFormOuAttrGroup=Расширенное редактирование связи для класса Отдел
complexFormTeamAttrGroup=Расширенное редактирование связи для класса Команда
complexOuAttrGroup=Группа атрибутов в списке для класса Отдел
complexRelation=Расширенное редактирование связей
complexRelationType=Тип списка
complexRelationType[false]=Выключено
complexRelationType[flat]=Плоский список
complexRelationType[flat_with_full_text_search]=Плоский список с полнотекстовым поиском
complexRelationType[hierarchy]=С использованием структуры
complexTeamAttrGroup=Группа атрибутов в списке для класса Команда
composite=Составной
compositeValue=Составное
computableOnForm=Вычисление значения при редактировании
computableOnFormScript=Скрипт вычисления значения при редактировании
computeAnyCatalogElementsScript=Скрипт вычисления элементов справочника
dateTimeAttributeRestriction=Установлено ограничение "{0} атрибута {1}".
dateTimeAttributeRestrictionInfo={0} атрибута {1}
dateTimeCommonRestrictions=Значение атрибута допустимо указывать
dateTimeRestrictionAttribute=Атрибут
dateTimeRestrictionCondition=Условие
dateTimeRestrictionScript=Скрипт ограничения значения атрибута
dateTimeRestrictionType=Дополнительное ограничение на ввод даты
dateTimeScriptRestriction=Ограничение скриптом
defaultByScript=Вычислимое
defaultValue=Значение по умолчанию
defaultValueByScript=Вычислимое значение по умолчанию
description=Описание
determinableByCorrespondanceTable=По таблице соответствий
determinableByNameRules=По правилу именования
determinableByNumbersFormationRule=По правилу формирования номера
determinableByValueMap=Определяемый по таблице соответствий
determinedBy=Особенность вычисления
digitsCountRestrictions=Ограничение на ввод десятичных знаков
directLink=Прямая ссылка
displayValueWithHierarchy=Показывать значение атрибута
editAttribute=Редактирование атрибута
editOnComplexFormOnly=Редактирование только через расширенную форму
editPresentation=Представление для редактирования
editable=Редактируемый
editableInLists=Редактируемый в списках
exportNDAP=Доступен из системы мониторинга
filterWhileEditing=Фильтрация значений при редактировании
filteringOnEdit=Фильтруется при редактировании
formDateTimeCommonRestrictions=Значение параметра допустимо указывать
formDateTimeRestrictionAttribute=Параметр
formDateTimeRestrictionScript=Скрипт ограничения значения параметра
hasGroupSeparators=Разделять по разрядам
hideArchived=Скрывать архивные объекты
hideCaptionAttribute=Скрывать название атрибута
hideWhenEmpty=Скрывать при отображении, если не заполнен
hideWhenNo=Скрывать при отображении, если значение "Нет"
hideWhenNoPossibleValues=Скрывать при редактировании, если нет значений для выбора
hideWhenZero=Скрывать при отображении, если значение "0"
hides=Скрывается
inheritParams=Наследовать параметры
inputmask=Маска ввода
inputmaskAliasHelp=<b>Синтаксис маски</b>: При выбранном режиме маски ''Псевдоним'' в поле ''Маска ввода'' указывается название псевдонима. Псевдоним влияет на:<br/><ul style=''list-style-type:disc''><li>внешний вид пустого поля ввода с установленным курсором - в нем отображается подсказка, в каком виде требуется ввести значение</li><li>реакцию поля на ввод недопустимых символов, автоматическую подстановку служебных символов</li><li>валидацию значения при окончании ввода</li></ul><br/>Полный список доступных стандартных псевдонимов приведен в документации к системе, ниже приведены некоторые часто используемые варианты.<br/><br/><table><tr><td style=''-moz-user-select:text;vertical-align:top;''><b>Общие псевдонимы</b>:<br/><ul style=''list-style-type:disc''><li>ip - ip-адрес вида ***********</li><li>email - адрес электронной почты</li><li>mac - стандартный 12-символьный сетевой адрес. Например, FD:98:DF:DF:F5:6D</li></ul><br/></td><td style=''-moz-user-select:text;vertical-align:top;''><b>Псевдонимы для численных значений</b>:<br/><ul style=''list-style-type:disc''><li>decimal - дробное число</li><li>integer - целое число</li><li>currency - число c ограничением дробной части до 2 знаков</li><li>currencyWithGroups - число с делением целой части на разряды и ограничением дробной части до 2 знаков</li><li>percentage - дробное число от 0 до ста со знаком %</li></ul><br/></td></tr></table>
inputmaskAttributeValidationMessage=Неверный формат маски ввода. Указания по заполнению поля описаны в разделе [Справка].
inputmaskDefinitionsHelp=<b>Синтаксис маски</b>: При выбранном режиме маски ''Маска с сокращениями'' в поле ''Маска ввода'' указывается строка, которая может содержать, как обычные символы, так и специальные символы-сокращения. Обычные символы вставляются в маску ввода, как шаблоны, и не могут быть из нее исключены. Символы-сокращения служат для обозначения допустимых значений. Например, в маске ''MCК-9999'' подстрока ''МСК-'' не содержит спецсимволов и будет распознана, как шаблон, а подстрока ''9999'' будет распознана, как 4 цифры подряд. Пользователь сможет ввести строки МСК-1234 или МСК-0000. Полный список доступных сокращений приведен в документации к системе, ниже приведены некоторые часто используемые варианты.<br/><br/><b>Общие сокращения</b>:<br><ul style=''list-style-type:disc''><li>9 - одна цифра</li><li>a - одна буква любого регистра</li><li>A - одна буква, автоматически приводимая к верхнему регистру</li><li>* - одна цифра или буква любого регистра</li><li>& - одна цифра или буква, автоматически приводимая к верхнему регистру</li><li># - шестнадцатеричная цифра, 0-F</li></ul><b>Сокращения для дат и времени:</b><br><ul style=''list-style-type:disc''><li>h - часы</li><li>s - секунды или минуты</li><li>d - дни</li><li>m - месяцы</li><li>y - год</li></ul>Таким образом, маска ввода ''y.m.d h:s:s'' будет требовать от пользователя ввода даты в формате ''год.месяц.день часы:минуты:секунды'', в которой должны быть заполнены все символы. Например, ''2015.01.01 23:59:59''<br><br><b>Доступные спецсимволы</b>:<br><ul style=''list-style-type:disc''><li>Группировка: ( и )</li><li>Объединение по ИЛИ: |. Например, (aaa)|(999). Подходящие значения - ''abc'', ''123''.</li><li>Необязательность: [ и ]. Например, 99[99]-999. Подходящие значения - ''12-345'', ''1234-567''.</li><li>Динамика: '{'n'}' - n повторений; '{'n,m'}' - от n до m повторений; '{'+'}' - начиная с 1; '{'*'}' - начиная с 0. Например, aa-9'{'1,4'}'. Подходящие значения - ''bc-34'', ''sd-1234''</li><li>Экранирование: \\ отменяет специальное значение для символа. Например, \\#99-ААА999. Подходящие значения: #12-ASK654</li></ul>
inputmaskMode=Режим маски ввода
inputmaskModeAlias=Псевдоним
inputmaskModeDefinitions=Маска с сокращениями
inputmaskModeRegex=Регулярное выражение
inputmaskRegexHelp=При выбранном режиме маски ''Регулярное выражение'' в поле ''Маска ввода'' указывается регулярное выражение, которое будет применено для валидации введенной строки при вводе символов, выходе из поля и сохранении формы ввода. Если вводимый символ, согласно регулярному выражению, не может стоять на данном месте строки, то он не попадет в поле. Если после окончания ввода введенное выражение не будет подходить под условия регулярного выражения, пользователь увидит ошибку валидации.
intervalAvailableUnits=Единицы измерения, доступные при редактировании
levelOfHierarchy=Уровень иерархии
linkAttribute=Атрибут связи
linkedTo=Ссылается на
mandatory=Обязательный
mandatoryInInterface=Обязательный для заполнения в интерфейсе
needStoreUnits=Запоминать выбранные единицы измерения
needStoreUnitsAttention=Информация об единицах измерения, введённых пользователями ранее, будет потеряна. Временной интервал будет приводится к наибольшей доступной единице измерения, в которой значение можно представить в целочисленном виде. Вы действительно хотите изменить значение параметра?
needStoreUnitsInfo=Если признак выставлен, при отображении значения будет использоваться единица измерения, которую указал пользователь при редактировании. Иначе значение будет приводиться к наибольшей единице измерения, в которой можно представить значение в целочисленном виде.
no=Нет
objectClass=Класс объекта
parentHierarchy0=Связанного объекта
parentHierarchy1=Родителя связанного объекта
parentHierarchy2=Родителя 2-ого уровня связанного объекта
parentHierarchy3=Родителя 3-его уровня связанного объекта
parentHierarchyN=Родителя {0}-ого уровня связанного объекта
parentHierarchyTop=Родителя верхнего уровня
quickAddForm=Форма быстрого добавления
quickEditForm=Форма быстрого редактирования
relatedAttrsToExport=Атрибуты, доступные из системы мониторинга
required=Обязательный
requiredInInterface=Обязательный для заполнения в интерфейсе
ruleToDetermine=Правило определения
script=Скрипт
selectSorting=Сортировка списка
showPresentation=Представление для отображения
sortBy=Сортировать по:
sortByCode=По коду
sortByTitle=По названию
sortByValueType=По типу значения
sortSystemFirst=Сначала системные
sortUserFirst=Сначала пользовательские
structuredObjectsView=Структура
structuredObjectsViewForBuildingTree=Структура для построения дерева
syncOnlyWhenObjectUpdated=Синхронизация осуществляется только после обновления объекта или при использовании метода api.ndap.syncProperties(Object object)
systemAttributes=Системные атрибуты
template=Шаблон
timer=Счётчик
timerDefinition=Счетчик времени
type=Тип
unique=Уникальный
useSystemParams=Использовать системные параметры
userAttributes=Пользовательские атрибуты
yes=Да
