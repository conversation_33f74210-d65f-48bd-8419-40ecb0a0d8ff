#
#Wed Jun 05 12:08:02 YEKT 2013
addingSchedulerTask=Добавление задачи планировщика
addingTrigger=Добавление правила
addTask=Добавить задачу
advImportSchedulerTask=Синхронизация
bigDaily=Ежедневно.
bigMonthly=Ежемесячно.
bigWeekly=Еженедельно.
bigYearly=Ежегодно.
calculationStrategy=Стратегия расчета
confirmForInboundMailServer=При обработке входящей почты письма загрузятся в систему и <b>будут удалены с почтового сервера</b>. История обработки писем отобразится в логе входящей почты.<br><br>Если вы хотите оставить копии писем на сервере, выберите в параметрах подключения протокол EWS, MS Graph или IMAP4 и укажите папки для писем без ошибок и с ошибками.<br><br>
confirmRunQuestion=Вы действительно хотите выполнить задачу планировщика "{0}"?
confirmRunQuestion2=У задачи планировщика "{0}" нет ни одного включенного правила. Вы действительно хотите выполнить эту задачу?
confirmRunQuestionInboundMailServer=После запуска задачи планировщика письма загрузятся в систему и <b>будут удалены с почтового сервера</b>. История обработки писем отобразится в логе входящей почты.<br><br>Если вы хотите оставить копии писем на сервере, выберите в параметрах подключения протокол EWS или IMAP4 и укажите папки для писем без ошибок и с ошибками.<br><br>Вы действительно хотите выполнить задачу планировщика?
creationDate=Дата создания
daily=ежедневно
editingSchedulerTask=Редактирование задачи планировщика
editingTrigger=Редактирование правила
executeAtConcreteDate=Выполнение в определенную дату и время. {0}
executeAtConcreteDateType=Выполнение в определенную дату и время
executeScriptTask=Скрипт
executionDate=Дата/время выполнения задачи
fromLastExecution=от момента последнего выполнения
fromStart=от момента начала действия
headTitle=Задачи планировщика
interval=Длительность
isOn=Включено
lastExecutionDate=Дата последнего выполнения
monthly=ежемесячно
parameters=Параметры
period=Период
periodicExecution=Периодическое выполнение
periodicRule=Периодическое правило.
planExecutionDate=Плановая дата выполнения
questionChangeInboundMailServer=Вы действительно хотите изменить параметры подключения?
questionChangeInboundMailServerInTask=Вы действительно хотите изменить сервер входящей почты для задачи планировщика?
questionEnableInboundMailServer=Вы действительно хотите включить подключение?
questionEnableRuleInboundMailServer=Вы действительно хотите включить правило выполнения задачи планировщика?
randomizeDelay=Рандомизировать время запуска задачи для снижения нагрузки на систему
receiveMailTask=Обработка входящей почты
schedulerTask=Задача планировщика
schedulerTaskCard=Карточка задачи планировщика
schedulerTaskLog=Лог задачи планировщика
schedulerTasksSelected=выбранные задачи планировщика
script=Скрипт
startDate=Дата/время начала действия правила
taskCard=Карточка задачи планировщика
taskType=Тип задачи
theSchedulerTask=задачу планировщика
toSchedulerTask=к задаче планировщика
trigger=правило выполнения задачи планировщика
triggerCard=Карточка правила
triggers=Расписание
triggerType=Тип правила
version=Версия
weekly=еженедельно
yearly=ежегодно