addTag=Tag hinzufügen
addTagFromWidget=[hinzufügen] {0}
tags=Tags
addingTag=Hinzufügen eines Tags
backToTags=zu den Tags
enabled=Aktiviert
confirmDeleteTag=Möchten Sie das Tag wirklich entfernen? Nach der Bestätigung wird das Tag aus allen Einstellungen entfernt, in denen es verwendet wird.
contentAddForm=Formular Hinzufügen
contentEditForm=Formular Bearbeiten
confirmMassDeleteTags=Möchten Sie die ausgewählten Tags wirklich löschen? Nach der Bestätigung werden die Tags aus allen Einstellungen entfernt, in denen sie verwendet werden.
contentTab=Tab "{0}"
deleteMass=löschen
contentWindow=Objektkarte
contents=Inhalt
deleteTag=Tag löschen
disabledAttributesSearchWarning=Die Attributsuche wird durch verwendete Tags deaktiviert. Um die Suche zu aktivieren, aktivieren Sie entweder alle verwendeten Tags oder deaktivieren Sie das Attribut.
disabledItemMenuByClassWarning=Die Klasse/Typen, für die das Menüelement konfiguriert ist, sind mit Tags deaktiviert: {0}. Um ein Element einzuschließen, müssen Sie Tags in Klassen/Typen aktivieren.
disabledItemMenuByAttributeWarning=Attribute, auf die das Menüelement eingestellt ist, sind mit Tags deaktiviert: {0}. Um ein Element zu aktivieren, müssen Sie Tags in die Attribute aktivieren.
disabledItemMenuByContentWarning=Der Inhalt, auf den der Menüpunkt eingestellt ist, wird durch die Tags ausgeschaltet: {0}. Um das Element zu aktivieren, müssen Sie die Tags im Inhalt aktivieren.
disabledItemMenuWarning=Das Element wird durch die verwendeten Tags deaktiviert. Der Artikel kann nur eingeschaltet werden, wenn die Tags eingeschaltet sind.
disabledItemMenuByParentWarning=Das Element wird durch Tags im Abschnitt deaktiviert: {0}. Das Element ist nur verfügbar, wenn die Tags aktiviert sind.
disabledMetaClassSearchWarning=Die Attributsuche der Metaklasse wird durch die verwendeten Tags ausgeschaltet. Um die Suche zu aktivieren, schalten Sie alle verwendeten Tags ein, oder deaktivieren Sie die Tags der Metaklasse.
disabledSchedulerTaskWarning=Scheduler-Aufgabe wird durch die verwendeten Tags ausgeschaltet.
disabledMetaClassWarning=Die Metaklasse wurde durch die verwendeten Tags deaktiviert. Alle Schaltflächen des oberen Menüs vom Typ "Schaltfläche fur Objekt Hinzufügen" und Erwähnungen, die für diese Metaklasse konfiguriert sind, werden ausgeblendet.
disabledSecurityGroupsWarning=Die Benutzergruppe ist durch die verwendeten Tags deaktiviert und wird nicht in der Dropdown-Liste der Systemattributwerte angezeigt: Mitarbeitergruppen, Abteilungsbenutzergruppen, Teambenutzergruppen.
disabledStateListWarning=Der Status {0} ist durch die verwendeten Tags deaktiviert.
disabledStateWarning=Der Status wird von den verwendeten Tags ausgeschaltet. Um den Status einzuschalten, schalten Sie alle verwendeten Tags ein oder entfernen ein Tag aus dem Status.
eventActions=Ereignisaktionen
disabledStatesListWarning=Die Status {0} sind durch die verwendeten Tags deaktiviert.
disabledInListContentRestriction=Die Listeninhaltsbeschränkung verwendet Attribute, die durch Tags deaktiviert werden. Die Einschränkung durch diese Attribute gilt nicht.
disabledInRestrictionContentStructureElement=Die Inhaltseinschränkung des Strukturelements verwendet Attribute, die durch Tags deaktiviert werden. Die Einschränkung mit diesen Attributen entfällt.
groupsWithDisabledAttributesWarning=Attributgruppen {0} enthalten Attribute, die durch Tags deaktiviert wurden.
editingTag=Bearbeiten eines Tags
enterTagTitlePlaceholder=Geben Sie einen Namen für den neuen oder bestehenden Tag ein
groupWithDisabledAttributesWarning=Die Attributgruppe {0} enthält Attribute, die durch Tags deaktiviert sind.
disabledInContentDisplayConditions=Bedingungen für die Anzeige von Inhalten enthalten Attribute, die durch Tags ausgeschaltet werden. Die Bedingung, die diese Attribute verwendet, gilt nicht.
disabledInDefaultFiltering=Die Standard-Filterbedingungen enthalten Attribute, die durch Tags deaktiviert sind. Die Bedingung für die Filterung nach diesen Attributen gilt nicht.
isLicensed=lizenziert
leftMenuItem=Menüpunkt links:
metaCase=Typ {0}
metaClass=Klasse {0}
mobileAddForm=Formular zum Hinzufügen von Objekten "{0}" ({1})
mobileContents=Einstellungen der mobilen App
mobileEditForm=Formular zur Objektbearbeitung "{0}"
securityGroups=Benutzergruppen
schedulerTaskType=Aufgabentyp "{0}"
states=Status
mobileNavigationMenuItem=Navigationsmenüpunkt "{0}" ({1})
mobileObjectCard=Objektkarte "{0}"
mobileObjectList=Liste der Objekte "{0}" ({1})
navigationSettings=Einstellungen zur Navigation
turnOffMass=deaktivieren
schedulerTasks=Scheduler-Aufgaben
turnOnMass=aktivieren
usagePoints=Orte der Verwendung
mobileCardContent=Inhalt {0} auf Objektkarte "{1}"
disabledHomePageWarning=Die Home-Seite wird durch die verwendeten Tags ausgeschaltet.
homePages=Home-Seiten
