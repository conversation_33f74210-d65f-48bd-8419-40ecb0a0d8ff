addUserAttribute=Add user attribute
advlistSemanticFiltering=Semantic filtering in advanced lists
aggregateClasses=Aggregate classes
aggregatingAttributes=Aggregated attributes
attibuteOfRelatedClass=Attribute of related class
attribute=Attribute
attributeParameters=Attribute parameters
attributeParamsChanging=Attribute parameter change
attributeUsageRestrictionPlace=Attribute restriction settings "{0}".
attributeUsedInOtherRestriction=The attribute is not available for setting restriction of value attribute, as this attribute is used in the attribute restriction settings "{0}".
attributeValue=Attribute value
buildHierarchyFrom=Build a hierarchy from
calculatingByScript=Compute by script
calculatingOnEdit=Computed during edit
caseAtributes=Type attributes
caseProperties=Type properties
catalog=Catalog
checkBoxDefValue=Default value "{0}"
classAtributes=Class attributes
classProperties=Class properties
clazz=Class
complexAttrGroup=The group of attributes list
complexEmplAttrGroup=Attribute group in list for class Employee
complexFormEmplAttrGroup=Extended links editing for class Employee
complexFormOuAttrGroup=Extended links editing for class Department
complexFormTeamAttrGroup=Extended links editing for class Team
complexOuAttrGroup=Attribute group in list for class Department
complexRelation=Extended links editing
complexRelationType=List type
complexRelationType[false]=Off
complexRelationType[flat]=Flat list
complexRelationType[flat_with_full_text_search]=Full-text search flat list
complexRelationType[hierarchy]=Using structure
complexTeamAttrGroup=Attribute group in list for class Team
composite=Composite
compositeValue=Composite
computableOnForm=Compute value during edit
computableOnFormScript=Script used to compute value during edit
computeAnyCatalogElementsScript=Script used to compute items of catalog
dateTimeAttributeRestriction=Restriction: "{0} attribute {1}".
dateTimeAttributeRestrictionInfo={0} attribute {1}
dateTimeCommonRestrictions=Attribute value is allowed to specify
dateTimeRestrictionAttribute=Attribute
dateTimeRestrictionCondition=Condition
dateTimeRestrictionScript=Attribute value restriction script
dateTimeRestrictionType=Other restriction of value attribute
dateTimeScriptRestriction=Script restriction
defaultByScript=Computable
defaultValue=Value by default
defaultValueByScript=Computable value by default
description=Description
determinableByCorrespondanceTable=By correspondence table
determinableByNameRules=By naming rule
determinableByNumbersFormationRule=By numeration rule
determinableByValueMap=Definable by correspondence table
determinedBy=Computational feature
digitsCountRestrictions=Restriction on the input of decimals
directLink=Direct link
displayValueWithHierarchy=Show value of attribute
editAttribute=Edit attribute
editOnComplexFormOnly=Edit on complex form only
editPresentation=Representation to edit
editable=Editable
editableInLists=Editable in list
exportNDAP=Available for monitoring system
filterWhileEditing=Filter values during edit
filteringOnEdit=Filtered during edit
formDateTimeCommonRestrictions=Parameter value is allowed to specify
formDateTimeRestrictionAttribute=Parameter
formDateTimeRestrictionScript=Parameter value restriction script
hasGroupSeparators=Number with digits divided into groups
hideArchived=Hide archive objects
hideCaptionAttribute=Hide title
hideWhenEmpty=Hide in view mode if not filled
hideWhenNo=Hide in view mode if value equals "No"
hideWhenNoPossibleValues=Hide in edit mode if there are no values to select
hideWhenZero=Hide in view mode if value equals "0"
hides=Hides
inheritParams=Inherit parameters
inputmask=Mask
inputmaskAliasHelp=<b>Mask syntax</b>: ''Input mask'' field specifies the name of an alias if ''alias'' mode is selected. Alias influences:<br><ul><li>appearance of empty focused input field - it displays a hint about the type of value in the field</li><li>response to invalid characters input, automatic insertion of auxiliary symbols</li><li>validation of input values after the end of input</li></ul><br>Full list of available standard aliases is given in the system documentation. Some commonly used options are given below.<br><br><table><tr><td style=''-moz-user-select:text;vertical-align:top;''><b>Common aliases</b>:<br><ul><li>ip - ip-address like ***********</li><li>email - standard email address</li><li>mac - 12-character network address, such as FD:98:DF:DF:F5:6D</li></ul><br></td><td style=''-moz-user-select:text;vertical-align:top;''><b>Aliases for numerical values</b>:<br><ul><li>decimal - decimal number</li><li>integer - integer number</li><li>currency - real number with fractional part limited by two symbols</li><li>currencyWithGroups - real number with digits divided into groups and fractional part limited by two symbols</li><li>percentage - fractional number from 0 to 100 with ''%'' sign</li></ul><br></td></tr></table>
inputmaskAttributeValidationMessage=Invalid mask format. Directions for completing the field are described in the [Help] section.
inputmaskDefinitionsHelp=<b>Mask syntax</b>: ''Input mask'' field specifies a string with common symbols and special abbreviations if ''mask with abbreviation'' mode is selected. Common symbols are inserted into the input mask as a pattern and may not be excluded from it. Abbreviations serve to designate acceptable values. For example, in the mask ''MSK-9999'' substring ''MSK-'' does not contain any special abbreviations and will be recognized as a common characters, however the substring ''9999'' will be recognized as a 4-digit row. User can enter values like ''MSK-1234'' and ''MSK-0000''. Full list of available standard abbreviations is given in the system documentation. Some commonly used options are given below.<br><br><b>Common abbreviations</b>:<br><ul><li>9 - single digit</li><li>a - single letter of any case</li><li>A - single letter driven to uppercase automatically</li><li>* - single digit or letter of any case</li><li>& - single digit or letter driven to uppercase automatically</li><li># - hexadecimal digit (0-F)</li></ul><b>Abbreviations for dates and times:</b><br><ul><li>h - hour</li><li>s - second or minute</li><li>d - day</li><li>m - month</li><li>y - year</li></ul>Thus, mask ''y.m.d h:s:s'' will require user to enter date in the format ''year.month.day hours:minutes:seconds'', where all positions should be written in, for example: ''2015.01.01 23:59:59''<br><br><b>Available special characters</b>:<br><ul><li>Grouping: ( and )</li><li>Union by ''OR'': |. For example, (aaa)|(999). Suitable values - ''abc'', ''123''.</li><li>Optionality: [ and ]. For example, 99[99]-999. Suitable values - ''12-345'', ''1234-567''.</li><li>Dynamics: '{'n'}' - n repetitions; '{'n,m'}' - from n to m repetitions; '{'+'}' - starting with 1; '{'*'}' - starting with 0. For example, aa-9'{'1,4'}'. Suitable values - ''bc-34'', ''sd-1234''</li><li>Escaping: \\ cancels the special meaning for the character. For example, \\#99-AAA999. Suitable values: #12-ASK654</li></ul>
inputmaskMode=Mask mode
inputmaskModeAlias=Alias
inputmaskModeDefinitions=Mask with abbreviation
inputmaskModeRegex=Regular expression
inputmaskRegexHelp=''Input mask'' field specifies a regular expression if ''Regular expression'' mode is selected. It is applied for value validation when entering symbols, leaving the field or saving the input form. If an input character does not fit its location according to the regular expression, it will be omitted. If after finishing the input an expression does not meet the conditions of the regular expression, a user will see a validation error.
intervalAvailableUnits=Units of time available on edit
levelOfHierarchy=Level of hierarchy
linkAttribute=Link attribute
linkedTo=Linked to
mandatory=Required
mandatoryInInterface=Required to fill in interface
needStoreUnits=Save selected units of time
needStoreUnitsAttention=Information about units of time entered by users before will be lost. For time interval will be used unit of time in which the value can be represented in integer. Are you sure to change parameter?
needStoreUnitsInfo=Value will be displayed with the unit of time which user choosed during edit, if parameter is set. Otherwise, value will be represent to the largest unit of time, in which the value can represent integer.
no=No
objectClass=Object class
parentHierarchy0=Value of related object
parentHierarchy1=Value of related object''s parent
parentHierarchy2=Value of related object''s 2nd-level parent
parentHierarchy3=Value of related object''s 3rd-level parent
parentHierarchyN=Value of related object''s {0}th-level parent
parentHierarchyTop=Value of related object''s top-level parent
quickAddForm=Quick add form
quickEditForm=Quick edit form
relatedAttrsToExport=Attributes that are available from the monitoring system
required=Required
requiredInInterface=Required in interface
ruleToDetermine=Determination rule
script=Script
selectSorting=Sort list
showPresentation=Representation to view
sortBy=Sort by:
sortByCode=By code
sortByTitle=By title
sortByValueType=By value type
sortSystemFirst=System first
sortUserFirst=User first
structuredObjectsView=Structure
structuredObjectsViewForBuildingTree=Structure for building a tree
syncOnlyWhenObjectUpdated=Synchronization is performed only after the object is updated or when using the method api.ndap.syncProperties(Object object)
systemAttributes=System attributes
template=Template
timer=Timer
timerDefinition=Timer
type=Type
unique=Unique
useSystemParams=Use system parameters
userAttributes=User attributes
yes=Yes
