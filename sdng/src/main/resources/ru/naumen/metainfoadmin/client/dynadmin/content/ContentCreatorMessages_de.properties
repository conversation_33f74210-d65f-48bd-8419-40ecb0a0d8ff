files=Dateien
hideSingleTab=Verstecken Sie die Tab-Wurzel, wenn dies die einzige ist
relatedObjectClass=Klasse des verlinkten Objekts
relatedObjects=Verwandte Objekte
afterHierarchy=Hierarchieobjekte werden über ein Attribut mit Listenobjekten verknüpft
beforeHierarchy=Objekthierarchie (abwärts) ausgehend vom Objekt aufbauen
changeHistory=Geschichte der Änderungen
comments=Kommentare
collapsedByDefault=Standardmäßig eingeklappt
collapsedHelpText=Eingeklappt: {0}
currentObject=Aktuelles Objekt
currentObjectAfterHierarchy=Hierarchieobjekt (Klasse: {0})
currentObjectBeforeHierarchyClass=Aktuelles Objekt (Klasse: {0})
currentObjectBeforeHierarchyType=Aktuelles Objekt (Typ: {0})
deleteToolsAttention=Wenn die Änderungen gespeichert werden, werden die Schaltflächen {0} aus der Aktionsleiste entfernt, da ihre Einstellungen nicht mehr mit den Inhaltseinstellungen übereinstimmen.
displayType=Kommentare anzeigen
fromAttributes=Angehängt an
hierarchyAttribute=In einer Hierarchie sind Nachkommen durch ein Attribut mit Vorfahren verbunden
hideTabBorder=Tab-Umriss ausblenden, wenn es der einzige ist
linkedWithObject=mit einem Verweis auf das aktuelle Objekt
linkedWithRelatedObject=mit Links zu verknüpften Objekten
moreAttributesGroup=Attributgruppe für Block Mehr
hierarchyClass=Klasse der Hierarchieobjekte
nestedObjects=Verschachtelte Objekte
networkScheme=Diagramm des Netzes
objectClass=Klasse der Listenobjekte
objectsList=Liste der Objekte
objectsTypes=Typen von Objekten
ofCurrentObject=aktuelles Objekt
relatedObject=zugehöriges Objekt
ownObjectFiles=Objekt
relationAttributeInCommentClass=Beziehungsattribut in der Klasse Kommentar
relationAttributeInFileClass=Beziehungsattribut in der Klasse File
relationType=Dateien anzeigen
resetIfHidden=Eingegebene Werte beim Speichern zurücksetzen, wenn kein Inhalt angezeigt wird
relatedObjectSet=verwandte Objekte
relationAttribute=Beziehungsattribut
relationScheme=Verknüpfungsschema
reportPrintingForm=Bericht, gedrucktes Formular
reportsPrintingFormsList=Liste der Berichte, gedruckte Formulare
selectCase=Auswählen eines Objekttyps
selectContacts=Auswahl einer Kontaktperson
showNestedInNested=In verschachtelten Objekten in der Liste verschachtelte Objekte anzeigen
showRelatedWithNested=Mit der Hierarchie verbundene Objekte in der Liste anzeigen
tabVisibilityCondition=Bedingungen für die Anzeige einer Tabs
tabVisibilityConditionForm=Einstellen einer Bedingung für die Anzeige einer Tabs
toAttribute=Attribut ''{0}''
userHistory=Geschichte der Veränderung von Verantwortung und Status
visibilityCondition=Bedingung für die Anzeige des Inhalts
workflowDiagram=Workflow-Diagramm
visibilityConditionForm=Einstellung der Bedingung für die Anzeige des Inhalts
editFormAttributeGroup=Attributgruppe für das Kommentarbearbeitungsformular
addFormAttributeGroup=Gruppe von Attributen für das Formular zum Hinzufügen eines Kommentars
restrictContent=Inhalte einschränken
restrictContentForm=Konfigurieren von Inhaltsbeschränkungen
showAttrDescriptionOnAddForm=Attributbeschreibungen für das Hinzufügen-Formular anzeigen
showAttrDescriptionOnEditForm=Attributbeschreibungen für das Bearbeitungsformular anzeigen
showAttributeAttachedFiles=Dateianhänge aus dem Attribut anzeigen
