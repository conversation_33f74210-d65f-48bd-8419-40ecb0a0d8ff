actionL=aktion
action=aktion
actionProperties=Aktionsattribute
actionType=Aktionstyp
addState=Status hinzufügen
script=Skript
addStateCaption=Status hinzufügen
addTransition=Übergang hinzufügen
addingAction=Adding action
addingCondition=Hinzufügen einer Bedingung
conditionType=Bedingungstyp
attentionIfEndStateSwitch=1. Der Vorgang kann lange dauern.
attentionIfEndStateSwitchFalseOnTrue=2. Im Endzustand werden die Systemzeitzähler gestoppt. Bei allen bestehenden Anfragen im Status "{0}" wird der Zähler gestoppt.\n3. Objekte im Endzustand blockieren nicht die Archivierung des für sie zuständigen Mitarbeiters/Teams/Abteilung und die Entfernung/Löschung des für das Objekt im Endzustand verantwortlichen Mitarbeiters aus dem Team innerhalb des Teams.
conditionL=Bedingung
attentionIfEndStateSwitchTrueOnFalse=2. Zeit in einem anderen Status als endgültig wird in Systemzählern berücksichtigt. Bei allen bestehenden Anfragen im Status "{0}" wird der Zähler nicht fortgesetzt.\n3. Objekte in einem anderen als dem letzten Status sperren die Archivierung des für sie zuständigen Mitarbeiters/Teams/Abteilung und die Entfernung/Löschung des für das Objekt verantwortlichen Mitarbeiters in einem anderen als dem letzten Status aus dem Team innerhalb des Teams .
confirmDelete=Möchten Sie {0} wirklich löschen?
workflow=Workflow
copyWf=Einstellungen kopieren
disableStateConfirmation=Wollen Sie den Status "{0}" wirklich ausschalten? Übergänge in den Status werden verboten.
disableStateConfirmationCaption=Bestätigung des Status aus
editStateCaption=Status der Bearbeitung
editingAction=Bearbeiten einer Aktion
editingCondition=Bearbeiten einer Bedingung
endState=Endgültiger Status
from=aus dem Status
notFound=(Nicht gefunden)
number=Nummer
otherParams=Andere Parameter
possibleTransitions=Mögliche Übergänge
removeTransitionConfirmation=Wollen Sie den Übergang ''{0}'' wirklich löschen?
resetStateSettingsConfirmationAll=Möchten Sie wirklich alle Parametereinstellungen für alle Status zurücksetzen?
postActions=Zu ergreifende Maßnahmen beim Verlassen des Status
postConditions=Bedingungen beim Verlassen des Status
preActions=Aktionen bei Eingabe des Status
preConditions=Bedingungen für die Eingabe des Status
resetLayout=Umbauen
resetStateSettingsConfirmation=Wollen Sie wirklich alle Einstellungen für den Status ''{0}'' zurücksetzen?
resetWf=Einstellungen zurücksetzen
resetWfConfirmation=Wollen Sie wirklich alle Workflow-Einstellungen für {0} zurücksetzen?
resetWfConfirmationCaption=Bestätigen einer Zurücksetzung
responsibleType=Klasse von Verantwortlicher im Status
stateAttrSettings=Verwaltung von Parametern in Status und Übergängen
workFlowAttrSettings=Parameterverwaltung in Status
stateL=Status
stateProperties=Status-Attribute
stateTab=Status
statesAndTransition=Status und mögliche Übergänge
states=Zustände
workflowDiagram=Workflow-Diagramm
to=zu
toState=Zum Status ''{0}''
transitionAddingModeActivated=Der Modus Übergang hinzufügen ist aktiviert. Geben Sie den anfänglichen Übergangsstatus und dann den endgültigen Übergangsstatus an
transitionOutgoingError=Wenn der Status ''{0}'' mindestens einen Eingang hat, muss es auch mindestens einen Ausgang geben.
transitionRegisteredError=Für den Status ''{0}'' muss es mindestens einen Ausgang geben.
transitionClosedError=Es muss mindestens ein Eingang für den Status "{0}" vorhanden sein.
transitionIncomingError=Wenn es mindestens einen Ausgang aus dem Status ''{0}'' gibt, muss es auch mindestens einen Eingang geben.
transitions=Übergänge
warningProbablyWorkflowCanBroke=Warnung. Sobald der Status ausgeschaltet ist, kann der Workflow inkorrekt werden: {0}
warningProbablyWorkflowCanBrokeAfterDelete=Warnung. Nach dem Löschen von Zuständen und Übergängen kann der Workflow falsch werden{0}
warningProbablyWorkflowCanBrokeAfterStateRemoval=Warnung. Sobald der Status gelöscht ist, kann der Workflow falsch werden: {0}
warningProbablyWorkflowIsSetIncorrectly=Achtung! Möglicherweise wurde der Workflow nicht korrekt eingerichtet.
workflowDiagramError=Fehler beim Zeichnen des Workflow-Diagramms
workflowDiagramIsTooBig=Es gibt zu viele Übergänge im Workflow, um sie korrekt in einem Diagramm abzubilden
configureLater=Später einrichten
configure=Einrichten
confirmTransition=Bestätigung des Übergangs
nameTransitionButton=Name der Übergangstaste
accessTransitionsHasUnsavedChanges=Es gibt nicht gespeicherte Änderungen in der Übergangsmatrix. Wenn Sie zu einer anderen Seite wechseln, ohne die Änderungen zu speichern, gehen sie verloren. Mit der Aktion fortfahren?
setTransitionTitleDialogCaption=Hinzufügen einer Übergangstaste
accessStateHasUnsavedChanges=Es gibt ungespeicherte Änderungen in der Tabelle "Parameter in Status und Übergängen verwalten". Wenn Sie den Übergang zu einer anderen Seite fortsetzen, ohne die Änderungen zu speichern, gehen sie verloren. Die Aktion fortsetzen?
accessStatesHasUnsavedChanges=Es gibt ungespeicherte Änderungen in der Tabelle "Parameter in Status und Übergängen verwalten". Wenn Sie weiter zu einer anderen Seite navigieren, ohne die Änderungen zu speichern, gehen sie verloren. Die Aktion fortsetzen?
addingHeader=Hinzufügen eines Titels
addingItem=Hinzufügen eines Artikels
selectState=Status
accessTransitionsAndStatesHasUnsavedChanges=Es gibt ungespeicherte Änderungen in der Übergangsmatrix und in der Tabelle "Parameter in Status und Übergängen verwalten". Wenn Sie den Übergang zu einer anderen Seite fortsetzen, ohne die Änderungen zu speichern, gehen sie verloren. Die Aktion fortsetzen?
attentionForChangeStateWithoutOpenForm=Eine Änderung des Status ohne Öffnen des Formulars ist nicht möglich, da das Formular beim Übergang Attribute oder Kommentare anzeigt, die auszufüllen sind.
buttonTransitionTitle=Name der Navigationstaste
changeStateWithoutOpenForm=Änderung des Status ohne Öffnen eines Formulars
currentState=Aktueller Status
deleteButtonTransitionTitleConfirm=Sobald der Name der Übergangsschaltfläche entfernt wird, ist auch die Schaltfläche für die schnelle Statusänderung nicht mehr verfügbar.
editingHeader=Bearbeiten eines Titels
editingItem=Bearbeiten eines Artikels
editingTransition=Bearbeiten von Übergangseigenschaften
header=Titel
itemsOnFormChangeState=Elemente auf dem Formular zur Statusänderung
messageInformationState=Um die Werte zu bearbeiten, gehen Sie zur Übergangskarte
newState=Neuer Status
required=Erforderlich
resetSettingsConfirmationIsClass=Die für die aktuellen Klassenstatus vorgenommenen Übergangseinstellungen werden wiederhergestellt. Fortfahren mit dem Zurücksetzen der Übergangsformeinstellungen {0} -> {1}?
selectStates=Status
selectTransitions=Übergänge
titleButtonTransition=Name der Übergangstaste
requiredForFill=Obligatorisch auszufüllen
resetSettingsConfirmationIsCase=Die für Status im Typ "{2}" und für den Übergang im übergeordneten Typ/Klasse vorgenommenen Übergangseinstellungen werden wiederhergestellt. Übergangsform {0} -> {1} weiter zurücksetzen?
toWorkflowMetaClass=Auf der Tab des Lebenszyklus der Klasse {0}
view[comment]=Kommentar
view[header]=Titel
view[state]=Status
view[attribute]=Attribute
toWorkflowMetaCase=Auf einem Tab eines Lebenszyklus vom Typ {0}
transition=Übergang {0} ➝ {1}
transitionItemConfirmDelete=Wollen Sie wirklich ''{0}'' aus dem Formular für Statusänderungen entfernen?
transitionItemConfirmDeleteForAttr=Wollen Sie wirklich ''{0}'' ({1}) aus dem Formular für Statusänderungen entfernen?
transitionProperties=Übergangseigenschaften
view={0}
view[commentAttr]=Kommentarattribut
