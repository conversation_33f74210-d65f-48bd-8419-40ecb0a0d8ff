#
#Wed Jun 05 12:08:02 YEKT 2013
addingSchedulerTask=Add scheduler task
addingTrigger=Add rule
addTask=Add task
advImportSchedulerTask=Synchronization
bigDaily=Daily.
bigMonthly=Monthly.
bigWeekly=Weekly.
bigYearly=Yearly.
calculationStrategy=Timing strategy
confirmForInboundMailServer=When processing incoming mail, messages will be loaded into the system and <b>deleted from the mail server</b>. The message processing history will be displayed in the incoming mail log.<br><br>If you want to leave copies of messages on the server, select the EWS, MS Graph or IMAP4 protocol in the connection settings and specify the folders for messages with and without errors.<br><br>
confirmRunQuestion=Do you really want to exec scheduler task "{0}"?
confirmRunQuestion2=You do not have any rule of scheduler task execution for "{0}". Do you really want to exec this scheduler task?
confirmRunQuestionInboundMailServer=After running the scheduler task, messages will be loaded into the system and <b>deleted from the mail server</b>. The message processing history will be displayed in the incoming mail log.<br><br>If you want to leave copies of messages on the server, select the EWS or IMAP4 protocol in the connection settings and specify the folders for messages with and without errors.<br><br>Do you really want to perform the scheduler task?
creationDate=Creation date
daily=daily
editingSchedulerTask=Edit scheduler task
editingTrigger=Edit rule
executeAtConcreteDate=Execute at defined date and time. {0}
executeAtConcreteDateType=Execute at defined date and time
executeScriptTask=Script
executionDate=Task execution date/time
fromLastExecution=from the last execution
fromStart=from the start of action
headTitle=Scheduler tasks
interval=Duration
isOn=On
lastExecutionDate=Last execution date
monthly=monthly
parameters=Parameters
period=Period
periodicExecution=Periodic execution
periodicRule=Periodic rule.
planExecutionDate=Planned execution date
questionChangeInboundMailServer=Do you really want to change your connection settings?
questionChangeInboundMailServerInTask=Do you really want to change the incoming mail server for the scheduler task?
questionEnableInboundMailServer=Do you really want to enable the connection?
questionEnableRuleInboundMailServer=Do you really want to enable the scheduler task execution rule?
randomizeDelay=Randomize the task start time to reduce the load on the system
receiveMailTask=Incoming mail processing
schedulerTask=Scheduler task
schedulerTaskCard=Scheduler task card
schedulerTaskLog=Scheduler task log
schedulerTasks=Scheduler tasks
schedulerTasksSelected=selected scheduler tasks
script=Script
startDate=Date/time of rule start
taskCard=Scheduler task card
taskType=Task type
theSchedulerTask=scheduler task
toSchedulerTask=to scheduler task
trigger=rule of scheduler task execution
triggerCard=Rule card
triggers=Schedule
triggerType=Rule type
version=Version
weekly=weekly
yearly=yearly