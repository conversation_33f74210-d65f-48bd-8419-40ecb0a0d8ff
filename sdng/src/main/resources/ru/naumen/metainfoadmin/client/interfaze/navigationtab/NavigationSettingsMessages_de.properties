allProfiles=Alle
area=Panelbereich
addTile=<PERSON><PERSON> hinz<PERSON>gen
additionalDeleteWithListTemplate=Vorlage ''{0}'' wird nicht gelöscht. Auf der Seite "Systemeinstellungen -> Vorlagen -> Listenvorlagen" können Sie eine Vorlage löschen.
adminArea=Unveränderlicher Bereich der Symbolleiste für den Schnellzugriff
adminAreaDescription=Kacheln im nicht bearbeitbaren Bereich des Panels können nur in der Admin-Oberfläche entfernt oder hinzugefügt werden. Benutzer können in diesem Bereich keine Kacheln entfernen oder hinzufügen.
allProfilesDescription=Es stehen nur Profile mit absoluten Rollen zur Auswahl
addButtonMenuType=Schaltfläche "Objekte hinzufügen"
additionalDeleteMenuItemInfo=Alle verschachtelten Elemente werden zusammen mit dem übergeordneten Element entfernt:\n{0}\nWeiter löschen?
profiles=Profile
breadCrumb=Navigationskette ("Breadcrumbs")
cardTemplate=Kartenvorlage
chapterMenuType=Menü-Abschnitt
companyMenuType=Firma
content=Inhalt
contentCard=Tab von Karte
contentContent=Tab für Inhalt
contentType=Inhaltstyp
createBlockTitle=Erstellen von Objekten über die allgemeine Schaltfläche "Hinzufügen"
currentUser=Aktueller Benutzer
currentUserType=Typ des Mitarbeiters
editCreateBlockTitle=Bearbeitung der gemeinsamen Schaltfläche "Hinzufügen"
formatting=Formatierung
hasElementWithTypeAlready=Das Element ist bereits in der Menüstruktur vorhanden. Es ist ratsam, Doppelarbeit zu vermeiden, es sei denn, Ihre Unternehmensprozesse erfordern dies.
favoritesMenuType=Favoriten
historyMenuType=Historie
leftMenu=Linkes Menü
hint=Tooltip-Text
insertedInChapter=Eingebettet in den Abschnitt
itemType=Typ des Elements
leftMenuRoot=Menü (alle Artikel)
linkObject=Objekt, mit dem die Anwendung arbeitet
linkObjectAttribute=Attribut, das ein Objekt referenziert
menuElement=Menüpunkt
linkTemplateForContentOfDate=Inhaltslink-Listenvorlage von {0}
linkToContentMenuType=Link zum Inhalt
listOfProfiles=Verfügbar für eine begrenzte Liste von Profilen
linkObjectUUID=Objekt UUID
listPageTitle=Titel der Inhaltsseite
listOfProfilesDescription=Die Auswahl an Profilen ist begrenzt, da in den übergeordneten Abschnitten Sichtbarkeitseinschränkungen bestehen.<br />Das Feld kann leer sein, in diesem Fall gelten nur die Einschränkungen der übergeordneten Abschnitte für das aktuelle Element.
menuItem=Punkt von Links Menü
menuLeftElementBy=Punkt von Linkes Menü
menuElementAttributes=Element-Attribute
menuElementBy=Menüpunkt
menuElementWithTitle=Menüpunkt "{0}"
mobileMenuItemCodeIsFound=Der eingegebene Code wird bereits im Navigationsmenü verwendet. Geben Sie einen eindeutigen Code ein.
menuTopElementBy=Top-Menüpunkt
module=Sektion
mustSelectOneArea=Sie müssen mindestens einen Bereich der Symbolleiste für den Schnellzugriff auswählen.
notDefined=[undefiniert]
objLinkedToCurrentUser=Mit dem aktuellen Benutzer verbundenes Objekt
quickAccessPanel=Schnellzugriffspanel
referenceMenuType=Link zur Karte
noProfiles=Niemand
quickAccessTileBy=Kacheln
quickTileAccus=Menükachel für den Schnellzugriff
resultProfiles=Wer wird sehen
section=Abschnitt
showTopMenu=Oberes Menü anzeigen
showAdminArea=Einen unveränderten Bereich der Symbolleiste für den Schnellzugriff anzeigen
showBreadCrumb="Brotkrümel" zeigen
showLeftMenu=Linkes Menü anzeigen
showSystemArea=Den Servicebereich der Schnellzugriffsleiste anzeigen
showUserArea=Den Benutzerbereich der Schnellzugriffsleiste anzeigen
systemArea=Schnellzugriffstafel-Servicebereich
attrForTitle=Attribut ''{0}''
topMenu=Oberes Menü
userArea=Benutzerdefinierter Bereich der Symbolleiste für den Schnellzugriff
toTopMenuElements=zur Liste der Navigationsmenüpunkte
tile=Kachel
visibilityHidden=Sichtbarkeit deaktiviert
customButtonMenuType=Benutzerdefinierte Schaltfläche
customLinkMenuType=Willkürliche Referenz
contentAttrTitle=Attribut für den Namen
contentUseAttrTitle=Name aus einem Objektattribut
contentLinkToCard=Link zur Karte
contentLinkAttribute=Beziehungsattribut
contentObjectClass=Objektklasse
contentObjectType=Typ des Objekts
contentMenuItemObject={0}
contentMenuItemObject[CURRENT_USER]=Aktueller Benutzer
contentMenuItemObject[CURRENT_USER_OU]=Abteilung des aktuellen Benutzers
contentMenuItemObject[ROOT]=Firmen
contentMenuItemObject[REFERENCE_TO_USER]=Mit dem Benutzer verbundenes Objekt
contentMenuItemObject[REFERENCE_TO_OU]=Mit der Abteilung des Benutzers verbundenes Objekt
contentMenuItemObject[REFERENCE_TO_ROOT]=Firmenbezogenes Objekt
userAreaDescription=In diesem Bereich können Benutzer Kacheln für Menüpunkte manuell entfernen und hinzufügen. Sie können auch eine Kachel entfernen, die beim Anpassen des Menüs in der Admin-Oberfläche hinzugefügt wurde.
visibilityEditing=Sichtbarkeit editieren
homePage=Home-Seite
homePageElementBy=von Home-Seite
homePageCardName=Home-Seite "{0}"
showHomePage=Taste "Home-Seite erstellen" anzeigen
toHomePageElements=zur Liste der Homepages
