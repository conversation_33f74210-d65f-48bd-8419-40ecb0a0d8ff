@CHARSET "UTF-8";

@external .templateMode;

.background-panel {	
	z-index: 9;
	position: absolute !important;
	left: 0;
	top: 0;	
	width: 100%;
	height: 0%;	
	overflow: hidden;	
	
	-webkit-transition: height ease-out 0.6s;
    -moz-transition: height ease-out 0.6s;
    -o-transition: height ease-out 0.6s;
    transition: height ease-out 0.6s;
	background-color: rgb(200, 200, 200);    
    background-color: rgba(0, 0, 0, 0.3);    
	overflow: hidden;
}
.background-panel.shown-panel {
	height: 100%;
}
.background-panel.mode-offset {
	margin-top: 46px;
	}
	.templateMode .background-panel.mode-offset {
		margin-top: 43px;
		}
.panel {
	left: 22px;	
	height: 100%;	
}
.panel-part{	
	overflow: hidden;		
}

.fixed-panel-part > div {
   position: fixed !important;
}
.panel-part > div {
   text-align: center;
   font-size: 80px;
   color: white;
   margin-top: 40px;
}

.dragger {		
	width: 4px;	
}
.dragger:after {
    content:"";
    position: absolute;     
    top: 0;
    bottom: 0;
    left: 25%;
    border-left: 2px solid black;    
}	