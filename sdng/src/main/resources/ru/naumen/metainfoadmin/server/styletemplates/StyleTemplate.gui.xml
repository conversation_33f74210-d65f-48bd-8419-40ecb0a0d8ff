<publisher
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    place-class="ru.naumen.metainfoadmin.client.style.templates.StyleTemplatePlace"
    controller="ru.naumen.metainfoadmin.server.style.templates.StyleTemplateCardController"
    title=":styletemplates-styleTemplate:"
    name="StyleTemplate">
    <tab name="info"
        title=":styletemplates-styleTemplate:">
        <property-list name="info" 
            title=":properties:"
            attention="info#attention">
            <button name="edit" 
                title=":styletemplates-edit:" 
                debug-id="edit" 
                style-code="edit"
                form="ru/naumen/metainfoadmin/server/styletemplates/EditStyleTemplate.form.xml"
                permissionVisibleCode="EDIT"/>
            <button name="delete"
                title=":styletemplates-delete:" 
                debug-id="del"
                style-code="del"
                dialog-caption=":styletemplates-deleteTemplateCaption:"
                dialog-message=":styletemplates-deleteTemplateMessage:"
                controller="ru.naumen.metainfoadmin.server.style.templates.DeleteSingleStyleTemplateOperationController"
                permissionVisibleCode="DELETE"/>
            <property name="title" 
                presentation="text" 
                title=":title:"
                debug-id="titleCaption" />
            <property name="code" 
                presentation="text" 
                title=":code:"
                debug-id="codeCaption" />
            <property name="creationDate" 
                presentation="text" 
                title=":styletemplates-creationDate:"
                debug-id="creationDateCaption" />
            <property name="lastModifiedDate" 
                presentation="text" 
                title=":styletemplates-lastModifiedDate:"
                debug-id="lastModifiedDateCaption" />
            <property name="usagePlaces" 
                presentation="htmlText" 
                title=":styletemplates-usagePlaces:"
                debug-id="usagePlacesCaption" />
            <property name="settingsSet"
                      presentation="htmlText"
                      title=":styletemplates-set:"
                      debug-id="Info.settingsSet" />
        </property-list>
        <property-list name="text" 
            title=":styletemplates-templateText:"
            attention="info#attention"
            show-captions="false">
            <property name="templateText" 
                presentation="richTextView" 
                title=":styletemplates-templateText:"
                debug-id="templateTextCaption" />
        </property-list>
    </tab>
</publisher>