<form
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    title=":scriptcatalog-scriptModuleAddition:"
    controller="ru.naumen.metainfoadmin.server.script.modules.AddScriptModuleFormController"
    fixed="false"
    name="AddScriptModule">
    <field name="code" 
        presentation="textBox" 
        title=":code:"
        debug-id="code" 
        required="true" 
        validation="CodeScriptModule"/>
    <field name="description" 
        presentation="textArea" 
        title=":description:"
        debug-id="description"
        validation="XmlChars"/>
    <field name="moduleVersion" 
        presentation="integer" 
        title=":version:"
        debug-id="moduleVersion" />
    <script-field name="script"
        presentation="scriptEdit" 
        title=":text:" 
        debug-id="script" 
        required="true"
        script-category="modul"
        validation="XmlChars"/>
    <check-box-field name="superUserReadable" 
        title=":scriptcatalog-superUserReadable:"
        debug-id="superUserReadable" />
    <check-box-field name="superUserWritable" 
        title=":scriptcatalog-superUserWritable:"
        debug-id="superUserWritable" />
    <check-box-field name="restAllowed"
        title=":scriptcatalog-restAllowed:"
        value="restAllowed"
        debug-id="restAllowed" />
    <field name="settingsSet"
           title=":scriptcatalog-set:"
           presentation="listBoxWithEmptyOpt"
           debug-id="settingsSet"
           elements="settingsSet#elements"
           value="settingsSet#value"/>
</form>