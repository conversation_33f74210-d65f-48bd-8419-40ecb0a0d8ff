<publisher xmlns:guic="http://www.w3.org/2001/XMLSchema-instance" guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    place-class="ru.naumen.metainfoadmin.client.script.ScriptPlace" controller="ru.naumen.metainfoadmin.server.script.card.ScriptCardController"
    title=":scriptcatalog-scriptCardTitle:" name="ScriptCard">
    <tab name="info"
        title=":scriptcatalog-scriptCardTitle:">
        <property-list name="info" 
            title=":properties:"
            attention="info#attention">
            <button name="edit"
                title=":edit:"
                style-code="edit"
                debug-id="edit"
                form="ru/naumen/metainfoadmin/server/script/EditScript.form.xml"
                permissionVisibleCode="EDIT"/>
            <button name="delete"
                title=":delete:"
                style-code="del"
                debug-id="del"
                dialog-caption=":deleteCaption:"
                dialog-message=":deleteMessage:"
                controller="ru.naumen.metainfoadmin.server.script.card.DeleteScriptOperationController"
                permissionVisibleCode="DELETE"/>
            <property name="title" 
                presentation="text" 
                title=":title:"
                debug-id="titleCaption" />
            <property name="code" 
                presentation="text" 
                title=":code:"
                debug-id="codeCaption" />
            <property name="categoriesLink" 
                presentation="htmlText"
                title=":scriptcatalog-categoriesUsages:" 
                debug-id="categoriesCaption" />
            <property name="categoriesDefault" 
                presentation="htmlText"
                title=":scriptcatalog-categoriesDefault:" 
                debug-id="categoriesDefault" />
            <property name="body"
                presentation="scriptView"
                title=":scriptcatalog-text:"
                debug-id="textCaption" />
            <property name="documentation"
                presentation="richTextView"
                title=":scriptcatalog-documentation:"
                debug-id="documentationCaption" />
            <property name="settingsSet"
                      presentation="htmlText"
                      title=":scriptcatalog-set:"
                      debug-id="Info.settingsSet" />
        </property-list>
        <table-list name="usagePoints"
                title=":scriptcatalog-usagePoints:"
                controller="ru.naumen.metainfoadmin.server.script.card.ScriptUsagePointsController"
                debug-id="usagePoints"
                selection-enabled="false"
                show-title="true">
            <column name="title"
                title=":title:"
                filtered="true"
                filter-conditions="StringContains StringNotContains"
                debug-id="title"
                max-line-length="80"
                width="30%"/>
            <column name="category"
                title=":categories:"
                filtered="true"
                filter-conditions="SelectListContains SelectListNotContains"
                debug-id="category"
                width="30%"/>
            <column name="metaClass"
                title=":classes:"
                filtered="true"
                filter-conditions="SelectListContains SelectListNotContains"
                debug-id="metaClass"
                width="30%"/> 
         </table-list> 
    </tab>
</publisher>