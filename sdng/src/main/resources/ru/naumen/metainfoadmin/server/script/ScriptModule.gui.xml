<publisher
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    place-class="ru.naumen.metainfoadmin.client.script.ScriptModulePlace"
    controller="ru.naumen.metainfoadmin.server.script.modules.ScriptModuleCardController"
    title=":scriptcatalog-moduleCardTitle:"
    name="ScriptModule">
    <tab name="info"
        title=":scriptcatalog-moduleCardTitle:">
        <property-list name="info" 
            title=":properties:"
            attention="info#attention">
            <button name="edit" 
                title=":scriptcatalog-edit:" 
                debug-id="edit" 
                style-code="edit"
                form="ru/naumen/metainfoadmin/server/sctiptmodules/EditScriptModules.form.xml"
                permissionVisibleCode="EDIT"/>
            <button name="delete"
                title=":scriptcatalog-delete:" 
                debug-id="del"
                style-code="del"
                dialog-caption=":scriptcatalog-deleteModuleCaption:"
                dialog-message=":scriptcatalog-deleteModuleMessage:"
                controller="ru.naumen.metainfoadmin.server.script.modules.DeleteSingleScriptModuleOperationController"
                permissionVisibleCode="DELETE"/>
            <property name="code" 
                presentation="text" 
                title=":scriptcatalog-code:"
                debug-id="codeCaption" />
            <property name="description" 
                presentation="richTextView"
                title=":scriptcatalog-description:"
                debug-id="descriptionCaption" />
            <property name="moduleVersion" 
                presentation="text" 
                title=":scriptcatalog-version:"
                debug-id="moduleVersionCaption" />
            <property name="superUserReadable" 
                presentation="booleanImage" 
                title=":scriptcatalog-superUserReadable:"
                debug-id="superUserReadableCaption" />
            <property name="superUserWritable" 
                presentation="booleanImage" 
                title=":scriptcatalog-superUserWritable:"
                debug-id="superUserWritableCaption" />
            <property name="restAllowed"
                presentation="booleanImage"
                title=":scriptcatalog-restAllowed:"
                debug-id="restAllowedCaption" />
            <property name="script"
                presentation="scriptView"
                title=":scriptcatalog-text:"
                debug-id="scriptCaption" />
            <property name="documentation"
                 presentation="richTextView"
                 title=":scriptcatalog-documentation:"
                 debug-id="documentationCaption" />
            <property name="settingsSet"
                      presentation="htmlText"
                      title=":scriptcatalog-set:"
                      debug-id="Info.settingsSet" />
        </property-list>
    </tab>
</publisher>