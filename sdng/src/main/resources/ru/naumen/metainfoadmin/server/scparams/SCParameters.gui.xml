<publisher
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    place-class="ru.naumen.metainfoadmin.client.scparams.SCParamsPlace" 
    controller="ru.naumen.metainfoadmin.server.scparams.SCParametersCardController"
    title=":SCParametersMessages.serviceCallParameters:"
    name="SCParameters">
    <tab name="info"
        title=":SCParametersMessages.serviceCallParameters:">
        <property-list name="order" 
            title=":SCParametersMessages.agreementServiceOrCase:" 
            debug-id="fieldsOrder"
            attention="order#attention">
            <button name="editOrder" 
                title=":edit:" 
                debug-id="editOrder" 
                style-code="edit"
                form="ru/naumen/metainfoadmin/server/scparams/EditSCParametersOrder.form.xml"
                permissionVisibleCode="EDIT"/>
            <property name="orderScSettings" 
                presentation="text" 
                title=":SCParametersMessages.orderValue:"
                debug-id="orderScSetting" />
        </property-list>
        
        <property-list name="info" 
            title=":SCParametersMessages.agreementServiceField:"
		    debug-id="agreementServiceField"
            attention="info#attention">
            <button name="edit" 
                title=":edit:" 
                debug-id="edit" 
                style-code="edit"
                form="ru/naumen/metainfoadmin/server/scparams/EditSCParameters.form.xml"
                permissionVisibleCode="EDIT"/>
            <property name="agreementServiceSetting" 
                presentation="text" 
                title=":SCParametersMessages.agreementServiceValue:"
                debug-id="agreementServiceSetting" />
            <property name="agreementServiceEditPrs" 
                presentation="text" 
                title=":SCParametersMessages.editPresentation:"
                debug-id="agreementServiceEditPrs" />
            <property name="isFilterAgreements" 
                presentation="booleanImage"
                title=":SCParametersMessages.agreementsFiltration:"
                debug-id="filterAgreements" />
            <property name="agreementsFiltrationScript" 
                presentation="scriptComponentView"
                title=":SCParametersMessages.agreementsFiltrationScript:" 
                debug-id="agreementsFiltrationScript" />
            <property name="isFilterServices" 
                presentation="booleanImage" 
                title=":SCParametersMessages.servicesFiltration:"
                debug-id="filterServices" />
            <property name="servicesFiltrationScript" 
                presentation="scriptComponentView" 
                title=":SCParametersMessages.servicesFiltrationScript:" 
                debug-id="servicesFiltrationScript" />
        </property-list>
        
        <property-list name="client" 
            title=":SCParametersMessages.client:"
		    debug-id="client"
            attention="client#attention">
            <button name="editClient" 
                title=":edit:" 
                debug-id="editClient" 
                style-code="edit"
                form="ru/naumen/metainfoadmin/server/scparams/EditSCParametersClient.form.xml"
                permissionVisibleCode="EDIT"/>
            <property name="clientRequiredEditable" 
                presentation="booleanImage" 
                title=":SCParametersMessages.clientRequiredEditableTitle:"
                debug-id="clientRequiredEditable" />
            <property name="clientAutoResolve" 
                presentation="booleanImage" 
                title=":SCParametersMessages.clientAutoResolveTitle:"
                debug-id="clientAutoResolve" />
        </property-list>
        
        <property-list name="casesFiltering" 
            title=":SCParametersMessages.casesFilteringTitle:"
			debug-id="scCaseField">
            <button name="edit" 
                title=":edit:" 
                debug-id="edit" 
                style-code="edit"
                form="ru/naumen/metainfoadmin/server/scparams/EditSCParametersCasesFiltering.form.xml"
                permissionVisibleCode="EDIT"/>
            <property name="isFilterCases" 
                presentation="booleanImage" 
                title=":SCParametersMessages.casesFilteringParamCaption:"
                debug-id="filterCases" />
            <property name="casesFiltrationScript" 
                presentation="scriptComponentView" 
                title=":SCParametersMessages.casesFilteringScriptCaption:" 
                debug-id="casesFiltrationScript" />
        </property-list>
    </tab>
</publisher>
