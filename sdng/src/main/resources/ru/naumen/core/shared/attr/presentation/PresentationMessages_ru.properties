richTextPresentation=Текст в формате RTF
richTextUnsafePresentation=Текст в формате RTF, небезопасный
caseList=Набор типов класса
checkBoxField=Флажок
colorIndicatorPresentation=Индикатор цвета
dynamicFieldSet=Набор динамических полей
selectFieldWithFolders=Поле выбора с папками
selectCatalogItemTree=Дерево выбора
dateTimeIntervalPresentation=Временной интервал
suggestionFromCatalog=Выбор из справочника
selectCatalogItemList=Список элементов справочника
backTimerDeadLinePresentation=Время окончания
textPresentationDefaultValue=пример текста\nпример текста
fileUploadPresentation=Форма загрузки
catalogItems=Набор элементов справочника
licenseEditPresentation=Выпадающий список множественного выбора
linkedClasses=Классы/типы объектов
selectField=Поле выбора
selectList=Список выбора
selectListFolders=Список выбора с папками
selectTree=Дерево выбора
selectTreeFolders=Дерево выбора с папками
structureBasedSelectionTree=Дерево выбора на основе структуры
structureBasedSelectionTreeWithFolders=Дерево выбора с папками на основе структуры
imagePresentation=Изображение
twoInputFields=Два поля ввода
hyperlinkPresentation=Гиперссылка
unknownTypePresentation=Неизвестный тип
stateCodePresentation=Код статуса
catalogItemColorIsNotSet=Цвет элемента справочника не задан
imageAndTitlePresentation=Название и изображение
fileDownloadPresentation=Ссылка для скачивания
integerPresentation=Целое число
coloredTitlePresentation=Выделение названия цветом
clientCurrentTime=Текущее дата/время клиента
boLinkPresentation=Ссылка на БО
boLinksPresentation=Набор ссылок на БО
textWidePresentation=Текст (по всей ширине)
coloredText=Цветная строка
stringPresentation=Строка
selectCaseList=Список выбора
yesNoBackTimerPresentation=Просрочен/Не просрочен
timerElapsedPresentation=Время
oneZeroPresentation=1/0
catalogItemDefaultValue=пример элемента справочника
textPresentation=Текст
metaClassViewPresentation=Метакласс
inputField=Поле ввода
stateTitlePresentationExampleValue=Активный
inputHyperlinkField=Поле ввода гиперссылки
timerStatusPresentation=Статус
catalogItemIconIsNotSet=Изображение элемента справочника не задано
stringPresentationDefaultValue=Строка
colorWithTitlePresentation=Название и индикатор цвета
doublePresentation=Вещественное число
catalogItemsDefaultValue=пример элемента справочника 1, пример элемента справочника 2
fileDownloadPresentationDefaultValue=Название файла.doc
aggregateDefaultValue=пример бизнес-объекта1/пример бизнес-объекта2
stateTitlePresentation=Название статуса
licenseViewPresentation=Строка
timerAllowancePresentation=Остаток времени
radioButtonField=Переключатели
yes=да
yesCapitalized=Да
yesNoPresentation=Да/Нет
richTextWidePresentation=Текст в формате RTF (по всей ширине)
richTextUnsafeWidePresentation=Текст в формате RTF, небезопасный (по всей ширине)
richTextPresentationDefaultValue=<b>пример</b> текста<br><i>пример <b>текста</b></i>
inputDateTimeIntervalField=Поле ввода временного интервала
responsibleListEdit=Список со сдвигом
datePresentation=Дата
dateTimePresentation=Дата/время
metaClassEditPlanePresentation=Плоский список
metaClassEditTreePresentation=Дерево выбора
boLinksDefaultValue=пример бизнес-объекта 1, пример бизнес-объекта 2
boLinkDefaultValue=пример бизнес-объекта
secGroupsPresentation=Набор групп пользователей
quickSelectionField=Поле быстрого выбора
multiclassObjects=Набор ссылок на произвольные БО
no=нет
noCapitalized=Нет
commentObjects=Набор ссылок на комментарии
takeButton=себе
takeTeamButton=своей команде
inputFieldWithMask=Поле ввода с маской
sourceCodePresentation=Текст с подсветкой синтаксиса
sourceCodeWidePresentation=Текст с подсветкой синтаксиса (по всей ширине)
metricValuePresentation=Значение метрики
executionResultPresentation=Результат планировщика
dateTimeWithSecondPresentations=Дата/время (с секундами)
dateTimeWithMillisPresentations=Дата/время (с миллисекундами)
passwordPresentation=Пароль
richTextPlainPresentation=Текст в формате RTF, очищенный от стилей
richTextStyledPresentation=Текст в формате RTF с сохранением стилей
richTextDefaultPresentation=Текст в формате RTF в соответствии с системной настройкой ({0})
richTextDefaultPresentationPlain=очищенный от стилей
richTextDefaultPresentationStyled=с сохранением стилей
