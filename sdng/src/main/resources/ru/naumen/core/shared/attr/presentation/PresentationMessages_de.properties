richTextPresentation=Text im RTF-Format
richTextUnsafePresentation=Text im RTF-Format, unsicher
caseList=<PERSON><PERSON> von <PERSON>entypen
checkBoxField=Kontrollkästchen
colorIndicatorPresentation=Farbanzeige
selectFieldWithFolders=Auswahlfeld mit Ordnern
selectCatalogItemTree=Auswahlbaum
dateTimeIntervalPresentation=Zeitintervall
suggestionFromCatalog=Wählen Sie aus dem Verzeichnis
selectCatalogItemList=Liste der Verzeichnisselemente
backTimerDeadLinePresentation=Endzeit
textPresentationDefaultValue=Textbeispiel\nTextbeispiel
fileUploadPresentation=Formular hochladen
catalogItems=Satz von Verzeichnis-Elementen
licenseEditPresentation=Dropdown-Liste
selectField=Auswahlfeld
selectList=Auswahlliste
selectListFolders=Auswahlliste mit Ordnern
selectTree=Auswahlbaum
selectTreeFolders=Auswahlbaum mit Ordnern
structureBasedSelectionTree=Strukturbasierter Auswahlbaum
structureBasedSelectionTreeWithFolders=Strukturbasier<PERSON> Auswahlbaum mit Ordnern
imagePresentation=Bild
twoInputFields=Zwei Eingabefelder
hyperlinkPresentation=Hyperlink
unknownTypePresentation=Unbekannter Typ
stateCodePresentation=Statuscode
catalogItemColorIsNotSet=Die Farbe des Verzeichnis-Elements ist nicht festgelegt
imageAndTitlePresentation=Titel und Bild
fileDownloadPresentation=Download-Link
integerPresentation=Ganze Zahl
coloredTitlePresentation=Farbhighlight des Titels
clientCurrentTime=Aktuelles Datum/Zeit des Kunden
boLinkPresentation=Link zu Geschäftsobjekt
boLinksPresentation=Satz von Links zu Geschäftsobjekten
textWidePresentation=Text (volle Breite)
coloredText=Farbige Linie
stringPresentation=String
selectCaseList=Auswahlliste
yesNoBackTimerPresentation=Abgelaufen/Nicht abgelaufen
timerElapsedPresentation=Zeit
oneZeroPresentation=1/0
catalogItemDefaultValue=Beispiel eines Verzeichnis-Element
textPresentation=Text
metaClassViewPresentation=Metaklasse
inputField=Eingabefeld
stateTitlePresentationExampleValue=Aktiv
inputHyperlinkField=Hyperlink-Eingabefeld
timerStatusPresentation=Status
catalogItemIconIsNotSet=Das Bild des Verzeichnis-Elements ist nicht definiert
stringPresentationDefaultValue=String
colorWithTitlePresentation=Farbname und Anzeige
doublePresentation=Reelle Zahl
catalogItemsDefaultValue=Beispiel für Verzeichnis-Element 1, Beispiel für Verzeichnis-Element 2
fileDownloadPresentationDefaultValue=Dateiname.doc
aggregateDefaultValue=Beispiel eines BO1/Beispiel eines BO2
stateTitlePresentation=Statustitel
licenseViewPresentation=String
timerAllowancePresentation=Verbleibende Zeit
radioButtonField=Radio buttons
yesNoPresentation=Ja/Nein
richTextWidePresentation=Text im RTF-Format (volle Breite)
richTextUnsafeWidePresentation=Text im RTF-Format, unsicher (volle Breite)
richTextPresentationDefaultValue=<b>Beispiel</b> für Text <br><i> Beispiel <b> für Text</b></i>
inputDateTimeIntervalField=Zeitintervall-Eingabefeld
responsibleListEdit=Liste mit Schichten
datePresentation=Datum
dateTimePresentation=Datum/Zeit
metaClassEditPlanePresentation=Flatlist
metaClassEditTreePresentation=Auswahlbaum
boLinksDefaultValue=Beispiel eines BO1/Beispiel eines BO2
boLinkDefaultValue=Beispiel eines BO
quickSelectionField=Schnellauswahlfeld
multiclassObjects=Satz von Links zu zufällige Geschäftsobjekte
commentObjects=Satz von Links zu Kommentaren
takeTeamButton=meinem Team zuweisen
takeButton=mir selbst zuweisen
inputFieldWithMask=Eingabefeld mit Maske
sourceCodePresentation=Text mit Syntaxhervorhebung
sourceCodeWidePresentation=Text mit Syntaxhervorhebung (volle Breite)
metricValuePresentation=Metrischer Wert
dateTimeWithSecondPresentations=Datum/Zeit (mit Sekunden)
dateTimeWithMillisPresentations=Datum/Zeit (mit Millisekunden)
passwordPresentation=Kennwort
richTextPlainPresentation=Text im RTF-Format ohne Stile
richTextStyledPresentation=Text im RTF-Format mit Stilen
richTextDefaultPresentation=Text im RTF-Format gemäß Systemeinstellungen ({0})
richTextDefaultPresentationPlain=ohne Stile
richTextDefaultPresentationStyled=mit Stilen
secGroupsPresentation=Gruppe von Benutzergruppen
dynamicFieldSet=Satz dynamischer Felder
linkedClasses=Objektklassen/-typen
executionResultPresentation=Scheduler-Ergebnis
yes=ja
no=Nein
noCapitalized=Nein
yesCapitalized=Ja
