richTextPresentation=Text in RTF format
richTextUnsafePresentation=Text in RTF format, unsafe
caseList=Set of class types
checkBoxField=Checkbox
colorIndicatorPresentation=Color indicator
dynamicFieldSet=Set of dynamic fields
selectFieldWithFolders=Selection field with folders
selectCatalogItemTree=Selection tree
dateTimeIntervalPresentation=Time interval
suggestionFromCatalog=Select from catalog
selectCatalogItemList=List of the catalog items
backTimerDeadLinePresentation=End time
textPresentationDefaultValue=text example\ntext example
fileUploadPresentation=Upload form
catalogItems=Set of catalog items
licenseEditPresentation=Drop-down list
linkedClasses=Object classes/types
selectField=Selection field
selectList=Selection list
selectListFolders=Selection list with folders
selectTree=Selection tree
selectTreeFolders=Selection tree with folders
structureBasedSelectionTree=Structure-based selection tree
structureBasedSelectionTreeWithFolders=Structure-based selection tree with folders
imagePresentation=Image
twoInputFields=Two input fields
hyperlinkPresentation=Hyperlink
unknownTypePresentation=Unknown type
stateCodePresentation=State code
catalogItemColorIsNotSet=Color of catalog item is not specified
imageAndTitlePresentation=Title and image
fileDownloadPresentation=Download link
integerPresentation=Integer
coloredTitlePresentation=Color highlight of title
clientCurrentTime=Current date/time of the client
boLinkPresentation=Link to BO
boLinksPresentation=Set of BO links
textWidePresentation=Text (full width)
coloredText=Colored line
stringPresentation=String
selectCaseList=Selection list
yes=yes
yesCapitalized=Yes
yesNoBackTimerPresentation=Expired/Not expired
timerElapsedPresentation=Time
oneZeroPresentation=1/0
catalogItemDefaultValue=Example of catalog item
textPresentation=Text
metaClassViewPresentation=Metaclass
inputField=Input field
stateTitlePresentationExampleValue=Active
inputHyperlinkField=Hyperlink input field
timerStatusPresentation=State
catalogItemIconIsNotSet=Image of catalog item is not specified
stringPresentationDefaultValue=String
colorWithTitlePresentation=Color name and indicator
doublePresentation=Real number
catalogItemsDefaultValue=example of catalog item 1, example of catalog item 2
fileDownloadPresentationDefaultValue=file name.doc
aggregateDefaultValue=example of business object1/example of business object2
stateTitlePresentation=State title
licenseViewPresentation=String
timerAllowancePresentation=Remaining time
radioButtonField=Radio buttons
yesNoPresentation=Yes/No
richTextWidePresentation=Text in RTF format (full width)
richTextUnsafeWidePresentation=Text in RTF format, unsafe (full width)
richTextPresentationDefaultValue=<b>example</b> of text<br><i>example <b>of text</b></i>
inputDateTimeIntervalField=Time interval input field
responsibleListEdit=List with shifts
datePresentation=Date
dateTimePresentation=Date/time
metaClassEditPlanePresentation=Flat list
metaClassEditTreePresentation=Selection tree
no=no
noCapitalized=No
boLinksDefaultValue=example of business object 1, example of business object 2
boLinkDefaultValue=example of business object
quickSelectionField=Quick selection field
multiclassObjects=Set of links to random object
commentObjects=Set of links to comments
takeTeamButton=assign to my team
takeButton=assign to myself
inputFieldWithMask=Input field with mask
sourceCodePresentation=Text with syntax highlighting
sourceCodeWidePresentation=Text with syntax highlighting (full width)
metricValuePresentation=Metric value
executionResultPresentation=Scheduler result
dateTimeWithSecondPresentations=Date/time (with seconds)
dateTimeWithMillisPresentations=Date/time (with milliseconds)
passwordPresentation=Password
richTextPlainPresentation=Text in RTF format without styles
richTextStyledPresentation=Text in RTF format with styles
richTextDefaultPresentation=Text in RTF format according to system settings ({0})
richTextDefaultPresentationPlain=without styles
richTextDefaultPresentationStyled=with styles
secGroupsPresentation=Set of users groups
