<?xml version="1.0" encoding="UTF-8"?>
<domain code="serviceCall">
	
	<marker-group code="add" order="110">
		<title lang="ru">Добавление объекта</title>
        <title lang="en">Add object</title>
	</marker-group>

	
	<marker xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="markerOverride"
		code="alwaysReadable" group="viewAttrs" enabled="false" granted="true">
		<attributes>state</attributes>
		<attributes>massProblem</attributes>
		<attributes>masterMassProblem</attributes>
		<attributes>massProblemSlaves</attributes>
	</marker>
	
	<marker xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="markerOverride"
		code="editRestAttributes" group="editAttrs" unlicense-allowed="true">
		<title lang="ru">Остальные атрибуты</title>
		<title lang="en">The remaining attributes</title>
	</marker>
	
	<action code="AddToEmployee" group="add" unlicense-allowed="true">
		<title lang="ru">Добавление запроса для клиента-сотрудника</title>
		<title lang="en">Add issue for contact client-employee</title>
	</action>
	<action code="AddToOU" group="add" unlicense-allowed="true">
		<title lang="ru">Добавление запроса для клиента-отдела</title>
		<title lang="en">=Add issue for client-departament</title>
	</action>
	<action code="AddToTeam" group="add">
		<title lang="ru">Добавление запроса для клиента-команды</title>
		<title lang="en">Add issue for client-team</title>
	</action>
    <action code="AddWithoutClient" group="add" enabled="false">
        <title lang="ru">Добавление запроса без контрагента</title>
        <title lang="en">Add issue without client</title>
    </action>

    <action code="ChangeMainAssociation" group="default">
        <title lang="ru">Изменение привязки запроса</title>
        <title lang="en">Change of issue contact and servce</title>
        
    </action>
    
    <action code="massProblemManipulation" group="default">
        <title lang="ru">Изменение массовости</title>
        <title lang="en">Change mass mode</title>        
        <attributes>massProblem</attributes>
        <attributes>masterMassProblem</attributes>
        <attributes>massProblemSlaves</attributes>
        <attributes>wfProfile</attributes>
    </action>
    
    <action code="copyCommentToMaster" group="comment">
        <title lang="ru">Копирование комментариев из ведомого в ведущий объект</title>
        <title lang="en">Copy comments from slave to master object</title>
    </action>
    
    <action code="copyCommentToSlaves" group="comment">
        <title lang="ru">Копирование комментариев из ведущего в ведомые объекты</title>
        <title lang="en">Copy comments from master to slave objects</title>
    </action>

	<action code="editState" group="editStateGrp" unlicense-allowed="true">
		<title lang="ru">Остальные переходы</title>
		<title lang="en">Other transitions</title>
	</action>

	<action code="AddObject" enabled="false" />
	<action code="RemoveObject" enabled="false" />
	<action code="UnRemoveObject" enabled="false" />
	<action code="MoveObject" enabled="false" />
	<action code="CopyObject" enabled="false" />
	
	<role code="ServiceCallAuthor" type="SYSTEM">
		<title lang="ru">Автор запроса</title>
		<title lang="en">Issue author</title>
		<property code="snippet">serviceCallAuthorRoleSnippet</property>
	</role>
	<role code="ServiceCallSolver" type="SYSTEM">
		<title lang="ru">Сотрудник, решивший запрос</title>
		<title lang="en">Issue resolved by employee</title>
		<property code="snippet">serviceCallSolvedByRoleSnippet</property>
	</role>
	<role code="ServiceCallSolvedByTeamMember" type="SYSTEM">
		<title lang="ru">Член команды, решившей запрос</title>
		<title lang="en">Issue resolved by team member</title>
		<property code="snippet">serviceCallSolvedByTeamMemberRoleSnippet</property>
	</role>
    <role code="ServiceCallEmployeeOfClientOU" type="SYSTEM">
        <title lang="ru">Сотрудник отдела-контрагента запроса</title>
        <title lang="en">=Issue contact department employee</title>
        <property code="snippet">serviceCallEmployeeOfClientOURoleSnippet</property>
    </role>
    <role code="ServiceCallClient" type="SYSTEM">
        <title lang="ru">Контрагент запроса</title>
        <title lang="en">Issue contact</title>
        <property code="snippet">serviceCallClientRoleSnippet</property>
    </role>
</domain>