accessToFullReloadMetainfo=Access to full replacement of settings when loading meta-information
action=Action
addCommentInContent=Adding a comment in content
addCommentInlineForm=Add comment inline
addForms=Add forms
additionalLibraries=Additional libraries
adminLog=Admin actions log
adminLogDescription=Used to work with the history of settings changes
adminLogRecordTitle=Record of admin actions log from
adminProfiles=Administration profiles
adminProfilesDescription=Used to manage administration profiles
administration=Administration
administrationDescription=For system administrator''s work with metainformation, license files and access sessions
administrationLiteDescription=Used for system administrator work with license files and access sessions
allScripts=All scripts
analizers=Types of analyzers available in the system
applicationLog=System logs
applications=Applications
applicationsDescription=To configure embedded applications
areYouReallyWantUploadMetaInfo=<b>Attention!</b> Are you sure you want to load meta information in full reload mode?
attribute=attribute ''{0}''
attentionAddCommentInlineFormTrue=Scripts from the add comment form will be executed when the "Comments" content is loaded.
authorAction=Author
byCategories=By category
byClasses=By class
categoryAction=Category
certificateErrorLoading=Error when loading certificate.
certificateUploadedSuccessfully=Certificate is successfully uploaded.
certificates=Certificates
changeDropDownListSettings=Change settings of drop-down lists
compressionRatio=Compression ratio of images in attributes "Text in RTF format"
connectionParameters=Connection parameters
console=Console
consoleDescription=To run scripts in the system interface and to view the application log
contentTemplates=Content templates
contentTemplatesDescription=Used for work with content templates that intend for quick setting of similar contents.
cti=CTI
ctiDescription=To configure connection to IP telephony server
currentLog=Current system log
currentPeriodLog=Current period system logs
currentPeriodLogShort=Period system logs
customJSCatalog=Customization files catalog
dateTimeAction=Date
defaultFormPresentation=Inline view of the add comment form by default
description=Description
differenceInSettings=Differences
downloadHasNoExportObjectError={0} cannot be downloaded. No {1} in the system.
downloadHasNoExportObjectSingularError={0} cannot be downloaded. No {1} in the system.
downloadLicense=Download licenses
downloadMetainfo=Download metainformation
downloadReportTemplates=Download report templates
downloadStatistics=Download statistics
downloadSystemInfo=Download system information
dropDownLists=Drop-down lists
editCommonMentionSettings=Editing general mention settings
editForm=Edit form
editForms=Edit forms
editingCommentsSettings=Editing comment settings
enableRightsWarning=Enabling control of permissions to the items of the search results list may lead to increased loading of server resources and reduce system performance. After turning on the feature to use the search you will need to configure the rights.
eventActionSettings=Event actions
eventActionSettingsDescription=To manage actions, executed because a certain event has occurred
eventActions=Event actions
eventStorageRuleControl=Event log management
fastLinkRightsEnabled=Use permissions check to the objects available for mention
fastLinks=Mentions
fastLinksDescription=To configure mentions in the ''RTF text'' attribute
formsSelectedInOneListNotAvailableInAnother=Forms selected in one list will not be available for selection in another list.
groupsAndRoles=User groups and roles
hasWorkflow=Workflow
highlightPrivateCommentInList=Highlight a comment in the list with a colored background
highlightPrivateCommentOnForm=Highlight the Text field on a form with a colored background
importMetainfoServerTimedOut=Server response timed out.Upload process of metainformation continues in the background.To avoid errors in the system do not load metainformation files before the end of the current upload process.
inputmaskExtensions=Input field with mask extensions
interfaceAndNavigation=Interface and navigation
interfaceDescription=To configure the user interface
interfaze=Interface
isAsynchronousCountObjectsInTab=Count objects asynchronously in tabs
isCompleteSetOfLicensesNotRequired=Login with an incomplete set of licenses
isUiCssTransitionsEnabled=Animation of interface elements
fullReloadMetainfo=Full replacement of settings
jmsQueues=Queues
jsFilesCatalog=Customization files catalog
jsFilesCatalogDescription=Used to customize application pages
libraryUploaded=Library ''{0}'' was successfully uploaded
licenseUploadComplete=License file is uploaded
licenses=Licenses
licensing=Licensing
listTemplates=List templates
listTemplatesDescription=Used for work with list templates that intend for quick setting of similar lists
logSize=Size
mail=Mail
mailDescription=To configure connection to incoming and outgoing mail servers, to configure incoming mail filtering and to access the mail log
mailLog=Incoming mail log
mailProcessingRules=Processing rules
mainInfo=Main information
maxOpenedBrowserTabsPerUser=Maximum number of simultaneously open tabs for a single user
maxSearchResults=Maximum number of objects in search results
mentions=Mentions
menuItemBusinessProcessSettings=Business process settings
menuItemCatalogs=Catalogs
menuItemClasses=Classes
menuItemFolders=Directories
menuItemFoldersDescription=To configure folders of business objects
menuItemGroups=Groups
menuItemGroupsAndRoles=User groups and roles
menuItemGroupsAndRolesDescription=To work with access permission subjects: roles and user groups
menuItemMailSetting=Mail settings
menuItemRoles=Roles
menuItemSecurity=Access permissions
menuItemSettings=System settings
menuItemSystemCatalogs=System catalogs
menuItemSystemClasses=Of the class
menuItemUserCatalogs=User catalogs
menuItemUserClasses=Of the class
metaclassAction=Metaclass
metainfoFile=Meta-information file
modificationDate=Modified date
modulesUploadCompleted=Modules are uploaded
monitoringPropertiesTitle=Requests registration
monitoringSystem=Monitoring system
monitoringSystemDescription=To configure interaction with the monitoring system
navigation=Navigation
needCompressImage=Compress images in attributes "Text in RTF format"
noAttributeWithTheCodeContent=There is no attribute ''{0}'' (''content'') in the selected group, without it will be impossible to add a file.
notificationLog=List notice in interface
off=Switch off
onForAllAttributesOnForms=Enabled for all attributes on forms
onForRequiredAttributesOnForms=Enabled only for required attributes on forms
onlineSupport=Live chat
otherAdminOptions=Other settings
ownObjectFiles=object
privateComments=Private comments
privateCommentColor=Background color
privateCommentColorDescription=To maintain the contrast of the text, we recommend choosing a color for the background with a lightness index L = 95% (HSL)
quotaCaseObjectCount=Objects were created within the type
quotaExpirationDate=Validity period
quotaMultipleCases=Limitation on set
quotaLimitedType=Limited type
quotaName=Name of quota
quotaObjectLimit=Maximum number of objects per quota
quotaRemainder=Available remainder
quotaSummary=Use of a quantitative license
relatedObjectFiles=related object
reportTemplatesAndPrintingForms=Report and printing form templates
responsibilityChangeForms=Responsibility change forms
schedulerTaskSettings=Task scheduler
schedulerTaskSettingsDescription=To manage tasks executed according to schedule
schedulerTasks=Scheduler tasks
scriptCatalog=Script catalog
scriptCatalogDescription=Used for script navigation
scriptModules=Module catalog
scriptModulesDescription=Used to configure script modules
scriptsAndModules=Script and module catalog
scriptsAndModulesDescription=For script navigation and to configure script modules
searchSettings=Search
searchSettingsDescription=To manage search settings
searchSettingsTitle=Common search settings
securitySettings=Access permissions settings
serviceCallParameters=Request parameters
serviceCallParametersDescription=To configure requests association options
settingsWillBeOverridden=If the action is confirmed, the settings that are not in the downloaded file, but are present in the current configuration <b>will be deleted without the possibility of recovery</b>.
sets=Sets
setsDescription=Used to manage set settings
sizeAfterArchiving=Approximate size after archiving
statusChangeForms=Status change forms
structuredObjectsViews=Structures
structuredObjectsViewsDescription=Used to form a hierarchical view of lists
startUploadMetainfo=Start uploading
styleTemplates=Style templates
styleTemplatesDescription=Used to configure templates of notification views
substituteSingleValue=Substitute a single value on forms
superuser=Superuser
synchronization=Synchronization
synchronizationDescription=To configure single data import to the system or periodical data synchronization with external source
systemControl=System management
systemLogs=System logs
systemMetaClass=System class
systemName=System name
tags=Tags
tagsDescription=To separate system settings by business processes
templates=Templates
templatesDescription=For work with style templates, list templates, report and printing form templates
timers=Timers
timersDescription=To fix time parameters of request processing in all workflow statuses.
transferApplications=Application files
transferExport=Download
transferExportBtn=Download
transferHeader=Download / Upload
transferImport=Upload
transferImportBtn=Upload
transferLicense=Licenses
transferMetainfo=Metainformation
transferReports=Report and printing form templates
transferScripts=Modules
transferStatistic=Statistics
transferSystemInfo=System information
uploadComplete=Metainformation is uploaded
uploadCompleteWithProblems=Metainformation uploaded with problems (v. system log).
uploadingSettings=Uploading meta-information
uploadLicenses=Upload licenses
uploadMetainfo=Upload metainformation
uploadModules=Upload modules
userEAParametersForms=User event action parameter forms
useRightsInLists=Use permissions check to the items of the search results list
userInterface=Go to the view
wfProfiles=Profiles of related workflows
wfProfilesDescription=To configure rules of copying attributes values from a master object to a child object, rules of copying comments and unlinking objects in a certain workflow status.
enableFastLinkRightsWarning=Enabling control of permissions to the items of the mentions list may lead to increased loading of server resources and reduce system performance. After turning on the feature to use the mentions you will need to configure the rights.