accessToFullReloadMetainfo=Доступ на полное замещение настроек при загрузке метаинформации
action=Действие
addCommentInContent=Добавление комментария в контенте
addCommentInlineForm=Inline добавление комментария
addForms=Формы добавления
additionalLibraries=Дополнительные библиотеки
adminLog=Лог действий технолога
adminLogDescription=Используется для работы с историей изменений настроек
adminLogRecordTitle=Запись лога действий технолога от
adminProfiles=Профили администрирования
adminProfilesDescription=Используется для управления профилями администрирования
administration=Администрирование
administrationDescription=Используется для работы администратора системы с метаинформацией, файлами лицензий и сеансами доступа
administrationLiteDescription=Используется для работы администратора системы с файлами лицензий и сеансами доступа
allScripts=Все скрипты
analizers=Типы анализаторов, доступные в системе
applicationLog=Логи приложения
applications=Приложения
applicationsDescription=Используется для настройки встроенных приложений
areYouReallyWantUploadMetaInfo=<b>Внимание!</b> Вы действительно хотите загрузить метаинформацию в режиме полного замещения настроек?
attentionAddCommentInlineFormTrue=Скрипты с формы добавления комментария будут выполнены при загрузке контента "Комментарии".
attribute=атрибуту ''{0}''
authorAction=Автор действия
byCategories=По категориям
byClasses=По классам
categoryAction=Категория действия
certificateErrorLoading=Ошибка загрузки сертификата.
certificateUploadedSuccessfully=Сертификат успешно загружен.
certificates=Сертификаты
changeDropDownListSettings=Изменение параметров выпадающих списков
compressionRatio=Коэффициент сжатия изображений в атрибутах "Текст в формате RTF"
connectionParameters=Параметры подключения
console=Консоль
consoleDescription=Используется для выполнения скриптов через интерфейс системы, а также для просмотра лога приложения
contentTemplates=Шаблоны контентов
contentTemplatesDescription=Используется для работы с шаблонами контентов, предназначенными для быстрой настройки однотипных контентов.
cti=CTI
ctiDescription=Используется для настройки подключения к серверу IP-телефонии
currentLog=Текущий лог приложения
currentPeriodLog=Текущий набор коротких логов приложения
currentPeriodLogShort=Набор коротких логов
customJSCatalog=Каталог файлов кастомизации
dateTimeAction=Дата действия
defaultFormPresentation=Вид inline формы добавления комментария по умолчанию
description=Описание
differenceInSettings=Изменения в настройке
downloadHasNoExportObjectError={0} не могут быть выгружены. В системе отсутствуют {1}.
downloadHasNoExportObjectSingularError={0} не может быть выгружен. В системе отсутствует {1}.
downloadLicense=Выгрузить лицензии
downloadMetainfo=Выгрузить метаинформацию
downloadReportTemplates=Выгрузить шаблоны отчетов
downloadStatistics=Выгрузить статистику
downloadSystemInfo=Выгрузить информацию о системе
dropDownLists=Выпадающие списки выбора
editCommonMentionSettings=Редактирование общих настроек упоминаний
editForm=Форма редактирования
editForms=Формы редактирования
editingCommentsSettings=Редактирование настроек комментариев
enableRightsWarning=Включение механизма контроля прав пользователей на объекты при поиске может привести к повышенной загрузке ресурсов сервера и понизить быстродействие системы. После включения признака для использования поиска необходимо будет произвести настройку прав.
eventActionSettings=Действия по событиям
eventActionSettingsDescription=Используется для управления задачами, выполнение которых обусловлено наступлением определенного события
eventActions=Действия по событиям
eventStorageRuleControl=Управление логом событий
fastLinkRightsEnabled=Использовать механизм контроля прав пользователей на объекты, доступные для упоминания
fastLinks=Упоминание объектов
fastLinksDescription=Используется для настройки упоминаний объектов в атрибуте ''текст RTF''
formsSelectedInOneListNotAvailableInAnother=Формы, выбранные в одном списке, будут недоступны для выбора в другом.
fullReloadMetainfo=Полное замещение настроек
groupsAndRoles=Группы пользователей и роли
hasWorkflow=Жизненный цикл
highlightPrivateCommentInList=Выделить цветным фоном комментарий в списке
highlightPrivateCommentOnForm=Выделить цветным фоном поле Текст на форме
importMetainfoServerTimedOut=Превышено время ожидания ответа от сервера. Загрузка метаинформации продолжается в фоновом режиме. Во избежание ошибок в работе системы не загружайте файлы метаинформации до окончания текущего процесса загрузки.
inputmaskExtensions=Расширения настроек поля ввода с маской
interfaceAndNavigation=Интерфейс и навигация
interfaceDescription=Используется для настройки интерфейса оператора
interfaze=Интерфейс
isAsynchronousCountObjectsInTab=Асинхронный подсчет объектов на вкладках
isCompleteSetOfLicensesNotRequired=Вход в систему с неполным набором лицензий
isUiCssTransitionsEnabled=Анимация элементов интерфейса
jmsQueues=Очереди
jsFilesCatalog=Каталог файлов кастомизации
jsFilesCatalogDescription=Используется для дополнительных настроек страниц приложения
libraryUploaded=Библиотека ''{0}'' была успешно загружена
licenseUploadComplete=Лицензионный файл загружен
licenses=Лицензии
licensing=Лицензирование
listTemplates=Шаблоны списков
listTemplatesDescription=Используется для работы с шаблонами списков, предназначенными для быстрой настройки однотипных списков
logSize=Размер
mail=Почта
mailDescription=Используется для настройки подключения к серверам входящей и исходящей почты, настройки фильтрации входящей почты, доступа к логу почты
mailLog=Лог входящей почты
mailProcessingRules=Правила обработки
mainInfo=Основная информация
maxOpenedBrowserTabsPerUser=Максимальное количество одновременно открытых вкладок для одного пользователя
maxSearchResults=Максимальное количество объектов в результатах поиска
mentions=Упоминание объектов
menuItemBusinessProcessSettings=Настройка бизнес-процессов
menuItemCatalogs=Справочники
menuItemClasses=Классы
menuItemFolders=Каталоги
menuItemFoldersDescription=Используется для настройки папок бизнес объектов
menuItemGroups=Группы
menuItemGroupsAndRoles=Группы пользователей и роли
menuItemGroupsAndRolesDescription=Используется для работы с группами субъектов прав доступа: ролями и группами пользователей
menuItemMailSetting=Настройка почты
menuItemRoles=Роли
menuItemSecurity=Права доступа
menuItemSettings=Настройка системы
menuItemSystemCatalogs=Системные справочники
menuItemSystemClasses=Класса
menuItemUserCatalogs=Пользовательские справочники
menuItemUserClasses=Класса
metaclassAction=Метакласс
metainfoFile=Файл метаинформации
modificationDate=Дата изменения
modulesUploadCompleted=Модули загружены
monitoringPropertiesTitle=Регистрация запросов
monitoringSystem=Система мониторинга
monitoringSystemDescription=Используется для настройки взаимодействия с системой мониторинга
navigation=Навигация
needCompressImage=Сжатие изображений в атрибутах "Текст в формате RTF"
noAttributeWithTheCodeContent=В выбранной группе нет атрибута ''{0}'' (''content''), без него добавление файла будет невозможно.
notificationLog=Лог уведомлений
off=Выключено
onForAllAttributesOnForms=Включено для всех атрибутов на формах
onForRequiredAttributesOnForms=Включено только для обязательных атрибутов на формах
onlineSupport=Онлайн поддержка
otherAdminOptions=Прочие настройки
ownObjectFiles=объекту
privateComments=Приватные комментарии
privateCommentColor=Цвет фона
privateCommentColorDescription=Для сохранения контрастности текста рекомендуем выбирать для фона цвет с показателем светлоты L= 95% (HSL)
quotaCaseObjectCount=Создано объектов в рамках типа
quotaExpirationDate=Срок действия
quotaMultipleCases=Ограничение на набор
quotaLimitedType=Ограничиваемый тип
quotaName=Название квоты
quotaObjectLimit=Максимальное количество объектов на квоту
quotaRemainder=Доступный остаток
quotaSummary=Использование количественной лицензии
relatedObjectFiles=связанному объекту
reportTemplatesAndPrintingForms=Шаблоны отчетов и печатных форм
responsibilityChangeForms=Формы смены ответственного
schedulerTaskSettings=Планировщик задач
schedulerTaskSettingsDescription=Используется для управления задачами, выполняемыми по графику
schedulerTasks=Задачи планировщика
scriptCatalog=Каталог скриптов
scriptCatalogDescription=Используется для навигации по скриптам
scriptModules=Каталог модулей
scriptModulesDescription=Используется для настройки скриптовых модулей
scriptsAndModules=Каталог скриптов и модулей
scriptsAndModulesDescription=Используется для навигации по скриптам и настройки скриптовых модулей
searchSettings=Поиск
searchSettingsDescription=Используется для настройки параметров поиска
searchSettingsTitle=Общие настройки поиска
securitySettings=Настройка прав доступа
serviceCallParameters=Параметры запросов
serviceCallParametersDescription=Используется для настройки возможностей привязки запросов
settingsWillBeOverridden=В случае подтверждения действия настройки, которые отсутствуют в загружаемом файле, но присутствуют в текущей конфигурации <b>будут удалены без возможности восстановления</b>.
sets=Комплекты
setsDescription=Используется для управления комплектами настроек
sizeAfterArchiving=Примерный размер после архивации
statusChangeForms=Формы смены статуса
structuredObjectsViews=Структуры
structuredObjectsViewsDescription=Используется для формирования иерархического представления списков
startUploadMetainfo=Начать загрузку
styleTemplates=Шаблоны стилей
styleTemplatesDescription=Используется для настройки шаблонов внешнего вида оповещений и уведомлений в интерфейсе
substituteSingleValue=Подставлять единственное значение на формах
superuser=Суперпользователь
synchronization=Синхронизация
synchronizationDescription=Используется для настройки конфигураций однократного импорта данных в систему или их периодической синхронизации с внешним источником
systemControl=Управление системой
systemLogs=Логи приложения
systemMetaClass=Системный
systemName=Название системы
tags=Метки
tagsDescription=Используется для разделения настроек системы по бизнес-процессам
templates=Шаблоны
templatesDescription=Используется для работы с шаблонами стилей, списков, отчетов и печатных форм
timers=Счетчики времени
timersDescription=Используются для фиксации временных показателей обработки запросов на всех стадиях ЖЦ запроса.
transferApplications=Файлы приложений
transferExport=Выгрузка
transferExportBtn=Выгрузить
transferHeader=Выгрузка / загрузка
transferImport=Загрузка
transferImportBtn=Загрузить
transferLicense=Лицензии
transferMetainfo=Метаинформация
transferReports=Шаблоны отчетов и печатных форм
transferScripts=Модули
transferStatistic=Статистика
transferSystemInfo=Информация о системе
uploadComplete=Метаинформация загружена
uploadCompleteWithProblems=Метаинформация загружена с замечаниями (см. лог приложения).
uploadingSettings=Загрузка метаинформации
uploadLicenses=Загрузить лицензии
uploadMetainfo=Загрузить метаинформацию
uploadModules=Загрузить модули
userEAParametersForms=Формы параметров ПДПС
useRightsInLists=Использовать механизм контроля прав пользователей на объекты, выводимые в список результатов поиска
userInterface=Перейти в отображение
wfProfiles=Профили связанных жизненных циклов
wfProfilesDescription=Используются для настройки для установления правил копирования значений атрибутов из ведущего объекта в ведомый, правил копирования комментариев и правил разрыва связей между объектами при входе в определенный статус жизненного цикла.
enableFastLinkRightsWarning=Включение механизма контроля прав пользователей на объекты, доступные для упоминания может привести к повышенной загрузке ресурсов сервера и понизить быстродействие системы. После включения признака необходимо будет произвести настройку прав.
