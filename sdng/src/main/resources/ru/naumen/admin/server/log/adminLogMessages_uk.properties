metaClassLog.actionType=Зміна налаштувань метакласів
adminLog.categories.metaClassAdd=Створення метакласу
adminLog.categories.metaClassAdd.description=Створено метаклас ''{0}'' ({1}).
adminLog.categories.metaClassEdit=Зміна метакласу
adminLog.categories.metaClassEdit.description=Змінено метаклас ''{0}'' ({1}):\n{2}
adminLog.categories.metaClassCopy=Копіювання метакласу
adminLog.categories.metaClassCopy.description=Створено копію метакласу ''{0}'' ({1}). Новий метаклас: ''{2}'' ({3}).
adminLog.categories.metaClassDelete=Видалення метакласу
adminLog.categories.metaClassDelete.description=Видалено метаклас ''{0}'' ({1}).
metaClass.title=Назва
metaClass.description=Опис
metaClass.removed=Архівний
metaClass.defaultService=Послуга за замовчуванням
metaClass.defaultAgreement=Угода за замовчуванням
metaClass.defaultCase=Тип запиту за замовчуванням
metaClass.objectAreNestedInto=Об''єкти вкладені в
metaClass.tags=Мітки
metaClass.allowPlannedVersion=Дозволити створення планових версій
metaClass.depthEnvironment=Глибина рівнів середовища
attribute.decimalsCountRestriction=Обмеження на введення і відображення десяткових знаків
attribute.isHasGroupSeparator=Розділяти по розрядам
adminLog.categories.attributeAdd=Створення атрибута
adminLog.categories.attributeAdd.description=В метакласі ''{0}'' ({1}) створено атрибут ''{2}'' ({3}).
adminLog.categories.attributeDelete=Видалення атрибута
adminLog.categories.attributeDelete.description=В метакласі ''{0}'' ({1}) видалено атрибут ''{2}'' ({3}).
adminLog.categories.attributeEdit=Зміна атрибута
adminLog.categories.attributeEdit.description=В метакласі ''{0}'' ({1}) змінено атрибут ''{2}'' ({3}):\n{4}.
attribute.title=Назва
attribute.isEditable=Ознака редагування
attribute.isEditableInLists=Ознака редагування в списках
attribute.isHiddenAttrCaption=Приховувати назву атрибута
attribute.isRequired=Ознака обов''язковості
attribute.isRequiredInInterface=Ознака обов''язковості для заповнення в інтерфейсі
attribute.isUnique=Ознака унікальності
attribute.isDeterminable=Ознака визначення по таблиці відповідності
attribute.determiner=Правило визначення(код таблиці відповідності)
attribute.isComputable=Ознака обчислюваності
attribute.script=Скрипт значення атрибута
attribute.isFilteredByScript=Ознака фільтрації значень атрибута
attribute.scriptForFiltration=Скрипт фильтрації значень атрибута
attribute.viewPresentationCode=Подання для відображення
attribute.editPresentationCode=Подання для редагування
attribute.isDefaultByScript=Ознака значення за замовчуванням
attribute.defaultValue=Значення за замовчуванням
attribute.scriptForDefault=Скрипт значення за замовчуванням
attribute.generationRule=Правило генерації
attribute.permittedTypeFqn=Допустимі типи
attribute.isComplexRelation=Складна форма додавання зв''язків
attribute.complexStructuredObjectsViewCode=Структура
attribute.complexRelationAttrGroup=Група атрибутів в списку
attribute.complexRelationOuAttrGroup=Група атрибутів в списку для класу Відділ
attribute.complexRelationTeamAttrGroup=Група атрибутів в списку для класу Команда
attribute.complexRelationEmployeeAttrGroup=Група атрибутів в списку для класу Співробітник
attribute.isEditOnComplexFormOnly=Редагування тільки через розширену форму
attribute.exportNDAP=Доступний із системи моніторингу
attribute.relatedAttrsToExport=Атрибути, доступні із системи моніторингу
attribute.isNeedStoreUnits=Запам''ятовувати вибрані одиниці виміру
attribute.intervalAvailableUnits=Одиниці виміру, доступні при редагуванні
attribute.hiddenWhenEmpty=Ховати при відображенні, якщо не заповнено
attribute.hiddenWhenNoPossibleValues=Ховати при редагуванні, якщо немає значень для вибору
attribute.quickAddForm=Форма швидкого додавання
attribute.quickEditForm=Форма швидкого редагування
attribute.relatedObjectHierarchyLevel=Показувати значення атрибута
attribute.tags=Мітки
attribute.dateTimeCommonRestrictions=Значення атрибута допустимо вказувати
attribute.dateTimeCommonRestrictions.PAST=в минулому
attribute.dateTimeCommonRestrictions.FUTURE=в майбутньому
attribute.dateTimeRestrictionAttribute=Атрибут
attribute.dateTimeRestrictionCondition=Умова
attribute.dateTimeRestrictionCondition.GTE=більше або дорівнює
attribute.dateTimeRestrictionCondition.GT=більше
attribute.dateTimeRestrictionCondition.LTE=менше або дорівнює
attribute.dateTimeRestrictionCondition.LT=менше
attribute.dateTimeRestrictionScript=Скрипт обмеження значення атрибута
attribute.dateTimeRestrictionType=Додаткове обмеження на введення дати
attribute.dateTimeRestrictionType.NO_RESTRICTION=Без обмежень
attribute.dateTimeRestrictionType.RESTRICTION_BY_SCRIPT=Обмеження скриптом
attribute.dateTimeRestrictionType.ATTRIBUTE_RESTRICTION=Задати залежність від атрибута
attribute.hideArchived=Ховати архівні об''єкти
adminLog.categories.attributeGroupEdit=Зміна групи атрибутів
adminLog.categories.attributeGroupEdit.add.description=В метакласі ''{0}'' ({1}) створена група атрибутів ''{2}'' ({3}).
adminLog.categories.attributeGroupEdit.delete.description=В метакласі ''{0}'' ({1}) видалена група атрибутів ''{2}'' ({3}).
adminLog.categories.attributeGroupEdit.edit.description=В метакласі ''{0}'' ({1}) змінена група атрибутів ''{2}'' ({3}):\n{4}.
attributeGroup.title=Назва
attributeGroup.attributes=Список атрибутів
attributeGroup.attributesOrder=Порядок атрибутів
adminLog.categories.windowEdit=Зміна інтерфейсу картки об''єктів
adminLog.categories.windowEdit.description=В метакласі ''{0}'' ({1}) змінено інтерфейс картки об''єктів.
adminLog.categories.newEntryFormEdit=Зміна інтерфейсу форми додавання об''єктів метакласу
adminLog.categories.newEntryFormEdit.description=В метакласі ''{0}'' ({1}) змінено інтерфейс форми додавання об''єктів.
adminLog.categories.editFormEdit=Зміна інтерфейсу форми редагування об''єктів метакласу
adminLog.categories.editFormEdit.description=В метакласі ''{0}'' ({1}) змінено інтерфейс форми редагування об''єктів.
adminLog.categories.changeCaseFormEdit=(арх.)Зміна інтерфейсу форми зміни типа об''єктів метакласу
adminLog.categories.changeCaseFormEdit.description=В метакласі ''{0}'' ({1}) змінено інтерфейс форми зміни типа об''єктів.
#changeUserFormSettings
adminLog.categories.changeUserFormsSettings=Налаштування користувацьких форм
adminLog.categories.changeUserFormsSettings.description=В метакласі ''{0}'' ({1}) змінено користувацький інтерфейс форми.
adminLog.categories.changeUserFormsSettings.formCreated.description=В метакласі ''{0}'' ({1}) створена користувацька {2} ({3}).
adminLog.categories.changeUserFormsSettings.formEdited.description=В метакласі ''{0}'' ({1}) змінена користувацька {2} ({3}): {4}
adminLog.categories.changeUserFormsSettings.formDeleted.description=В метакласі ''{0}'' ({1}) видалена користувацька{2} ({3}).
adminLog.forTypes=Для типів
adminLog.forTransitionsInTypes=Для переходів в типи
adminLog.attributeGroup=Група атрибутів
adminLog.commentOnForm=Коментар на формі
adminLog.attributesDescription=Показувати опис атрибутів
adminLog.useAsDefault=Використовувати як форму за замовчуванням
adminLog.notFill=Не заповнювати
adminLog.fill=Заповнювати
adminLog.mustFill=Обов''язково заповнювати
adminLog.yes=Так
adminLog.no=Ні
ChangeCaseForm=форма зміни типу
ChangeResponsibleForm=форма зміни відповідального
QuickForm=форма швидкого додавання і редагування
MassEditForm=форма масового редагування
adminLog.lists.notSpecified=[не вказано]
adminLog.categories.preStateActionAdd=Зміна налаштувань статуса
adminLog.categories.postStateActionAdd=Зміна налаштувань статуса
adminLog.categories.preStateActionAdd.description=У метакласі ''{3}'' ({4}) додано дію ''{0}'' при вході в статус ''{1}'' ({2}).
adminLog.categories.postStateActionAdd.description=У метакласі ''{3}'' ({4}) додано дію ''{0}'' при виході зі статусу ''{1}'' ({2}).
adminLog.categories.preStateActionDelete=Зміна налаштувань статусу
adminLog.categories.postStateActionDelete=Зміна налаштувань статусу
adminLog.categories.preStateActionDelete.description=У метакласі ''{3}'' ({4}) видалено дію ''{0}'' при вході до статусу ''{1}'' ({2}).
adminLog.categories.postStateActionDelete.description=У метакласі ''{3}'' ({4}) видалено дію ''{0}'' при виході зі статусу ''{1}'' ({2}).
adminLog.categories.preStateActionEdit=Зміна налаштувань статусу
adminLog.categories.postStateActionEdit=Зміна налаштувань статусу
adminLog.categories.preStateActionEdit.description=У метакласі ''{3}'' ({4}) змінено дію ''{0}'' при вході в статус ''{1}'' ({2}).
adminLog.categories.postStateActionEdit.description=У метакласі ''{3}'' ({4}) змінено дію ''{0}'' при виході зі статусу ''{1}'' ({2}).
adminLog.categories.preStateConditionAdd=Зміна налаштувань статусу
adminLog.categories.postStateConditionAdd=Зміна налаштувань статусу
adminLog.categories.preStateConditionAdd.description=У метакласі "{3}" ({4}) додано умову "{0}" при вході в статус "{1}" ({2}).
adminLog.categories.postStateConditionAdd.description=У метакласі ''{3}'' ({4}) додано умову ''{0}'' при виході зі статусу ''{1}'' ({2}).
adminLog.categories.preStateConditionDelete=Зміна налаштувань статусу
adminLog.categories.postStateConditionDelete=Зміна налаштувань статусу
adminLog.categories.preStateConditionDelete.description=У метакласі ''{3}'' ({4}) видалено умову ''{0}'' при вході в статус ''{1}'' ({2}).
adminLog.categories.postStateConditionDelete.description=У метакласі ''{3}'' ({4}) видалено умову ''{0}'' при виході зі статусу ''{1}'' ({2}).
adminLog.categories.preStateConditionEdit=Зміна налаштувань статусу
adminLog.categories.postStateConditionEdit=Зміна налаштувань статусу
adminLog.categories.preStateConditionEdit.description=У метакласі "{3}" ({4}) змінено умову "{0}" при вході в статус "{1}" ({2}).
adminLog.categories.postStateConditionEdit.description=У метакласі ''{3}'' ({4}) змінено умову ''{0}'' при виході зі статусу ''{1}'' ({2}).
adminLog.categories.updateAccessMatrix=Зміна прав доступу до об''єктів
adminLog.categories.updateAccessMatrix.description=У метакласі ''{0}'' ({1}) змінено налаштування прав.
adminLog.categories.workflowEdit=Зміна життєвого циклу об''єктів
adminLog.categories.workflowEdit.description=У метакласі "{0}" ({1}) змінено життєвий цикл об''єктів.
adminLog.categories.responsibleTransferEdit=Зміна налаштування передачі відповідальності за об''єкти метакласу
adminLog.categories.responsibleTransferEdit.description=У метакласі ''{0}'' ({1}) змінено налаштування налаштувань матриці передачі відповідальності.
adminLog.categories.editAttributeSearch=Зміна правил пошуку об''єктів
adminLog.categories.editAttributeSearch.description=У метакласі ''{0}'' ({1}) змінено правило пошуку об''єктів за атрибутом ''{2}'' ({3}).
adminLog.categories.reindex=Переіндексація об''єктів метакласу
adminLog.categories.reindex.description=У метакласі "{0}" ({1}) запущена переіндексація.
catalogLog.actionType=Зміна налаштувань довідників
adminLog.categories.catalogReindex=Переіндексація елементів довідника
adminLog.categories.catalogReindex.description=У довіднику ''{0}'' ({1}) запущено переіндексацію.
adminLog.categories.catalogCreate=Створення довідника
adminLog.categories.catalogCreate.description=Створено довідник "{0}" ({1}).
adminLog.categories.catalogDelete=Видалення довідника
adminLog.categories.catalogDelete.description=Видалено довідник ''{0}'' ({1}).
adminLog.categories.catalogEdit=Зміна довідника
adminLog.categories.catalogEdit.description=Змінено довідник ''{0}'' ({1}):\n{2}.
adminLog.categories.catalogItemEdit=Зміна елементів довідника
adminLog.categories.catalogItemEdit.edit.description=Змінено елементи довідника "{0}" ({1}): змінено елемент "{2}" ({3}).
adminLog.categories.catalogItemEdit.add.description=Змінено елементи довідника "{0}" ({1}): додано елемент "{2}" ({3}).
adminLog.categories.catalogItemEdit.delete.description=Змінено елементи довідника "{0}" ({1}): видалено елемент "{2}" ({3}).
adminLog.categories.directorySettings=Зміна налаштувань каталогів
adminLog.categories.directorySettings.edit.description=У каталозі ''{0}'' ({1}) змінено папку ''{2}'' ({3}):\n{4}.
adminLog.categories.directorySettings.add.description=У каталозі ''{0}'' ({1}) додано папку ''{2}'' ({3}).
adminLog.categories.directorySettings.delete.description=У каталозі ''{0}'' ({1}) видалено папку ''{2}'' ({3}).
catalog.title=Назва
catalog.description=Опис
catalog.parent=Батьки
catalog.removalDate=Дата перенесення до архіву
catalog.removed=Архівний
businessProcessLog.actionType=Зміна налаштувань бізнес-процесів
adminLog.categories.secGroupAndRoles=Зміна налаштувань груп користувачів та ролей
adminLog.categories.secGroupAndRoles.groupAdd.description=Додано групу користувачів ''{0}'' ({1}).
adminLog.categories.secGroupAndRoles.groupEdit.description=Змінено групу користувачів ''{0}'' ({1}):\n{2}.
adminLog.categories.secGroupAndRoles.groupDelete.description=Видалено групу користувачів "{0}" ({1}).
adminLog.categories.secGroupAndRoles.roleAdd.description=Додано роль ''{0}'' ({1}).
adminLog.categories.secGroupAndRoles.roleEdit.description=Змінено роль ''{0}'' ({1}):\n{2}.
adminLog.categories.secGroupAndRoles.roleDelete.description=Видалено роль ''{0}'' ({1}).
adminLog.categories.secProfileEdit=Зміна профілів прав доступу
adminLog.categories.secProfileEdit.profileAdd.description=Додано профіль прав доступу ''{0}'' ({1}).
adminLog.categories.secProfileEdit.profileEdit.description=Змінено профіль прав доступу ''{0}'' ({1}):\n{2}.
adminLog.categories.secProfileEdit.profileDelete.description=Видалено профіль прав доступу "{0}" ({1}).
adminLog.categories.secPlanningModeProfileEdit=Зміна профілів прав доступу до режиму планування
adminLog.categories.secPlanningModeProfileEdit.profileAdd.description=Додано профіль прав доступу до режиму планування "{0}" ({1}).
adminLog.categories.secPlanningModeProfileEdit.profileEdit.description=Змінено профіль прав доступу до режиму планування "{0}" ({1}):\n{2}.
adminLog.categories.secPlanningModeProfileEdit.profileDelete.description=Видалено профіль прав доступу до режиму планування "{0}" ({1}).
securityGroup.title=Назва
securityGroup.ous=Відділи
securityGroup.teams=Команди
securityGroup.employees=Співробітники
securityGroup.tags=Мітки
securityRole.title=Назва
securityRole.hasScriptedAccess=Визначити права доступу користувача до об''єкту
securityRole.accessScript=Скрипт визначення прав доступу
securityRole.hasScriptedOwners=Визначити список користувачів, які мають роль
securityRole.ownersScript=Скрипт визначення списку користувачів
securityProfile.title=Назва
securityProfile.roles=Ролі користувачів
securityProfile.groups=Групи користувачів
adminLog.categories.scParametersEdit=Зміна параметрів запиту
adminLog.categories.scParametersEdit.description=Змінено параметри запиту:\n{0}.
scParameters.orderSetting=Вибирати спочатку
scParameters.agsSetting=Угода/Послуга
scParameters.agsEditPrs=Подання для редагування
scParameters.isFilterAgreements=Фільтрування угод під час редагування
scParameters.agreementsFiltrationScript=Скрипт фільтрації угод під час редагування
scParameters.isFilterServices=Фільтрування послуг під час редагування
scParameters.servicesFiltrationScript=Скрипт фільтрації послуг під час редагування
scParameters.isFilterCases=Фільтрування типів під час редагування
scParameters.casesFiltrationScript=Скрипт фільтрації типів під час редагування
adminLog.categories.wfProfileSettings=Зміна налаштувань профілю пов''язаних життєвих циклів
adminLog.categories.wfProfileSettings.wfProfileAdd.description=Доданий профіль пов''язаних життєвих циклів "{0}".
adminLog.categories.wfProfileSettings.wfProfileEdit.description=Змінено профіль пов''язаних життєвих циклів ''{0}'':\n{1}.
adminLog.categories.wfProfileSettings.wfProfileDelete.description=Видалено профіль пов''язаних життєвих циклів ''{0}''.
wfProfile.title=Назва
wfProfile.state=Статус для розиву зв''язку
wfProfile.description=Опис
wfProfile.enable=Ввімкнено
wfProfile.masterClass=Ведучий
wfProfile.slaveClass=Ведений
wfProfile.inheritanceAttribute=Атрибути для успадкування значень з ведучого до веденого
adminLog.categories.timerDefinitionSettings=Зміна налаштувань лічильників часу
adminLog.categories.timerDefinitionSettings.timerAdd.description=Доданий лічильник "{0}" ({1}).
adminLog.categories.timerDefinitionSettings.timerEdit.description=Змінено параметри лічильника "{0}" ({1}):\n{2}.
adminLog.categories.timerDefinitionSettings.timerDelete.description=Вилучено лічильник "{0}" ({1}).
timerProperties.title=Назва
timerProperties.description=Опис
timerProperties.timeZone=Часовий пояс
timerProperties.serviceTime=Клас обслуговування
timerProperties.removed=Архівний
timerProperties.enableResumingOnResolutionTimeChange=Дозволити відновлення лічильника зі статусу “Скінчився запас часу” при зміні значення проміжку часу в об''єкті
timerProperties.enableRecalcOnServiceTimeChange=Дозволити перерахунок тимчасових характеристик при зміні класу обслуговування об''єкта
timerProperties.pauseCondition=Умова призупинення відліку
timerProperties.resumeCondition=Умови поновлення відліку
timerProperties.stopCondition=Умови закінчення відліку
timerProperties.startCondition=Умова початку відліку
timerProperties.statusSelected=Враховувати час у статусах
timerProperties.statusStopStates=Зупиняти лічильник у статусах
adminLog.categories.eventActionSettings=Зміна налаштувань дій щодо події
adminLog.categories.eventActionSettings.eventActionAdd.description=Додано дію за подією ''{0}'' (''{1}'') типу ''{2}''.
adminLog.categories.eventActionSettings.eventActionEdit.description=Змінено дію за подією ''{0}'' (''{1}'') типу ''{2}'':\n{3}.
adminLog.categories.eventActionSettings.eventActionDelete.description=Видалено дію за подією ''{0}'' (''{1}'') типу ''{2}''.
eventAction.title=Назва
eventAction.objects=Об''єкти
eventAction.eventType=Подія
eventAction.actionType=Дія
eventAction.isOn=Ввімкнено
eventAction.template=Шаблон
eventAction.subject=Тема
eventAction.who=Кому
eventAction.message=Текст сповіщення
eventAction.isHtml=У форматі HTML
eventAction.notificationScript=Скрипт
eventAction.actionScript=Скрипт
eventAction.pushMessage=Текст повідомлення
eventAction.isSync=Виконувати синхронно
eventAction.isSlow=Взаємодія із зовнішньою системою
eventAction.pushPosition=Розташування на екрані (для повідомлень в інтерфейсі)
eventAction.pushPresentationType=Спосіб відображення повідомлення
eventAction.pushHeaderFormat=Формат заголовку повідомлення
eventAction.tags=Мітки
adminLog.categories.eventActionSettings.eventActionConditionAdd.description=Додано умову ''{2}''для події ''{0}'' (''{1}'').
adminLog.categories.eventActionSettings.eventActionConditionEdit.description=Змінено умову ''{2}'' дії за подією ''{0}'' (''{1}''):\n{3}.
adminLog.categories.eventActionSettings.eventActionConditionDelete.description=Видалено умову ''{2}'' щодо події ''{0}'' (''{1}'').
eventActionCondition.title=Назва
eventActionCondition.script=Скрипт
eventActionCondition.syncVerification=Виконувати перевірку синхронно
adminLog.categories.eventActionSettings.userEventParameterAdd.description=Додано параметр ''{2}'' щодо події ''{0}'' (''{1}'').
adminLog.categories.eventActionSettings.userEventParameterEdit.description=Змінено параметр ''{2}'' щодо події ''{0}'' (''{1}'').
adminLog.categories.eventActionSettings.userEventParameterDelete.description=Видалено параметр ''{2}'' щодо події ''{0}'' (''{1}'').
escalationLog.actionType=Зміна налаштувань ескалації
valueMapLog.actionType=Зміна налаштувань таблиць відповідностей
adminLog.categories.escalationSchemeSettings=Зміна налаштувань схем ескалації
adminLog.categories.escalationSchemeSettings.schemeAdd.description=Додана схема ескалації "{0}" ({1}).
adminLog.categories.escalationSchemeSettings.schemeEdit.description=Змінено схему ескалації ''{0}'' ({1}):\n{2}.
adminLog.categories.escalationSchemeSettings.schemeDelete.description=Видалено схему ескалації ''{0}'' ({1}).
adminLog.categories.escalationSchemeSettings.levelAdd.description=У схемі ескалації "{0}" ({1}) доданий рівень ескалації №{2}.
adminLog.categories.escalationSchemeSettings.levelEdit.description=У схемі ескалації ''{0}'' ({1}) змінено рівень ескалації №{2}.
adminLog.categories.escalationSchemeSettings.levelDelete.description=У схемі ескалації "{0}" ({1}) видалено рівень ескалації №{2}.
adminLog.categories.escalationActionSettings=Зміна настройок дій ескалації
adminLog.categories.escalationActionSettings.eventActionAdd.description=Додано дію за подією ''{0}'' (''{1}'') типу ''{2}''.
adminLog.categories.escalationActionSettings.eventActionEdit.description=Змінено дію за подією ''{0}'' (''{1}'') типу ''{2}'':\n{3}.
adminLog.categories.escalationActionSettings.eventActionDelete.description=Видалено дію за подією ''{0}'' (''{1}'') типу ''{2}''.
adminLog.categories.escalationTableSettings=Зміна налаштувань таблиці відповідності ескалації
adminLog.categories.escalationTableSettings.edit.description=Змінено елемент таблиці відповідностей ''{0}'' ({1}):\n{2}.
adminLog.categories.escalationTableSettings.add.description=Доданий елемент таблиці відповідностей "{0}" ({1}).
adminLog.categories.escalationTableSettings.delete.description=Видалено елемент таблиці відповідностей ''{0}'' ({1}).
adminLog.categories.correspondenceTableSettings=Зміна налаштувань таблиць відповідностей
adminLog.categories.correspondenceTableSettings.edit.description=Змінено елемент таблиці відповідностей ''{0}'' ({1}):\n{2}.
adminLog.categories.correspondenceTableSettings.add.description=Доданий елемент таблиці відповідностей "{0}" ({1}).
adminLog.categories.correspondenceTableSettings.delete.description=Видалено елемент таблиці відповідностей ''{0}'' ({1}).
valueMap.title=Назва
valueMap.parent=parent
valueMap.defaultObject=Значення за замовчуванням
valueMap.description=Опис
valueMap.rowSet=Набір рядків
valueMap.removalDate=Дата архівування
valueMap.removed=Архівний
valueMap.linkedClass=Зв''язаний метаклас
valueMap.linkedClasses=Пов''язані метакласи
escalationScheme.title=Назва
escalationScheme.description=Опис
escalationScheme.targetTypes=Об''єкти
escalationScheme.timerCode=Лічильник часу
escalationScheme.isOn=Ввімкнено
schedulerLog.actionType=Зміна налаштувань системи
schedulerTask.title=Назва
schedulerTask.description=Опис
schedulerTask.script=Скрипт
schedulerTask.maxReceivedLetterInPeriod=Максимальна кількість листів, які отримуються за період
schedulerTask.maxProcessedLetterInPeriod=Максимальна кількість листів, що обробляються за період
schedulerTask.isSaveOriginalLetter=Зберігати у системі вихідний лист після обробки
schedulerTask.tags=Мітки
adminLog.categories.schedulerTasksSettings=Зміна завдань планувальника
adminLog.categories.schedulerTasksSettings.taskAdd.description=Додано завдання планувальника ''{0}'' (''{1}'') типу ''{2}''.
adminLog.categories.schedulerTasksSettings.taskEdit.description=Змінено завдання планувальника ''{0}'' (''{1}'') типу ''{2}'':\n{3}.
adminLog.categories.schedulerTasksSettings.taskDelete.description=Видалено завдання планувальника ''{0}'' (''{1}'') типу ''{2}''.
adminLog.categories.schedulerTasksSettings.triggerAdd.description=Додано розклад завдання планувальника ''{0}'' (''{1}'') типу ''{2}''.
adminLog.categories.schedulerTasksSettings.triggerEdit.description=Змінено розклад завдання планувальника ''{0}'' (''{1}'') типу ''{2}''.
adminLog.categories.schedulerTasksSettings.triggerDelete.description=Видалено розклад завдання планувальника ''{0}'' (''{1}'') типу ''{2}''.
adminLog.categories.schedulerTasksSettings.runNow.description=Завдання планувальника ''{0}'' (''{1}'') типу ''{2}'' запущено вручну за кнопкою ''Виконати зараз''.
mailSettingsLog.actionType=Зміна налаштувань пошти
adminLog.categories.incomingMailSettings=Зміна параметрів підключення до сервера вхідної пошти
adminLog.categories.incomingMailSettings.serverAdd.description=Додано підключення до сервера вхідної пошти "{0}".
adminLog.categories.incomingMailSettings.serverEdit.description=Змінено підключення до сервера вхідної пошти ''{0}'':\n{1} .
adminLog.categories.incomingMailSettings.serverDelete.description=Видалено підключення до сервера вхідної пошти ''{0}''.
adminLog.categories.outgoingMailSettings=Зміна параметрів підключення до сервера вихідної пошти
adminLog.categories.outgoingMailSettings.serverAdd.description=Додано підключення до сервера вихідної пошти "{0}".
adminLog.categories.outgoingMailSettings.serverEdit.description=Змінено з''єднання з сервером вихідної пошти ''{0}'':\n{1}.
adminLog.categories.outgoingMailSettings.paramsEdit.description=Змінено параметри надсилання вихідної пошти ''{0}'':\n{1}.
adminLog.categories.outgoingMailSettings.serverDelete.description=Видалено з''єднання з сервером вихідної пошти ''{0}''.
adminLog.categories.mailRulesSettings=Зміна правил обробки вхідної пошти
adminLog.categories.mailRulesSettings.ruleAdd.description=Додано правило обробки вхідної пошти "{0}".
adminLog.categories.mailRulesSettings.ruleEdit.description=Змінено правило обробки вхідної пошти ''{0}'':\n{1}.
adminLog.categories.mailRulesSettings.ruleDelete.description=Видалено правило обробки вхідної пошти "{0}".
inboundMailServer.host=Сервер
inboundMailServer.port=Порт
inboundMailServer.username=Логін
inblundMailServer.sharedMailbox=Загальна поштова скринька
inboundMailServer.password=Пароль
inboundMailServer.protocol=Протокол
inboundMailServer.ssl=SSL з''єднання
inboundMailServer.skipCertVerification=Ігнорувати перевірку SSL сертифіката (менш безпечно)
inboundMailServer.folders=Папки для сканування
inboundMailServer.receiveMailTask=Завдання планувальника
inboundMailServer.enabled=Ввімнено
inboundMailServer.schedulerTask=Задача планувальника
outgoinMailServer.host=Сервер
outgoinMailServer.port=Порт
outgoinMailServer.username=Логін
outgoinMailServer.password=Пароль
outgoinMailServer.securityProtocol=Протокол шифрування
outgoinMailServer.needAuth=Аутентифікація
outgoinMailServer.enabled=Ввімкнено
outgoinMailServer.defaultConnection=Використовувати за замовчуванням
outgoinMailServer.skipCertVerification=Ігнорувати перевірку SSL сертифіката (менш безпечно)
mailParameters.feedbackAddress=E-mail адреса служби техпідтримки (кому відповісти "Reply to")
mailParameters.from=E-mail адреса системи (від кого "From")
mailParameters.name=Ім''я відправника листів із системи
mailParameters.encoding=Кодування тексту
mailParameters.transliterate=Транслітерація заголовків листів
mailParameters.sendAttempts=Максимальна кількість невдалих спроб надсилання листа
mailParameters.resendDelay=Пауза між спробами відправки листа, сік
mailParameters.messagesPerIteration=Максимальна кількість листів, що надсилаються в одному пакеті
mailParameters.iterationDelay=Мінімальний інтервал між пакетами листів, що відсилаються, сек
mailParameters.sendPartial=Відсилати частинами
mailProcessorRule.title=Назва
mailProcessorRule.script=Скрипт
mailProcessorRule.enabled=Ввімкнено
superUser.actionType=Зміна налаштувань суперкористувачів
adminLog.categories.superUserChangePassword=Зміна пароля суперкористувача
adminLog.categories.superUserChangePassword.description=Змінено пароль для суперкористувача ''{0}''
adminLog.categories.superUserDeleted=Видалення суперкористувача
adminLog.categories.superUserDeleted.description=Видалений суперкористувач ''{0}''
adminLog.categories.superUserAdd=Додавання суперкористувача
adminLog.categories.superUserAdd.description=Доданий суперкористувач ''{0}''
adminLog.categories.uploadMetainfo=Завантаження метаінформації
adminLog.categories.uploadMetainfo.versionVersionInfo=Завантаження метаінформації версії '{}' від '{}'
adminLog.categories.uploadMetainfo.otherVersionVersionInfo=Завантаження метаінформації іншої версії: '{}' != '{}'
adminLog.categories.uploadMetainfo.responsibleTransferEdit=Зміна налаштування передачі відповідальності за об''єкти метакласу
adminLog.categories.uploadMetainfo.responsibleTransferEdit.description=У метакласі ''{0}'' ({1}) змінено налаштування налаштування матриці передачі відповідальності.
adminLog.categories.uploadMetainfo.metaClassEdit.description=Змінено метаклас ''{0}'' ({1}):\n{2}
adminLog.categories.uploadMetainfo.metaClassAdd.description=Створено метаклас "{0}" ({1}).
adminLog.categories.uploadMetainfo.scParametersEdit.description=Змінено параметри запиту:\n{0}.
adminLog.categories.uploadMetainfo.catalogEdit.description=Змінено довідник "{0}" ({1}).
adminLog.categories.uploadMetainfo.catalogCreate.description=Створено довідник "{0}" ({1}).
adminLog.categories.uploadMetainfo.secGroupAndRoles.groupEdit.description=Змінено групу користувачів ''{0}''.
adminLog.categories.uploadMetainfo.secGroupAndRoles.groupAdd.description=Додано групу користувачів ''{0}''.
adminLog.categories.uploadMetainfo.timerDefinitionSettings.timerAdd.description=Доданий лічильник "{0}" ({1}).
adminLog.categories.uploadMetainfo.timerDefinitionSettings.timerEdit.description=Змінено лічильник "{0}" ({1}).
adminLog.categories.uploadMetainfo.secGroupAndRoles.roleAdd.description=Додано роль "{0}" ({1}).
adminLog.categories.uploadMetainfo.secGroupAndRoles.roleEdit.description=Змінено роль "{0}" ({1}).
adminLog.categories.uploadMetainfo.secDomain.description=Змінено налаштування прав доступу для метакласу ''{0}'' ({1}):\n{2}.
adminLog.categories.uploadMetainfo.eventActionSettings.eventActionAdd.description=Додано дію за подією ''{0}'' (''{1}'') типу ''{2}''.
adminLog.categories.uploadMetainfo.eventActionSettings.eventActionEdit.description=Змінено дію за подією ''{0}'' (''{1}'') типу ''{2}'':\n{3}
adminLog.categories.uploadMetainfo.wfProfileFolder.description=Змінено профілі пов''язаних життєвих циклів.
adminLog.categories.uploadMetainfo.mailRulesSettings.ruleAdd.description=Додано правило обробки вхідної пошти "{0}".
adminLog.categories.uploadMetainfo.mailRulesSettings.ruleEdit.description=Змінено правило обробки вхідної пошти ''{0}'':\n{1}.
adminLog.categories.uploadMetainfo.schedulerTasksSettings.taskAdd.description=Додано завдання планувальника ''{0}'' типу ''{1}''.
adminLog.categories.uploadMetainfo.schedulerTasksSettings.taskEdit.description=Змінено завдання планувальника ''{0}'' типу ''{1}'':\n{2}.
adminLog.categories.uploadMetainfo.incomingMailSettings.serverEdit.description=Змінено підключення до сервера вхідної пошти ''{0}'':\n{1}.
adminLog.categories.uploadMetainfo.escalationSchemeSettings.schemeAdd.description=Додана схема ескалації "{0}" ({1}).
adminLog.categories.uploadMetainfo.escalationSchemeSettings.schemeEdit.description=Змінено схему ескалації ''{0}'' ({1}):\n{2}.
adminLog.categories.uploadMetainfo.escalationActionSettings.eventActionAdd.description=Додано дію за подією ''{0}'' типу ''{1}''.
adminLog.categories.uploadMetainfo.escalationActionSettings.eventActionEdit.description=Змінено дію за подією ''{0}'' типу ''{1}'':\n{2}.
adminLog.categories.uploadMetainfo.windowEdit.description=У метакласі ''{0}'' ({1}) змінено інтерфейс картки об''єктів.
adminLog.categories.uploadMetainfo.newEntryFormEdit.description=У метакласі ''{0}'' ({1}) змінено інтерфейс форми додавання об''єктів.
adminLog.categories.uploadMetainfo.changeCaseFormEdit.description=У метакласі ''{0}'' ({1}) змінено інтерфейс форми зміни типу об''єктів.
adminLog.categories.uploadMetainfo.changeUserFormsSettings.description=У метакласі ''{0}'' ({1}) змінено інтерфейс користувача форми.
adminLog.categories.uploadMetainfo.editFormEdit.description=У метакласі ''{0}'' ({1}) змінено інтерфейс форми редагування об''єктів.
adminLog.categories.uploadMetainfo.secProfileEdit.profileAdd.description=Додано профіль ''{0}''.
adminLog.categories.uploadMetainfo.secProfileEdit.profileEdit.description=Змінено профіль ''{0}''.
adminLog.categories.uploadMetainfo.secPlanningModeProfileEdit.profileAdd.description=Додано профіль режиму планування "{0}".
adminLog.categories.uploadMetainfo.secPlanningModeProfileEdit.profileEdit.description=Змінено профіль планування ''{0}''.
adminLog.categories.uploadMetainfo.adminLiteSettings.description=Змінено налаштування полегшеного інтерфейсу.
adminLog.categories.uploadMetainfo.reportTemplatesSettings.addReportTemplate.description=Додано шаблон звітів "{0}" ({1}).
adminLog.categories.uploadMetainfo.reportTemplatesSettings.editReportTemplate.description=Змінено шаблон звітів ''{0}'' ({1}):\n{2}.
adminLog.categories.uploadMetainfo.configurationSettings.addConf.description=Додано конфігурацію імпорту "{0}" ("{1}").
adminLog.categories.uploadMetainfo.configurationSettings.editConf.description=Змінено конфігурацію імпорту ''{0}'' ('{1}''):\n{2}
adminLog.categories.uploadMetainfo.monitoringSystemSettings.description=Змінено налаштування системи моніторингу:\n{0}.
adminLog.categories.uploadMetainfo.scriptDelete.description=Видалено скрипт ''{0}''.
adminLog.categories.uploadMetainfo.scriptAdd.description=Додано скрипт ''{0}'':\nСписок місць використання:\n{1}.
adminLog.categories.uploadMetainfo.scriptEdit.description=Змінено скрипт ''{0}'':\n{1}.
adminLog.categories.uploadMetainfo.embeddedApplicationSettings.addEmbeddedApplication.description=Додано вбудовану програму ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.embeddedApplicationSettings.editEmbeddedApplication.description=Змінено вбудований додаток''{0}'' ({1}):\n{2}.
administrationSettings.actionType=Зміна налаштувань системи
adminLog.categories.administrationSettings=Дія адміністрування
adminLog.categories.administrationSettings.description=Здійснено дію на сторінці "Адміністрація"
adminLog.categories.administrationSettings.reportTemplatesSettings.addReportTemplate.description=Додано шаблон звітів "{0}" ({1}).
adminLog.categories.administrationSettings.reportTemplatesSettings.editReportTemplate.description=Змінено шаблон звітів ''{0}'' ({1}):\n{2}.
adminLog.categories.administrationSettings.editEmbeddedApplication.description=Змінено шаблон звітів ''{0}'' ({1}):\n{2}.
uploadMetainfo.addedAttribute=Додані атрибути: {0}
uploadMetainfo.editedAttribute=Додані атрибути: {0}
uploadMetainfo.deletedAttribute=Видалені атрибути: {0}
uploadMetainfo.addedAttributeGroup=Додано групи атрибутів: {0}
uploadMetainfo.editedAttributeGroup=Змінено групи атрибутів: {0}
uploadMetainfo.deletedAttributeGroup=Видалено групи атрибутів: {0}
uploadMetainfo.editedWorkflowState=Змінено статуси життєвого циклу: {0}
uploadMetainfo.addedWorkflowState=Додані статуси життєвого циклу: {0}
uploadMetainfo.deletedWorkflowState=Видалено статуси життєвого циклу: {0}
uploadMetainfo.workflowTransitionChange=Змінено життєвий цикл об''єктів
uploadMetainfo.addedSearchSetting=Додано налаштування пошуку за коментарями та файлами: {0}
uploadMetainfo.editedSearchSetting=Змінено налаштування пошуку за коментарями та файлами: {0}
uploadMetainfo.deletedSearchSetting=Видалено налаштування пошуку за коментарями та файлами: {0}
uploadMetainfo.accessMatrixChange=Змінено налаштування матриці прав
uploadMetainfo.addedSecRole=Додані ролі: {0}
uploadMetainfo.editedSecRole=Змінено ролі: {0}
uploadMetainfo.addedMarkers=Додано маркери прав: {0}
uploadMetainfo.editedMarkers=Змінено маркери прав: {0}
uploadMetainfo.changeTitleMetaClass=назва: {0} -> {1}
console.actionType=Дії в консолі
adminLog.categories.consoleActions=Виконання скрипта в консолі
adminLog.categories.consoleActions.description=Виконано скрипт у консолі.
onceMigrationScript.actionType=Дія скрипту преімпорт/постімпорт метаінформації.
adminLog.categories.preImportScriptAction=Виконання скрипта преімпорта метаінформації.
adminLog.categories.preImportScriptAction.description=Виконано скрипт преімпорту метаінформації.
adminLog.categories.postImportScriptAction=Виконання скрипта постімпорту метаінформації.
adminLog.categories.postImportScriptAction.description=Виконання скрипта постімпорту метаінформації.
administrationLog.actionType=Дія адміністрування
adminLog.import=Переглянути зміни, пов''язані з поточним завантаженням (<a href="{0}">{1}</a>).
adminLog.importError=Помилка: {0}
adminLog.categories.uploadLicense=Завантаження ліцензії
adminLog.categories.uploadLicense.description=Завантажено файл ліцензії.
adminLog.categories.exportLicense=Вивантаження ліцензії
adminLog.categories.exportLicense.description=Вивантажено ліцензійний файл.
adminLog.categories.exportMetainfo=Вивантаження метаінформації
adminLog.categories.exportMetainfo.description=Вивантажено файл метаінформації.
adminLog.categories.uploadModule=Завантаження модуля
adminLog.categories.uploadModule.description=Завантажено модуль.
adminLog.categories.uploadCertificate=Завантаження сертифіката
adminLog.categories.uploadCertificate.description=Завантажено сертифікат.
adminLog.categories.importMetainfoStart=Початок завантаження метаінформації
adminLog.categories.importMetainfoStart.description=Початок завантаження метаінформації.\nПереглянути зміни, пов''язані з поточним завантаженням (<a href="{0}">{1}</a>).
adminLog.categories.importMetainfoDone=Завантаження метаінформації завершено
adminLog.categories.importMetainfoDone.description=Завантаження метаінформації завершено за {0} сек\nВерсія програми: {2}\nВерсія файлу метаінформації: {3}\nСпосіб вивантаження: {1}\nСпосіб завантаження: {11}\nДата вивантаження: {4}\nКількість попереджень: {5}\n{6}Лог завантаження: (<a href="{7}">{8}</a>).\nПереглянути зміни, пов''язані з поточним завантаженням (<a href="{9}">{10}</a>).
adminLog.categories.plannedVersion.couldNotExcludeOriginalObject=Неможливо виключити об''єкт {0} ({1}) з основної гілки. Об''єкт, що виключається, не має об''єкта з інформацією про версію.
adminLog.categories.plannedVersion.couldNotExcludeSourceObject=Неможна виключити версію об''єкта, яка є джерелом іншого об''єкта!
adminLiteLog.actionType=Зміна налаштувань полегшеного інтерфейсу налаштування
adminLog.categories.adminLiteSettings=Зміна налаштувань полегшеного інтерфейсу налаштування
adminLog.categories.adminLiteSettings.description=Змінено параметри полегшеного інтерфейсу налаштування:\n{0}.
adminlite.enabled=Увімкнено
adminlite.groups=Доступний групам користувачів
adminlite.catalogs=Сторінки інтерфейсу (довідники)
adminlite.processSettings=Сторінки інтерфейсу (налаштування бізнес-процесів)
adminlite.systemSettings=Сторінки інтерфейсу (налаштування системи)
ctiSettings.actionType=Зміна налаштування CTI
adminLog.categories.ctiSettings=Зміна налаштувань телефонії (CTI)
adminLog.categories.ctiSettings.description=Змінено налаштування телефонії (CTI):\n{0}.
ctiConfig.callProcessingScript=Скрипт (правило обробки дзвінків)
ctiConfig.ctiTeams=Відповідальні команди
ctiConfig.enable=Включено
ctiConfig.ctiServerType=Тип сервера
ctiConfig.serverAddress=Адреса сервера
ctiConfig.serverPort=Порт
ctiConfig.login=Логин
ctiConfig.password=Пароль
reportTemplatesSettings.actionType=Зміна параметрів шаблонів звітів та друкованих форм
adminLog.categories.reportTemplatesSettings=Зміна параметрів шаблонів звітів та друкованих форм
adminLog.categories.reportTemplatesSettings.deleteReportTemplate.description=Видалено шаблон "{0}" ({1}).
adminLog.categories.reportTemplatesSettings.addReportTemplate.description=Додано шаблон "{0}" ({1}).
adminLog.categories.reportTemplatesSettings.editReportTemplate.description=Змінено шаблон ''{0}'' ({1}):\n{2}.
reportTemplate.title=Назва
reportTemplate.description=Опис
reportTemplate.script=Скрипт
reportTemplate.file=Файл
reportTemplate.parameters=Параметри шаблону
adminLog.categories.monitoringSystemSettings=Зміна налаштувань системи моніторингу
adminLog.categories.monitoringSystemSettings.description=Змінено налаштування системи моніторингу:\n{0}.
synchronization.actionType=Зміна налаштувань синхронізації
adminLog.categories.configurationSettings=Зміна параметрів конфігурації
adminLog.categories.configurationSettings.editConf.description=Змінено конфігурацію імпорту ''{0}'' (''{1}''):\n{2}.
adminLog.categories.configurationSettings.runSynchronization.description=Запущено конфігурацію імпорту ''{0}'' (''{1}'').
adminLog.categories.configurationSettings.deleteConf.description=Видалено конфігурацію імпорту "{0}" ("{1}").
adminLog.categories.configurationSettings.addConf.description=Додано конфігурацію імпорту "{0}" ("{1}").
adminLog.categories.connectingSettings=Зміна параметрів підключення
adminLog.categories.connectingSettings.deleteCon.description=Видалено підключення ''{0}''.
adminLog.categories.connectingSettings.addCon.description=Додано підключення ''{0}''.
adminLog.categories.connectingSettings.editCon.description=Змінено підключення ''{0}'':\n{1}.
synchronizationConfiguration.title=Назва
synchronizationConfiguration.script=Конфігурація у XML-виді
synchronizationConnection.title=Назва
synchronizationConnection.connectionString=Рядок підключення
synchronizationConnection.authType=Тип ідентифікації
synchronizationConnection.secProtocol=Протокол безпеки
synchronizationConnection.timeout=Таймаут підключення, хв
synchronizationConnection.login=Ім''я користувача
synchronizationConnection.password=Пароль
synchronizationConnection.jdbcDriver=Клас реалізації протокола jdbc
mobileSettings.actionType=Зміна налаштувань мобільної програми
adminLog.categories.mobileListSettings=Зміна налаштувань мобільної програми
adminLog.categories.mobileListSettings.addMobileList.description=Зміна налаштувань мобільної програми.
adminLog.categories.mobileListSettings.deleteMobileList.description=Видалено список об''єктів {1} у мобільному додатку.
adminLog.categories.mobileListSettings.editMobileList.description=Змінено список об''єктів {1} у мобільному додатку:\n{2}.
adminLog.categories.mobileListSettings.copyMobileList.description=Скопійовано список об''єктів {2} у мобільному додатку. Список копій: {1}.
adminLog.categories.mobileCardSettings=Зміна налаштувань карток у мобільному додатку
adminLog.categories.mobileCardSettings.addObjectCard.description=Створено картку об''єктів з кодом {1} у мобільному додатку.
adminLog.categories.mobileCardSettings.deleteObjectCard.description=Видалено картку об''єктів з кодом {1} у мобільному додатку.
adminLog.categories.mobileCardSettings.editObjectCard.description=Змінено картку об''єктів з кодом {1} у мобільному додатку:\n{2}.
adminLog.categories.mobileCardSettings.copyObjectCard.description=Скопійовано картку об''єктів з кодом "{2}" у мобільному додатку. Картка, створена копіюванням: {1}.
adminLog.categories.mobileCardSettings.editMobileContent.description=Змінено картку об''єктів з кодом {0} у мобільному додатку:\nконтент {1} ({2}).
adminLog.categories.mobileCardSettings.addMobileContent.description=Змінено картку об''єктів з кодом {0} у мобільному додатку:\nДодано контент {1} ({2}).
adminLog.categories.mobileEditFormSettings=Зміна налаштувань форм редагування об''єктів у мобільному додатку
adminLog.categories.mobileEditFormSettings.addEditForm.description=Створена форма редагування об''єктів з кодом {1} у додатку.
adminLog.categories.mobileEditFormSettings.deleteEditForm.description=Видалено форму редагування об''єктів з кодом {1} у мобільному додатку.
adminLog.categories.mobileEditFormSettings.editEditForm.description=Змінено форму редагування об''єктів з кодом {1} у мобільному додатку:\n{2}.
adminLog.categories.mobileEditFormSettings.copyEditForm.description=Скопійовано форму редагування об''єктів з кодом "{2}" у мобільному додатку. Форма, створена копіюванням: {1}.
adminLog.categories.mobileAddFormSettings=Зміна налаштувань форм додавання об''єктів у мобільному додатку
adminLog.categories.mobileAddFormSettings.addAddForm.description=Створено форму додавання об''єктів {1} у мобільному додатку.
adminLog.categories.mobileAddFormSettings.deleteAddForm.description=Видалено форму додавання об''єктів {1} у мобільному додатку.
adminLog.categories.mobileAddFormSettings.editAddForm.description=Змінено форму додавання об''єктів {1} у мобільному додатку:\n{2}.
adminLog.categories.mobileAddFormSettings.copyAddForm.description=Скопійовано форму додавання об''єктів {2} у мобільному додатку. Форма, створена копіюванням: {1}.
adminLog.categories.mobileNavigationSettings=Зміна налаштувань навігаційного меню мобільного додатка
adminLog.categories.mobileNavigationSettings.addElement.description=Додано елемент ''{1}'' у меню навігації мобільного додатка.
adminLog.categories.mobileNavigationSettings.deleteElement.description=Видалено елемент "{1}" з навігаційного меню мобільного додатка.
adminLog.categories.mobileNavigationSettings.editElement.description=Змінено елемент ''{1}'' у навігаційному меню мобільного додатку:\n{2}.
adminLog.categories.mobileOtherSettings=Зміна інших налаштувань у мобільному додатку
adminLog.categories.mobileOtherSettings.description=Змінено інші налаштування в мобільному додатку: {1}
adminLog.categories.mobileGeolocationSettings=Зміна налаштувань геолокації в мобільному додатку
adminLog.categories.mobileGeolocationSettings.description=Налаштування геолокації в мобільному додатку змінилися: {1}
adminLog.categories.mobileSecuritySettings=Зміна налаштувань безпеки в мобільному додатку
adminLog.categories.mobileSecuritySettings.description=Змінено налаштування безпеки в мобільному додатку: {1}
adminLog.categories.mobileBarcodeScannerSettings=Зміна налаштувань сканера штрих-кодів у мобільному додатку
adminLog.categories.mobileBarcodeScannerSettings.description=Змінено налаштування сканера штрих-кодів у мобільному додатку:\n{1}
mobileSecuritySettings.accessKeyLifetime=Час життя ключа доступу в мобільному додатку (у секундах)
mobileSecuritySettings.passwordStorageTime=Час зберігання пароля на клієнті (у секундах)
mobileSecuritySettings.additionalAuthentication=Обов'язково запитувати додаткову аутентифікацію за ПІН-кодом або біометрією в мобільному додатку
mobileSecuritySettings.loginAttemptsCount=Кількість невдалих спроб введення ПІН-коду, після яких будуть стерті всі дані програми
mobileOtherSettings.direct=Пропонувати відкривати посилання на об''єкти системи, отримані зі сторонніх джерел, всередині мобільного додатку
mobileOtherSettings.voiceFillComment=Заповнювати коментар голосом
mobileOtherSettings.voiceServicesAvailability=ОС, для яких доступне голосове створення об''єктів та коментарів
mobileOtherSettings.systemName=Назва системи
mobileGeolocationSettings.enablesMovementAttribute=Атрибут увімкнення режиму відстеження переміщень
mobileGeolocationSettings.frequency=Періодичність опитування (в хв)
mobileGeolocationSettings.maxVolume=Максимальний обсяг історії переміщень
mobileBarcodeScannerSettings.availableInAttrs=Доступний в атрибутах
mobileBarcodeScannerSettings.profiles=Доступний у навігаційному меню для профілів
mobileList.title=Назва
mobileList.class=Клас
mobileList.cases=Типи
mobileList.profiles=Доступний профілям
mobileList.tags=Мітки
mobileList.attributes=Атрибути, які виводяться в список
mobileList.attrChain=Зв'язок з поточним користувачем
mobileList.filter=Фільтрація
mobileList.order=Сортування
mobileList.allowActionsWithObjects=Дозволити дії з об''єктами зі списку
objectCard.class=Клас
objectCard.cases=Типи
objectCard.objectCaption=Заголовок об''єкта в мобільному додатку
objectCard.profiles=Доступна профілям
objectCard.tags=Мітки
objectCard.comments=Меню ''Коментарі'' доступне
objectCard.files=Меню ''Файли'' доступне
objectCard.transitions=Доступні переходи між статусами
objectCard.objectActions=Доступні елементи керування
objectCard.attributes=Атрибути, що виводяться на картку
objectCard.useAvailableStates=Використовувати налаштування переходів між статусами із веб-клієнта
objectCard.contents=Контенти
objectCard.quickStateChangeAvailable=Швидка зміна статусу доступна
editForm.class=Клас
editForm.cases=Типи
editForm.profiles=Доступна профілям
editForm.tags=Мітки
editForm.attributes=Атрибути, які виводяться на форму редагування
addForm.title=Назва
addForm.class=Клас
addForm.cases=Типи
addForm.profiles=Доступна профілям
addForm.tags=Мітки
addForm.attributes=Атрибути, які виводяться на форму додавання
addForm.showInNavigationMenu=Швидка зміна статусу доступна
addForm.isTransferDeviceGeoPosition=Передавати геопозицію пристрою
addForm.blockInheritanceBroken=успадкування блоку ''{0}'' розірвано
addForm.blockInheritanceRestored=успадкування блоку ''{0}'' відновлено
addForm.block.VoiceCreation=Створення об''єкта голосом
addForm.block.AttrsOnForm=Атрибути, які виводяться на форму додавання
mobileMenuItem.title=Назва
mobileMenuItem.parent=Вкладено до
mobileMenuItem.type=Вигляд елемента
mobileMenuItem.value=Значення
navigationSettings.actionType=Зміна налаштувань навігації
adminLog.categories.navigationSettings=Змінено налаштування навігації
adminLog.categories.navigationSettings.description=Змінено налаштування навігації:\n{0}.
navigationSettings.showTopMenu=Показувати верхнє меню
navigationSettings.showLeftMenu=Показувати ліве меню
navigationSettings.showBreadCrumb=Показувати "хлібні крихти"
navigationSettings.navigationMenuItems=Верхнє меню
navigationSettings.breadCrumb=Навігаційний ланцюжок ("Хлібні крихти")
scriptCatalog.actionType=Зміна каталогу скриптів
adminLog.categories.scriptAdd=Додавання скрипта
adminLog.categories.scriptEdit=Зміна скрипта
adminLog.categories.scriptDelete=Видалення скрипта
adminLog.categories.scriptDelete.description=Видалено скрипт ''{0}''.
adminLog.categories.scriptAdd.description=Додано скрипт "{0}".\nНалаштування, де використовується скрипт:\n{1}.
adminLog.categories.scriptEdit.description=Змінено скрипт ''{0}'':\n{1}.
script.scriptBody=Тіло скрипта
script.usagePoint=Налаштування, де використовується скрипт
script.defaultCategories=Категорії (імовірні місця використання)
script.title=Назва
script.code=Код
script.description=Опис
scriptModule.actionType=Зміна каталогу модулів
adminLog.categories.moduleAdd=Додавання модуля
adminLog.categories.moduleEdit=Зміна модуля
adminLog.categories.moduleDelete=Видалення модуля
adminLog.categories.moduleAdd.description=Додано модуль "{0}".
adminLog.categories.moduleDelete.description=Видалено модуль ''{0}''.
adminLog.categories.moduleEdit.description=Змінено модуль ''{0}'':\n{1}.
securityPolicyEdit.actionType=Зміна налаштувань політики безпеки
adminLog.categories.securityPolicyEdit=Зміна налаштувань політики безпеки
adminLog.categories.securityPolicyEdit.edit.description=Змінено параметри політики безпеки: {0}
adminLog.categories.securityPolicyEdit.passwordChange.description=Запущено примусову зміну паролів
filePreviewSettingsEdit.actionType=Зміна налаштувань попереднього перегляду файлів
adminLog.categories.filePreviewSettingsEdit=Зміна налаштувань попереднього перегляду файлів
adminLog.categories.filePreviewSettingsEdit.edit.description=Змінено параметри попереднього перегляду файлів: {0}
advListSettingsEdit.actionType=Зміна налаштувань складних списків
adminLog.categories.advListSettingsEdit=Зміна налаштувань складних списків
adminLog.categories.advListSettingsEdit.edit.description=Редагування налаштувань - {0}
advListSettings.props.shareView=Створення загальних видів доступне
advListSettings.props.richTextViewMode=Очищати тексти в полях типу "Текст у форматі RTF" від стилів для економії ресурсів браузера
scriptModule.description=Опис
scriptModule.version=Версія
scriptModule.script=Текст
scriptModule.isSuperUserReadable=Доступний для перегляду суперкористувачами
scriptModule.isSuperUserWritable=Доступний для редагування суперкористувачами
adminLog.categories.uploadInputmask=Додавання шаблону маски вводу
adminLog.categories.uploadInputmask.description=Завантажено шаблон маски вводу
embeddedApplicationSettings.actionType=Зміна налаштувань вбудованих програм
adminLog.categories.embeddedApplicationSettings=Зміна налаштувань вбудованих програм
adminLog.categories.embeddedApplicationSettings.deleteEmbeddedApplication.description=Видалено вбудовану програму "{0}" ({1}).
adminLog.categories.embeddedApplicationSettings.addEmbeddedApplication.description=Додано вбудовану програму "{0}" ({1}).
adminLog.categories.embeddedApplicationSettings.editEmbeddedApplication.description=Змінено вбудовану програму ''{0}'' ({1}):\n{2}.
embeddedApplication.title=Назва
embeddedApplication.description=Опис
embeddedApplication.code=Код
embeddedApplication.applicationType=Тип додатку
embeddedApplication.on=Ввімкнено
embeddedApplication.applicationAddress=Адреса додатку
embeddedApplication.applicationFile=Файл додатку
embeddedApplication.initialHeight=Вихідна висота додатку
embeddedApplication.mobileHeight=Висота контенту в мобільному додатку
embeddedApplication.script=Скрипт
embeddedApplication.fullscreenAllowed=Дозволяти розкривати на всю сторінку
styleTemplatesSettings.actionType=Зміна налаштувань шаблонів стилів
adminLog.categories.styleTemplatesSettings=Змінено налаштування шаблонів стилів
adminLog.categories.styleTemplatesSettings.add.description=Доданий шаблон стилів {0} ({1}).
adminLog.categories.styleTemplatesSettings.edit.description=Змінено шаблон стилів "{0}" ({1}):\n{2}.
adminLog.categories.styleTemplatesSettings.remove.description=Видалено шаблон стилів "{0}" ({1}).
styleTemplatesSettings.props.title=Назва
styleTemplatesSettings.props.text=Текст шаблона
fastLinksSettings.actionType=Зміна параметрів згадки об''єктів
adminLog.categories.fastLinksSettings=Зміна параметрів згадки об''єктів
adminLog.categories.fastLinksSettings.add.description=Додано згадку об''єктів "{0}" ({1}).
adminLog.categories.fastLinksSettings.edit.description=Змінено згадку об''єктів ''{0}'' ({1}):\n{2}.
adminLog.categories.fastLinksSettings.remove.description=Видалено згадку об''єктів "{0}" ({1}).
fastLinksSettings.props.title=Назва
fastLinksSettings.props.mentionTypes=Об''єкти
fastLinksSettings.props.alias=Префікс для згадки об''єкту
fastLinksSettings.props.contextTypes=У контексті об''єктів
fastLinksSettings.props.mentionAttribute=Атрибут для формування посилання
fastLinksSettings.props.attributeGroup=Група атрибутів для складної форми згадки
fastLinksSettings.props.profiles=Профілі
learningProcess.actionType=Зміна каталогу моделей
adminLog.categories.processLearningAdd=Додавання процесу навчання
adminLog.categories.processLearningAdd.description=Додано процес навчання "{0}" ({1})
adminLog.categories.processLearningDelete=Видалення процесу навчання
adminLog.categories.processLearningDelete.description=Видалено процес навчання ''{0}'' ({1})
adminLog.categories.processLearningEdit=Зміна процесу навчання
adminLog.categories.processLearningEdit.description=Змінено параметри процесу навчання ''{0}'' ({1}):\n{2}
adminLog.categories.processLearningStart=Запуск процесу навчання
adminLog.categories.processLearningStart.description=Запущено процес навчання ''{0}'' ({1})
learningProcess.props.title=Назва
learningProcess.props.code=Код
learningProcess.props.description=Опис
learningProcess.props.scriptGatheringData=Скрипт збирання даних
learningProcess.props.scriptDataPreparation=Скрипт підготовки даних
learningProcess.props.scriptSaveData=Скрипт збереження даних
learningProcess.props.scriptLearningAndValidation=Скрипт навчання та тестування.
smiaModel.actionType=Зміна каталогу моделей
adminLog.categories.smiaModelAdd=Додавання моделі
adminLog.categories.smiaModelAdd.description=Додано модель ''{0}'' ({1}, {2})
adminLog.categories.smiaModelDelete=Видалення моделі
adminLog.categories.smiaModelDelete.description=Видалено модель ''{0}'' ({1}, {2})
adminLog.categories.smiaModelEdit=Зміна моделі
adminLog.categories.smiaModelEdit.description=Змінено параметри моделі ''{0}'' ({1}, {2}):\n{3}
adminLog.categories.smiaModelStateChange=Зміна статусу моделі
adminLog.categories.smiaModelStateChange.description=Змінився статус моделі ''{0}'' ({1}, {2}):\n{3} ({4}) -> {5} ({6})
smiaModel.props.title=Назва
smiaModel.props.code=Код
smiaModel.props.description=Опис
smiaModel.props.version=Версія
smiaModel.props.learningProcess=Процес навчання
smiaModel.props.state=Статус
smiaModel.props.server=Сервер
tagsSettings.actionType=Зміна налаштувань міток
adminLog.categories.tagsSettings=Змінено налаштування позначок
adminLog.categories.tagsSettings.add.description=Додано мітку "{0}" ({1}).
adminLog.categories.tagsSettings.edit.description=Змінено мітку ''{0}'' ({1}):\n{2}.
adminLog.categories.tagsSettings.remove.description=Видалено мітку ''{0}'' ({1}).
tagsSettings.props.title=Назва
tagsSettings.props.description=Опис
tagsSettings.props.enabled=Ввімкнена
customizationFiles.actionType=Зміна налаштувань файлів кастомізації
adminLog.categories.customizationFilesAdd=Додавання файлу кастомізації
adminLog.categories.customizationFilesAdd.description=Додано файл кастомізації "{0}" ({1}).
adminLog.categories.customizationFilesEdit=Зміна файлу кастомізації
adminLog.categories.customizationFilesEdit.description=Змінено параметри кастомізації ''{0}'' ({1}):\n{2}.
adminLog.categories.customizationFilesDelete=Видалення файлу кастомізації
adminLog.categories.customizationFilesDelete.description=Видалений файл кастомізації "{0}" ({1}).
customizationFiles.props.title=Назва
customizationFiles.props.description=Опис
customizationFiles.props.file=Файл
customizationFiles.props.targetPlace=Місце застосування
adminLog.categories.dropDownLists=Випадаючі списки вибору
adminLog.categories.dropDownLists.description=Змінено налаштування списків вибору:\n{0}
adminLog.categories.dropDownLists.changes=''Підставляти єдине значення на формах додавання''
customLoginForm.actionType=Зміна налаштувань інтерфейсу
adminLog.categories.customLoginFormSettings=Зміна налаштувань інтерфейсу
adminLog.categories.customLoginForm.enabled=Увімкнено
adminLog.categories.customLoginForm.template=''Шаблон сторінки входу''
adminLog.categories.customLoginFormSettings.edit.description=Змінені параметри входу для сторінки, що налаштовується: {0}.
interfaceSettings.actionType=Зміна налаштувань інтерфейсу
adminLog.categories.interfaceSettings=Зміна налаштувань інтерфейсу
adminLog.categories.interfaceSettings.contentInternalScroll=Внутрішній скролінг елементів сторінки
adminLog.categories.interfaceSettings.contentFullscreen=Розкриття контентів на всю сторінку
adminLog.categories.interfaceSettings.edit.description=Зміна налаштувань інтерфейсу:\n{0}.
listTemplatesSettings.actionType=Зміна параметрів шаблонів списків
adminLog.categories.listTemplatesSettings=Змінено налаштування шаблонів списків
adminLog.categories.listTemplatesSettings.add.description=Додано шаблон списку "{0}" ({1}).
adminLog.categories.listTemplatesSettings.edit.description=Змінено шаблон списку ''{0}'' ({1}):\n{2}.
adminLog.categories.listTemplatesSettings.edit2.description=Змінено шаблон списку "{0}" ({1}).
adminLog.categories.listTemplatesSettings.remove.description=Видалено шаблон списку "{0}" ({1}).
contentTemplatesSettings.actionType=Зміна параметрів шаблонів контентів
adminLog.categories.contentTemplatesSettings=Змінено налаштування шаблонів контентів
adminLog.categories.contentTemplatesSettings.add.description=Додано шаблон контенту "{0}" ({1}).
adminLog.categories.contentTemplatesSettings.edit.description=Змінено шаблон контенту {0} ({1}).
adminLog.categories.contentTemplatesSettings.remove.description=Видалено шаблон контенту "{0}" ({1}).
listTemplatesSettings.props.title=Назва
maintenance.actionType=Зміна налаштувань блокування входу на час технічних робіт
adminLog.categories.maintenanceStart=Запуск блокування на час технічних робіт
adminLog.categories.maintenanceStart.description=Запущено блокування входу\nТип блокування: {0}\nДата старту: {1}\nДата закінчення: {2}.
adminLog.categories.maintenanceStop=Вимкнення блокування на час технічних робіт
adminLog.categories.maintenanceStop.description=Блокування входу вимкнено.
adminLog.categories.maintenanceSchedule=Планування блокування на час технічних робіт
adminLog.categories.maintenanceSchedule.description=Заплановано блокування входу\nТип блокування: {0}\nДата старту: {1}\nДата закінчення: {2}.
adminLog.categories.maintenanceUnschedule=Скасування запланованого блокування на час технічних робіт
adminLog.categories.maintenanceUnschedule.description=Блокування входу скасовано.
adminLog.categories.maintenanceChangeSettings=Зміна параметрів запуску блокування під час технічних робіт
adminLog.categories.maintenanceChangeSettings.description=Зміна параметрів блокування входу\n{0}.
structuredObjectsViewsSettings.actionType=Зміна налаштувань структур
adminLog.categories.structuredObjectsViewsSettings=Змінено налаштування структур
adminLog.categories.structuredObjectsViewsSettings.add.description=Додано структуру "{0}" ({1}).
adminLog.categories.structuredObjectsViewsSettings.edit.description=Змінено структуру "{0}" ({1}):\n{2}.
adminLog.categories.structuredObjectsViewsSettings.remove.description=Видалено структуру "{0}" ({1}).
structuredObjectsViewsSettings.props.title=Назва
structuredObjectsViewsSettings.props.description=Опис
adminLog.categories.structuredObjectsViewsSettings.addItem.description=Додано елемент ''{0}'' ({1}) структури ''{2}'' ({3}).
adminLog.categories.structuredObjectsViewsSettings.removeItem.description=Видалено елемент ''{0}'' ({1}) структури ''{2}'' ({3}).
adminLog.categories.structuredObjectsViewsSettings.editItem.description=Змінено елемент ''{0}'' ({1}) структури ''{2}'' ({3}):\n{4}.
structuredObjectsViewsSettings.props.parentCode=Вкладений в елемент
structuredObjectsViewsSettings.props.classFqn=Клас об''єктів
structuredObjectsViewsSettings.props.relAttrFqn=Атрибут зв'язку
structuredObjectsViewsSettings.props.attrGroupCode=Група атрибутів
structuredObjectsViewsSettings.props.showNested=Показувати об''єкти, вкладені у вкладені
structuredObjectsViewsSettings.props.objectFilter=Обмеження вмісту елемента
otherAdminOptions.actionType=Зміна у розділі "Інші налаштування"
adminLog.categories.otherAdminOptions=Зміна у розділі "Інші налаштування"
adminLog.categories.otherAdminOptions.edit.description=Зміни параметрів у розділі "Інші налаштувкання": {0}
adminLog.categories.otherAdminOptions.edit.needCompressImage.enable=Стиснення зображень в атрибутах "Текст у форматі RTF": Увімкнено
adminLog.categories.otherAdminOptions.edit.compressionRatio=Коефіцієнт стиснення зображень в атрибутах "Текст RTF".
adminLog.categories.otherAdminOptions.edit.isUiCssTransitions.enable=Анімація елементів інтерфейсу: Увімкнено
adminLog.categories.otherAdminOptions.edit.isCompleteSetOfLicensesNotRequired.enable=Вхід до системи з неповним набором ліцензій: Увімкнено
adminLog.categories.otherAdminOptions.edit.isAsynchronousCountObjectsInTab.enable=Асинхронний підрахунок об''єктів на вкладках: Увімкнено
adminLog.categories.otherAdminOptions.edit.maxOpenedBrowserTabsPerUser=Максимальна кількість одночасно відкритих вкладок для одного користувача
adminLog.categories.otherAdminOptions.edit.systemName=Назва системи
commonSearchSettings.actionType=Зміна загальних налаштувань пошуку
adminLog.categories.commonSearchSettings=Зміна загальних налаштувань пошуку
adminLog.categories.commonSearchSettings.description=Зміна загальних параметрів пошуку: {0}
blockedStatusesSettings.actionType=Зміна налаштувань планового версіонування
adminLog.categories.blockedStatusesSettings=Зміна налаштувань статусу гілки для блокування версій об''єктів
adminLog.categories.blockedStatusesSettings.description=Зміна налаштування статусу гілки для блокування версій об''єктів: ''{0}''
blockedStatusesSettings.statesForPartialBlockingMainVersions=Статуси гілки для часткового блокування основних версій об''єктів
blockedStatusesSettings.statesForCompleteBlockingMainVersions=Статуси гілки для повного блокування основних версій об''єктів
blockedStatusesSettings.statesForPartialBlockingPlannedVersions=Статуси гілки для часткового блокування планових версій об''єктів
blockedStatusesSettings.statesForCompleteBlockingPlannedVersions=Статуси гілки для часткового блокування планових версій об''єктів
schemaOptimization.actionType=Оптимізація бази даних
adminLog.categories.schemaOptimizationStart=Запуск оптимізації бази даних
adminLog.categories.schemaOptimizationStart.description=Запущена оптимизація бази даних
omnichannel.connectionSettings.props.host=Адреса сервера
omnichannel.connectionSettings.props.port=Порт
omnichannel.connectionSettings.props.accessKey=Ключ доступу
omnichannel.connectionSettings.props.enabled=Ввімнено
omnichannel.connectionSettings.props.fileStorageAddress=Адреса файлового сховища
omnichannel.connectionSettings.props.ignoreCertificateCheck=Ігнорувати перевірку сертифіката
