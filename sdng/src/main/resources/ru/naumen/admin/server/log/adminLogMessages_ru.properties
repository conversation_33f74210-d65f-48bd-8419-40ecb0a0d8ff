ChangeCaseForm=форма смены типа
ChangeResponsibleForm=форма смены ответственного
MassEditForm=форма массового редактирования
QuickForm=форма быстрого добавления и редактирования
addForm.attributes=Атрибуты, выводимые на форму добавления
addForm.block.AttrsOnForm=Атрибуты, выводимые на форму добавления
addForm.block.VoiceCreation=Создание объекта голосом
addForm.blockInheritanceBroken=наследование блока ''{0}'' разорвано
addForm.blockInheritanceRestored=наследование блока ''{0}'' восстановлено
addForm.cases=Типы
addForm.class=Класс
addForm.isTransferDeviceGeoPosition=Передавать геопозицию устройства
addForm.profiles=Доступна профилям
addForm.showInNavigationMenu=Показывать в навигационном меню
addForm.settingsSet=Комплект
addForm.tags=Метки
addForm.title=Название
adminLiteLog.actionType=Изменение настроек облегченного интерфейса настройки
adminLocalizedLog.actionType=Изменение настроек локализации
adminLog.attributeGroup=Группа атрибутов
adminLog.attributesDescription=Показывать описание атрибутов
adminLog.categories.NDAPCompleteAudit=Аудит NDAP выполнен
adminLog.categories.NDAPCompleteAudit.description=Аудит NDAP выполнен
adminLog.categories.NDAPDeleteAudit=Удаление аудита NDAP
adminLog.categories.NDAPDeleteAudit.description=Удален аудит NDAP от {0}
adminLog.categories.NDAPEndAudit=Аудит NDAP завершен
adminLog.categories.NDAPEndAudit.description=Аудит NDAP от {0} завершен
adminLog.categories.NDAPStartAudit=Запуск аудита NDAP
adminLog.categories.NDAPStartAudit.description=Запущено выполнение аудита NDAP
adminLog.categories.NDAPStartSynchronization=Запуск синхронизации в рамках аудита NDAP
adminLog.categories.NDAPStartSynchronization.description=Запущена синхронизация в рамках аудита NDAP
adminLog.categories.NDAPEndSynchronization=Завершение синхронизации в рамках аудита NDAP
adminLog.categories.NDAPEndSynchronization.description=Завершена синхронизация в рамках аудита NDAP от {0}
adminLog.categories.adminLiteSettings=Изменение настроек облегченного интерфейса настройки
adminLog.categories.adminLiteSettings.description=Изменены параметры облегченного интерфейса настройки:\n{0}.
adminLog.categories.adminProfiles.add.description=Добавлен профиль администрирования ''{0}'' ({1}).
adminLog.categories.adminProfiles.edit.description=Изменен профиль администрирования ''{0}'' ({1}):\n{2}.
adminLog.categories.adminProfiles.remove.description=Удален профиль администрирования ''{0}'' ({1}).
adminLog.categories.adminProfiles=Изменены настройки профилей администрирования
adminLog.categories.administrationSettings.description=Совершено действие на странице "Администрирование"
adminLog.categories.administrationSettings.editEmbeddedApplication.description=Изменено встроенное приложение ''{0}'' ({1}):\n{2}.
adminLog.categories.administrationSettings.reportTemplatesSettings.addReportTemplate.description=Добавлен шаблон отчётов ''{0}'' ({1}).
adminLog.categories.administrationSettings.reportTemplatesSettings.editReportTemplate.description=Изменен шаблон отчётов ''{0}'' ({1}):\n{2}.
adminLog.categories.administrationSettings=Действие администрирования
adminLog.categories.advListSettingsEdit.edit.description=Редактирование настроек - {0}
adminLog.categories.advListSettingsEdit=Изменение настроек сложных списков
adminLog.categories.attributeAdd.description=В метаклассе ''{0}'' ({1}) создан атрибут ''{2}'' ({3}).
adminLog.categories.attributeAdd=Создание атрибута
adminLog.categories.attributeDelete.description=В метаклассе ''{0}'' ({1}) удален атрибут ''{2}'' ({3}).
adminLog.categories.attributeDelete=Удаление атрибута
adminLog.categories.attributeEdit.description=В метаклассе ''{0}'' ({1}) изменен атрибут ''{2}'' ({3}):\n{4}.
adminLog.categories.attributeEdit=Изменение атрибута
adminLog.categories.attributeGroupEdit.add.description=В метаклассе ''{0}'' ({1}) создана группа атрибутов ''{2}'' ({3}).
adminLog.categories.attributeGroupEdit.delete.description=В метаклассе ''{0}'' ({1}) удалена группа атрибутов ''{2}'' ({3}).
adminLog.categories.attributeGroupEdit.edit.description=В метаклассе ''{0}'' ({1}) изменена группа атрибутов ''{2}'' ({3}):\n{4}.
adminLog.categories.attributeGroupEdit=Изменение группы атрибутов
adminLog.categories.blockedStatusesSettings.description=Изменение настройки статусов ветки для блокирования версий объектов: ''{0}''
adminLog.categories.blockedStatusesSettings=Изменение настроек статусов ветки для блокирования версий объектов
adminLog.categories.catalogCreate.description=Создан справочник ''{0}'' ({1}).
adminLog.categories.catalogCreate=Создание справочника
adminLog.categories.catalogDelete.description=Удален справочник ''{0}'' ({1}).
adminLog.categories.catalogDelete=Удаление справочника
adminLog.categories.catalogEdit.description=Изменен справочник ''{0}'' ({1}):\n{2}.
adminLog.categories.catalogEdit=Изменение справочника
adminLog.categories.catalogItemEdit.add.description=Изменены элементы справочника ''{0}'' ({1}): добавлен элемент ''{2}'' ({3}).
adminLog.categories.catalogItemEdit.delete.description=Изменены элементы справочника ''{0}'' ({1}): удален элемент ''{2}'' ({3}).
adminLog.categories.catalogItemEdit.edit.description=Изменены элементы справочника ''{0}'' ({1}): изменен элемент ''{2}'' ({3}).
adminLog.categories.catalogItemEdit=Изменение элементов справочника
adminLog.categories.catalogReindex.description=В справочнике ''{0}'' ({1}) запущена переиндексация.
adminLog.categories.catalogReindex=Переиндексация элементов справочника
adminLog.categories.changeCaseFormEdit.description=В метаклассе ''{0}'' ({1}) изменен интерфейс формы смены типа объектов.
adminLog.categories.changeCaseFormEdit=(арх.)Изменение интерфейса формы смены типа объектов метакласса
adminLog.categories.changeUserFormsSettings.description=В метаклассе ''{0}'' ({1}) изменен пользовательской интерфейс формы.
adminLog.categories.changeUserFormsSettings.formCreated.description=В метаклассе ''{0}'' ({1}) создана пользовательская {2} ({3}).
adminLog.categories.changeUserFormsSettings.formDeleted.description=В метаклассе ''{0}'' ({1}) удалена пользовательская {2} ({3}).
adminLog.categories.changeUserFormsSettings.formEdited.description=В метаклассе ''{0}'' ({1}) изменена пользовательская {2} ({3}): {4}
adminLog.categories.changeUserFormsSettings=Настройка пользовательских форм
adminLog.categories.commentAddForm.edit.addCommentInlineFormDefaultPresentation.Compact=Компактная
adminLog.categories.commentAddForm.edit.addCommentInlineFormDefaultPresentation.Full=Полная
adminLog.categories.commentAddForm.edit.addCommentInlineFormDefaultPresentation=Вид формы добавления комментария по умолчанию
adminLog.categories.commentAddForm.edit.description=Изменены настройки формы добавления комментариев: {0}
adminLog.categories.commentAddForm.edit.isAddCommentInlineForm.enable=Inline добавление комментария: Включено
adminLog.categories.commentAddForm=Изменение настроек формы добавления комментариев
adminLog.categories.commonMentionSettings.description=Изменение общих настроек упоминаний: {0}
adminLog.categories.commonMentionSettings=Изменение общих настроек упоминаний
adminLog.categories.commonSearchSettings.description=Изменение общих настроек поиска: {0}
adminLog.categories.commonSearchSettings=Изменение общих настроек поиска
adminLog.categories.configurationSettings.addConf.description=Добавлена конфигурация импорта ''{0}'' (''{1}'').
adminLog.categories.configurationSettings.deleteConf.description=Удалена конфигурация импорта ''{0}'' (''{1}'').
adminLog.categories.configurationSettings.editConf.description=Изменена конфигурация импорта ''{0}'' (''{1}''):\n{2}.
adminLog.categories.configurationSettings.runSynchronization.description=Запущена конфигурация импорта ''{0}'' (''{1}'').
adminLog.categories.configurationSettings=Изменение параметров конфигурации
adminLog.categories.connectingOmnichannelSettings.edit.description=Изменены параметры подключения к шлюзу MessageBox: \n{0}.
adminLog.categories.connectingOmnichannelSettings=Изменение настроек подключения к шлюзу MessageBox
adminLog.categories.connectingOmniGateSettings.edit.description=Изменены параметры подключения к шлюзу OmniGate: \n{0}.
adminLog.categories.connectingOmniGateSettings=Изменение настроек подключения к шлюзу OmniGate
adminLog.categories.connectingSettings.addCon.description=Добавлено подключение ''{0}''.
adminLog.categories.connectingSettings.deleteCon.description=Удалено подключение ''{0}''.
adminLog.categories.connectingSettings.editCon.description=Изменено подключение ''{0}'':\n{1}.
adminLog.categories.connectingSettings=Изменение параметров подключения
adminLog.categories.consoleActions.description=Выполнен скрипт в консоли.
adminLog.categories.consoleActions=Выполнение скрипта в консоли
adminLog.categories.contentTemplatesSettings.add.description=Добавлен шаблон контента ''{0}'' ({1}).
adminLog.categories.contentTemplatesSettings.edit.description=Изменен шаблон контента ''{0}'' ({1}).
adminLog.categories.contentTemplatesSettings.remove.description=Удален шаблон контента ''{0}'' ({1}).
adminLog.categories.contentTemplatesSettings=Изменены настройки шаблонов контентов
adminLog.categories.correspondenceTableSettings.add.description=Добавлен элемент таблицы соответствий ''{0}'' ({1}).
adminLog.categories.correspondenceTableSettings.delete.description=Удален элемент таблицы соответствий ''{0}'' ({1}).
adminLog.categories.correspondenceTableSettings.edit.description=Изменен элемент таблицы соответствий ''{0}'' ({1}):\n{2}.
adminLog.categories.correspondenceTableSettings=Изменение настроек таблиц соответствий
adminLog.categories.ctiSettings.description=Изменена настройка телефонии (CTI):\n{0}.
adminLog.categories.ctiSettings=Изменение настроек телефонии (CTI)
adminLog.categories.customLoginForm.enabled=Включена
adminLog.categories.customLoginForm.template=''Шаблон страницы входа''
adminLog.categories.customLoginFormSettings.edit.description=Изменены параметры настраиваемой страницы входа: {0}.
adminLog.categories.customLoginFormSettings=Изменение настроек интерфейса и навигации
adminLog.categories.customizationFilesAdd.description=Добавлен файл кастомизации ''{0}'' ({1}).
adminLog.categories.customizationFilesAdd=Добавление файла кастомизации
adminLog.categories.customizationFilesDelete.description=Удален файл кастомизации ''{0}'' ({1}).
adminLog.categories.customizationFilesDelete=Удаление файла кастомизации
adminLog.categories.customizationFilesEdit.description=Изменены параметры файла кастомизации ''{0}'' ({1}):\n{2}.
adminLog.categories.customizationFilesEdit=Изменение файла кастомизации
adminLog.categories.directorySettings.add.description=В каталоге ''{0}'' ({1}) добавлена папка ''{2}'' ({3}).
adminLog.categories.directorySettings.delete.description=В каталоге ''{0}'' ({1}) удалена папка ''{2}'' ({3}).
adminLog.categories.directorySettings.edit.description=В каталоге ''{0}'' ({1}) изменена папка ''{2}'' ({3}):\n{4}.
adminLog.categories.directorySettings=Изменение настроек каталогов
adminLog.categories.dropDownLists.substitution.enabled=''Подставлять единственное значение на формах добавления''
adminLog.categories.dropDownLists.substitution.allAttrs=''Включено для всех атрибутов на формах''
adminLog.categories.dropDownLists.substitution.requiredAttrs=''Включено только для обязательных атрибутов на формах''
adminLog.categories.dropDownLists.description=Изменены настройки выпадающих списков выбора:\n{0}
adminLog.categories.dropDownLists=Выпадающие списки выбора
adminLog.categories.editAttributeSearch.description=В метаклассе ''{0}'' ({1}) изменено правило поиска объектов по атрибуту ''{2}'' ({3}).
adminLog.categories.editAttributeSearch=Изменение правил поиска объектов
adminLog.categories.editFormEdit.description=В метаклассе ''{0}'' ({1}) изменен интерфейс формы редактирования объектов.
adminLog.categories.editFormEdit=Изменение интерфейса формы редактирования объектов метакласса
adminLog.categories.embeddedApplicationSettings.addEmbeddedApplication.description=Добавлено встроенное приложение ''{0}'' ({1}).
adminLog.categories.embeddedApplicationSettings.addEmbeddedApplicationUsagePoint.description=Добавлено место использования ''{0}'' ({1}) для встроенного приложения ''{2}'' ({3}).
adminLog.categories.embeddedApplicationSettings.deleteEmbeddedApplication.description=Удалено встроенное приложение ''{0}'' ({1}).
adminLog.categories.embeddedApplicationSettings.deleteEmbeddedApplicationUsagePoint.description=Удалено место использования {0} для встроенного приложения ''{1}'' ({2}).
adminLog.categories.embeddedApplicationSettings.deleteEmbeddedApplicationUsagePoints.description=Удалены места использования {0} для встроенного приложения ''{1}'' ({2}).
adminLog.categories.embeddedApplicationSettings.editEmbeddedApplication.description=Изменено встроенное приложение ''{0}'' ({1}):\n{2}.
adminLog.categories.embeddedApplicationSettings.editEmbeddedApplicationUsagePoint.description=Изменено место использования ''{0}'' ({1}) для встроенного приложения ''{2}'' ({3}):\n{4}.
adminLog.categories.embeddedApplicationSettings=Изменение настроек встроенных приложений
adminLog.categories.escalationActionSettings.eventActionAdd.description=Добавлено действие по событию ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.escalationActionSettings.eventActionDelete.description=Удалено действие по событию ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.escalationActionSettings.eventActionEdit.description=Изменено действие по событию ''{0}'' (''{1}'') типа ''{2}'':\n{3}.
adminLog.categories.escalationActionSettings=Изменение настроек действий эскалации
adminLog.categories.escalationSchemeSettings.levelAdd.description=В схеме эскалации ''{0}'' ({1}) добавлен уровень эскалации №{2}.
adminLog.categories.escalationSchemeSettings.levelDelete.description=В схеме эскалации ''{0}'' ({1}) удален уровень эскалации №{2}.
adminLog.categories.escalationSchemeSettings.levelEdit.description=В схеме эскалации ''{0}'' ({1}) изменен уровень эскалации №{2}.
adminLog.categories.escalationSchemeSettings.schemeAdd.description=Добавлена схема эскалации ''{0}'' ({1}).
adminLog.categories.escalationSchemeSettings.schemeDelete.description=Удалена схема эскалации ''{0}'' ({1}).
adminLog.categories.escalationSchemeSettings.schemeEdit.description=Изменена схема эскалации ''{0}'' ({1}):\n{2}.
adminLog.categories.escalationSchemeSettings=Изменение настроек схем эскалации
adminLog.categories.escalationTableSettings.add.description=Добавлен элемент таблицы соответствий ''{0}'' ({1}).
adminLog.categories.escalationTableSettings.delete.description=Удален элемент таблицы соответствий ''{0}'' ({1}).
adminLog.categories.escalationTableSettings.edit.description=Изменен элемент таблицы соответствий ''{0}'' ({1}):\n{2}.
adminLog.categories.escalationTableSettings=Изменение настроек таблицы соответствий эскалации
adminLog.categories.eventActionSettings.eventActionAdd.description=Добавлено действие по событию ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.eventActionSettings.eventActionConditionAdd.description=Добавлено условие ''{2}'' действия по событию ''{0}'' (''{1}'').
adminLog.categories.eventActionSettings.eventActionConditionDelete.description=Удалено условие ''{2}'' действия по событию ''{0}'' (''{1}'').
adminLog.categories.eventActionSettings.eventActionConditionEdit.description=Изменено условие ''{2}'' действия по событию ''{0}'' (''{1}''):\n{3}.
adminLog.categories.eventActionSettings.eventActionDelete.description=Удалено действие по событию ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.eventActionSettings.eventActionEdit.description=Изменено действие по событию ''{0}'' (''{1}'') типа ''{2}'':\n{3} (локаль: "{4}").
adminLog.categories.eventActionSettings.userEventParameterAdd.description=Добавлен параметр ''{2}'' действия по событию ''{0}'' (''{1}'').
adminLog.categories.eventActionSettings.userEventParameterDelete.description=Удален параметр ''{2}'' действия по событию ''{0}'' (''{1}'').
adminLog.categories.eventActionSettings.userEventParameterEdit.description=Изменен параметр ''{2}'' действия по событию ''{0}'' (''{1}'').
adminLog.categories.eventActionSettings=Изменение настроек действий по событию
adminLog.categories.eventCleanerJobSettings.description=Изменены параметры задачи очистки лога событий: {0}
adminLog.categories.eventCleanerJobSettings=Изменение параметров задачи очистки
adminLog.categories.eventStorageRule.add.description=Добавлено правило хранения лога событий ''{0}'' ({1}).
adminLog.categories.eventStorageRule.edit.description=Изменено правило хранения лога событий ''{0}'' ({1}):\n{2}.
adminLog.categories.eventStorageRule.remove.description=Удалено правило хранения лога событий ''{0}'' ({1}).
adminLog.categories.eventStorageRule=Изменение правил хранения лога событий
adminLog.categories.exportLicense.description=Выгружен лицензионный файл.
adminLog.categories.exportLicense=Выгрузка лицензии
adminLog.categories.exportMetainfo.description=Выгружен файл метаинформации.
adminLog.categories.exportMetainfo=Выгрузка метаинформации
adminLog.categories.fastLinksSettings.add.description=Добавлено упоминание объектов ''{0}'' ({1}).
adminLog.categories.fastLinksSettings.edit.description=Изменено упоминание объектов ''{0}'' ({1}):\n{2}.
adminLog.categories.fastLinksSettings.remove.description=Удалено упоминание объектов ''{0}'' ({1}).
adminLog.categories.fastLinksSettings=Изменение настроек упоминаний объектов
adminLog.categories.filePreviewSettingsEdit.edit.description=Изменены параметры предварительного просмотра файлов: {0}
adminLog.categories.filePreviewSettingsEdit=Изменение настроек предварительного просмотра файлов
adminLog.categories.highlightingPrivateComments=Изменение настроек выделения приватных комментариев
adminLog.categories.highlightingPrivateComments.edit.description=Изменены настройки выделения приватных комментариев: {0}
adminLog.categories.highlightingPrivateComments.edit.isHighlightPrivateCommentOnForm.enable=Выделить цветным фоном поле Текст на форме: Включено
adminLog.categories.highlightingPrivateComments.edit.isHighlightPrivateCommentInList.enable=Выделить цветным фоном комментарий в списке: Включено
adminLog.categories.highlightingPrivateComments.edit.privateCommentColor=Цвет фона
adminLog.categories.importMetainfoDone.description=Загрузка метаинформации завершена за {0} сек\nВерсия приложения: {2}\nВерсия файла метаинформации: {3}\nСпособ выгрузки: {1}\nСпособ загрузки: {11}\nДата выгрузки: {4}\nКоличество предупреждений: {5}\n{6}Лог загрузки: (<a href="{7}">{8}</a>).\nПросмотреть изменения, связанные с текущей загрузкой (<a href="{9}">{10}</a>).
adminLog.categories.importMetainfoDone=Загрузка метаинформации завершена
adminLog.categories.importMetainfoStart.description=Начало загрузки метаинформации.\nПросмотреть изменения, связанные с текущей загрузкой (<a href="{0}">{1}</a>).
adminLog.categories.importMetainfoStart=Начало загрузки метаинформации
adminLog.categories.incomingMailSettings.serverAdd.description=Добавлено подключение к серверу входящей почты ''{0}''.
adminLog.categories.incomingMailSettings.serverDelete.description=Удалено подключение к серверу входящей почты ''{0}''.
adminLog.categories.incomingMailSettings.serverEdit.description=Изменено подключение к серверу входящей почты ''{0}'':\n{1}.
adminLog.categories.incomingMailSettings=Изменение параметров подключения к серверу входящей почты
adminLog.categories.interfaceSettings.contentFullscreen=Раскрытие контентов на всю страницу
adminLog.categories.interfaceSettings.contentInternalScroll=Внутренний скроллинг элементов страницы
adminLog.categories.interfaceSettings.edit.description=Изменены настройки интерфейса:\n{0}.
adminLog.categories.interfaceSettings.theme.add.description=Добавлена тема интерфейса ''{0}'' (''{1}'').
adminLog.categories.interfaceSettings.theme.delete.description=Удалена тема интерфейса ''{0}'' (''{1}'').
adminLog.categories.interfaceSettings.theme.edit.description=Изменены параметры темы интерфейса ''{0}'' (''{1}''):\n{2}.
adminLog.categories.interfaceSettings=Изменение настроек интерфейса
adminLog.categories.interfaceThemesSettings.add.description=Добавлена тема интерфейса ''{0}'' (''{1}'').
adminLog.categories.interfaceThemesSettings.delete.description=Удалена тема интерфейса ''{0}'' (''{1}'').
adminLog.categories.interfaceThemesSettings.edit.description=Изменены параметры темы интерфейса ''{0}'' (''{1}''):\n{2}.
adminLog.categories.interfaceThemesSettings=Изменение параметров темы интерфейса
adminLog.categories.jmsQueue.add.description=Создана пользовательская очередь ''{0}'' ({1}).
adminLog.categories.jmsQueue.changeLink.description=Изменена очередь ''{0}'' ({1}):\nДобавлена/разорвана связь с действием по событию ''{2}''
adminLog.categories.jmsQueue.edit.description=Изменена очередь ''{0}'' ({1}):\n{2}.
adminLog.categories.jmsQueue.remove.description=Удалена пользовательская очередь ''{0}'' ({1}).
adminLog.categories.jmsQueue=Изменены настройки очередей
adminLog.categories.listTemplatesSettings.add.description=Добавлен шаблон списка ''{0}'' ({1}).
adminLog.categories.listTemplatesSettings.edit.description=Изменен шаблон списка ''{0}'' ({1}):\n{2}.
adminLog.categories.listTemplatesSettings.edit2.description=Изменен шаблон списка ''{0}'' ({1}).
adminLog.categories.listTemplatesSettings.remove.description=Удален шаблон списка ''{0}'' ({1}).
adminLog.categories.listTemplatesSettings=Изменены настройки шаблонов списков
adminLog.categories.loadingAdditionalLibraries=Загрузка дополнительных библиотек
adminLog.categories.loadingAdditionalLibraries.description=Загружена дополнительная библиотека в виде JAR архива: {0}
adminLog.categories.mailRulesSettings.ruleAdd.description=Добавлено правило обработки входящей почты ''{0}''.
adminLog.categories.mailRulesSettings.ruleDelete.description=Удалено правило обработки входящей почты ''{0}''.
adminLog.categories.mailRulesSettings.ruleEdit.description=Изменено правило обработки входящей почты ''{0}'':\n{1}.
adminLog.categories.mailRulesSettings=Изменение правил обработки входящей почты
adminLog.categories.maintenanceChangeSettings.description=Изменены параметры блокировки входа\n{0}.
adminLog.categories.maintenanceChangeSettings=Изменение параметров запуска блокировки на время технических работ
adminLog.categories.maintenanceSchedule.description=Запланирована блокировка входа\nТип блокировки: {0}\nДата старта: {1}\nДата окончания: {2}.
adminLog.categories.maintenanceSchedule=Планирование блокировки на время технических работ
adminLog.categories.maintenanceStart.description=Запущена блокировка входа\nТип блокировки: {0}\nДата старта: {1}\nДата окончания: {2}.
adminLog.categories.maintenanceStart=Запуск блокировки на время технических работ
adminLog.categories.maintenanceStop.description=Блокировка входа выключена.
adminLog.categories.maintenanceStop=Выключение блокировки на время технических работ
adminLog.categories.maintenanceUnschedule.description=Блокировка входа отменена.
adminLog.categories.maintenanceUnschedule=Отмена запланированной блокировки на время технических работ
adminLog.categories.metaClassAdd.description=Создан метакласс ''{0}'' ({1}).
adminLog.categories.metaClassAdd=Создание метакласса
adminLog.categories.metaClassCopy.description=Создана копия метакласса ''{0}'' ({1}). Новый метакласс: ''{2}'' ({3}).
adminLog.categories.metaClassCopy=Копирование метакласса
adminLog.categories.metaClassDelete.description=Удален метакласс ''{0}'' ({1}).
adminLog.categories.metaClassDelete=Удаление метакласса
adminLog.categories.metaClassEdit.description=Изменен метакласс ''{0}'' ({1}):\n{2}
adminLog.categories.metaClassEdit=Изменение метакласса
adminLog.categories.mobileAddFormSettings.addAddForm.description=Создана форма добавления объектов {1} в мобильном приложении.
adminLog.categories.mobileAddFormSettings.copyAddForm.description=Скопирована форма добавления объектов {2} в мобильном приложении. Форма, созданная копированием: {1}.
adminLog.categories.mobileAddFormSettings.deleteAddForm.description=Удалена форма добавления объектов {1} в мобильном приложении.
adminLog.categories.mobileAddFormSettings.editAddForm.description=Изменена форма добавления объектов {1} в мобильном приложении:\n{2}.
adminLog.categories.mobileAddFormSettings=Изменение настроек форм добавления объектов в мобильном приложении
adminLog.categories.mobileBarcodeScannerSettings.description=Изменены настройки сканера штрих-кодов в мобильном приложении:\n{1}
adminLog.categories.mobileBarcodeScannerSettings=Изменение настроек сканера штрих-кодов в мобильном приложении
adminLog.categories.mobileCardSettings.addMobileContent.description=Изменена карточка объектов с кодом {0} в мобильном приложении:\nДобавлен контент {1} ({2}).
adminLog.categories.mobileCardSettings.addObjectCard.description=Создана карточка объектов с кодом {1} в мобильном приложении.
adminLog.categories.mobileCardSettings.changedAddFormOnContent.description=В контенте ''{0}'' с кодом [''{1}''] изменена форма добавления у действия ''Добавить объект'':\nФорма добавления: [''{2}''] -> [''{3}'']
adminLog.categories.mobileCardSettings.changedEditForm.description=У действия ''Редактировать'' с названием ''{0}'' в меню объекта изменена форма редактирования:\nФорма редактирования: ''{1}''-> ''{2}''
adminLog.categories.mobileCardSettings.changedEditFormOnContent.description=У действия ''Редактировать'' с названием ''{0}'' в контенте 'Параметры объекта' с кодом ''{1}'' изменена форма редактирования:\nФорма редактирования: ''{2}''-> ''{3}''
adminLog.categories.mobileCardSettings.copyObjectCard.description=Скопирована карточка объектов с кодом ''{2}'' в мобильном приложении. Карточка, созданная копированием: {1}.
adminLog.categories.mobileCardSettings.deleteObjectCard.description=Удалена карточка объектов с кодом {1} в мобильном приложении.
adminLog.categories.mobileCardSettings.editMobileContent.description=Изменена карточка объектов с кодом {0} в мобильном приложении:\nконтент {1} ({2}).
adminLog.categories.mobileCardSettings.editObjectCard.description=Изменена карточка объектов с кодом {1} в мобильном приложении:\n{2}.
adminLog.categories.mobileCardSettings=Изменение настроек карточек в мобильном приложении
adminLog.categories.mobileEditFormSettings.addEditForm.description=Создана форма редактирования объектов с кодом {1} в мобильном приложении.
adminLog.categories.mobileEditFormSettings.copyEditForm.description=Скопирована форма редактирования объектов с кодом ''{2}'' в мобильном приложении. Форма, созданная копированием: {1}.
adminLog.categories.mobileEditFormSettings.deleteEditForm.description=Удалена форма редактирования объектов с кодом {1} в мобильном приложении.
adminLog.categories.mobileEditFormSettings.editEditForm.description=Изменена форма редактирования объектов с кодом {1} в мобильном приложении:\n{2}.
adminLog.categories.mobileEditFormSettings=Изменение настроек форм редактирования объектов в мобильном приложении
adminLog.categories.mobileGeolocationSettings.description=Настройки геолокации в мобильном приложении изменились: {1}
adminLog.categories.mobileGeolocationSettings=Изменение настроек геолокации в мобильном приложении
adminLog.categories.mobileListSettings.addMobileList.description=Создан список объектов {1} в мобильном приложении.
adminLog.categories.mobileListSettings.copyMobileList.description=Скопирован список объектов {2} в мобильном приложении. Список, созданный копированием: {1}.
adminLog.categories.mobileListSettings.deleteMobileList.description=Удален список объектов {1} в мобильном приложении.
adminLog.categories.mobileListSettings.editMobileList.description=Изменен список объектов {1} в мобильном приложении:\n{2}.
adminLog.categories.mobileListSettings=Изменение настроек списков объектов в мобильном приложении
adminLog.categories.mobileNavigationSettings.addElement.description=Добавлен элемент ''{1}'' в навигационное меню мобильного приложения.
adminLog.categories.mobileNavigationSettings.deleteElement.description=Удален элемент ''{1}'' из навигационного меню мобильного приложения.
adminLog.categories.mobileNavigationSettings.editElement.description=Изменен элемент ''{1}'' в навигационном меню мобильного приложения:\n{2}.
adminLog.categories.mobileNavigationSettings=Изменение настроек навигационного меню мобильного приложения
adminLog.categories.mobileOtherSettings.description=Изменены прочие настройки в мобильном приложении: {1}
adminLog.categories.mobileOtherSettings=Изменение прочих настроек в мобильном приложении
adminLog.categories.mobileSecuritySettings.description=Изменены настройки безопасности в мобильном приложении: {1}
adminLog.categories.mobileSecuritySettings=Изменение настроек безопасности в мобильном приложении
adminLog.categories.moduleAdd.description=Добавлен модуль ''{0}''.
adminLog.categories.moduleAdd=Добавление модуля
adminLog.categories.moduleDelete.description=Удален модуль ''{0}''.
adminLog.categories.moduleDelete=Удаление модуля
adminLog.categories.moduleEdit.description=Изменен модуль ''{0}'':\n{1}.
adminLog.categories.moduleEdit=Изменение модуля
adminLog.categories.monitoringSystemSettings.description=Изменена настройка системы мониторинга:\n{0}.
adminLog.categories.monitoringSystemSettings=Изменение настроек системы мониторинга
adminLog.categories.navigationSettings.description=Изменение настроек навигации:\n{0}.
adminLog.categories.navigationSettings.homePageSetting=Изменение настроек домашней страницы
adminLog.categories.navigationSettings.homePageSetting.add.description=Добавлена домашняя страница ''{0}''.
adminLog.categories.navigationSettings.homePageSetting.delete.description=Удалена домашняя страница ''{0}''.
adminLog.categories.navigationSettings.homePageSetting.edit.description=Изменены параметры домашней страницы ''{0}'':\n{1}.
adminLog.categories.navigationSettings.leftMenuItem.add.description=Добавлен элемент левого меню ''{0}''.
adminLog.categories.navigationSettings.leftMenuItem.delete.description=Удален элемент левого меню ''{0}''.
adminLog.categories.navigationSettings.leftMenuItem.edit.description=Изменены параметры элемента левого меню ''{0}'':\n{1}.
adminLog.categories.navigationSettings.leftMenuItem=Изменение настроек левого меню
adminLog.categories.navigationSettings.quickAccessPanel.add.description=Добавлена плитка левого меню ''{0}''.
adminLog.categories.navigationSettings.quickAccessPanel.delete.description=Удалена плитка левого меню ''{0}''.
adminLog.categories.navigationSettings.quickAccessPanel.edit.description=Изменены параметры плитки левого меню ''{0}'':\n{1}.
adminLog.categories.navigationSettings.quickAccessPanel=Изменение настроек панели быстрого доступа
adminLog.categories.navigationSettings.topMenuItem.add.description=Добавлен элемент верхнего меню ''{0}''.
adminLog.categories.navigationSettings.topMenuItem.delete.description=Удален элемент верхнего меню ''{0}''.
adminLog.categories.navigationSettings.topMenuItem.edit.description=Изменены параметры элемента верхнего меню ''{0}'':\n{1}.
adminLog.categories.navigationSettings.topMenuItem=Изменение настроек верхнего меню
adminLog.categories.navigationSettings.visibility.description=Изменены настройки видимости элементов навигации\n{0}.
adminLog.categories.navigationSettings.visibility=Изменены настройки видимости элементов навигации
adminLog.categories.navigationSettings=Изменение настроек навигации
adminLog.categories.newEntryFormEdit.description=В метаклассе ''{0}'' ({1}) изменен интерфейс формы добавления объектов.
adminLog.categories.newEntryFormEdit=Изменение интерфейса формы добавления объектов метакласса
adminLog.categories.notificationLocalizationSettings.localizedEdit.description=Изменен параметр ''Локализация оповещений'': ''{0}'' -> ''{1}''.
adminLog.categories.notificationLocalizationSettings=Изменение настроек локализации оповещений
adminLog.categories.objectCardsTemplatesSettings.add.description=Добавлен шаблон карточки ''{0}'' ({1}).
adminLog.categories.objectCardsTemplatesSettings.edit.description=Изменен шаблон карточки ''{0}'' ({1}).
adminLog.categories.objectCardsTemplatesSettings.remove.description=Удален шаблон карточки ''{0}'' ({1}).
adminLog.categories.objectCardsTemplatesSettings=Изменены настройки шаблонов карточек
adminLog.categories.otherAdminOptions.edit.compressionRatio=Коэффициент сжатия изображений в атрибутах "Текст в формате RTF".
adminLog.categories.otherAdminOptions.edit.description=Изменены параметры в разделе "Прочие настройки": {0}
adminLog.categories.otherAdminOptions.edit.isAsynchronousCountObjectsInTab.enable=Асинхронный подсчет объектов на вкладках: Включено
adminLog.categories.otherAdminOptions.edit.isCompleteSetOfLicensesNotRequired.enable=Вход в систему с неполным набором лицензий: Включено
adminLog.categories.otherAdminOptions.edit.isUiCssTransitions.enable=Анимация элементов интерфейса: Включено
adminLog.categories.otherAdminOptions.edit.maxOpenedBrowserTabsPerUser=Максимальное количество одновременно открытых вкладок для одного пользователя
adminLog.categories.otherAdminOptions.edit.needCompressImage.enable=Сжатие изображений в атрибутах "Текст в формате RTF": Включено
adminLog.categories.otherAdminOptions.edit.systemName=Название системы
adminLog.categories.otherAdminOptions=Изменение в разделе "Прочие настройки"
adminLog.categories.outgoingMailSettings.paramsEdit.description=Изменены параметры отправки исходящей почты ''{0}'':\n{1}.
adminLog.categories.outgoingMailSettings.serverAdd.description=Добавлено подключение к серверу исходящей почты ''{0}''.
adminLog.categories.outgoingMailSettings.serverDelete.description=Удалено подключение к серверу исходящей почты ''{0}''.
adminLog.categories.outgoingMailSettings.serverEdit.description=Изменено подключение к серверу исходящей почты ''{0}'':\n{1}.
adminLog.categories.outgoingMailSettings=Изменение параметров подключения к серверу исходящей почты
adminLog.categories.partitioning=Секционирование таблицы метакласса
adminLog.categories.partitioning.add.description=Для класса ''{0}'' подготовлена миграция.
adminLog.categories.partitioning.createPartitions.description=Для класса ''{0}'' успешно созданы секции: {1}.
adminLog.categories.partitioning.startMigration.description=Для класса ''{0}'' начата миграция.
adminLog.categories.partitioning.migrated.description=Миграция для класса ''{0}'' успешно завершена.
adminLog.categories.partitioning.rollback.description=Для класса ''{0}'' отмена миграции завершена.
adminLog.categories.partitioning.cleanup.description=Для класса ''{0}'' очищены данные миграции.
adminLog.categories.partitioning.paused.description=Для класса ''{0}'' остановлена миграция.
adminLog.categories.partitioning.resumed.description=Для класса ''{0}'' возобновлена миграция.
adminLog.categories.partitioning.startRollback.description=Для класса ''{0}'' запущена отмена миграции.
adminLog.categories.partitioning.delete.description=Удалена секция ''{0}'' с диапазоном ''{1}''.
adminLog.categories.partitioning.detach.description=Отключена секция ''{0}'' с диапазоном ''{1}''.
adminLog.categories.partitioning.getNames.description=Для таблицы класса ''{0}'' существуют следующие секции: {1}.
adminLog.categories.plannedVersion.couldNotExcludeOriginalObject=Невозможно исключить объект {0} ({1}) из основной ветки. Исключаемый объект не имеет объекта с информацией о версии.
adminLog.categories.plannedVersion.couldNotExcludeSourceObject=Невозможно исключить версию объекта, которая является источником другого объекта!
adminLog.categories.plannedVersion.couldNotFindFileSourceObject=Файл {0} ({1}) не может быть добавлен в {2} как объект окружения, поскольку отсутствует связанный объект (из атрибута source)
adminLog.categories.postImportScriptAction.description=Выполнен скрипт постимпорта метаинформации.
adminLog.categories.postImportScriptAction=Выполнение скрипта постимпорта метаинформации.
adminLog.categories.postStateActionAdd.description=В метаклассе ''{3}'' ({4}) добавлено действие ''{0}'' при выходе из статуса ''{1}'' ({2}).
adminLog.categories.postStateActionAdd=Изменение настроек статуса
adminLog.categories.postStateActionDelete.description=В метаклассе ''{3}'' ({4}) удалено действие ''{0}'' при выходе из статуса ''{1}'' ({2}).
adminLog.categories.postStateActionDelete=Изменение настроек статуса
adminLog.categories.postStateActionEdit.description=В метаклассе ''{3}'' ({4}) изменено действие ''{0}'' при выходе из статуса ''{1}'' ({2}).
adminLog.categories.postStateActionEdit=Изменение настроек статуса
adminLog.categories.postStateConditionAdd.description=В метаклассе ''{3}'' ({4}) добавлено условие ''{0}'' при выходе из статуса ''{1}'' ({2}).
adminLog.categories.postStateConditionAdd=Изменение настроек статуса
adminLog.categories.postStateConditionDelete.description=В метаклассе ''{3}'' ({4}) удалено условие ''{0}'' при выходе из статуса ''{1}'' ({2}).
adminLog.categories.postStateConditionDelete=Изменение настроек статуса
adminLog.categories.postStateConditionEdit.description=В метаклассе ''{3}'' ({4}) изменено условие ''{0}'' при выходе из статуса ''{1}'' ({2}).
adminLog.categories.postStateConditionEdit=Изменение настроек статуса
adminLog.categories.preImportScriptAction.description=Выполнен скрипт преимпорта метаинформации.
adminLog.categories.preImportScriptAction=Выполнение скрипта преимпорта метаинформации.
adminLog.categories.preStateActionAdd.description=В метаклассе ''{3}'' ({4}) добавлено действие ''{0}'' при входе в статус ''{1}'' ({2}).
adminLog.categories.preStateActionAdd=Изменение настроек статуса
adminLog.categories.preStateActionDelete.description=В метаклассе ''{3}'' ({4}) удалено действие ''{0}'' при входе в статус ''{1}'' ({2}).
adminLog.categories.preStateActionDelete=Изменение настроек статуса
adminLog.categories.preStateActionEdit.description=В метаклассе ''{3}'' ({4}) изменено действие ''{0}'' при входе в статус ''{1}'' ({2}).
adminLog.categories.preStateActionEdit=Изменение настроек статуса
adminLog.categories.preStateConditionAdd.description=В метаклассе ''{3}'' ({4}) добавлено условие ''{0}'' при входе в статус ''{1}'' ({2}).
adminLog.categories.preStateConditionAdd=Изменение настроек статуса
adminLog.categories.preStateConditionDelete.description=В метаклассе ''{3}'' ({4}) удалено условие ''{0}'' при входе в статус ''{1}'' ({2}).
adminLog.categories.preStateConditionDelete=Изменение настроек статуса
adminLog.categories.preStateConditionEdit.description=В метаклассе ''{3}'' ({4}) изменено условие ''{0}'' при входе в статус ''{1}'' ({2}).
adminLog.categories.preStateConditionEdit=Изменение настроек статуса
adminLog.categories.processLearningAdd.description=Добавлен процесс обучения ''{0}'' ({1})
adminLog.categories.processLearningAdd=Добавление процесса обучения
adminLog.categories.processLearningDelete.description=Удален процесс обучения ''{0}'' ({1})
adminLog.categories.processLearningDelete=Удаление процесса обучения
adminLog.categories.processLearningEdit.description=Изменены параметры процесса обучения ''{0}'' ({1}):\n{2}
adminLog.categories.processLearningEdit=Изменение процесса обучения
adminLog.categories.processLearningStart.description=Запущен процесс обучения ''{0}'' ({1})
adminLog.categories.processLearningStart=Запуск процесса обучения
adminLog.categories.reindex.description=В метаклассе ''{0}'' ({1}) запущена переиндексация.
adminLog.categories.reindex=Переиндексация объектов метакласса
adminLog.categories.reportTemplatesSettings.addReportTemplate.description=Добавлен шаблон ''{0}'' ({1}).
adminLog.categories.reportTemplatesSettings.deleteReportTemplate.description=Удален шаблон ''{0}'' ({1}).
adminLog.categories.reportTemplatesSettings.editReportTemplate.description=Изменен шаблон ''{0}'' ({1}):\n{2}.
adminLog.categories.reportTemplatesSettings=Изменение настроек шаблонов отчётов и печатных форм
adminLog.categories.responsibleTransferEdit.description=В метаклассе ''{0}'' ({1}) изменена настройка матрицы передачи ответственности.
adminLog.categories.responsibleTransferEdit=Изменение настройки передачи ответственности за объекты метакласса
adminLog.categories.readOnlyClusterSettings=Привилегии на изменения в кластере
adminLog.categories.readOnlyClusterSettings.description=Модифицированы привилегии на изменения в кластере:\n{0}.
adminLog.categories.scParametersEdit.description=Изменены параметры запроса:\n{0}.
adminLog.categories.scParametersEdit=Изменение настроек параметров запроса
adminLog.categories.schedulerTasksSettings.runNow.description=Задача планировщика ''{0}'' (''{1}'') типа ''{2}'' запущена вручную по кнопке ''Выполнить сейчас''.
adminLog.categories.schedulerTasksSettings.taskAdd.description=Добавлена задача планировщика ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.schedulerTasksSettings.taskDelete.description=Удалена задача планировщика ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.schedulerTasksSettings.taskEdit.description=Изменена задача планировщика ''{0}'' (''{1}'') типа ''{2}'':\n{3}.
adminLog.categories.schedulerTasksSettings.triggerAdd.description=Добавлено расписание задачи планировщика ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.schedulerTasksSettings.triggerDelete.description=Удалено расписание задачи планировщика ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.schedulerTasksSettings.triggerEdit.description=Изменено расписание задачи планировщика ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.schedulerTasksSettings=Изменение задач планировщика
adminLog.categories.schemaOptimizationStart.description=Запущена оптимизация базы данных
adminLog.categories.schemaOptimizationStart=Запуск оптимизации базы данных
adminLog.categories.scriptAdd.description=Добавлен скрипт ''{0}''.\nНастройки, где используется скрипт:\n{1}.
adminLog.categories.scriptAdd=Добавление скрипта
adminLog.categories.scriptDelete.description=Удален скрипт ''{0}''.
adminLog.categories.scriptDelete=Удаление скрипта
adminLog.categories.scriptEdit.description=Изменен скрипт ''{0}'':\n{1}.
adminLog.categories.scriptEdit=Изменение скрипта
adminLog.categories.secGroupAndRoles.groupAdd.description=Добавлена группа пользователей ''{0}'' ({1}).
adminLog.categories.secGroupAndRoles.groupDelete.description=Удалена группа пользователей ''{0}'' ({1}).
adminLog.categories.secGroupAndRoles.groupEdit.description=Изменена группа пользователей ''{0}'' ({1}):\n{2}.
adminLog.categories.secGroupAndRoles.roleAdd.description=Добавлена роль ''{0}'' ({1}).
adminLog.categories.secGroupAndRoles.roleDelete.description=Удалена роль ''{0}'' ({1}).
adminLog.categories.secGroupAndRoles.roleEdit.description=Изменена роль ''{0}'' ({1}):\n{2}.
adminLog.categories.secGroupAndRoles=Изменение настроек групп пользователей и ролей
adminLog.categories.secPlanningModeProfileEdit.profileAdd.description=Добавлен профиль прав доступа режима планирования ''{0}'' ({1}).
adminLog.categories.secPlanningModeProfileEdit.profileDelete.description=Удален профиль прав доступа режима планирования ''{0}'' ({1}).
adminLog.categories.secPlanningModeProfileEdit.profileEdit.description=Изменен профиль прав доступа режима планирования ''{0}'' ({1}):\n{2}.
adminLog.categories.secPlanningModeProfileEdit=Изменение профилей прав доступа режима планирования
adminLog.categories.secProfileEdit.profileAdd.description=Добавлен профиль прав доступа ''{0}'' ({1}).
adminLog.categories.secProfileEdit.profileDelete.description=Удален профиль прав доступа ''{0}'' ({1}).
adminLog.categories.secProfileEdit.profileEdit.description=Изменен профиль прав доступа ''{0}'' ({1}):\n{2}.
adminLog.categories.secProfileEdit=Изменение профилей прав доступа
adminLog.categories.securityPolicyEdit.edit.description=Изменены параметры политики безопасности: {0}
adminLog.categories.securityPolicyEdit.passwordChange.description=Запущена принудительная смена паролей
adminLog.categories.securityPolicyEdit=Изменение настроек политики безопасности
adminLog.categories.setsSettings.add.description=Добавлен комплект ''{0}'' ({1}).
adminLog.categories.setsSettings.edit.description=Изменен комплект ''{0}'' ({1}):\n{2}.
adminLog.categories.setsSettings.remove.description=Удален комплект ''{0}'' ({1}).
adminLog.categories.setsSettings=Изменены настройки комплектов
adminLog.categories.smiaModelAdd.description=Добавлена модель ''{0}'' ({1}, {2})
adminLog.categories.smiaModelAdd=Добавление модели
adminLog.categories.smiaModelDelete.description=Удалена модель ''{0}'' ({1}, {2})
adminLog.categories.smiaModelDelete=Удаление модели
adminLog.categories.smiaModelEdit.description=Изменены параметры модели ''{0}'' ({1}, {2}):\n{3}
adminLog.categories.smiaModelEdit=Изменение модели
adminLog.categories.smiaModelStateChange.description=Изменился статус модели ''{0}'' ({1}, {2}):\n{3} ({4}) -> {5} ({6})
adminLog.categories.smiaModelStateChange=Смена статуса модели
adminLog.categories.structuredObjectsViewsSettings.add.description=Добавлена структура ''{0}'' ({1}).
adminLog.categories.structuredObjectsViewsSettings.addItem.description=Добавлен элемент ''{0}'' ({1}) структуры ''{2}'' ({3}).
adminLog.categories.structuredObjectsViewsSettings.edit.description=Изменена структура ''{0}'' ({1}):\n{2}.
adminLog.categories.structuredObjectsViewsSettings.editItem.description=Изменен элемент ''{0}'' ({1}) структуры ''{2}'' ({3}):\n{4}.
adminLog.categories.structuredObjectsViewsSettings.remove.description=Удалена структура ''{0}'' ({1}).
adminLog.categories.structuredObjectsViewsSettings.removeItem.description=Удален элемент ''{0}'' ({1}) структуры ''{2}'' ({3}).
adminLog.categories.structuredObjectsViewsSettings=Изменены настройки структур
adminLog.categories.styleTemplatesSettings.add.description=Добавлен шаблон стилей ''{0}'' ({1}).
adminLog.categories.styleTemplatesSettings.edit.description=Изменен шаблон стилей ''{0}'' ({1}):\n{2} (локаль: "{3}").
adminLog.categories.styleTemplatesSettings.remove.description=Удален шаблон стилей ''{0}'' ({1}).
adminLog.categories.styleTemplatesSettings=Изменены настройки шаблонов стилей
adminLog.categories.superUserAdd.description=Добавлен суперпользователь ''{0}''
adminLog.categories.superUserAdd=Добавление суперпользователя
adminLog.categories.superUserChangePassword.description=Изменен пароль для суперпользователя ''{0}''
adminLog.categories.superUserChangePassword=Изменение пароля суперпользователя
adminLog.categories.superUserChangeLogin.description=Изменен логин суперпользователя: ''{0}'' -> ''{1}''.
adminLog.categories.superUserChangeLogin=Методом API изменен логин для суперпользователя.
adminLog.categories.superUserDeleted.description=Удалён суперпользователь ''{0}''
adminLog.categories.superUserDeleted=Удаление суперпользователя
adminLog.categories.tagsSettings.add.description=Добавлена метка ''{0}'' ({1}).
adminLog.categories.tagsSettings.edit.description=Изменена метка ''{0}'' ({1}):\n{2}.
adminLog.categories.tagsSettings.remove.description=Удалена метка ''{0}'' ({1}).
adminLog.categories.tagsSettings=Изменены настройки меток
adminLog.categories.timerDefinitionSettings.timerAdd.description=Добавлен счетчик ''{0}'' ({1}).
adminLog.categories.timerDefinitionSettings.timerDelete.description=Удален счетчик ''{0}'' ({1}).
adminLog.categories.timerDefinitionSettings.timerEdit.description=Изменены параметры счетчика ''{0}'' ({1}):\n{2}.
adminLog.categories.timerDefinitionSettings=Изменение настроек счетчиков времени
adminLog.categories.transitionEdit.description=В метаклассе ''{4}'' ({5}) изменены настройки перехода из ''{0}'' ({1}) в ''{2}'' ({3}): {6}.
adminLog.categories.transitionEdit=Изменение настроек перехода
adminLog.categories.updateAccessMatrix.description=В метаклассе ''{0}'' ({1}) изменена настройка прав.
adminLog.categories.updateAccessMatrix=Изменение прав доступа к объектам
adminLog.categories.uploadCertificate.description=Загружен сертификат.
adminLog.categories.uploadCertificate=Загрузка сертификата
adminLog.categories.uploadInputmask.description=Загружен шаблон маски ввода
adminLog.categories.uploadInputmask=Добавление шаблона маски ввода
adminLog.categories.uploadLicense.description=Загружен лицензионный файл.
adminLog.categories.uploadLicense=Загрузка лицензии
adminLog.categories.uploadMetainfo.adminLiteSettings.description=Изменены настройки облегченного интерфейса.
adminLog.categories.uploadMetainfo.catalogCreate.description=Создан справочник ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.catalogEdit.description=Изменен справочник ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.changeCaseFormEdit.description=В метаклассе ''{0}'' ({1}) изменен интерфейс формы смены типа объектов.
adminLog.categories.uploadMetainfo.changeUserFormsSettings.description=В метаклассе ''{0}'' ({1}) изменен интерфейс пользовательской формы.
adminLog.categories.uploadMetainfo.configurationSettings.addConf.description=Добавлена конфигурация импорта ''{0}'' (''{1}'').
adminLog.categories.uploadMetainfo.configurationSettings.editConf.description=Изменена конфигурация импорта ''{0}'' (''{1}''):\n{2}
adminLog.categories.uploadMetainfo.editFormEdit.description=В метаклассе ''{0}'' ({1}) изменен интерфейс формы редактирования объектов.
adminLog.categories.uploadMetainfo.embeddedApplicationSettings.addEmbeddedApplication.description=Добавлено встроенное приложение ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.embeddedApplicationSettings.addEmbeddedApplicationUsagePoint.description=Добавлено место использования ''{0}'' ({1}) для встроенного приложения ''{2}'' ({3}).
adminLog.categories.uploadMetainfo.embeddedApplicationSettings.deleteEmbeddedApplicationUsagePoint.description=Удалено место использования {0} для встроенного приложения ''{1}'' ({2}).
adminLog.categories.uploadMetainfo.embeddedApplicationSettings.deleteEmbeddedApplicationUsagePoints.description=Удалены места использования {0} для встроенного приложения ''{1}'' ({2}).
adminLog.categories.uploadMetainfo.embeddedApplicationSettings.editEmbeddedApplication.description=Изменено встроенное приложение ''{0}'' ({1}):\n{2}.
adminLog.categories.uploadMetainfo.embeddedApplicationSettings.editEmbeddedApplicationUsagePoint.description=Изменено место использования ''{0}'' ({1}) для встроенного приложения ''{2}'' ({3}):\n{4}.
adminLog.categories.uploadMetainfo.escalationActionSettings.eventActionAdd.description=Добавлено действие по событию ''{0}'' типа ''{1}''.
adminLog.categories.uploadMetainfo.escalationActionSettings.eventActionEdit.description=Изменено действие по событию ''{0}'' (''{1}'') типа ''{2}'':\n{3}
adminLog.categories.uploadMetainfo.escalationSchemeSettings.schemeAdd.description=Добавлена схема эскалации ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.escalationSchemeSettings.schemeEdit.description=Изменена схема эскалации ''{0}'' ({1}):\n{2}.
adminLog.categories.uploadMetainfo.eventActionSettings.eventActionAdd.description=Добавлено действие по событию ''{0}'' (''{1}'') типа ''{2}''.
adminLog.categories.uploadMetainfo.eventActionSettings.eventActionEdit.description=Изменено действие по событию ''{0}'' (''{1}'') типа ''{2}'':\n{3}
adminLog.categories.uploadMetainfo.incomingMailSettings.serverEdit.description=Изменено подключение к серверу входящей почты ''{0}'':\n{1}.
adminLog.categories.uploadMetainfo.jmsQueue.add.description=Добавлена очередь ''{0}'' (''{1}'').
adminLog.categories.uploadMetainfo.jmsQueue.edit.description=Изменена очередь ''{0}'' (''{1}''):\n{2}
adminLog.categories.uploadMetainfo.mailRulesSettings.ruleAdd.description=Добавлено правило обработки входящей почты ''{0}''.
adminLog.categories.uploadMetainfo.mailRulesSettings.ruleEdit.description=Изменено правило обработки входящей почты ''{0}'':\n{1}.
adminLog.categories.uploadMetainfo.metaClassAdd.description=Создан метакласс ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.metaClassEdit.description=Изменен метакласс ''{0}'' ({1}):\n{2}
adminLog.categories.uploadMetainfo.monitoringSystemSettings.description=Изменена настройка системы мониторинга:\n{0}.
adminLog.categories.uploadMetainfo.newEntryFormEdit.description=В метаклассе ''{0}'' ({1}) изменен интерфейс формы добавления объектов.
adminLog.categories.uploadMetainfo.otherVersionVersionInfo=Загрузка метаинформации другой версии: '{}' != '{}'
adminLog.categories.uploadMetainfo.reportTemplatesSettings.addReportTemplate.description=Добавлен шаблон отчётов ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.reportTemplatesSettings.editReportTemplate.description=Изменен шаблон отчётов ''{0}'' ({1}):\n{2}.
adminLog.categories.uploadMetainfo.responsibleTransferEdit.description=В метаклассе ''{0}'' ({1}) изменена настройка матрицы передачи ответственности.
adminLog.categories.uploadMetainfo.responsibleTransferEdit=Изменение настройки передачи ответственности за объекты метакласса
adminLog.categories.uploadMetainfo.scParametersEdit.description=Изменены параметры запроса:\n{0}.
adminLog.categories.uploadMetainfo.schedulerTasksSettings.taskAdd.description=Добавлена задача планировщика ''{0}'' типа ''{1}''.
adminLog.categories.uploadMetainfo.schedulerTasksSettings.taskEdit.description=Изменена задача планировщика ''{0}'' типа ''{1}'':\n{2}.
adminLog.categories.uploadMetainfo.scriptAdd.description=Добавлен скрипт ''{0}'':\nСписок мест использований:\n{1}.
adminLog.categories.uploadMetainfo.scriptDelete.description=Удален скрипт ''{0}''.
adminLog.categories.uploadMetainfo.scriptEdit.description=Изменен скрипт ''{0}'':\n{1}.
adminLog.categories.uploadMetainfo.secDomain.description=Изменены настройки прав доступа для метакласса ''{0}'' ({1}):\n{2}.
adminLog.categories.uploadMetainfo.secGroupAndRoles.groupAdd.description=Добавлена группа пользователей ''{0}''.
adminLog.categories.uploadMetainfo.secGroupAndRoles.groupEdit.description=Изменена группа пользователей ''{0}''.
adminLog.categories.uploadMetainfo.secGroupAndRoles.roleAdd.description=Добавлена роль ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.secGroupAndRoles.roleEdit.description=Изменена роль ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.secPlanningModeProfileEdit.profileAdd.description=Добавлен профиль режима планирования ''{0}''.
adminLog.categories.uploadMetainfo.secPlanningModeProfileEdit.profileEdit.description=Изменен профиль режима планирования ''{0}''.
adminLog.categories.uploadMetainfo.secProfileEdit.profileAdd.description=Добавлен профиль ''{0}''.
adminLog.categories.uploadMetainfo.secProfileEdit.profileEdit.description=Изменен профиль ''{0}''.
adminLog.categories.uploadMetainfo.timerDefinitionSettings.timerAdd.description=Добавлен счетчик ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.timerDefinitionSettings.timerEdit.description=Изменен счетчик ''{0}'' ({1}).
adminLog.categories.uploadMetainfo.versionVersionInfo=Загрузка метаинформации версии '{}' от '{}'
adminLog.categories.uploadMetainfo.wfProfileFolder.description=Изменены профили связанных жизненных циклов.
adminLog.categories.uploadMetainfo.windowEdit.description=В метаклассе ''{0}'' ({1}) изменен интерфейс карточки объектов.
adminLog.categories.uploadMetainfo=Загрузка метаинформации
adminLog.categories.uploadModule.description=Загружен модуль.
adminLog.categories.uploadModule=Загрузка модуля
adminLog.categories.webSocketChannel.edit.description=Изменены настройки в блоке "Отслеживание изменений в режиме реального времени": {0}
adminLog.categories.webSocketChannel=Изменение настроек блока "Отслеживание изменений в режиме реального времени"
adminLog.categories.wfProfileSettings.wfProfileAdd.description=Добавлен профиль связанных жизненных циклов ''{0}''.
adminLog.categories.wfProfileSettings.wfProfileDelete.description=Удален профиль связанных жизненных циклов ''{0}''.
adminLog.categories.wfProfileSettings.wfProfileEdit.description=Изменен профиль связанных жизненных циклов ''{0}'':\n{1}.
adminLog.categories.wfProfileSettings=Изменение настроек профиля связанных жизненных циклов
adminLog.categories.windowEdit.description=В метаклассе ''{0}'' ({1}) изменен интерфейс карточки объектов.
adminLog.categories.windowEdit=Изменение интерфейса карточки объектов
adminLog.categories.workflowEdit.description=В метаклассе ''{0}'' ({1}) изменен жизненный цикл объектов.
adminLog.categories.workflowEdit=Изменение жизненного цикла объектов
adminLog.commentOnForm=Комментарий на форме
adminLog.fill=Заполнять
adminLog.forTransitionsInTypes=Для переходов в типы
adminLog.forTypes=Для типов
adminLog.import=Просмотреть изменения, связанные с текущей загрузкой (<a href="{0}">{1}</a>).
adminLog.importError=Ошибка: {0}\n
adminLog.lists.notSpecified=[не указано]
adminLog.mustFill=Обязательно заполнять
adminLog.no=Нет
adminLog.notFill=Не заполнять
adminLog.settingsSet=Комплект
adminLog.useAsDefault=Использовать как форму по умолчанию
adminLog.yes=Да
administrationLog.actionType=Действие администрирования
administrationSettings.actionType=Изменение настроек системы
adminlite.catalogs=Страницы интерфейса (справочники)
adminlite.enabled=Включено
adminlite.groups=Доступен группам пользователей
adminlite.processSettings=Страницы интерфейса (настройка бизнес-процессов)
adminlite.systemSettings=Страницы интерфейса (настройка системы)
adminProfiles.actionType=Изменение настроек профилей администрирования
adminProfiles.props.adminSettings=В профиле администрирования ''{0}'' ({1}) изменена настройка прав
adminProfiles.props.description=Описание
adminProfiles.props.title=Название
advListSettings.props.richTextViewMode=Очищать тексты в полях типа "Текст в формате RTF" от стилей для экономии ресурсов браузера
advListSettings.props.shareView=Создание общих видов доступно
advListSettingsEdit.actionType=Изменение настроек сложных списков
attribute.complexRelationAttrGroup=Группа атрибутов в списке
attribute.complexRelationEmployeeAttrGroup=Группа атрибутов в списке для класса Сотрудник
attribute.complexRelationOuAttrGroup=Группа атрибутов в списке для класса Отдел
attribute.complexRelationTeamAttrGroup=Группа атрибутов в списке для класса Команда
attribute.complexStructuredObjectsViewCode=Структура
attribute.dateTimeCommonRestrictions.FUTURE=в будущем
attribute.dateTimeCommonRestrictions.PAST=в прошлом
attribute.dateTimeCommonRestrictions=Значение атрибута допустимо указывать
attribute.dateTimeRestrictionAttribute=Атрибут
attribute.dateTimeRestrictionCondition.GT=больше
attribute.dateTimeRestrictionCondition.GTE=больше или равно
attribute.dateTimeRestrictionCondition.LT=меньше
attribute.dateTimeRestrictionCondition.LTE=меньше или равно
attribute.dateTimeRestrictionCondition=Условие
attribute.dateTimeRestrictionScript=Скрипт ограничения значения атрибута
attribute.dateTimeRestrictionType.ATTRIBUTE_RESTRICTION=Задать зависимость от атрибута
attribute.dateTimeRestrictionType.NO_RESTRICTION=Без ограничений
attribute.dateTimeRestrictionType.RESTRICTION_BY_SCRIPT=Ограничение скриптом
attribute.dateTimeRestrictionType=Дополнительное ограничение на ввод даты
attribute.decimalsCountRestriction=Ограничение на ввод и отображение десятичных знаков
attribute.defaultValue=Значение по умолчанию
attribute.determiner=Правило определения (код таблицы соответствий)
attribute.editPresentationCode=Представление для редактирования
attribute.exportNDAP=Доступен из системы мониторинга
attribute.generationRule=Правило генерации
attribute.hiddenWhenEmpty=Скрывать при отображении, если не заполнен
attribute.hiddenWhenNoPossibleValues=Скрывать при редактировании, если нет значений для выбора
attribute.hideArchived=Скрывать архивные объекты
attribute.intervalAvailableUnits=Единицы измерения, доступные при редактировании
attribute.isComplexRelation=Сложная форма добавления связи
attribute.isComputable=Признак вычислимости
attribute.isDefaultByScript=Признак вычислимости значения по умолчанию
attribute.isDeterminable=Признак определения по таблице соответствия
attribute.isEditOnComplexFormOnly=Редактирование только через расширенную форму
attribute.isEditable=Признак редактируемости
attribute.isEditableInLists=Признак редактируемости в списках
attribute.isFilteredByScript=Признак фильтрации значений атрибута
attribute.isHasGroupSeparator=Разделять по разрядам
attribute.isHiddenAttrCaption=Скрывать название атрибута
attribute.isNeedStoreUnits=Запоминать выбранные единицы измерения
attribute.isRequired=Признак обязательности
attribute.isRequiredInInterface=Признак обязательности для заполнения в интерфейсе
attribute.isUnique=Признак уникальности
attribute.permittedTypeFqn=Допустимые типы
attribute.quickAddForm=Форма быстрого добавления
attribute.quickEditForm=Форма быстрого редактирования
attribute.relatedAttrsToExport=Атрибуты, доступные из системы мониторинга
attribute.relatedObjectHierarchyLevel=Показывать значение атрибута
attribute.script=Скрипт значения атрибута
attribute.scriptForDefault=Скрипт значения по умолчанию
attribute.scriptForFiltration=Скрипт фильтрации значений атрибута
attribute.settingsSet=Комплект
attribute.structuredObjectsViewForBuildingTreeCode=Структура для построения дерева
attribute.tags=Метки
attribute.title=Название
attribute.viewPresentationCode=Представление для отображения
attributeGroup.attributes=Список атрибутов
attributeGroup.attributesOrder=Порядок атрибутов
attributeGroup.settingsSet=Комплект
attributeGroup.title=Название
audit.actionType=Аудит NDAP
blockedStatusesSettings.actionType=Изменение настроек планового версионирования
blockedStatusesSettings.statesForCompleteBlockingMainVersions=Статусы ветки для полной блокировки основных версий объектов
blockedStatusesSettings.statesForCompleteBlockingPlannedVersions=Статусы ветки для полной блокировки плановых версий объектов
blockedStatusesSettings.statesForPartialBlockingMainVersions=Статусы ветки для частичной блокировки основных версий объектов
blockedStatusesSettings.statesForPartialBlockingPlannedVersions=Статусы ветки для частичной блокировки плановых версий объектов
businessProcessLog.actionType=Изменение настроек бизнес-процессов
catalog.description=Описание
catalog.parent=Родитель
catalog.removalDate=Дата переноса в архив
catalog.removed=Архивный
catalog.settingsSet=Комплект
catalog.title=Название
catalogLog.actionType=Изменение настроек справочников
commentAddForm.actionType=Изменение настроек формы добавления комментариев
commonSearchSettings.actionType=Изменение общих настроек поиска
console.actionType=Действие в консоли
contentTemplatesSettings.actionType=Изменение настроек шаблонов контентов
ctiConfig.callProcessingScript=Скрипт (правило обработки звонков)
ctiConfig.ctiServerType=Тип сервера
ctiConfig.ctiTeams=Ответственные команды
ctiConfig.enable=Включен
ctiConfig.login=Логин
ctiConfig.password=Пароль
ctiConfig.serverAddress=Адрес сервера
ctiConfig.serverPort=Порт
ctiSettings.actionType=Изменение настройки CTI
customLoginForm.actionType=Изменение настроек интерфейса и навигации
customizationFiles.actionType=Изменение настроек файлов кастомизации
customizationFiles.props.description=Описание
customizationFiles.props.file=Файл
customizationFiles.props.targetPlace=Место применения
customizationFiles.props.title=Название
dropDownLists.addForm=Форма добавления
dropDownLists.changeResponsibleForm=Форма смены ответственного
dropDownLists.changeStateForm=Форма смены статуса
dropDownLists.editForm=Форма редактирования
dropDownLists.userEventActionForm=Форма параметров ПДПС
editForm.attributes=Атрибуты, выводимые на форму редактирования
editForm.cases=Типы
editForm.class=Класс
editForm.profiles=Доступна профилям
editForm.settingsSet=Комплект
editForm.tags=Метки
embeddedApplication.applicationAddress=Адрес приложения
embeddedApplication.applicationFile=Файл приложения
embeddedApplication.applicationType=Тип приложения
embeddedApplication.code=Код
embeddedApplication.description=Описание
embeddedApplication.fullscreenAllowed=Позволять раскрывать на всю страницу
embeddedApplication.initialHeight=Исходная высота приложения
embeddedApplication.mobileHeight=Высота контента в мобильном приложении
embeddedApplication.on=Включено
embeddedApplication.script=Скрипт
embeddedApplication.settingsSet=Комплект
embeddedApplication.title=Название
embeddedApplicationSettings.actionType=Изменение настроек встроенных приложений
escalationLog.actionType=Изменение настроек эскалации
escalationScheme.description=Описание
escalationScheme.isOn=Включено
escalationScheme.settingsSet=Комплект
escalationScheme.targetTypes=Объекты
escalationScheme.timerCode=Счетчик времени
escalationScheme.title=Название
eventAction.actionScript=Скрипт
eventAction.actionType=Действие
eventAction.attributes=Выполнять действие при изменении атрибутов
eventAction.contextAttributes=Атрибуты, передаваемые в контекст
eventAction.eventType=Событие
eventAction.excludeAuthor=Исключить автора действия из списка получателей
eventAction.isHtml=В формате HTML
eventAction.isOn=Включено
eventAction.isSlow=Взаимодействие с внешней системой
eventAction.isSync=Выполнять синхронно
eventAction.jmsQueue=Очередь обработки действия
eventAction.message=Текст оповещения
eventAction.messageStyle.Information=Информационное
eventAction.messageStyle.Warning=Предупреждение
eventAction.messageStyle=Стиль
eventAction.notificationScript=Скрипт
eventAction.objects=Объекты
eventAction.pushHeaderFormat=Формат заголовка уведомления
eventAction.pushMessage=Текст уведомления
eventAction.pushPosition=Расположение на экране (для уведомлений в интерфейсе)
eventAction.pushPresentationType=Способ отображения уведомления
eventAction.settingsSet=Комплект
eventAction.skipIfUserHasActiveSession=Не выполнять, если у пользователя есть активная сессия
eventAction.subject=Тема
eventAction.tags=Метки
eventAction.template=Шаблон
eventAction.title=Название
eventAction.uiAction.Autoreload=Автообновление
eventAction.uiAction.Message=Сообщение
eventAction.uiAction.MessageWithRefresh=Сообщение об изменении с кнопкой Обновить
eventAction.uiAction=Действие в веб-интерфейсе
eventAction.updateArea.AttrValues=Значения атрибутов
eventAction.updateArea.ChangedContents=Контенты с изменениями
eventAction.updateArea.WholePage=Страница
eventAction.updateArea=Область обновления
eventAction.useDefaultMessage=Использовать текст сообщения по умолчанию
eventAction.who=Кому
eventActionCondition.script=Скрипт
eventActionCondition.settingsSet=Комплект
eventActionCondition.syncVerification=Выполнять проверку синхронно
eventActionCondition.title=Название
eventCleanerJobSettingsEdit.actionType=Изменение параметров задачи очистки
eventCleanerJobSettings.batchSize=Размер пачки
eventCleanerJobSettings.allowedEndHour=Час окончания очистки
eventCleanerJobSettings.allowedStartHour=Час начала очистки
eventStorageRule.actionType=Изменение правил хранения лога событий
eventStorageRule.title=Название
eventStorageRule.events=Виды событий
eventStorageRule.classes=Объекты
eventStorageRule.storageTime=Срок хранения, дней
eventStorageRule.states=Статус
eventStorageRule.enabled=Включено
fastLinksSettings.actionType=Изменение настроек упоминаний объектов
fastLinksSettings.props.alias=Префикс для упоминания объекта
fastLinksSettings.props.attributeGroup=Группа атрибутов для сложной формы упоминания
fastLinksSettings.props.contextTypes=В контексте объектов
fastLinksSettings.props.mentionAttribute=Атрибут для формирования ссылки
fastLinksSettings.props.mentionTypes=Объекты
fastLinksSettings.props.profiles=Профили
fastLinksSettings.props.settingsSet=Комплект
fastLinksSettings.props.title=Название
filePreviewSettingsEdit.actionType=Изменение настроек предварительного просмотра файлов
highlightingPrivateComments.actionType=Изменение настроек выделения приватных комментариев 
inblundMailServer.sharedMailbox=Общий почтовый ящик
inboundMailServer.enabled=Включено
inboundMailServer.folders=Папки для сканирования
inboundMailServer.host=Сервер
inboundMailServer.password=Пароль
inboundMailServer.port=Порт
inboundMailServer.protocol=Протокол
inboundMailServer.receiveMailTask=Задача планировщика
inboundMailServer.schedulerTask=Задача планировщика
inboundMailServer.skipCertVerification=Игнорировать проверку SSL сертификата (менее безопасно)
inboundMailServer.ssl=SSL соединение
inboundMailServer.username=Логин
interfaceSettings.actionType=Изменение настроек интерфейса и навигации
interfaceSettings.allUser=Всем пользователям
interfaceSettings.byDefaultUser=Пользователям с настройкой язык по умолчанию
interfaceSettings.constantLang=Язык текстовых констант интерфейса
interfaceSettings.contentFullscreen=Раскрытие контентов на всю страницу
interfaceSettings.contentInternalScroll=Внутренний скроллинг элементов страницы
interfaceSettings.eventLocale=Язык истории изменений объекта
interfaceSettings.faviconSettings=Favicon
interfaceSettings.faviconStandard=Стандартный
interfaceSettings.installLang=Установить выбранный язык интерфейса
interfaceSettings.locale=Язык интерфейса
interfaceSettings.tabTitleSettings=Название вкладки
interfaceSettings.theme.All=Всем пользователям
interfaceSettings.theme.NewAndDefault=Пользователям с настройкой “тема по умолчанию” и новым
interfaceSettings.theme.OnlyNew=Только новым пользователям
interfaceSettings.theme.enabled=Доступна для выбора пользователями
interfaceSettings.theme.login.logo.standard=Стандартный
interfaceSettings.theme.login.logo.system=Логотип системы
interfaceSettings.theme.login.logo=Логотип окна входа
interfaceSettings.theme.logo=Логотип системы
interfaceSettings.theme.name=Название
interfaceSettings.theme.pattern=Шаблон
interfaceSettings.themeAdmin=Тема для интерфейса настройки
interfaceSettings.themeInstall=Установить выбранную тему
interfaceSettings.themeOperator=Тема для интерфейса отображения
interfaceSettings.ui2logo.dark=Логотип для темного режима
interfaceSettings.ui2logo.darkMini=Мини логотип для темного режима
interfaceSettings.ui2logo.light=Логотип
interfaceSettings.ui2logo.lightMini=Мини логотип
interfaceSettings.ui2logo.value.file=из файла ({0})
interfaceSettings.ui2logo.value.light=Логотип для светлого режима
interfaceSettings.ui2logo.value.standard=SMP
jmsQueue.actionType=Изменение настроек очередей
jmsQueue.props.description=Описание
jmsQueue.props.individualAck=Индивидуальное подтверждение обработки сообщения
jmsQueue.props.pubSubDomain=Режим доставки сообщений
jmsQueue.props.reportQueue=Очередь для построения отчетов
jmsQueue.props.threadCount=Количество потоков обработки
jmsQueue.props.title=Название
jmsQueue.props.type=Тип обрабатываемых действий
learningProcess.actionType=Изменение каталога моделей
learningProcess.props.code=Код
learningProcess.props.description=Описание
learningProcess.props.scriptDataPreparation=Скрипт подготовки данных
learningProcess.props.scriptGatheringData=Скрипт сборки данных
learningProcess.props.scriptLearningAndValidation=Скрипт обучения и тестирования.
learningProcess.props.scriptSaveData=Скрипт сохранения данных
learningProcess.props.title=Название
listTemplatesSettings.actionType=Изменение настроек шаблонов списков
listTemplatesSettings.props.settingsSet=Комплект
listTemplatesSettings.props.title=Название
mailParameters.encoding=Кодировка текста
mailParameters.feedbackAddress=E-mail адрес службы техподдержки (кому ответить "Reply to")
mailParameters.from=E-mail адрес системы (от кого "From")
mailParameters.iterationDelay=Минимальный интервал между пакетами отсылаемых писем, сек
mailParameters.messagesPerIteration=Максимальное количество писем, отправляемых в одном пакете
mailParameters.name=Имя отправителя писем из системы
mailParameters.resendDelay=Пауза между попытками отправки письма, сек
mailParameters.sendAttempts=Максимальное количество неудачных попыток отправки письма
mailParameters.sendPartial=Отсылать по частям
mailParameters.transliterate=Транслитерация заголовков писем
mailProcessorRule.enabled=Включен
mailProcessorRule.script=Скрипт
mailProcessorRule.settingsSet=Комплект
mailProcessorRule.title=Название
mailSettingsLog.actionType=Изменение настроек почты
maintenance.actionType=Изменение настроек блокировки входа на время технических работ
metaClass.allowPlannedVersion=Разрешить создание плановых версий
metaClass.defaultAgreement=Соглашение по-умолчанию
metaClass.defaultCase=Тип запроса по-умолчанию
metaClass.defaultService=Услуга по-умолчанию
metaClass.depthEnvironment=Глубина уровней окружения
metaClass.description=Описание
metaClass.objectAreNestedInto=Объекты вложены в
metaClass.removed=Архивный
metaClass.settingsSet=Комплект
metaClass.tabTitleAttribute=Использовать в названии вкладки значение атрибута
metaClass.tabTitleAttributeOverridden=Переопределить название вкладки браузера для карточки объекта
metaClass.tags=Метки
metaClass.title=Название
metaClass.workflow=Жизненный цикл
metaClassLog.actionType=Изменение настроек метаклассов
mobileBarcodeScannerSettings.availableInAttrs=Доступен в атрибутах
mobileBarcodeScannerSettings.profiles=Доступен в навигационном меню для профилей
mobileGeolocationSettings.enablesMovementAttribute=Атрибут включения режима отслеживания перемещений
mobileGeolocationSettings.frequency=Периодичность опроса (в мин)
mobileGeolocationSettings.maxVolume=Максимальный объём истории перемещений
mobileList.allowActionsWithObjects=Разрешить действия с объектами из списка
mobileList.attrChain=Связь с текущим пользователем
mobileList.attributes=Атрибуты, выводимые в список
mobileList.cases=Типы
mobileList.class=Класс
mobileList.filter=Фильтрация
mobileList.order=Сортировка
mobileList.profiles=Доступен профилям
mobileList.settingsSet=Комплект
mobileList.tags=Метки
mobileList.title=Название
mobileMenuItem.code=Код
mobileMenuItem.contentCode=Код контента со встроенным приложением
mobileMenuItem.embeddedApplication=Приложение
mobileMenuItem.linkObject=Объект, с которым работает приложение
mobileMenuItem.linkObjectAttrs=Атрибут связи с объектом работы приложения
mobileMenuItem.linkObjectCase=Тип сотрудника
mobileMenuItem.parent=Вложен в
mobileMenuItem.profiles=Профили
mobileMenuItem.settingsSet=Комплект
mobileMenuItem.tags=Метки
mobileMenuItem.title=Название
mobileMenuItem.type=Вид элемента
mobileMenuItem.value=Значение
mobileOtherSettings.direct=Предлагать открывать ссылки на объекты системы, полученные из сторонних источников, внутри мобильного приложения
mobileOtherSettings.systemName=Название системы
mobileOtherSettings.voiceFillComment=Заполнять комментарий голосом
mobileOtherSettings.voiceServicesAvailability=ОС, для которых доступно голосовое создание объектов и комментариев
mobileSecuritySettings.accessKeyLifetime=Время жизни ключа доступа в мобильном приложении (в секундах)
mobileSecuritySettings.additionalAuthentication=Обязательно запрашивать дополнительную аутентификацию по ПИН-коду или биометрии в мобильном приложении
mobileSecuritySettings.customLoginForm=Форма входа
mobileSecuritySettings.customLoginModule=Скриптовый модуль
mobileSecuritySettings.loginAttemptsCount=Количество неудачных попыток ввода ПИН-кода, после которых будут стерты все данные приложения
mobileSecuritySettings.loginType=Тип аутентификации
mobileSecuritySettings.passwordStorageTime=Время хранения пароля на клиенте (в секундах)
mobileSettings.actionType=Изменение настроек мобильного приложения
navigationSettings.actionType=Изменение настроек интерфейса и навигации
navigationSettings.breadCrumb=Навигационная цепочка ("Хлебные крошки")
navigationSettings.homePageSetting.addressLink=Адрес ссылки
navigationSettings.homePageSetting.content=Содержимое
navigationSettings.homePageSetting.profiles=Профили
navigationSettings.homePageSetting.settingsSet=Комплект
navigationSettings.homePageSetting.tags=Метки
navigationSettings.homePageSetting.title=Название
navigationSettings.homePageSetting.type=Вид домашней страницы
navigationSettings.homePageSetting=Домашняя страница
navigationSettings.leftMenuItem.abbreviation=Сокращение
navigationSettings.leftMenuItem.addressLink=Адрес ссылки
navigationSettings.leftMenuItem.enabled=Включено
navigationSettings.leftMenuItem.formatting=Форматирование
navigationSettings.leftMenuItem.icon=Иконка
navigationSettings.leftMenuItem.linkToContent=Ссылка на контент
navigationSettings.leftMenuItem.marker=Маркер
navigationSettings.leftMenuItem.objectClassToAdd=Класс объектов для добавления
navigationSettings.leftMenuItem.parent=Вложен в раздел
navigationSettings.leftMenuItem.profiles=Профили
navigationSettings.leftMenuItem.reference.pattern=Шаблон карточки
navigationSettings.leftMenuItem.settingsSet=Комплект
navigationSettings.leftMenuItem.reference.tab=Вкладка карточки
navigationSettings.leftMenuItem.tags=Метки
navigationSettings.leftMenuItem.title=Название
navigationSettings.leftMenuItem.type=Тип
navigationSettings.leftMenuItem=Левое меню
navigationSettings.quickAccessPanel.abbreviation=Сокращение
navigationSettings.quickAccessPanel.area=Область панели
navigationSettings.quickAccessPanel.enabled=Включено
navigationSettings.quickAccessPanel.hint=Текст подсказки
navigationSettings.quickAccessPanel.icon=Иконка
navigationSettings.quickAccessPanel.marker=Маркер
navigationSettings.quickAccessPanel.profiles=Профили
navigationSettings.quickAccessPanel.settingsSet=Комплект
navigationSettings.quickAccessPanel.title=Название
navigationSettings.quickAccessPanel=Плитка левого меню
navigationSettings.showAdminArea=Показывать неизменяемую область панели быстрого доступа
navigationSettings.showBreadCrumb=Показывать ''хлебные крошки''
navigationSettings.showHomePageButton=Показывать кнопку ''Сделать домашней страницей''
navigationSettings.showLeftMenu=Показывать левое меню
navigationSettings.showTopMenu=Показывать верхнее меню
navigationSettings.showUserArea=Показывать пользовательскую область панели быстрого доступа
navigationSettings.topMenuItem.content=Содержимое
navigationSettings.topMenuItem.enabled=Включено
navigationSettings.topMenuItem.innerValue=Вкладка карточки
navigationSettings.topMenuItem.newTab=Открыть в новой вкладке
navigationSettings.topMenuItem.parent=Вложен в раздел
navigationSettings.topMenuItem.settingsSet=Комплект
navigationSettings.topMenuItem.title=Название
navigationSettings.topMenuItem.type=Тип
navigationSettings.topMenuItem=Верхнее меню
objectCard.attributes=Атрибуты, выводимые на карточку
objectCard.cases=Типы
objectCard.class=Класс
objectCard.comments=Меню ''Комментарии'' доступно
objectCard.contents=Контенты
objectCard.files=Меню ''Файлы'' доступно
objectCard.objectActions=Доступные элементы управления
objectCard.objectCaption=Заголовок объекта в мобильном приложении
objectCard.profiles=Доступна профилям
objectCard.quickStateChangeAvailable=Быстрая смена статуса доступна
objectCard.sendDeviceLocationAvailable=Передавать геопозицию устройства при быстрой смене статуса
objectCard.settingsSet=Комплект
objectCard.tags=Метки
objectCard.transitions=Доступные переходы между статусами
objectCard.useAvailableStates=Использовать настройку переходов между статусами из веб-клиента
objectCardsTemplatesSettings.actionType=Изменение настроек шаблонов контентов
omnichannel.connectionSettings.actionType=Изменение настроек подключения к шлюзу MessageBox
omnichannel.connectingOmniGateSettings.actionType=Изменение настроек подключения к шлюзу OmniGate
omnichannel.connectionSettings.props.accessKey=Ключ доступа
omnichannel.connectionSettings.props.enabled=Включено
omnichannel.connectionSettings.props.fileStorageAddress=Адрес файлового хранилища
omnichannel.connectionSettings.props.host=Адрес сервера
omnichannel.connectionSettings.props.ignoreCertificateCheck=Игнорировать проверку сертификата
omnichannel.connectionSettings.props.port=Порт
onceMigrationScript.actionType=Действие скрипта преимпорта/постимпорта метаинформации.
otherAdminOptions.actionType=Изменение в разделе "Прочие настройки"
outgoinMailServer.defaultConnection=Использовать по умолчанию
outgoinMailServer.enabled=Включено
outgoinMailServer.host=Сервер
outgoinMailServer.needAuth=Аутентификация
outgoinMailServer.password=Пароль
outgoinMailServer.port=Порт
outgoinMailServer.securityProtocol=Протокол шифрования
outgoinMailServer.skipCertVerification=Игнорировать проверку SSL сертификата (менее безопасно)
outgoinMailServer.username=Логин
partition.actionType=Миграция в секционированную таблицу
reportTemplate.description=Описание
reportTemplate.file=Файл
reportTemplate.parameters=Параметры шаблона
reportTemplate.script=Скрипт
reportTemplate.settingsSet=Комплект
reportTemplate.title=Название
reportTemplatesSettings.actionType=Изменение настроек шаблонов отчётов и печатных форм
readOnlyClusterSettings.actionType=Привилегии на изменения в кластере
readOnlyClusterSettings.props.description=Пользователи с разрешением на внесение изменений
scParameters.agreementsFiltrationScript=Скрипт фильтрации соглашений при редактировании
scParameters.agsEditPrs=Представление для редактирования
scParameters.agsSetting=Соглашение/Услуга
scParameters.casesFiltrationScript=Скрипт фильтрации типов при редактировании
scParameters.isFilterAgreements=Фильтрация соглашений при редактировании
scParameters.isFilterCases=Фильтрация типов при редактировании
scParameters.isFilterServices=Фильтрация услуг при редактировании
scParameters.orderSetting=Выбирать сначала
scParameters.servicesFiltrationScript=Скрипт фильтрации услуг при редактировании
schedulerLog.actionType=Изменение настроек системы
schedulerTask.description=Описание
schedulerTask.isSaveOriginalLetter=Сохранять в системе исходное письмо после обработки
schedulerTask.maxProcessedLetterInPeriod=Максимальное количество писем, обрабатываемых за период
schedulerTask.maxReceivedLetterInPeriod=Максимальное количество писем, получаемых за период
schedulerTask.script=Скрипт
schedulerTask.settingsSet=Комплект
schedulerTask.tags=Метки
schedulerTask.title=Название
schemaOptimization.actionType=Оптимизация базы данных
script.code=Код
script.defaultCategories=Категории (предполагаемые места использования)
script.description=Описание
script.scriptBody=Тело скрипта
script.settingsSet=Комплект
script.title=Название
script.usagePoint=Настройки, где используется скрипт
scriptCatalog.actionType=Изменение каталога скриптов
scriptModule.actionType=Изменение каталога модулей
scriptModule.description=Описание
scriptModule.isSuperUserReadable=Доступен для просмотра суперпользователями
scriptModule.isSuperUserWritable=Доступен для редактирования суперпользователями
scriptModule.script=Текст
scriptModule.settingsSet=Комплект
scriptModule.version=Версия
securityGroup.employees=Сотрудники
securityGroup.ous=Отделы
securityGroup.tags=Метки
securityGroup.settingsSet=Комплект
securityGroup.teams=Команды
securityGroup.title=Название
securityPolicyEdit.actionType=Изменение настроек политики безопасности
securityProfile.groups=Группы пользователей
securityProfile.roles=Роли пользователей
securityProfile.settingsSet=Комплект
securityProfile.title=Название
securityRole.accessScript=Скрипт определения прав доступа
securityRole.hasScriptedAccess=Определить права доступа пользователя к объекту
securityRole.hasScriptedOwners=Определить список пользователей, обладающих ролью
securityRole.ownersScript=Скрипт определения списка пользователей
securityRole.settingsSet=Комплект
securityRole.title=Название
setsSettings.actionType=Изменение настроек комплектов
setsSettings.props.adminProfiles=Профили администрирования
setsSettings.props.description=Описание
setsSettings.props.title=Название
smiaModel.actionType=Изменение каталога моделей
smiaModel.props.code=Код
smiaModel.props.description=Описание
smiaModel.props.learningProcess=Процесс обучения
smiaModel.props.server=Сервер
smiaModel.props.state=Статус
smiaModel.props.title=Название
smiaModel.props.version=Версия
structuredObjectsViewsSettings.actionType=Изменение настроек структур
structuredObjectsViewsSettings.props.attrGroupCode=Группа атрибутов
structuredObjectsViewsSettings.props.classFqn=Класс объектов
structuredObjectsViewsSettings.props.defaultSort=Сортировка по умолчанию
structuredObjectsViewsSettings.props.description=Описание
structuredObjectsViewsSettings.props.objectFilter=Ограничение содержимого элемента
structuredObjectsViewsSettings.props.parentCode=Вложен в элемент
structuredObjectsViewsSettings.props.relAttrFqn=Атрибут связи
structuredObjectsViewsSettings.props.showNested=Показывать объекты, вложенные во вложенные
structuredObjectsViewsSettings.props.settingsSet=Комплект
structuredObjectsViewsSettings.props.title=Название
styleTemplatesSettings.actionType=Изменение настроек шаблонов стилей
styleTemplatesSettings.props.settingsSet=Комплект
styleTemplatesSettings.props.text=Текст шаблона
styleTemplatesSettings.props.title=Название
superUser.actionType=Изменение настроек суперпользователей
synchronization.actionType=Изменение настроек синхронизации
synchronizationConfiguration.script=Конфигурация в XML-виде
synchronizationConfiguration.settingsSet=Комплект
synchronizationConfiguration.title=Название
synchronizationConnection.authType=Тип идентификации
synchronizationConnection.connectionString=Строка подключения
synchronizationConnection.jdbcDriver=Класс реализации протокола jdbc
synchronizationConnection.login=Имя пользователя
synchronizationConnection.password=Пароль
synchronizationConnection.secProtocol=Протокол безопасности
synchronizationConnection.timeout=Таймаут подключения, мин
synchronizationConnection.title=Название
tagsSettings.actionType=Изменение настроек меток
tagsSettings.props.description=Описание
tagsSettings.props.enabled=Включена
tagsSettings.props.settingsSet=Комплект
tagsSettings.props.title=Название
timerProperties.description=Описание
timerProperties.enableRecalcOnServiceTimeChange=Разрешить пересчет временных характеристик при смене класса обслуживания объекта
timerProperties.enableResumingOnResolutionTimeChange=Разрешить возобновление счетчика из статуса “Кончился запас времени” при изменении значения промежутка времени в объекте
timerProperties.pauseCondition=Условие приостановки отсчета
timerProperties.removed=Архивный
timerProperties.resumeCondition=Условие возобновления отсчета
timerProperties.serviceTime=Класс обслуживания
timerProperties.settingsSet=Комплект
timerProperties.startCondition=Условие начала отсчета
timerProperties.statusSelected=Учитывать время в статусах
timerProperties.statusStopStates=Останавливать счетчик в статусах
timerProperties.stopCondition=Условие окончания отсчета
timerProperties.timeZone=Часовой пояс
timerProperties.title=Название
transition.changeStateWithoutOpenForm=Смена статуса без открытия формы
transition.enabled=Включён
transition.from=Текущий статус
transition.inherited=Наследование
transition.settingsSet=Комплект
transition.showAttributesDescription=Показывать описание атрибутов
transition.title=Название кнопки перехода
transition.to=Новый статус
transitionChangeOrder=изменен порядок элементов на форме смены статуса
transitionItem.comments=Комментарии
transitionItem.commentAttributeGroup=Группа атрибутов для блока добавления комментария
transitionItem.currentState=Текущий статус
transitionItem.hide=Скрывать
transitionItem.inherited=Наследование
transitionItem.newState=Новый статус
transitionItem.required=Обязательность
transitionItem.title=Название
transitionItemAdd=добавлен элемент на форму смены статуса ''{0}'' ({1})
transitionItemDelete=удален элемент с формы смены статуса ''{0}'' ({1})
transitionItemEdit=изменен элемент на форме смены статуса ''{0}'' ({1}):
transitionReset=сброшены настройки перехода
uploadMetainfo.accessMatrixChange=Изменены настройки матрицы прав
uploadMetainfo.addedAttribute=Добавлены атрибуты: {0}
uploadMetainfo.addedAttributeGroup=Добавлены группы атрибутов: {0}
uploadMetainfo.addedMarkers=Добавлены маркеры прав: {0}
uploadMetainfo.addedSearchSetting=Добавлены настройки поиска по комментариям и файлам:{0}
uploadMetainfo.addedSecRole=Добавлены роли: {0}
uploadMetainfo.addedWorkflowState=Добавлены статусы жизненного цикла: {0}
uploadMetainfo.changeTitleMetaClass=название: {0} -> {1}
uploadMetainfo.deletedAttribute=Удалены атрибуты: {0}
uploadMetainfo.deletedAttributeGroup=Удалены группы атрибутов: {0}
uploadMetainfo.deletedSearchSetting=Удалены настройки поиска по комментариям и файлам:{0}
uploadMetainfo.deletedWorkflowState=Удалены статусы жизненного цикла: {0}
uploadMetainfo.editedAttribute=Изменены атрибуты: {0}
uploadMetainfo.editedAttributeGroup=Изменены группы атрибутов: {0}
uploadMetainfo.editedMarkers=Изменены маркеры прав: {0}
uploadMetainfo.editedSearchSetting=Изменены настройки поиска по комментариям и файлам:{0}
uploadMetainfo.editedSecRole=Изменены роли: {0}
uploadMetainfo.editedWorkflowState=Изменены статусы жизненного цикла: {0}
uploadMetainfo.workflowTransitionChange=Изменен жизненный цикл объектов
usagePoint.code=Код
usagePoint.customForms=Доступные формы
usagePoint.formType=Тип формы
usagePoint.fqns=Объекты
usagePoint.title=Название
usagePoint.transitions=Переходы между статусами
usagePoint.userEventActions=Доступные пользовательские действия по событию
valueMap.defaultObject=Значение по умолчанию
valueMap.description=Описание
valueMap.linkedClass=Связанный метакласс
valueMap.linkedClasses=Связанные метаклассы
valueMap.parent=Родитель
valueMap.removalDate=Дата архивирования
valueMap.removed=Архивный
valueMap.rowSet=Набор строк
valueMap.settingsSet=Комплект
valueMap.title=Название
valueMapLog.actionType=Изменение настроек таблиц соответствий
webSocketChannel.actionType=Изменение настроек блока "Отслеживание изменений в режиме реального времени"
webSocketChannel.props.objectChangeTrackingEnabled=Отслеживать редактирование и изменение текущего объекта
wfProfile.description=Описание
wfProfile.enable=Включено
wfProfile.inheritanceAttribute=Атрибуты для наследования значений из ведущего в ведомый
wfProfile.masterClass=Ведущий
wfProfile.settingsSet=Комплект
wfProfile.slaveClass=Ведомый
wfProfile.state=Статус для разрыва связи
wfProfile.title=Название
