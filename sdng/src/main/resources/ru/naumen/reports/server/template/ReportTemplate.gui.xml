<publisher
    xmlns:guic="http://www.w3.org/2001/XMLSchema-instance"
    guic:noNamespaceSchemaLocation="../../../guic/server/schemas/guic.xsd"
    place-class="ru.naumen.reports.client.common.ReportTemplatePlace" 
	controller="ru.naumen.reports.server.template.ReportTemplateController"
    title="title"
    name="ReportTemplate">
    
    <tab name="info"
        title=":title:">
        <property-list name="info" 
            attention="info#attention"
            show-title="false">
            <button name="edit"
                title=":edit:"
                style-code="edit"
                debug-id="edit"
                form="ru/naumen/reports/server/template/EditReportTemplate.form.xml"
                permissionVisibleCode="EDIT"/>
            <button name="delete"
                title=":delete:"
                style-code="del"
                debug-id="del"
                dialog-caption=":deleteCaption:"
                dialog-message=":deleteMessage:"
                controller="ru.naumen.reports.server.template.DeleteReportTemplateActionOperationController"
                permissionVisibleCode="DELETE"/>

            <property name="title" 
                presentation="text" 
                title=":title:"
                debug-id="title" />
            <property name="description" 
                presentation="text" 
                title=":description:"
                debug-id="description" />
            <property name="code" 
                presentation="text"
                title=":code:"
                debug-id="code" />
            <property name="reportFile" 
                presentation="htmlText"
                title=":file:" 
                debug-id="reportFile" />
            <property name="script" 
                presentation="scriptComponentView" 
                title=":script:" 
                debug-id="script" />
            <property name="settingsSet"
                      presentation="htmlText"
                      title=":settingsSet:"
                      debug-id="Info.settingsSet" />
        </property-list>
    </tab>
</publisher>
