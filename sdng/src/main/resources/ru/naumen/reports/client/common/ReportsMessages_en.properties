#
#Wed Jun 05 12:08:02 YEKT 2013
creationDate=Creation date
toReportsList=to template ''{0}''
showCapitalLetter=Show
resetCapitalLetter=Reset
addReportFormTitle=Add
reportTemplates=Report templates
reportTemplatesAndPrintingFormsDescription=To use for downloading and setting report and printing form templates
reportTemplatesAreLoaded=Report templates uploaded
parameters=Parameters...
addReport=Add report
reportTemplateAdding=Add template
helpText=Report | {0}
toReportTemplateCard=to template card ''{0}''
reportTemplate=Report template
reportTemplateEditing=Edit template
saveTo=Save to
back=Back
contentParametersEditing=Edit report parameters
addTemplate=Add template
report=Report
reportPF=Report, printing form
saveReportButton=Save...
exportReportButton=Export
sendReportButton=Send
loadReportTemplates=Upload report templates
pdf=PDF
exportPDF=Export to PDF
exportXLSX=Export to XLSX
exportDOCX=Export to DOCX
exportEmail=Send via email
template=Template
editableParameters=Editable parameters
createNewReportWithCurrentParameters=Add using current parameters
resetToDefaultValues=Reset to default values
rebuildReport=Rebuild
generationInProgress=Generation is in progress. Please, wait.
buildingInProgress=In progress
error=Error
generationReportError=System Error. Report generation failed. Please, contact your support.
missingQuoteInAdditionalParametersError=An error occurred during script execution for templates: ''{0}''<br>Check that the '' (single quote) symbol is set up correctly in the script
editParamsFormScriptParamsAttention=Default values for the following parameters are computed by the script in report template: {0}.
editParamsFormSettingsParamsAttention=Default values for the following parameters are redefined in the content: {0}.
reset=Reset
reportExport=Export report ({0})
sendByMail=Sending a report to the mail ({0})
reportSendDescription=Report "{0}" in the format {1} will be sent to the address below. Dispatch will be prepared and made in order of priority.
syncExportMemoryLimitExceeded=Exceeds the maximum size of memory available for the report downloading. Report will be prepared for loading in a queue.
defferedExportMail=Please, specify your email address. Download link will be sent to this address.
defferedSendByMail=Please enter your email address (you can write comma separated email addresses).
selectedReportInstances=selected report instances
templateSelected=selected report templates
csv=CSV
docx=DOCX
html=HTML
xlsx=XLSX
