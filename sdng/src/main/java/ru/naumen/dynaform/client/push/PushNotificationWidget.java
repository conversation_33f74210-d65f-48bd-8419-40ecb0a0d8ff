package ru.naumen.dynaform.client.push;

import com.google.gwt.core.client.GWT;
import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.EventTarget;
import com.google.gwt.dom.client.Style.Unit;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.uibinder.client.UiBinder;
import com.google.gwt.uibinder.client.UiField;
import com.google.gwt.user.client.Event;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.HTMLPanel;
import com.google.gwt.user.client.ui.HasEnabled;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.content.scroll.ScrollableWidget;
import ru.naumen.core.client.content.toolbar.display.StandardFontIconWidget;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.css.PushStyle;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.id.DebugIdBuilder;

/**
 * Виджет уведомления в интерфейсе.
 * <AUTHOR>
 * @since Sep 11, 2019
 */
public class PushNotificationWidget extends Composite implements HasEnabled
{
    private static final PushNotificationWidgetUiBinder uiBinder = GWT.create(PushNotificationWidgetUiBinder.class);

    public enum ExpandDirection
    {
        Up,
        Down
    }

    interface PushNotificationWidgetUiBinder extends UiBinder<ScrollableWidget, PushNotificationWidget>
    {
    }

    private final PushStyle pushStyle = WidgetResources.INSTANCE.pushStyle();
    private final CommonMessages messages = GWT.create(CommonMessages.class);

    @UiField
    protected ScrollableWidget scrollablePanel;
    @UiField
    protected HTMLPanel notificationText;
    @UiField
    protected Widget closeButton;
    @UiField
    protected FlowPanel collapseToolPanel;
    @UiField(provided = true)
    protected Widget collapseButton;
    @UiField(provided = true)
    protected Widget expandButton;

    private boolean expanded = false;
    private boolean enabled = true;
    private HandlerRegistration nativePreviewHandlerRegistration;

    public PushNotificationWidget(ExpandDirection expandDirection)
    {
        collapseButton = createToolButton(ExpandDirection.Up == expandDirection ? IconCodes.DOWN : IconCodes.UP,
                messages.collapse());
        expandButton = createToolButton(ExpandDirection.Up == expandDirection ? IconCodes.UP : IconCodes.DOWN,
                messages.expand());
        initWidget(uiBinder.createAndBindUi(this));
        closeButton.setTitle(messages.close());
        scrollablePanel.setScrollableContent(notificationText);
        scrollablePanel.setScrollPadding(2, 2, 2, 38);
        ensureDebugIds();
    }

    public void collapse()
    {
        updateWidget(false, true);
    }

    public void expand()
    {
        updateWidget(false, false);
    }

    public void fadeIn()
    {
        updateWidget(false, isShowCollapsed());
    }

    public void fadeOut()
    {
        updateWidget(true, true);
    }

    public Widget getCloseButton()
    {
        return closeButton;
    }

    public Widget getCollapseControl()
    {
        return collapseToolPanel;
    }

    public String getHtml()
    {
        return notificationText.getElement().getInnerHTML();
    }

    @Override
    public boolean isEnabled()
    {
        return enabled;
    }

    public boolean isExpanded()
    {
        return expanded;
    }

    @Override
    public void setEnabled(boolean enabled)
    {
        this.enabled = enabled;
    }

    public void setHtml(String html)
    {
        notificationText.getElement().setInnerHTML(html); // NOPMD NSDPRD-28509 unsafe html
    }

    public void toggle()
    {
        if (expanded)
        {
            collapse();
        }
        else
        {
            expand();
        }
    }

    protected Widget createToolButton(String code, String title)
    {
        StandardFontIconWidget icon = new StandardFontIconWidget(code, false);
        icon.setTitle(title);
        return icon;
    }

    protected boolean isShowCollapsed()
    {
        return true;
    }

    @Override
    protected void onAttach()
    {
        super.onAttach();
        if (null != nativePreviewHandlerRegistration)
        {
            return;
        }
        nativePreviewHandlerRegistration = Event.addNativePreviewHandler(event ->
        {
            Event nativeEvent = Event.as(event.getNativeEvent());
            EventTarget target = nativeEvent.getEventTarget();
            if (Element.is(target) && getElement().isOrHasChild(Element.as(target)))
            {
                event.consume();
            }
        });
    }

    @Override
    protected void onDetach()
    {
        super.onDetach();
        if (null != nativePreviewHandlerRegistration)
        {
            nativePreviewHandlerRegistration.removeHandler();
            nativePreviewHandlerRegistration = null;
        }
    }

    private void ensureDebugIds()
    {
        DebugIdBuilder.ensureDebugId(this, "notification");
        DebugIdBuilder.ensureDebugId(closeButton, "closeNotification");
    }

    private void updateWidget(boolean hidden, boolean collapsed)
    {
        setStyleName(pushStyle.pushNotificationHidden(), hidden);
        setStyleName(pushStyle.pushNotificationCollapsed(), !hidden && collapsed);
        expanded = !hidden && !collapsed;
        setStyleName(pushStyle.pushNotification(), expanded);
        if (expanded)
        {
            Element contentElement = notificationText.getElement();
            if (contentElement.getScrollHeight() < 250)
            {
                contentElement.getStyle().setProperty("maxHeight", contentElement.getScrollHeight(), Unit.PX);
                contentElement.getStyle().setProperty("minHeight", contentElement.getScrollHeight(), Unit.PX);
            }
            else
            {
                contentElement.getStyle().clearProperty("maxHeight");
                contentElement.getStyle().clearProperty("minHeight");
            }
            Scheduler.get().scheduleFixedDelay(() ->
            {
                scrollablePanel.enableControls();
                return false;
            }, 200);
        }
        else
        {
            notificationText.getElement().getStyle().clearProperty("maxHeight");
            notificationText.getElement().getStyle().clearProperty("minHeight");
            scrollablePanel.disableControls();
        }
    }
}
