package ru.naumen.dynaform.client.push.status;

import static ru.naumen.metainfo.shared.eventaction.tracking.TrackingUiAction.AUTO_RELOAD;
import static ru.naumen.metainfo.shared.eventaction.tracking.TrackingUiAction.MESSAGE_WITH_REFRESH;

import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.safehtml.shared.SafeHtmlBuilder;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.Window;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.ClientUtils;
import ru.naumen.core.client.browsertabs.TabsRegistry;
import ru.naumen.core.client.common.CommonMessages;
import ru.naumen.core.client.mvp.BasicPresenter;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.client.widgets.WindowLayoutHolder;
import ru.naumen.core.shared.changetracking.ChangeEventDetails;
import ru.naumen.core.shared.changetracking.ChangeTrackingMessage;
import ru.naumen.core.shared.changetracking.CloseFormEventDetails;
import ru.naumen.dynaform.client.changetracking.ChangeTrackingMessageFormatter;
import ru.naumen.dynaform.client.changetracking.ChangeTrackingUpdateService;
import ru.naumen.dynaform.client.changetracking.PageTitleChangeMarkerController;
import ru.naumen.dynaform.client.changetracking.events.ConfirmPageUpdateEvent;
import ru.naumen.dynaform.client.changetracking.events.ConfirmPageUpdateHandler;
import ru.naumen.dynaform.client.changetracking.events.ReceiveMessageEvent;
import ru.naumen.dynaform.client.changetracking.events.ReceiveMessageHandler;
import ru.naumen.dynaform.client.changetracking.events.StopListenerEvent;
import ru.naumen.dynaform.client.changetracking.events.StopListenerHandler;
import ru.naumen.metainfo.shared.eventaction.tracking.PageRefreshArea;
import ru.naumen.metainfo.shared.eventaction.tracking.TrackingUiAction;

/**
 * Представление панели состояния для отображения сообщений об изменениях.
 * <AUTHOR>
 * @since Apr 22, 2022
 */
public class StatusPanelPresenter extends BasicPresenter<StatusPanelDisplay> implements ReceiveMessageHandler,
        StopListenerHandler, ConfirmPageUpdateHandler
{
    private static final int MESSAGE_SHOW_TIME = 7_000;

    @Inject
    private WidgetResources resources;
    @Inject
    private CommonMessages commonMessages;
    @Inject
    private StatusPanelMessages messages;
    @Inject
    private ChangeTrackingMessageFormatter formatter;
    @Inject
    private ChangeTrackingUpdateService updateService;
    @Inject
    private TabsRegistry tabsRegistry;
    @Inject
    private PageTitleChangeMarkerController changeMarkerController;

    private boolean aggregate = false;
    private final Map<String, ChangeTrackingMessage> messageLog = new LinkedHashMap<>();
    private final Map<String, HandlerRegistration> logRefreshHandlers = new HashMap<>();
    private static final WindowLayoutHolder windowLayoutHolder = WindowLayoutHolder.INSTANCE;
    private final Timer aggregateTimer = new Timer()
    {
        @Override
        public void run()
        {
            showAggregateMessage();
        }
    };

    private class LogRefreshClickHandler implements ClickHandler
    {
        private final ChangeTrackingMessage message;

        private LogRefreshClickHandler(ChangeTrackingMessage message)
        {
            this.message = message;
        }

        @Override
        public void onClick(ClickEvent event)
        {
            message.setShouldModifyPage(true);
            updateService.startUpdate(message);
        }
    }

    @Inject
    public StatusPanelPresenter(StatusPanelDisplay display, EventBus eventBus)
    {
        super(display, eventBus);
    }

    @Override
    public void onListenerStopped(StopListenerEvent event)
    {
        reset();
    }

    @Override
    public void onMessageReceived(ReceiveMessageEvent event)
    {
        aggregateTimer.cancel();

        ChangeTrackingMessage receivedMessage = event.getMessage();
        String messageId = StringUtilities.toNonNullString(receivedMessage.getUuid());

        if (!CloseFormEventDetails.TYPE_ID.equals(receivedMessage.getEventDetails().getTypeId()))
        {
            TrackingUiAction uiAction = receivedMessage.getUiAction();
            String actionTitle = getActionTitle(uiAction);
            String formattedMessage = formatter.format(receivedMessage);
            if (!tabsRegistry.isCurrentTabVisible())
            {
                changeMarkerController.markAsEdited();
            }

            messageLog.put(messageId, receivedMessage);
            HandlerRegistration handler = getDisplay().addLogEntry(messageId, formattedMessage, actionTitle,
                    null == actionTitle ? null : new LogRefreshClickHandler(receivedMessage));
            if (handler != null)
            {
                logRefreshHandlers.put(messageId, handler);
            }
            if (AUTO_RELOAD.equals(uiAction))
            {
                updateService.startUpdate(receivedMessage);
            }
        }
        else
        {
            deleteLogEntry(messageId);
        }

        refreshStatusPanel(receivedMessage);
    }

    @Override
    public void onPageUpdateConfirmed(ConfirmPageUpdateEvent event)
    {
        aggregateTimer.cancel();
        ChangeTrackingMessage message = event.getMessage();
        String messageUuid = message.getUuid();
        if (messageUuid == null || !aggregate && message.equals(getLastMessage()))
        {
            reset();
        }
        else
        {
            deleteLogEntry(messageUuid);
            refreshStatusPanel(null);
        }
    }

    @Override
    public void refreshDisplay()
    {
        super.refreshDisplay();
        getDisplay().refresh();
    }

    public void reset()
    {
        logRefreshHandlers.values().forEach(HandlerRegistration::removeHandler);
        logRefreshHandlers.clear();
        aggregateTimer.cancel();
        getDisplay().clearLog();
        getDisplay().setLogVisible(false);
        getDisplay().asWidget().setVisible(false);
        messageLog.clear();
        windowLayoutHolder.setStatusPanelVisible(false);
    }

    @Override
    protected void onBind()
    {
        resources.statusPanel().ensureInjected();
        reset();
        registerHandler(eventBus.addHandler(ReceiveMessageEvent.TYPE, this));
        registerHandler(eventBus.addHandler(StopListenerEvent.TYPE, this));
        registerHandler(eventBus.addHandler(ConfirmPageUpdateEvent.TYPE, this));
        registerHandler(getDisplay().addCloseHandler(event -> reset()));
        registerHandler(Window.addResizeHandler(event -> getDisplay().refresh()));
        getDisplay().addToggleHandler(event ->
        {
            if (!getDisplay().isExpandable())
            {
                return;
            }
            if (aggregate)
            {
                getDisplay().setLogVisible(!getDisplay().isLogVisible());
            }
            getDisplay().toggle();
        }).forEach(this::registerHandler);
        registerHandler(getDisplay().addMessageActionHandler(event ->
        {
            if (aggregate)
            {
                ChangeTrackingMessage reloadPageMessage = new ChangeTrackingMessage();
                reloadPageMessage.setUuid(null);
                reloadPageMessage.setRefreshArea(PageRefreshArea.WHOLE_PAGE);
                reloadPageMessage.setShouldModifyPage(true);
                updateService.startUpdate(reloadPageMessage);
            }
            else
            {
                ChangeTrackingMessage lastMessage = getLastMessage();
                if (null != lastMessage)
                {
                    lastMessage.setShouldModifyPage(true);
                    updateService.startUpdate(lastMessage);
                }
            }
        }));
        refreshDisplay();
    }

    @Override
    protected void onUnbind()
    {
        reset();
        super.onUnbind();
    }

    private void deleteLogEntry(String messageId)
    {
        messageLog.remove(messageId);
        HandlerRegistration handler = logRefreshHandlers.remove(messageId);
        if (handler != null)
        {
            handler.removeHandler();
        }
        getDisplay().deleteLogEntry(messageId);
    }

    private String formatAggregateChangeMessage()
    {
        ChangeTrackingMessage lastMessage = getLastMessage();
        String objectTitle = null == lastMessage ? StringUtilities.EMPTY : lastMessage.getObjectTitle();
        return new SafeHtmlBuilder()
                .appendEscaped(messages.aggregatedChanges(objectTitle))
                .append(' ')
                .appendEscaped(commonMessages.more()).toSafeHtml().asString();
    }

    private String formatAggregateSessionMessage()
    {
        if (messageLog.isEmpty())
        {
            return StringUtilities.EMPTY;
        }
        else
        {
            String authors = messageLog.values().stream()
                    .sorted(Comparator.comparing(message -> StringUtilities.toNonNullString(message.getAuthorName())))
                    .map(formatter::formatMessageAuthor)
                    .map(SafeHtml::asString)
                    .distinct()
                    .collect(Collectors.joining(", "));
            return messages.aggregatedFormEvents(authors);
        }
    }

    @Nullable
    private ChangeTrackingMessage getLastMessage()
    {
        return messageLog.isEmpty() ? null :
                messageLog.values().stream().skip(messageLog.size() - 1L).findFirst().orElse(null);
    }

    private String getStyleName(ChangeTrackingMessage message)
    {
        switch (message.getMessageStyle())
        {
            case Information:
                return resources.statusPanel().infoMessage();
            case Warning:
            default:
                return resources.statusPanel().warningMessage();
        }
    }

    private void refreshStatusPanel(@Nullable ChangeTrackingMessage receivedMessage)
    {
        if (messageLog.isEmpty())
        {
            reset();
            return;
        }
        if (messageLog.size() == 1)
        {
            getDisplay().setLogVisible(false);
        }

        getDisplay().asWidget().setVisible(true);
        windowLayoutHolder.setStatusPanelVisible(true);

        boolean isChangeMessage = null != receivedMessage
                                  && ChangeEventDetails.TYPE_ID.equals(receivedMessage.getEventDetails().getTypeId());
        if (isChangeMessage && !getDisplay().isLogVisible() || messageLog.size() == 1)
        {
            ChangeTrackingMessage lastMessage = Objects.requireNonNull(getLastMessage());
            String actionTitle = getActionTitle(lastMessage.getUiAction());
            String formattedMessage = formatter.format(lastMessage);
            aggregate = false;
            getDisplay().updateMessage(formattedMessage, getStyleName(lastMessage), actionTitle, false);
            if (messageLog.size() > 1)
            {
                aggregateTimer.schedule(MESSAGE_SHOW_TIME);
            }
        }
        else if (messageLog.size() > 1)
        {
            showAggregateMessage();
        }
    }

    private void showAggregateMessage()
    {
        aggregate = true;
        Collection<ChangeTrackingMessage> changeTrackingMessages = messageLog.values();
        boolean hasAnyChangeMessage = changeTrackingMessages.stream()
                .anyMatch(message -> ChangeEventDetails.TYPE_ID.equals(message.getEventDetails().getTypeId()));
        if (hasAnyChangeMessage && changeTrackingMessages.stream()
                .anyMatch(message -> MESSAGE_WITH_REFRESH.equals(message.getUiAction())))
        {
            getDisplay().updateMessage(formatAggregateChangeMessage(), resources.statusPanel().warningMessage(),
                    commonMessages.refreshAll(), true);
        }
        else
        {
            String message = hasAnyChangeMessage ? formatAggregateChangeMessage() : formatAggregateSessionMessage();
            getDisplay().updateMessage(message, resources.statusPanel().infoMessage(), null,
                    true);
        }
    }

    @Nullable
    private String getActionTitle(TrackingUiAction uiAction)
    {
        String actionTitle = MESSAGE_WITH_REFRESH.equals(uiAction) ? commonMessages.refresh() : null;
        if (AUTO_RELOAD.equals(uiAction))
        {
            actionTitle = ClientUtils.isInlineFormOperator() ? null : commonMessages.show();
        }
        return actionTitle;
    }
}
