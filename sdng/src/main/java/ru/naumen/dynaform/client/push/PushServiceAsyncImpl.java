package ru.naumen.dynaform.client.push;

import java.util.ArrayList;
import java.util.List;

import jakarta.inject.Singleton;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.Inject;

import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.common.client.utils.SimpleResultCallbackDecorator;
import ru.naumen.core.shared.Constants.PushStates;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.push.PushDto;
import ru.naumen.metainfo.shared.dispatch2.push.ChangeNotificationsStateAction;
import ru.naumen.metainfo.shared.dispatch2.push.GetListOfNotificationsByUuidsAction;

/**
 * Асинхронный сервис для работы со всплывающими уведомлениями на клиенте
 *
 * <AUTHOR>
 * @since 11.11.15
 */
@Singleton
public class PushServiceAsyncImpl implements PushServiceAsync
{
    @Inject
    protected DispatchAsync dispatch;

    @Override
    public void getPushMessagesByUuids(List<String> pushUuids, AsyncCallback<List<PushDto>> callback)
    {
        dispatch.execute(new GetListOfNotificationsByUuidsAction(pushUuids),
                new SimpleResultCallbackDecorator<>(callback));
    }

    @Override
    public void markPushMessagesAsRead(List<String> pushUuids, AsyncCallback<List<DtObject>> callback)
    {
        if (pushUuids.isEmpty())
        {
            callback.onSuccess(new ArrayList<>());
        }
        dispatch.execute(new ChangeNotificationsStateAction(pushUuids, PushStates.READ_BY_USER),
                new SimpleResultCallbackDecorator<>(callback));
    }

    @Override
    public void markPushMessagesAsUnread(List<String> pushUuids, AsyncCallback<List<DtObject>> callback)
    {
        if (pushUuids.isEmpty())
        {
            callback.onSuccess(new ArrayList<>());
        }
        dispatch.execute(new ChangeNotificationsStateAction(pushUuids, PushStates.DELIVERED),
                new SimpleResultCallbackDecorator<>(callback));
    }
}
