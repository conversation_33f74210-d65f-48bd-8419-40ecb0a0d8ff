package ru.naumen.dynaform.client.push;

import static ru.naumen.core.shared.Constants.Push.LOCAL_STORAGE_PUSH_UUIDS_KEY;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.safehtml.shared.SafeHtml;
import com.google.gwt.storage.client.Storage;
import com.google.gwt.user.client.ui.RootPanel;
import com.google.inject.Singleton;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import net.customware.gwt.dispatch.client.DispatchAsync;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.NativeStorageEvent;
import ru.naumen.core.client.SecurityHelper;
import ru.naumen.core.client.StorageType;
import ru.naumen.core.client.attr.presentation.PresentationContext;
import ru.naumen.core.client.attr.presentation.PresentationFactories;
import ru.naumen.core.client.browsertabs.TabsRegistry;
import ru.naumen.core.client.browsertabs.TabsRegistry.VisibilityChangeHandler;
import ru.naumen.core.client.comet.CometServiceAsync;
import ru.naumen.core.client.events.UserChangedEvent;
import ru.naumen.core.client.events.UserChangedEventHandler;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.client.theme.ThemeLogos;
import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.core.shared.comet.events.PushMessageHandler;
import ru.naumen.core.shared.comet.events.ShowPushEvent;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.push.PushDto;
import ru.naumen.dynaform.client.push.NotificationArea.Position;
import ru.naumen.dynaform.client.push.ReadPushEvent.Source;
import ru.naumen.dynaform.client.push.log.NotificationLogSettingsHelper;
import ru.naumen.dynaform.client.push.status.StatusPanelPresenter;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.dispatch2.push.SetNeedClearPushInLocalStorageAction;
import ru.naumen.metainfo.shared.eventaction.push.PushPosition;
import ru.naumen.metainfo.shared.eventaction.push.PushPresentationType;

/**
 * Сервис, обрабатывающий всплывающие уведомления в интерфейсе оператора.
 *
 * Уведомления должны отображаться только на видимых вкладках. Видимость вкладки отслеживается через Visibility API.
 * Уведомления приходят через Comet. Если в браузере несколько вкладок с приложением, принимать comet сообщения
 * будет только одна вкладка. Передача уведомлений другим вкладкам организована через local storage браузера.
 * Локальный кэш уведомлений всех вкладок синхронизируется с содержимым local storage через подписку на изменение
 * последнего.
 * Таким же образом отслеживается закрытие уведомления в любой из вкладок - все остальные вкладки тоже закрывают его. 
 *
 * <AUTHOR>
 * @since 12.11.15
 */
@Singleton
public class LocalPushService implements PushMessageHandler, UserChangedEventHandler, VisibilityChangeHandler,
        ClosePushHandler, ReadPushHandler, DropPushHandler, ExpandPushHandler
{

    /**
     * Идентификаторы уже отображенных уведомлений
     */
    private final NotificationArea top;
    private final NotificationArea bottom;
    /**
     * Локальный кэш уведомлений
     */
    private final LinkedHashMap<String, PushDto> pushMessages = new LinkedHashMap<>();
    private final List<HandlerRegistration> handlerRegistrations = new ArrayList<>();
    /**
     * Логотип входа на страницу SD. Нужен для уведомления поверх браузера
     */
    private String logoUri;

    @Inject
    private PushServiceAsync pushService;
    @Inject
    private CometServiceAsync comet;
    @Inject
    private TabsRegistry tabsRegistry;
    @Inject
    private SecurityHelper securityHelper;
    @Inject
    private EventBus eventBus;
    @Inject
    @Named(StorageType.LocalStorage)
    private Storage localStorage;
    @Inject
    private PresentationFactories presentationFactories;
    @Inject
    private LocalPushGateway pushGateway;
    @Inject
    private ThemeLogos themeLogos;
    @Inject
    private StatusPanelPresenter statusPanelPresenter;
    @Inject
    private DispatchAsync dispatch;

    public LocalPushService()
    {
        boolean isLogEnabled = NotificationLogSettingsHelper.isLogEnabled();
        top = new NotificationArea(Position.Top, isLogEnabled);
        bottom = new NotificationArea(Position.Bottom, isLogEnabled);
        top.addStyleName(WidgetResources.INSTANCE.header().subHeaderPopup());
    }

    /**
     * Событие вызывается после изменения какого-либо значения в local storage браузера
     * @param event измененный параметр local storage браузера
     */
    public void onLocalStorageChanged(NativeStorageEvent event)
    {
        // Мы заинтересованы в обновлении списка идентификаторов уведомлений
        if (LOCAL_STORAGE_PUSH_UUIDS_KEY.equals(event.getKey()) && tabsRegistry.isCurrentTabVisible())
        {
            synchronizePushMessagesWithLocalStorage();
        }
    }

    @Override
    public void onPushClosed(ClosePushEvent event)
    {
        markAsRead(event.getNotificationId());
    }

    @Override
    public void onPushDropped(DropPushEvent event)
    {
        if (null != pushMessages.remove(event.getNotificationId()))
        {
            updateLocalStoragePushMessages();
        }
    }

    @Override
    public void onPushExpanded(ExpandPushEvent event)
    {
        markAsRead(event.getNotificationId());
    }

    @Override
    public void onPushRead(ReadPushEvent event)
    {
        if (Source.Popup == event.getEventSource())
        {
            return;
        }
        top.removeNotification(event.getPush().getUUID());
        bottom.removeNotification(event.getPush().getUUID());
    }

    @Override
    public void onShowPush(ShowPushEvent event)
    {
        final boolean showInternalPush;
        final boolean showBrowserPush;

        PushPresentationType pushPresentationType = PushPresentationType
                .valueOf(event.getPushDto().getPushPresentationType());

        switch (pushPresentationType)
        {
            case AlwaysInTheInterface:
                showInternalPush = true;
                showBrowserPush = tabsRegistry.getVisibleTabs().isEmpty();
                break;
            case InterfaceAndBrowserNotices:
                showInternalPush = true;
                showBrowserPush = true;
                break;
            case OnlyExternalPush:
                showInternalPush = false;
                showBrowserPush = true;
                break;
            case OnlyInTheInterface:
            default:
                showInternalPush = true;
                showBrowserPush = false;
                break;
        }

        if (showInternalPush)
        {
            if (tabsRegistry.isCurrentTabVisible())
            {
                pushMessages.put(event.getPushDto().getPushUuid(), event.getPushDto());
                updateLocalStoragePushMessages();
                showInternalNotification(event.getPushDto());
            }
            else
            {
                addNotificationToLocalStorage(event.getPushDto().getPushUuid());
            }
        }

        if (showBrowserPush)
        {
            // Дополнительно показываем уведомление поверх браузера для привлечения внимания пользователя.
            if (logoUri == null)
            {
                String logoUuid = securityHelper.getCurrentUser().getLoginLogo();
                logoUri = StringUtilities.isEmpty(logoUuid)
                        ? themeLogos.getStandartCurrentThemeLogo().asString()
                        : "./images/logo?uuid=" + logoUuid;
            }

            String notificationText = event.getPushDto().getPushPlainMessage();
            showBrowserNotification(notificationText, logoUri, event.getPushDto().getBrowserNoticeLink(),
                    showInternalPush ? null : event.getPushDto().getPushUuid());
        }
    }

    @Override
    public void onUserChanged(UserChangedEvent event)
    {
        // После смены пользователя необходимо очистить локальный список уведомлений и список из local storage
        pushMessages.clear();
        localStorage.removeItem(LOCAL_STORAGE_PUSH_UUIDS_KEY);
    }

    @Override
    public void setInvisible()
    {
    }

    @Override
    public void setVisible()
    {
        synchronizePushMessagesWithLocalStorage();
    }

    /**
     * Отображение всплывающего уведомления поверх браузера
     * @param notificationText текст уведомления
     * @param logoUri иконка для показа в уведомлении
     * @param link ссылка для перехода по клику на уведомление
     * @param pushUuidToRead идентификатор уведомления, которое необходимо отметить как прочитанное по клику на
     *                       уведомление
     */
    public native void showBrowserNotification(String notificationText, String logoUri, String link,
            String pushUuidToRead)
    /*-{
        var that = this;
        if (!("Notification" in window)) {
            // This browser does not support desktop notification
            return;
        }

        var showNotificationImpl = function(permission) {
            if ("granted" !== permission) {
                return;
            }

            if (notificationText.length > 180)
            {
                notificationText = notificationText.substring(0, 180) + '...';
            }

            var notification;
            try {
                notification = new Notification($wnd.productName, {
                        icon: logoUri,
                        body: notificationText
                    });
            } catch (e) {
                // Edge does not support data-URL in notification icon
                notification = new Notification($wnd.productName, {
                        body: notificationText
                    });
            }

            notification.onclick = function () {
                $wnd.focus();
                <EMAIL>::handleBrowserPushClick(*)(link, pushUuidToRead);
                link = null;
                pushUuidToRead = null;
                this.close();
            };

            notification.onclose = function () {
                <EMAIL>::handleBrowserPushClick(*)(null, pushUuidToRead);
                link = null;
                pushUuidToRead = null;
            };
        }

        if (Notification.permission === "granted") {
            showNotificationImpl("granted");
        } else if (Notification.permission !== 'denied') {
            Notification.requestPermission(showNotificationImpl);
        }
    }-*/;

    public void start()
    {
        //При инициализации сбрасываем флаг "необходимо очистить push_uids" в LocalStorage
        dispatch.execute(new SetNeedClearPushInLocalStorageAction(false), new BasicCallback<EmptyResult>());
        addNativeLocalStorageUpdateHandler(this);

        comet.start();
        registerHandler(tabsRegistry.registerVisibilityChangedHandler(this));
        registerHandler(pushGateway.addShowPushHandler(this));
        registerHandler(eventBus.addHandler(UserChangedEvent.TYPE, this));
        registerHandler(eventBus.addHandler(ReadPushEvent.TYPE, this));

        statusPanelPresenter.bind();
        bottom.addExternalQueue(statusPanelPresenter.getDisplay());

        top.addCloseHandler(this).forEach(this::registerHandler);
        bottom.addCloseHandler(this).forEach(this::registerHandler);
        top.addDropHandler(this).forEach(this::registerHandler);
        bottom.addDropHandler(this).forEach(this::registerHandler);
        top.addExpandHandler(this).forEach(this::registerHandler);
        bottom.addExpandHandler(this).forEach(this::registerHandler);

        RootPanel.getBody().add(top);
        RootPanel.getBody().add(bottom);

        synchronizePushMessagesWithLocalStorage();
    }

    public void stop()
    {
        statusPanelPresenter.unbind();
        comet.stop();
        handlerRegistrations.forEach(HandlerRegistration::removeHandler);
        handlerRegistrations.clear();

        top.removeFromParent();
        bottom.removeFromParent();
    }

    //@formatter:off
    private native void addNativeLocalStorageUpdateHandler(LocalPushService instance)
    /*-{
        if ($wnd.addEventListener) {
            $wnd.addEventListener("storage", function(event) {
                  $entry(<EMAIL>::onLocalStorageChanged(Lru/naumen/core/client/NativeStorageEvent;)(event))
               }, false);
        } else {
            $wnd.document.attachEvent("onstorage", function(event) {
                  $entry(<EMAIL>::onLocalStorageChanged(Lru/naumen/core/client/NativeStorageEvent;)(event))
            });
        };
    }-*/;
    //@formatter:on

    private void addNotificationToLocalStorage(String pushUuid)
    {
        List<String> pushUuids = getPushUuidsFromStorage();
        pushUuids.add(pushUuid);
        localStorage.setItem(LOCAL_STORAGE_PUSH_UUIDS_KEY, StringUtilities.join(pushUuids, StringUtilities.COMMA));
    }

    private List<String> getPushUuidsFromStorage()
    {
        String pushUuids = localStorage.getItem(LOCAL_STORAGE_PUSH_UUIDS_KEY);
        if (StringUtilities.isEmpty(pushUuids) || pushUuids.equalsIgnoreCase("null"))
        {
            return new ArrayList<>();
        }
        else
        {
            return Lists.newArrayList(pushUuids.split(StringUtilities.COMMA));
        }
    }

    private native void goToUrl(String url)
    /*-{
        $wnd.location.href = url;
    }-*/;

    private void handleBrowserPushClick(String link, String pushUuidToRead) // NOPMD
    {
        if (StringUtilities.isNotEmpty(link) && StringUtilities.isEmpty(pushUuidToRead))
        {
            goToUrl(link);
        }
        else if (StringUtilities.isNotEmpty(pushUuidToRead))
        {
            pushService.markPushMessagesAsRead(Lists.newArrayList(pushUuidToRead), new BasicCallback<List<DtObject>>()
            {
                @Override
                protected void handleFailure(String msg)
                {
                    if (StringUtilities.isNotEmpty(link))
                    {
                        goToUrl(link);
                    }
                }

                @Override
                protected void handleSuccess(List<DtObject> value)
                {
                    if (StringUtilities.isNotEmpty(link))
                    {
                        goToUrl(link);
                    }
                    eventBus.fireEvent(new ReadPushEvent(Source.Popup, value.get(0)));
                }
            });

        }
    }

    private void markAsRead(String notificationId)
    {
        if (null != pushMessages.remove(notificationId))
        {
            pushService.markPushMessagesAsRead(Lists.newArrayList(notificationId),
                    new BasicCallback<List<DtObject>>()
                    {
                        @Override
                        protected void handleFailure(String msg)
                        {
                            updateLocalStoragePushMessages();
                        }

                        @Override
                        protected void handleSuccess(List<DtObject> value)
                        {
                            updateLocalStoragePushMessages();
                            eventBus.fireEvent(new ReadPushEvent(Source.Popup, value.get(0)));
                        }
                    });
        }
    }

    private void registerHandler(HandlerRegistration registration)
    {
        handlerRegistrations.add(registration);
    }

    private void showInternalNotification(@Nullable PushDto pushDto)
    {
        if (null == pushDto)
        {
            return;
        }

        PushPosition position = PushPosition.fromString(pushDto.getPushPosition(), PushPosition.BottomRight);
        String uuid = pushDto.getPushUuid();
        String message;
        if (!isIgnoreOwaspInSafeHtml() || isWrapPushInIframe())
        {
            PresentationContext prsContext = new PresentationContext(null, null, null);
            SafeHtml html = presentationFactories.getViewPresentationFactory(Presentations.RICH_TEXT_VIEW).createHtml(
                    prsContext, pushDto.getPushMessage());
            message = html.asString();
        }
        else
        {
            message = pushDto.getPushMessage();
        }
        switch (position)
        {
            case System:
                bottom.getSystemQueue().addNotification(uuid, message);
                break;
            case TopLeft:
                top.getLeftQueue().addNotification(uuid, message);
                break;
            case TopRight:
                top.getRightQueue().addNotification(uuid, message);
                break;
            case BottomLeft:
                bottom.getLeftQueue().addNotification(uuid, message);
                break;
            case BottomRight:
            default:
                bottom.getRightQueue().addNotification(uuid, message);
                break;
        }
    }

    /**
     * Синхронизирует локальный список уведомлений в соответствии с содержимым local storage
     */
    private void synchronizePushMessagesWithLocalStorage()
    {
        List<String> localStorageUuids = getPushUuidsFromStorage();
        if (localStorageUuids.isEmpty())
        {
            pushMessages.keySet().forEach(uuid ->
            {
                top.removeNotification(uuid);
                bottom.removeNotification(uuid);
            });
            pushMessages.clear();
            return;
        }
        Set<String> localUuids = new HashSet<>(pushMessages.keySet());

        // Первым проходом удаляем закрытые уведомления.
        localUuids.stream()
                .filter(uuid -> !localStorageUuids.contains(uuid))
                .forEach(uuid ->
                {
                    pushMessages.remove(uuid);
                    top.removeNotification(uuid);
                    bottom.removeNotification(uuid);
                });

        // Вторым проходом получаем идентификаторы новых уведомлений.
        List<String> newUuids = localStorageUuids.stream()
                .filter(uuid -> !localUuids.contains(uuid))
                .collect(Collectors.toList());

        if (!newUuids.isEmpty())
        {
            pushService.getPushMessagesByUuids(newUuids, new BasicCallback<List<PushDto>>()
            {
                @Override
                protected void handleSuccess(List<PushDto> value)
                {
                    for (PushDto pushDto : value)
                    {
                        if (null != pushMessages.get(pushDto.getPushUuid()))
                        {
                            continue;
                        }
                        pushMessages.put(pushDto.getPushUuid(), pushDto);
                        showInternalNotification(pushDto);
                    }
                }
            });
        }
    }

    /**
     * Обновляет local storage в соответствии с содержимым локального списка уведомлений 
     */
    private void updateLocalStoragePushMessages()
    {
        localStorage.setItem(LOCAL_STORAGE_PUSH_UUIDS_KEY, StringUtilities.join(pushMessages.keySet(),
                StringUtilities.COMMA));
    }

    private native boolean isWrapPushInIframe()
    /*-{
        return $wnd.wrapPushInIframe;
    }-*/;

    private native boolean isIgnoreOwaspInSafeHtml()
    /*-{
        return $wnd.isIgnoreOwaspInSafeHtml;
    }-*/;
}
