package ru.naumen.dynaform.client.push;

import java.util.List;

import com.google.gwt.user.client.rpc.AsyncCallback;
import com.google.inject.ImplementedBy;

import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.push.PushDto;

/**
 * Асинхронный сервис для работы со всплывающими уведомлениями на клиенте
 *
 * <AUTHOR>
 * @since 11.11.15
 */
@ImplementedBy(PushServiceAsyncImpl.class)
public interface PushServiceAsync
{
    /**
     * Возвращает список уведомлений по уникальным идентификаторам.
     * @param pushUuids идентификаторы уведомлений
     * @param callback
     */
    void getPushMessagesByUuids(List<String> pushUuids, AsyncCallback<List<PushDto>> callback);

    /**
     * Помечает уведомления как прочитанные.
     * @param pushUuids идентификаторы уведомлений, которые необходимо пометить
     * @param callback
     */
    void markPushMessagesAsRead(List<String> pushUuids, AsyncCallback<List<DtObject>> callback);

    /**
     * Помечает уведомления как непрочитанные.
     * @param pushUuids идентификаторы уведомлений, которые необходимо пометить
     * @param callback
     */
    void markPushMessagesAsUnread(List<String> pushUuids, AsyncCallback<List<DtObject>> callback);
}
