package ru.naumen.dynaform.client.tour;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;

import com.google.gwt.event.dom.client.ClickHandler;
import com.google.gwt.event.shared.EventBus;
import com.google.gwt.user.client.ui.Widget;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.client.content.toolbar.display.buttons.ButtonToolDisplay;
import ru.naumen.core.client.content.toolbar.display.factories.IconCodes;
import ru.naumen.core.client.events.RefreshUserInterfaceEvent;
import ru.naumen.core.client.mvp.BasicCallback;
import ru.naumen.core.shared.utils.SuccessReadyState;
import ru.naumen.dynaform.client.header.HeaderTabIconPresenterBase;
import ru.naumen.dynaform.client.permissions.PermissionsCheckModuleService;
import ru.naumen.tour.client.SelectTourFormPresenter;
import ru.naumen.tour.client.TourController;
import ru.naumen.tour.client.TourFinishedEvent;
import ru.naumen.tour.client.TourMessages;
import ru.naumen.tour.client.TourResources;
import ru.naumen.tour.shared.Tour;

/**
 * Презентер иконки туров по системе
 *
 * <AUTHOR>
 * @since 30.09.22
 */
public class SelectTourIconPresenter extends HeaderTabIconPresenterBase
{
    private List<Tour> enabledTours;

    private final TourController tourController;
    private final PermissionsCheckModuleService permissionCheckModule;
    private final Provider<SelectTourFormPresenter> formProvider;
    private final TourMessages messages;
    private final TourResources resources;

    @Inject
    public SelectTourIconPresenter(ButtonToolDisplay display, EventBus eventBus,
            TourController tourController, PermissionsCheckModuleService permissionCheckModule,
            Provider<SelectTourFormPresenter> formProvider, TourMessages messages, TourResources resources)
    {
        super(display, eventBus);
        this.tourController = tourController;
        this.permissionCheckModule = permissionCheckModule;
        this.formProvider = formProvider;
        this.messages = messages;
        this.resources = resources;

        setPendingToursCount(0);
    }

    @Override
    public String getCode()
    {
        return IconCodes.TOUR;
    }

    @Override
    protected void loadData(SuccessReadyState readyState)
    {
        super.loadData(readyState);
        reloadTours(() ->
        {
        }, readyState);
    }

    @Override
    protected ClickHandler getClickHandler()
    {
        return event ->
        {
            event.preventDefault();
            formProvider.get().bind();
        };
    }

    @Override
    protected void updateIconState()
    {
        int toursCount = enabledTours.size();
        if (toursCount == 0)
        {
            disable();
        }
        else
        {
            setPendingToursCount(toursCount);
        }
    }

    @Override
    protected void onBind()
    {
        super.onBind();
        addHandler(RefreshUserInterfaceEvent.getType(), event -> updateTours());
        addHandler(TourFinishedEvent.TYPE, event ->
        {
            if (TourFinishedEvent.Result.COMPLETED == event.getResult())
            {
                updateTours();
            }
        });
    }

    /**
     * Перевести туры в ожидание
     */
    private void suspendTours()
    {
        if (permissionCheckModule.isOn())
        {
            tourController.suspendMarks();
        }
        else
        {
            tourController.resumeMarks();
        }
    }

    /**
     * Обновить туры и иконку
     */
    private void updateTours()
    {
        reloadTours(() ->
        {
            updateIconState();
            suspendTours();
        }, null);
    }

    /**
     * Перезагрузить список туров
     */
    private void reloadTours(Runnable callback, @Nullable SuccessReadyState readyState)
    {
        SuccessReadyState rs = readyState == null ? new SuccessReadyState(this) : readyState;
        tourController.reload(new BasicCallback<List<Tour>>(rs)
        {
            @Override
            protected void handleSuccess(List<Tour> value)
            {
                enabledTours = value.stream().filter(tour -> !tour.isDisabled()).collect(Collectors.toList());
                enabled = !enabledTours.isEmpty();
                callback.run();
            }
        });
    }

    /**
     * Установить количество ожидающих просмотра туров
     */
    private void setPendingToursCount(int count)
    {
        Widget w = getDisplay().asWidget();
        if (count > 0)
        {
            if (count > 100)
            {
                w.setTitle(messages.newToursMany("100+"));
            }
            else
            {
                String plural = StringUtilities.selectPlural(count);
                if ("[few]".equals(plural))
                {
                    w.setTitle(messages.newToursFew(Integer.toString(count)));
                }
                else if ("[many]".equals(plural))
                {
                    w.setTitle(messages.newToursMany(Integer.toString(count)));
                }
                else
                {
                    w.setTitle(messages.newTour());
                }
            }
            w.addStyleName(resources.style().newTourSelectionIcon());
        }
        else
        {
            w.setTitle(messages.showTours());
            w.removeStyleName(resources.style().newTourSelectionIcon());
        }
    }
}
