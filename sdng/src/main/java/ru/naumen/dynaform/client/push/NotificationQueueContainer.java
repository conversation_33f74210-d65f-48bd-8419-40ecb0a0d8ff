package ru.naumen.dynaform.client.push;

import java.util.ArrayDeque;
import java.util.Deque;
import java.util.HashMap;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import jakarta.annotation.Nullable;

import com.google.gwt.core.client.Scheduler;
import com.google.gwt.dom.client.AnchorElement;
import com.google.gwt.dom.client.Element;
import com.google.gwt.dom.client.EventTarget;
import com.google.gwt.event.dom.client.ClickEvent;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.Timer;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.dynaform.client.push.PushNotificationWidget.ExpandDirection;

/**
 * Контейнер для очереди отображаемых уведомлений в интерфейсе.
 * <AUTHOR>
 * @since Sep 11, 2019
 */
public class NotificationQueueContainer extends Composite
{
    private static final int REMOVE_FADE_DELAY = 400;
    private int notificationTimeout = 10_000;

    private static class NotificationInfo
    {
        private final String id;
        private final String message;

        public NotificationInfo(String id, String message)
        {
            this.id = id;
            this.message = message;
        }

        public String getId()
        {
            return id;
        }

        public String getMessage()
        {
            return message;
        }
    }

    public enum QueueDirection
    {
        Up,
        Down
    }

    private final QueueDirection direction;
    private final int maxSize;
    private final List<String> showingNotifications = new LinkedList<>();
    private final Map<String, PushNotificationWidget> notificationMap = new HashMap<>();
    private final Map<String, Timer> timerMap = new HashMap<>();
    private final Deque<NotificationInfo> hiddenNotifications = new ArrayDeque<>();
    private final boolean dropHiddenNotifications;

    private final FlowPanel container;

    private NotificationFactory notificationFactory;

    public NotificationQueueContainer(QueueDirection direction, int maxSize, boolean dropHiddenNotifications)
    {
        this.direction = Objects.requireNonNull(direction);
        this.maxSize = maxSize;
        this.container = new FlowPanel();
        this.notificationFactory = PushNotificationWidget::new;
        this.dropHiddenNotifications = dropHiddenNotifications;
        initWidget(container);
    }

    public HandlerRegistration addCloseHandler(ClosePushHandler handler)
    {
        return addHandler(handler, ClosePushEvent.TYPE);
    }

    public HandlerRegistration addDropHandler(DropPushHandler handler)
    {
        return addHandler(handler, DropPushEvent.TYPE);
    }

    public HandlerRegistration addExpandHandler(ExpandPushHandler handler)
    {
        return addHandler(handler, ExpandPushEvent.TYPE);
    }

    public void addNotification(String notificationId, String message)
    {
        if (maxSize < 1)
        {
            return;
        }
        while (showingNotifications.size() >= maxSize)
        {
            String id = showingNotifications.remove(0);
            PushNotificationWidget widget = notificationMap.remove(id);
            cancelTimer(id);
            if (dropHiddenNotifications)
            {
                fireEvent(new DropPushEvent(id));
            }
            else
            {
                hiddenNotifications.push(new NotificationInfo(id, widget.getHtml()));
            }
            removeWidget(widget);
        }
        showNotification(notificationId, message, false);
        if (dropHiddenNotifications)
        {
            Timer timer = new Timer()
            {
                @Override
                public void run()
                {
                    fireEvent(new DropPushEvent(notificationId));
                    removeNotification(notificationId);
                }
            };
            timer.schedule(notificationTimeout);
            timerMap.put(notificationId, timer);
        }
    }

    public void clear()
    {
        showingNotifications.stream().map(notificationMap::remove).forEach(this::removeWidget);
        showingNotifications.forEach(this::cancelTimer);
        showingNotifications.clear();
        hiddenNotifications.clear();
        container.clear();
    }

    public QueueDirection getDirection()
    {
        return direction;
    }

    public int getMaxSize()
    {
        return maxSize;
    }

    public boolean removeNotification(String notificationId)
    {
        if (showingNotifications.remove(notificationId))
        {
            removeWidget(notificationMap.remove(notificationId));
            cancelTimer(notificationId);
            return true;
        }
        return hiddenNotifications.removeIf(info -> info.getId().equals(notificationId));
    }

    public void setNotificationFactory(NotificationFactory notificationFactory)
    {
        this.notificationFactory = Objects.requireNonNull(notificationFactory);
    }

    public void setNotificationTimeout(int notificationTimeout)
    {
        this.notificationTimeout = notificationTimeout;
    }

    private void addWidget(PushNotificationWidget widget, boolean restore)
    {
        if (QueueDirection.Up == direction ^ restore)
        {
            container.add(widget);
        }
        else
        {
            container.insert(widget, 0);
        }
        Scheduler.get().scheduleDeferred(widget::fadeIn);
    }

    private void cancelTimer(String notificationId)
    {
        Timer timer = timerMap.remove(notificationId);
        if (null != timer)
        {
            timer.cancel();
        }
    }

    private void closeNotification(String notificationId)
    {
        if (removeNotification(notificationId))
        {
            fireEvent(new ClosePushEvent(notificationId));
            while (!hiddenNotifications.isEmpty() && showingNotifications.size() < maxSize)
            {
                NotificationInfo lastHidden = hiddenNotifications.pop();
                showNotification(lastHidden.getId(), lastHidden.getMessage(), true);
            }
        }
    }

    private void removeWidget(@Nullable PushNotificationWidget widget)
    {
        if (null == widget)
        {
            return;
        }
        widget.fadeOut();
        widget.setEnabled(false);
        Scheduler.get().scheduleFixedDelay(() ->
        {
            container.remove(widget);
            return false;
        }, REMOVE_FADE_DELAY);
    }

    private void restartTimer(String notificationId)
    {
        Timer timer = timerMap.get(notificationId);
        if (null != timer)
        {
            timer.schedule(notificationTimeout);
        }
    }

    private void showNotification(String notificationId, String message, boolean restore)
    {
        ExpandDirection expandDirection = QueueDirection.Up == direction ? ExpandDirection.Up : ExpandDirection.Down;
        PushNotificationWidget notification = notificationFactory.create(expandDirection);
        notification.setHtml(message);
        if (restore)
        {
            showingNotifications.add(0, notificationId);
        }
        else
        {
            showingNotifications.add(notificationId);
        }
        notificationMap.put(notificationId, notification);
        addWidget(notification, restore);
        notification.getCloseButton().addDomHandler(event ->
        {
            closeNotification(notificationId);
            event.preventDefault();
        }, ClickEvent.getType());
        notification.getCollapseControl().addDomHandler(event ->
        {
            notification.toggle();
            restartTimer(notificationId);
            if (notification.isExpanded())
            {
                fireEvent(new ExpandPushEvent(notificationId));
            }
            event.preventDefault();
        }, ClickEvent.getType());
        if (isWrapPushInIframe())
        {
            Scheduler.get().scheduleFixedDelay(() ->
            {
                addClickListener(notification.getElement(), notificationId);
                return false;
            }, 100);
        }
        else
        {
            notification.addDomHandler(event ->
            {
                EventTarget eventTarget = event.getNativeEvent().getEventTarget();
                if (AnchorElement.is(eventTarget) && !StringUtilities.isEmpty(
                        eventTarget.<AnchorElement> cast().getHref()))
                {
                    closeNotification(notificationId);
                    event.stopPropagation();
                }
            }, ClickEvent.getType());
        }
    }

    private static boolean is(EventTarget eventTarget) //NOPMD
    {
        return AnchorElement.is(eventTarget);
    }

    private native void addClickListener(Element element, String notificationId)
    /*-{
        var that = this;
        var handler = function(e) {
            var eventTarget = e.target
            if (@ru.naumen.dynaform.client.push.NotificationQueueContainer::is(*)(eventTarget))
            {
                <EMAIL>::closeNotification(*)(notificationId);
                e.stopPropagation()
            }
        }

        var iframeBody = element.getElementsByTagName('iframe')[0].contentWindow.document.body;
        if (iframeBody.addEventListener)
        {
            iframeBody.addEventListener('click', handler, true);
        }
        else
        {
            iframeBody.attachEvent('onclick', handler);
        }
    }-*/;

    private native boolean isWrapPushInIframe()
    /*-{
        return $wnd.wrapPushInIframe;
    }-*/;
}
