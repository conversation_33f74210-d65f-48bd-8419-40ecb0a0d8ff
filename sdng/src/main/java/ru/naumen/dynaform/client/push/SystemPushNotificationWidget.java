package ru.naumen.dynaform.client.push;

import com.google.gwt.user.client.ui.HTML;
import com.google.gwt.user.client.ui.Widget;

/**
 * Виджет системного уведомления в интерфейсе.
 * <AUTHOR>
 * @since Sep 17, 2019
 */
public class SystemPushNotificationWidget extends PushNotificationWidget
{
    public SystemPushNotificationWidget(ExpandDirection expandDirection)
    {
        super(expandDirection);
        scrollablePanel.setScrollPaddingLeft(0);
    }

    @Override
    protected Widget createToolButton(String code, String title)
    {
        return new HTML(); // NOPMD NSDPRD-28509 unsafe html
    }

    @Override
    protected boolean isShowCollapsed()
    {
        return false;
    }
}
