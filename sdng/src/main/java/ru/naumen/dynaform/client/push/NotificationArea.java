package ru.naumen.dynaform.client.push;

import java.util.List;
import java.util.Objects;

import com.google.common.collect.Lists;
import com.google.gwt.event.shared.HandlerRegistration;
import com.google.gwt.user.client.ui.Composite;
import com.google.gwt.user.client.ui.FlowPanel;
import com.google.gwt.user.client.ui.IsWidget;

import ru.naumen.core.client.widgets.WidgetResources;
import ru.naumen.dynaform.client.push.NotificationQueueContainer.QueueDirection;

/**
 * Область уведомлений. Может быть расположена сверху или снизу.
 * <AUTHOR>
 * @since Sep 11, 2019
 */
public class NotificationArea extends Composite
{
    public enum Position
    {
        Top,
        Bottom
    }

    private final Position position;

    private final FlowPanel container;
    private final NotificationQueueContainer leftQueue;
    private final NotificationQueueContainer rightQueue;
    private final NotificationQueueContainer systemQueue;

    public NotificationArea(Position position, boolean dropHiddenNotifications)
    {
        WidgetResources.INSTANCE.pushStyle().ensureInjected();

        this.position = Objects.requireNonNull(position);

        leftQueue = new NotificationQueueContainer(getDirection(), 3, dropHiddenNotifications);
        rightQueue = new NotificationQueueContainer(getDirection(), 3, dropHiddenNotifications);
        systemQueue = new NotificationQueueContainer(getDirection(), 3, dropHiddenNotifications);

        leftQueue.addStyleName(WidgetResources.INSTANCE.pushStyle().notificationQueueLeft());
        rightQueue.addStyleName(WidgetResources.INSTANCE.pushStyle().notificationQueueRight());
        systemQueue.addStyleName(WidgetResources.INSTANCE.pushStyle().notificationQueueSystem());

        FlowPanel sideContainer = new FlowPanel();
        sideContainer.add(leftQueue);
        sideContainer.add(rightQueue);

        container = new FlowPanel();
        addToFlow(sideContainer, container);
        addToFlow(systemQueue, container);
        container.addStyleName(getAreaStyle());
        initWidget(container);
        systemQueue.setNotificationFactory(expandDirection -> new SystemPushNotificationWidget(expandDirection));
    }

    public List<HandlerRegistration> addCloseHandler(ClosePushHandler handler)
    {
        return Lists.newArrayList(
                getLeftQueue().addCloseHandler(handler),
                getRightQueue().addCloseHandler(handler),
                getSystemQueue().addCloseHandler(handler)
        );
    }

    public List<HandlerRegistration> addDropHandler(DropPushHandler handler)
    {
        return Lists.newArrayList(
                getLeftQueue().addDropHandler(handler),
                getRightQueue().addDropHandler(handler),
                getSystemQueue().addDropHandler(handler)
        );
    }

    public List<HandlerRegistration> addExpandHandler(ExpandPushHandler handler)
    {
        return Lists.newArrayList(
                getLeftQueue().addExpandHandler(handler),
                getRightQueue().addExpandHandler(handler),
                getSystemQueue().addExpandHandler(handler)
        );
    }

    public void addExternalQueue(IsWidget queue)
    {
        addToFlow(queue, container);
    }

    public void clearQueues()
    {
        getLeftQueue().clear();
        getRightQueue().clear();
        getSystemQueue().clear();
    }

    public NotificationQueueContainer getLeftQueue()
    {
        return leftQueue;
    }

    public Position getPosition()
    {
        return position;
    }

    public NotificationQueueContainer getRightQueue()
    {
        return rightQueue;
    }

    public NotificationQueueContainer getSystemQueue()
    {
        return systemQueue;
    }

    public void removeNotification(String notificationId)
    {
        leftQueue.removeNotification(notificationId);
        rightQueue.removeNotification(notificationId);
        systemQueue.removeNotification(notificationId);
    }

    private void addToFlow(IsWidget widget, FlowPanel container)
    {
        if (Position.Top == position)
        {
            container.insert(widget, 0);
        }
        else
        {
            container.add(widget);
        }
    }

    private String getAreaStyle()
    {
        switch (position)
        {
            case Top:
                return WidgetResources.INSTANCE.pushStyle().notificationAreaTop();
            case Bottom:
            default:
                return WidgetResources.INSTANCE.pushStyle().notificationAreaBottom();
        }
    }

    private QueueDirection getDirection()
    {
        switch (position)
        {
            case Top:
                return QueueDirection.Down;
            case Bottom:
            default:
                return QueueDirection.Up;
        }
    }
}
