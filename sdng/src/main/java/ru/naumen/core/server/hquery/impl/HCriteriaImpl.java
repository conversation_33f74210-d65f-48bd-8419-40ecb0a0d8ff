package ru.naumen.core.server.hquery.impl;

import java.util.List;
import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.persistence.criteria.JoinType;

import org.hibernate.query.Query;
import org.hibernate.Session;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HOrder;
import ru.naumen.core.server.hquery.HPredicate;
import ru.naumen.core.server.hquery.HSource;
import ru.naumen.core.server.hquery.impl.source.BaseSource;
import ru.naumen.core.server.hquery.impl.source.TextSource;

/**
 *
 * <AUTHOR>
 *
 */
public class HCriteriaImpl extends HColumnImpl implements HCriteria
{
    private final HCriteriaDelegate delegate;

    private final HCriteriaJoinCache joinCache = new HCriteriaJoinCache();

    public HCriteriaImpl()
    {
        this(new HCriteriaDelegate());
    }

    private HCriteriaImpl(HCriteriaDelegate delegate)
    {
        this(delegate, delegate.getNextAlias());
    }

    private HCriteriaImpl(HCriteriaDelegate delegate, String alias)
    {
        super(alias, alias);
        this.delegate = delegate;
    }

    @Override
    public HCriteria add(HCriterion criterion)
    {
        delegate.add(criterion);
        return this;
    }

    @Override
    public HCriteria add(Iterable<HCriterion> criterions)
    {
        delegate.add(criterions);
        return this;
    }

    @Override
    public HCriteria addCTESource(HCriteria criteria)
    {
        String cteAlias = delegate.getNextAlias();
        HCriteriaDelegate cteDelegate = criteria.getDelegate();
        delegate.addCTE(cteDelegate, cteAlias);
        return new HCriteriaImpl(delegate, cteAlias);
    }

    @Override
    public HCriteria addColumn(HColumn column)
    {
        delegate.addColumn(column);
        return this;
    }

    @Override
    public HCriteria addColumn(final String column)
    {
        delegate.addColumn(new HColumnImpl(column));
        return this;
    }

    @Override
    public HCriteria addColumn(String column, String alias)
    {
        delegate.addColumn(new HColumnImpl(column, alias));
        return this;
    }

    @Override
    public HCriteria addStartColumn(String column, String alias)
    {
        delegate.addStartColumn(new HColumnImpl(column, alias));
        return this;
    }

    @Override
    public HCriteria addGroupColumn(HGroupColumn groupColumn)
    {
        delegate.addGroupColumn(groupColumn);
        return this;
    }

    @Override
    public HCriteria addInnerJoin(String path)
    {
        return addJoin(path, JoinType.INNER, null);
    }

    @Override
    public HCriteria addInnerJoin(String path, HCriterion joinCondition)
    {
        return addJoin(path, JoinType.INNER, joinCondition);
    }

    @Override
    public HCriteria addLeftJoin(String path)
    {
        return addJoin(path, JoinType.LEFT, null);
    }

    @Override
    public HCriteria addLeftJoin(String path, HCriterion joinCondition)
    {
        return addJoin(path, JoinType.LEFT, joinCondition);
    }

    @Override
    public HCriteria addOrder(HOrder order)
    {
        delegate.addOrder(order);
        return this;
    }

    @Override
    public HCriteria addPropertyColumn(String property)
    {
        delegate.addColumn(getProperty(property));
        return this;
    }

    @Override
    public HCriteria addPropertyColumn(String property, String alias)
    {
        delegate.addColumn(getProperty(property, alias));
        return this;
    }

    @Override
    public HCriteria addRightJoin(String path)
    {
        return addJoin(path, JoinType.RIGHT, null);
    }

    @Override
    public HCriteria addRightJoin(String path, HCriterion joinCondition)
    {
        return addJoin(path, JoinType.RIGHT, joinCondition);
    }

    public HCriteriaImpl addSource(BaseSource hSource)
    {
        delegate.addSource(hSource);
        return new HCriteriaImpl(delegate, hSource.getAlias());
    }

    @Override
    public HCriteria addSource(String path)
    {
        String joinAlias = delegate.getNextAlias();
        delegate.addSource(path, joinAlias);
        return new HCriteriaImpl(delegate, joinAlias);
    }

    @Override
    public HCriteriaImpl createCopy()
    {
        HCriteriaDelegate newDelegate = delegate.cloneSeparatedNames();
        return new HCriteriaImpl(newDelegate, getAlias());
    }

    @Override
    public HCriteriaImpl createOver()
    {
        return new HCriteriaImpl(delegate.createOver());
    }

    @Override
    public Query createQuery(Session session)
    {
        return delegate.createQuery(session);
    }

    @Override
    public Query createQuery(Session session, Class<?> expectedResultType)
    {
        return delegate.createQuery(session, expectedResultType);
    }

    @Override
    public String createHql(Session session)
    {
        return delegate.generateHQL(session);
    }

    @Override
    public NameGenerator getAliasCounter()
    {
        return delegate.getAliasGenerator();
    }

    /**
     * @return the delegate
     */
    @Override
    public HCriteriaDelegate getDelegate()
    {
        return delegate;
    }

    @Override
    public HSource getHierarchyPrior()
    {
        TextSource priorSource = delegate.getPriorSource();
        return new HSourceImpl(priorSource.getHQLSource(), priorSource.getAlias());
    }

    @Override
    public List<HOrder> getOrders()
    {
        return delegate.getOrders();
    }

    @Override
    public NameGenerator getParameterCounter()
    {
        return delegate.getParamNameGenerator();
    }

    public BaseSource getSource()
    {
        return delegate.getSource(getAlias());
    }

    @Override
    public String getSourceString()
    {
        BaseSource source = delegate.getSource(getAlias());
        return source.getHQLSource();
    }

    @Override
    public boolean hasOrder(@Nullable HColumn column)
    {
        if (column == null)
        {
            return false;
        }
        for (HOrder order : getOrders())
        {
            if (column.toString().equals(order.getProperty().toString()))
            {
                return true;
            }
        }
        return false;
    }

    public void removeSource()
    {
        delegate.removeSource(getAlias());
    }

    @Override
    public void setAliasGenerator(NameGenerator aliasGenerator)
    {
        delegate.setAliasGenerator(aliasGenerator);
    }

    @Override
    public HCriteria setConnectCriterion(HCriterion connectCriterion)
    {
        delegate.changeConnectCriterion(connectCriterion);
        return this;
    }

    @Override
    public void setParameters(Query query)
    {
        super.setParameters(query);
        delegate.setParameters(query);
    }

    @Override
    public HCriteria setFirstResult(int firstResult)
    {
        delegate.setFirstResult(firstResult);
        return this;
    }

    @Override
    public HCriteria setMaxResults(@Nullable Integer maxResult)
    {
        delegate.setMaxResults(maxResult);
        return this;
    }

    @Override
    public HCriteria setPredicate(HPredicate predicate)
    {
        delegate.setPredicate(predicate);
        return this;
    }

    @Override
    public HCriteria setStartCriterion(HCriterion startCriterion)
    {
        delegate.changeStartCriterion(startCriterion);
        return this;
    }

    @Override
    public String toVerbosString()
    {
        return delegate.toString();
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        return getAlias();
    }

    @Override
    public void visit(HBuilder builder)
    {
        builder.select(getHQL(builder));
    }

    private HCriteria addJoin(String path, JoinType joinType, @Nullable HCriterion joinCondition)
    {
        HCriteria result = joinCache.get(joinType, joinCondition, path);
        if (result != null)
        {
            return result;
        }
        String joinAlias = delegate.getNextAlias();
        result = new HCriteriaImpl(delegate, joinAlias);
        delegate.addJoin(getAlias() + '.' + path, joinAlias, joinType,
                joinCondition == null ? null : joinCondition.createCopy().fillEmptyBaseProperty(result));
        joinCache.put(joinType, joinCondition, path, result);
        return result;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        HCriteriaImpl hCriteria = (HCriteriaImpl)o;
        return Objects.equals(delegate, hCriteria.delegate);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), delegate);
    }

    @Override
    public String toString()
    {
        return "HCriteriaImpl{" +
               "column='" + column + '\'' +
               ", alias=" + getAlias() +
               '}';
    }
}
