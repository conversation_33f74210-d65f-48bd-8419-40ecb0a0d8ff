package ru.naumen.core.server.catalog;

import jakarta.persistence.Cacheable;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CategoryCatalog;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Элемент справочника "Категории"
 *
 * <AUTHOR>
 */
// @formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Table(name = "tbl_category",uniqueConstraints = { 
        @UniqueConstraint(name = "tbl_category_code_key", columnNames = {"code"})}, indexes={@jakarta.persistence.Index(name = "idx_category_parent", columnList="parent")})
@Cacheable
@DiscriminatorValue("-")
@DiscriminatorFormula(FlexHelper.NULL_DISCRIMINATOR_FORMULA)
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(CategoryCatalogItem.CLASS_ID)
@Metaclass(id = CategoryCatalogItem.CLASS_ID,
        title = { @LStr(value = "Элемент справочника 'Категории'"),
                @LStr(lang = "en", value = "'Categories' catalog item"),
                @LStr(lang = "de", value = "Katalogartikel 'Kategorien'")},
        withCase = false)
@Catalog(code = CategoryCatalog.CODE, flat = false, withFolders = true,
    title = { @LStr(value = "Категории"),
            @LStr(lang = "en", value = "Categories"),
            @LStr(lang = "de", value = "Kategorien") },
    description = { @LStr(value = "Содержит иерархический список категорий для классификации запросов."), 
                    @LStr(lang = "en", value = "Contains hierarchical list of categories for requests classification."),
                    @LStr(lang = "de", value = "Enthält  hierarchische Kategorienliste für Einstufung der Anforderungen.")})
//@formatter:on
public class CategoryCatalogItem extends CatalogItem<CategoryCatalogItem>
{
    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    public static final String CLASS_ID = CategoryCatalog.ITEM_CLASS_ID;

    @Override
    public ClassFqn getMetaClass()
    {
        return CategoryCatalog.ITEM_FQN;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }
}
