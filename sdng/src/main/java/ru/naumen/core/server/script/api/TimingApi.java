package ru.naumen.core.server.script.api;

import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.EnumSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TimeZone;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.hibernate.SessionFactory;
import org.joda.time.format.DateTimeFormat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import java.util.HashMap;

import com.google.common.collect.Sets;

import ru.naumen.bcp.server.events.AfterObjectEditedEvent;
import ru.naumen.bcp.server.operations.context.HasObjectBOContext;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.catalog.servicetime.ServiceTimeCatalogItem;
import ru.naumen.core.server.catalog.servicetime.ServiceTimeCatalogUtils;
import ru.naumen.core.server.catalog.timezone.TimeZoneCatalogItem;
import ru.naumen.core.server.common.Accessor;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.common.AccessorWithRawValue;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.api.metainfo.ITimeIntervalWrapper;
import ru.naumen.core.server.script.api.metainfo.ITimerDefinitionWrapper;
import ru.naumen.core.server.script.api.metainfo.TimeIntervalWrapper;
import ru.naumen.core.server.script.api.metainfo.TimerDefinitionWrapper;
import ru.naumen.core.server.script.spi.IScriptDtObject;
import ru.naumen.core.server.script.spi.ScriptUtils;
import ru.naumen.core.server.timer.AbstractTimer;
import ru.naumen.core.server.timer.BackTimer;
import ru.naumen.core.server.timer.ServiceTimeDateCalculationStrategy;
import ru.naumen.core.server.timer.Timer;
import ru.naumen.core.server.timer.TimerUtils;
import ru.naumen.core.server.timer.bcp.TimerCalculationContext;
import ru.naumen.core.server.timer.bcp.conditionstrategies.TimerConditionStrategy;
import ru.naumen.core.server.timer.bcp.conditionstrategies.TimerConditionStrategySelector;
import ru.naumen.core.server.timing.calculate.IDateWithServiceTimeOperationsHelper;
import ru.naumen.core.server.timing.calculate.ITimingCalculator;
import ru.naumen.core.server.util.log.IChangeLog;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.timer.BackTimerDto;
import ru.naumen.core.shared.timer.Status;
import ru.naumen.core.shared.timer.definition.TimerCondition;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.Constants.BackTimerAttributeType;
import ru.naumen.metainfo.shared.IClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * API для вычисления времени обслуживания
 *
 * <AUTHOR>
 * @since *******
 */
@Component("timing")
public class TimingApi implements ITimingApi
{
    private static final String PARSE_DATE_FORMAT = "dd.MM.yyyy HH:mm:ss";
    private static final Logger LOG = LoggerFactory.getLogger(TimingApi.class);
    private static final Set<Status> RESET_STATUSES = Sets.immutableEnumSet(EnumSet.of(Status.ACTIVE, Status.EXCEED,
            Status.STOPED));
    @Inject
    protected IPrefixObjectLoaderService prefixObjectLoaderService;
    @Inject
    protected CommonUtils commonUtils;
    @Inject
    protected AccessorHelper accessorHelper;
    @Inject
    protected TimerUtils timerUtils;
    @Inject
    protected ScriptUtils scriptUtils;
    @Inject
    protected ApiUtils apiUtils;
    @Inject
    protected SessionFactory sessionFactory;
    @Inject
    protected MetainfoService metainfoService;
    @Inject
    protected ApplicationEventPublisher eventPublisher;
    @Inject
    private ServiceTimeCatalogUtils serviceTimeUtils;
    @Inject
    private TimerConditionStrategySelector strategySelector;
    @Inject
    private ServiceTimeDateCalculationStrategy dateCalculationStrategy;

    @Override
    public Date addWorkingDays(@Nullable Date date, int amountOfDays, IUUIDIdentifiable serviceTime,
            @Nullable IUUIDIdentifiable timeZone)
    {
        IDateWithServiceTimeOperationsHelper helper = getDateOperationHelper(serviceTime, timeZone);
        return helper.addWorkingDays(date, amountOfDays);
    }

    @Override
    public Date addWorkingHours(@Nullable Date date, int amountOfHours, IUUIDIdentifiable serviceTime,
            @Nullable IUUIDIdentifiable timeZone)
    {
        IDateWithServiceTimeOperationsHelper helper = getDateOperationHelper(serviceTime, timeZone);
        return helper.addWorkingHours(date, amountOfHours);
    }

    @Override
    @Nullable
    public Date getEndOfWorkingDay(@Nullable Date date, IUUIDIdentifiable serviceTime,
            @Nullable IUUIDIdentifiable timeZone)
    {
        IDateWithServiceTimeOperationsHelper helper = getDateOperationHelper(serviceTime, timeZone);
        return helper.getEndOfWorkingCalendarField(date, Calendar.DAY_OF_YEAR);
    }

    @Override
    @Nullable
    public Date getEndOfWorkingMonth(@Nullable Date date, IUUIDIdentifiable serviceTime,
            @Nullable IUUIDIdentifiable timeZone)
    {
        IDateWithServiceTimeOperationsHelper helper = getDateOperationHelper(serviceTime, timeZone);
        return helper.getEndOfWorkingCalendarField(date, Calendar.MONTH);
    }

    @Override
    @Nullable
    public Date getEndOfWorkingYear(@Nullable Date date, IUUIDIdentifiable serviceTime,
            @Nullable IUUIDIdentifiable timeZone)
    {
        IDateWithServiceTimeOperationsHelper helper = getDateOperationHelper(serviceTime, timeZone);
        return helper.getEndOfWorkingCalendarField(date, Calendar.YEAR);
    }

    @Override
    @Nullable
    public Date getStartOfWorkingDay(@Nullable Date date, IUUIDIdentifiable serviceTime,
            @Nullable IUUIDIdentifiable timeZone)
    {
        IDateWithServiceTimeOperationsHelper helper = getDateOperationHelper(serviceTime, timeZone);
        return helper.getStartOfWorkingCalendarField(date, Calendar.DAY_OF_YEAR);
    }

    @Override
    @Nullable
    public Date getStartOfWorkingMonth(@Nullable Date date, IUUIDIdentifiable serviceTime,
            @Nullable IUUIDIdentifiable timeZone)
    {
        IDateWithServiceTimeOperationsHelper helper = getDateOperationHelper(serviceTime, timeZone);
        return helper.getStartOfWorkingCalendarField(date, Calendar.MONTH);
    }

    @Override
    @Nullable
    public Date getStartOfWorkingYear(@Nullable Date date, IUUIDIdentifiable serviceTime,
            @Nullable IUUIDIdentifiable timeZone)
    {
        IDateWithServiceTimeOperationsHelper helper = getDateOperationHelper(serviceTime, timeZone);
        return helper.getStartOfWorkingCalendarField(date, Calendar.YEAR);
    }

    @Override
    public Date parseDate(String text)
    {
        return DateTimeFormat.forPattern(PARSE_DATE_FORMAT).parseDateTime(text).toDate();
    }

    @Override
    public void resetTimer(Object object, String attrCode)
    {
        resetTimer(object, attrCode, true, true);
    }

    @Override
    public void resetTimer(Object object, String attrCode, boolean calculateTimerParameters)
    {
        resetTimer(object, attrCode, calculateTimerParameters, true);
    }

    @Override
    public void resetTimer(Object object, String attrCode, boolean calculateTimerParameters, boolean resetEscalation)
    {
        try
        {
            IUUIDIdentifiable obj = apiUtils.getObject(object);
            MetaClass metaClass = metainfoService.getMetaClass(obj);
            AbstractTimer timer = getTimer(obj, attrCode);
            if (timer instanceof Timer)
            {
                resetCommonTimer(obj, timer);
            }
            else if (timer instanceof BackTimer)
            {
                BackTimer backTimer = (BackTimer)timer;
                BackTimer oldTimerValue = backTimer.clone();
                if (calculateTimerParameters)
                {
                    smartResetBackTimer(object, attrCode, obj, metaClass, oldTimerValue);
                }
                else
                {
                    dumbResetBackTimer(obj, backTimer);
                }
                if (resetEscalation)
                {
                    recalcEscalationEvents(obj, attrCode, oldTimerValue, backTimer);
                }
            }
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage(), e);
        }
    }

    @Override
    public Date serviceEndTime(IScriptDtObject serviceTime, IScriptDtObject timeZone, Date startTime, long interval)
    {
        TimeZoneCatalogItem tz = prefixObjectLoaderService.get(timeZone.getUUID());
        ServiceTimeCatalogItem st = prefixObjectLoaderService.get(serviceTime.getUUID());
        return serviceEndTime(st, tz, startTime, interval);
    }

    @Override
    public Date serviceEndTime(String serviceTime, String timeZone, Date startTime, long interval)
    {
        ServiceTimeCatalogItem st = getServiceTimeByCode(serviceTime);
        TimeZoneCatalogItem tz = getTimeZoneByCode(timeZone);
        return serviceEndTime(st, tz, startTime, interval);
    }

    @Override
    public Date serviceStartTime(Date start, IUUIDIdentifiable serviceTime, IUUIDIdentifiable timeZone)
    {
        TimeZoneCatalogItem tz = prefixObjectLoaderService.get(timeZone.getUUID());
        ServiceTimeCatalogItem st = prefixObjectLoaderService.get(serviceTime.getUUID());
        ITimingCalculator calculator = serviceTimeUtils.getCalculator(tz.getTimeZone(), st);
        calculator.setStartDate(start);
        calculator.setServiceTime(0);
        return calculator.getServiceStartDate();
    }

    @Override
    public Date serviceStartTime(IUUIDIdentifiable serviceTime, IUUIDIdentifiable timeZone)
    {
        return serviceStartTime(new Date(), serviceTime, timeZone);
    }

    @Override
    public long serviceTime(IUUIDIdentifiable serviceTime, IUUIDIdentifiable timeZone, Date startTime, Date endTime)
    {
        TimeZoneCatalogItem tz = prefixObjectLoaderService.get(timeZone.getUUID());
        ServiceTimeCatalogItem st = prefixObjectLoaderService.get(serviceTime.getUUID());
        ITimingCalculator calculator = serviceTimeUtils.getCalculator(tz.getTimeZone(), st);
        calculator.setStartDate(startTime);
        calculator.setEndDate(dateCalculationStrategy.getEndDate(startTime, endTime));
        return calculator.getServiceTime();
    }

    @Override
    public long serviceTime(String serviceTime, String timeZone, Date startTime, Date endTime)
    {
        ServiceTimeCatalogItem st = getServiceTimeByCode(serviceTime);
        TimeZoneCatalogItem tz = getTimeZoneByCode(timeZone);
        return serviceTime(st, tz, startTime, endTime);
    }

    @Override
    public void setBackTimer(Object object, String attrCode, Date startTime, Date deadLineTime, String state,
            String serviceTime, String timeZone)
    {
        long resolutionTimeMillis = deadLineTime.getTime() - startTime.getTime();
        setBackTimer(object, attrCode, startTime, deadLineTime, state, serviceTime, timeZone, resolutionTimeMillis);
    }

    @Override
    public void setBackTimer(Object object, String attrCode, Date startTime, long resolutionTimeMillis, String state,
            String serviceTime, String timeZone)
    {
        Date deadLineTime = serviceEndTime(serviceTime, timeZone, startTime, resolutionTimeMillis);
        setBackTimer(object, attrCode, startTime, deadLineTime, state, serviceTime, timeZone, resolutionTimeMillis);
    }

    @Override
    public ITimerDefinitionWrapper timerDefinition(IClassFqn fqn, String attrCode)
    {
        return new TimerDefinitionWrapper(getTimerDefinition(fqn, attrCode));
    }

    protected Date serviceEndTime(ServiceTimeCatalogItem st, TimeZoneCatalogItem tz, Date startTime, long interval)
    {
        ITimingCalculator calculator = serviceTimeUtils.getCalculator(tz.getTimeZone(), st);
        calculator.setStartDate(startTime);
        calculator.setServiceTime(interval);
        return calculator.getServiceEndDate();
    }

    private void resetCommonTimer(IUUIDIdentifiable obj, AbstractTimer timer)
    {
        timer.setStartTime(System.currentTimeMillis());
        timer.setElapsed(0L);
        timer.setStatus(Status.ACTIVE);
        sessionFactory.getCurrentSession().merge(obj);
    }

    private void smartResetBackTimer(Object object, String attrCode, IUUIDIdentifiable obj, MetaClass metaClass,
            BackTimer oldTimerValue)
    {

        TimerDefinition timerDef = getTimerDefinition(metaClass.getFqn(), attrCode);
        Status newState = getNewBackTimerStatus(object, attrCode, metaClass, timerDef, oldTimerValue);
        if (newState == Status.NOTSTARTED)
        {
            return;
        }

        Date startTime = new Date();
        ServiceTimeCatalogItem serviceTime = getServiceTime(obj, timerDef);
        TimeZoneCatalogItem timeZone = getTimeZone(obj, timerDef);
        long interval = getInterval(obj, timerDef);
        Date deadLineTime = serviceEndTime(serviceTime, timeZone, startTime, interval);

        metaClass.getAttributes().stream()
                .filter(attr -> BackTimerAttributeType.CODE.equals(attr.getType().getCode()) &&
                                timerDef.equals(timerUtils.getTimerDefinition(attr)))
                .forEach(backTimerAttr ->
                {
                    BackTimer sameBackTimer = (BackTimer)getTimer(obj, backTimerAttr.getCode());
                    resetBackTimerAttr(sameBackTimer, newState, startTime, deadLineTime, timerDef);
                });
        sessionFactory.getCurrentSession().merge(obj);
    }

    private void resetBackTimerAttr(BackTimer backTimer, Status newState, Date startTime, Date deadLineTime,
            TimerDefinition timerDef)
    {
        backTimer.setElapsedFromOverdue(0L);
        backTimer.setElapsed(0L);
        backTimer.setActiveTimeIntervals(new ArrayList<>());
        if (RESET_STATUSES.contains(newState))
        {
            backTimer.setStartTime(startTime.getTime());
            backTimer.setDeadLineTime(deadLineTime.getTime());
            if (timerDef.isEnableRecalc() || timerDef.isEnableRecalcOnServiceTimeChange())
            {
                backTimer.setActiveTimeIntervals(Lists.newArrayList(new Pair<>(startTime.getTime(), null)));
            }
            backTimer.setStatus(Status.ACTIVE);
        }
        else
        {
            backTimer.setStartTime(0L);
            backTimer.setDeadLineTime(null);
            backTimer.setStatus(newState);
        }
    }

    private void dumbResetBackTimer(IUUIDIdentifiable obj, BackTimer backTimer)
    {
        Date startTime = new Date();
        long allowanceTimeMillis = Math
                .abs((backTimer.getDeadLineTime() - backTimer.getStartTime()));
        Date deadLineTime = new Date(startTime.getTime() + allowanceTimeMillis);
        backTimer.setStartTime(startTime.getTime());
        backTimer.setDeadLineTime(deadLineTime.getTime());
        backTimer.setElapsed(0L);
        backTimer.setStatus(Status.ACTIVE);
        sessionFactory.getCurrentSession().merge(obj);
    }

    /**
     * Вычисление нового статуса обратного счетчика
     */
    private Status getNewBackTimerStatus(Object object, String attrCode, MetaClass metaClass, TimerDefinition timerDef,
            BackTimer oldTimerValue)
    {
        AbstractBO dbObject = object instanceof DtObject
                ? (AbstractBO)apiUtils.getObject(((DtObject)object).getUUID())
                : (AbstractBO)apiUtils.getObject(object);
        Attribute backTimerAttr = metaClass.getAttribute(attrCode);
        TimerCalculationContext<BackTimer, BackTimerDto> tCalcContext = new TimerCalculationContext<>
                (backTimerAttr, oldTimerValue, oldTimerValue.getStatus(), timerDef, metaClass);
        HasObjectBOContext<IHasMetaInfo> opContext = new HasObjectBOContext<>(dbObject, metaClass.getFqn());
        TimerConditionStrategy<? extends TimerCondition> conditionStrategy = strategySelector
                .getConditionStrategy(timerDef);
        return conditionStrategy.getState(tCalcContext, opContext);
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private long getInterval(IUUIDIdentifiable obj, TimerDefinition timerDef)
    {
        Attribute resolutionTimeAttr = metainfoService.getMetaClass(obj).getAttribute(timerDef
                .getResolutionTimeAttribute());
        Accessor accessor = accessorHelper.getAccessor(resolutionTimeAttr);
        DateTimeInterval resolutionTime = (DateTimeInterval)accessor.get(obj, resolutionTimeAttr);
        return resolutionTime == null ? 0 : resolutionTime.toMiliseconds();
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private TimeZoneCatalogItem getTimeZone(IUUIDIdentifiable obj, TimerDefinition timerDef)
    {
        Attribute timeZoneAttr = metainfoService.getMetaClass(obj).getAttribute(timerDef.getTimeZoneAttribute());
        Accessor accessor = accessorHelper.getAccessor(timeZoneAttr);
        return (TimeZoneCatalogItem)accessor.get(obj, timeZoneAttr);
    }

    @SuppressWarnings({ "rawtypes", "unchecked" })
    private ServiceTimeCatalogItem getServiceTime(IUUIDIdentifiable obj, TimerDefinition timerDef)
    {
        Attribute serviceTimeAttr = metainfoService.getMetaClass(obj).getAttribute(timerDef.getServiceTimeAttribute());
        Accessor accessor = accessorHelper.getAccessor(serviceTimeAttr);
        return (ServiceTimeCatalogItem)accessor.get(obj, serviceTimeAttr);
    }

    private TimerDefinition getTimerDefinition(IClassFqn fqn, String attrCode)
    {
        Attribute attribute = metainfoService.getMetaClass(fqn).getAttribute(attrCode);
        return timerUtils.getTimerDefinition(attribute);
    }

    private IDateWithServiceTimeOperationsHelper getDateOperationHelper(IUUIDIdentifiable serviceTime,
            @Nullable IUUIDIdentifiable timeZone)
    {
        ServiceTimeCatalogItem st = prefixObjectLoaderService.get(serviceTime.getUUID());
        TimeZone tz = getTimeZoneIfNotNull(timeZone);
        IDateWithServiceTimeOperationsHelper dateOperationHelper = serviceTimeUtils.getDateOperationHelper(tz, st);
        dateOperationHelper.setServiceTime(st);
        dateOperationHelper.setTimeZone(tz);
        return dateOperationHelper;
    }

    /**
     * Метод возвращает значение атрибута источника нормативного времени обслуживания.
     * @param object объект, к которому относится счетчик
     * @param metaClass метакласс, к которому относится счетчик
     * @param timerDefinition определение счетчика времени
     * @return {@link DateTimeInterval} нормативного времени обслуживания
     */
    private DateTimeInterval getResolutionTime(IUUIDIdentifiable object, MetaClass metaClass,
            TimerDefinition timerDefinition)
    {
        Attribute resolutionTimeAttribute = metaClass.getAttribute(timerDefinition.getResolutionTimeAttribute());
        return accessorHelper.getAttributeValue(object, resolutionTimeAttribute);
    }

    private ServiceTimeCatalogItem getServiceTimeByCode(String serviceTime)
    {
        Map<String, Object> stParameters = new HashMap<>();
        stParameters.put(Constants.CatalogItem.ITEM_CODE, serviceTime);
        ServiceTimeCatalogItem st = commonUtils.get(Constants.ServiceTimeCatalog.ITEM_CLASS_ID, stParameters);
        return st;
    }

    /**
     * Метод возвращает счетчик времени у объекта для конкретного атрибута.
     * @param obj объект, к которому относится счетчик
     * @param attribute атрибут счетчика
     * @return счетчик времени {@link AbstractTimer}
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    private AbstractTimer getTimer(IUUIDIdentifiable obj, Attribute attribute)
    {
        AccessorWithRawValue accessor = (AccessorWithRawValue)accessorHelper.getAccessor(attribute);
        return (AbstractTimer)accessor.getRawValue(obj, attribute);
    }

    /**
     * Метод возвращает счетчик времени у объекта по коду атрибута.
     * @param obj объект, к которому относится счетчик
     * @param attrCode код атрибута счетчика
     * @return счетчик времени {@link AbstractTimer}
     */
    private AbstractTimer getTimer(IUUIDIdentifiable obj, String attrCode)
    {
        Attribute attribute = metainfoService.getMetaClass(obj).getAttribute(attrCode);
        return getTimer(obj, attribute);
    }

    private TimeZoneCatalogItem getTimeZoneByCode(String timeZone)
    {
        Map<String, Object> tzParameters = new HashMap<>();
        tzParameters.put(Constants.CatalogItem.ITEM_CODE, timeZone);
        TimeZoneCatalogItem tz = commonUtils.get(Constants.TimezoneCatalogItem.CLASS_ID, tzParameters);
        return tz;
    }

    @Nullable
    private TimeZone getTimeZoneIfNotNull(@Nullable IUUIDIdentifiable timeZone)
    {
        if (timeZone != null)
        {
            TimeZoneCatalogItem tz = prefixObjectLoaderService.get(timeZone.getUUID());
            return tz.getTimeZone();
        }
        return null;
    }

    /**
     * Метод выполняет препарирование кода статуса счетчика времени.
     */
    private String normalizeCode(String code)
    {
        if (StringUtilities.isEmptyTrim(code))
        {
            return StringUtilities.EMPTY;
        }
        if (code.length() > 1)
        {
            code = code.substring(0, 1);
        }
        return code.toLowerCase();
    }

    /**
     * Метод выполняет форсированное обновление уровней эскалации в зависимости
     * от нового значения обратного счетчика времени.
     * @param obj объект, к которому относится счетчик
     * @param attrCode код атрибута "Счетчик времени"
     * @param oldTimerValue {@link BackTimer} старое значение атрибута с кодом attrCode
     * @param newTimerValue {@link BackTimer} новое значение атрибута с кодом attrCode
     */
    @SuppressWarnings({ "rawtypes", "unchecked" })
    private void recalcEscalationEvents(IUUIDIdentifiable obj, String attrCode, BackTimer oldTimerValue,
            BackTimer newTimerValue)
    {
        MapProperties properties = new MapProperties();
        MetaClass metaClass = metainfoService.getMetaClass(obj);
        Attribute timerAttribute = metaClass.getAttribute(attrCode);
        properties.setProperty(attrCode, newTimerValue);
        HasObjectBOContext<IUUIDIdentifiable> ctx = new HasObjectBOContext<>(obj, metaClass.getFqn(), properties);
        IChangeLog changeLog = ctx.getChangeLog();

        changeLog.logChange(timerAttribute, oldTimerValue, newTimerValue, false);
        ctx.setChangeLogger(changeLog);
        eventPublisher.publishEvent(new AfterObjectEditedEvent(ctx));
    }

    private void setBackTimer(Object object, String attrCode, Date startTime, Date deadLineTime, String state,
            String serviceTime, String timeZone, long resolutionTimeMillis)
    {
        try
        {
            IUUIDIdentifiable obj = apiUtils.getObject(object);
            AbstractTimer timer = getTimer(obj, attrCode);
            if (timer instanceof BackTimer)
            {
                MapProperties properties = new MapProperties();
                MetaClass metaClass = metainfoService.getMetaClass(obj);
                Attribute timerAttribute = metaClass.getAttribute(attrCode);
                TimerDefinition timerDefinition = timerUtils.getTimerDefinition(timerAttribute);
                DateTimeInterval actualResolutionTime = getResolutionTime(obj, metaClass, timerDefinition);
                long allowanceTimeMillis = serviceTime(serviceTime, timeZone, startTime, deadLineTime);
                long elapsedTimeMillis = new Date().getTime() - startTime.getTime();
                if (resolutionTimeMillis < allowanceTimeMillis)
                {
                    resolutionTimeMillis = allowanceTimeMillis;
                }
                if (actualResolutionTime.toMiliseconds() != resolutionTimeMillis)
                {
                    properties.setProperty(timerDefinition.getResolutionTimeAttribute(),
                            new DateTimeInterval(resolutionTimeMillis));
                }
                ((BackTimer)timer).setStartTime(startTime.getTime());
                ((BackTimer)timer).setDeadLineTime(deadLineTime.getTime());
                ((BackTimer)timer).setElapsed(elapsedTimeMillis);
                ((BackTimer)timer).setStatus(Status.getByCode(normalizeCode(state)));
                sessionFactory.getCurrentSession().merge(obj);
                if (!properties.isEmpty())
                {
                    commonUtils.edit(obj, properties);
                }
            }
        }
        catch (Exception e)
        {
            LOG.error(e.getMessage(), e);
        }
    }

    @Override
    public List<ITimeIntervalWrapper> getActiveIntervals(Object object, String attrCode)
    {
        IUUIDIdentifiable bo = apiUtils.load(ApiUtils.getUuid(object));

        MetaClass metaClass = metainfoService.getMetaClass(bo);
        if (!metaClass.hasAttribute(attrCode))
        {
            return List.of();
        }

        BackTimer backTimer = (BackTimer)timerUtils.getTimer(bo, attrCode);
        if (backTimer == null)
        {
            return List.of();
        }

        return backTimer.getActiveTimeIntervals().stream()
                .map(interval -> new TimeIntervalWrapper(interval.left, interval.right))
                .collect(Collectors.toList());
    }
}