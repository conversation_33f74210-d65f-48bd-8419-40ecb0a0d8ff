package ru.naumen.core.server.script.spi.resolvers;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Objects;
import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import com.google.common.collect.Sets;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.attrdescription.resolvers.UUIDIdentifiableResolver;
import ru.naumen.core.server.attrdescription.resolvers.UUIDResover;
import ru.naumen.core.server.script.spi.LazyScriptTreeDtObject;
import ru.naumen.core.server.treefilter.IsActivePredicate;
import ru.naumen.core.server.util.StopWatchFactory;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Обрабатывает результаты работы скрипта фильтрации атрибутов типа "Ссылка на БО",
 * строит иерархию {@link LazyScriptTreeDtObject}, не загружая объекты целиком,
 * что позволяет ускорить преобразование и построение дерева.
 * <AUTHOR>
 * @since Nov 7, 2014
 */
@Component
public class ScriptTreeDtObjectsResolver extends ScriptTreeObjectsResolverBase
{
    private static final Logger LOG = LoggerFactory.getLogger(ScriptTreeDtObjectsResolver.class);

    @Inject
    private UUIDResover uuidResolver;
    @Inject
    @Named("UUIDIdentifiableResolver")
    private UUIDIdentifiableResolver boResolver;

    /**
     * Строит иерархию {@link LazyScriptTreeDtObject} для указанных объектов.
     * @param rawValue список "сырых" объектов
     * @param classFqn класс объектов атрибута
     * @param sourceFqn класс объекта, обладающего атрибутом
     * @param attributeCode код атрибута
     * @param withFolders признак необходимости загрузки папок для представления дерева
     * @param allowRemoved допустимы ли архивные объекты в результирующей иерархии
     * @return объекты с загруженной иерархией
     */
    @SuppressWarnings("unchecked")
    public Object resolve(Object rawValue, ClassFqn classFqn, ClassFqn sourceFqn, String attributeCode,
            boolean withFolders, boolean allowRemoved)
    {
        if (!isApplicable(rawValue, classFqn))
        {
            return rawValue;
        }

        StopWatch sw = StopWatchFactory.create("ScriptTreeDtObjectsResolverUuids", LOG.isDebugEnabled());

        ObjectResolveContext context = new ObjectResolveContext();
        context.classFqn = classFqn;
        context.sourceFqn = sourceFqn;
        context.withFolders = withFolders;
        context.attributeCode = attributeCode;
        context.allowRemoved = allowRemoved;

        rawValue = doResolve(sw, (Collection<Object>)rawValue, context);

        if (LOG.isDebugEnabled())
        {
            LOG.debug("\n{}", sw.prettyPrint());
        }

        return rawValue;
    }

    @Override
    protected Set<String> filter(Set<String> uuids, ResolveContext context)
    {
        return filterPerformers(context, filterByClass(uuids, context));
    }

    @Override
    protected Set<String> prepareObjects(Collection<Object> rawValue, ResolveContext context)
    {
        Set<String> uuidsToResolve = new HashSet<>();

        List<Object> stack = new ArrayList<>(rawValue);
        while (!stack.isEmpty())
        {
            Object obj = stack.removeLast();
            if (obj instanceof Collection<?>)
            {
                stack.addAll((Collection<?>)obj);
            }
            else if (isUuid(obj))
            {
                uuidsToResolve.add(uuidResolver.doResolve(obj));
            }
            else if (obj instanceof IUUIDIdentifiable)
            {
                IUUIDIdentifiable bo = boResolver.doResolve(obj);
                if (!IsActivePredicate.INSTANCE.test(bo) && !context.allowRemoved)
                {
                    //Игнорируем архивные объекты
                    continue;
                }
                context.cache.put(bo.getUUID(), bo);
                context.result.add(bo);
            }
            else
            {
                context.result.add(obj);
            }
        }
        return uuidsToResolve;
    }

    /**
     * @return uuidы, отфильтрованные по классу объектов атрибута
     */
    private Set<String> filterByClass(Set<String> uuids, ResolveContext context)
    {
        ObjectResolveContext ctx = (ObjectResolveContext)context;
        return Sets.newHashSet(CollectionUtils.select(uuids,
                UuidHelper.TO_PREFIX.andThen(s -> Objects.equals(s, ctx.classFqn.getId()))::apply));
    }

    /**
     * @return uuidы объектов, отфильтрованные по признаку "Исполнитель" (для атрибута "Руководитель" в командах).
     */
    private Set<String> filterPerformers(ResolveContext context, Set<String> uuidsToResolve)
    {
        ObjectResolveContext ctx = (ObjectResolveContext)context;
        if (!Team.FQN.equals(ctx.sourceFqn) || !Team.LEADER.equals(ctx.attributeCode))
        {
            return uuidsToResolve;
        }

        HashSet<String> performerUuids = daoHelper.getPerformerUUIDs(Team.FQN);

        uuidsToResolve.retainAll(performerUuids);
        for (Object resObj : new ArrayList<>(context.result))
        {
            if (resObj instanceof IUUIDIdentifiable iuuidIdentifiable && !performerUuids.contains(
                    iuuidIdentifiable.getUUID()))
            {
                context.result.remove(resObj);
                context.cache.remove(iuuidIdentifiable.getUUID());
            }
        }

        return uuidsToResolve;
    }

    /**
     * @return true, если rawValue коллекция и объекты атрибута не элементы справочников:
     * для справочников нужна немного другая логика, пока не видится актуальным оптимизация справочников,
     * в будущем можно распространить и на них
     */
    private boolean isApplicable(Object rawValue, ClassFqn classFqn)
    {
        return rawValue instanceof Collection && !metainfoService.getMetaClass(classFqn).isCatalogItem();
    }
}