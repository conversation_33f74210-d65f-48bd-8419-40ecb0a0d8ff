package ru.naumen.core.server.catalog;

import jakarta.persistence.Cacheable;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants.BlackListSubjectCatalog;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Элемент справочника "Черный список значений темы письма"
 *
 * <AUTHOR>
 * @since 21.05.2014
 */
//@formatter:off
@Entity
@Table(name = "tbl_blacklist_subject", uniqueConstraints = { 
  @UniqueConstraint(name = "tbl_blacklist_subject_code_key", columnNames = {"code"})},
        indexes={@jakarta.persistence.Index(name = "idx_blacklist_subject_parent", columnList="parent")})
@Cacheable
@DiscriminatorValue("-")
@DiscriminatorFormula(FlexHelper.NULL_DISCRIMINATOR_FORMULA)
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(BlackListSubjectCatalogItem.CLASS_ID)
@Metaclass(id = BlackListSubjectCatalogItem.CLASS_ID,
        title = { @LStr(value = "Элемент справочника 'Черный список значений темы письма'"),
                @LStr(lang = "en", value = "'Blacklist of e-mail subject values' catalog item"),
                @LStr(lang = "de", value = "Katalogartikel 'Die schwarze Liste für Email-Thema'") },
        withCase = false)
@Catalog(code = BlackListSubjectCatalog.CODE, flat = true, withFolders = false,
  title = { @LStr(value = "Черный список значений темы письма"),
          @LStr(lang = "en", value = "Blacklist of e-mail subject values"),
          @LStr(lang = "de", value = "Die schwarze Liste für Email-Thema") },
  description = { @LStr(value = "Содержит список значений поля 'Тема письма', выступающих в качестве ограничения для обработки почты."),
                  @LStr(lang = "en", value = "Contains list of e-mail subject values to prevent mail processing."),
                  @LStr(lang = "de", value = "Enthält eine Liste von Werten des Feldes 'Email-Thema', die als Einschränkung für die Bearbeitung von Emails dienen.") })
//@formatter:on
public class BlackListSubjectCatalogItem extends CatalogItem<BlackListSubjectCatalogItem>
{
    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    public static final String CLASS_ID = BlackListSubjectCatalog.ITEM_CLASS_ID;

    @Override
    public ClassFqn getMetaClass()
    {
        return BlackListSubjectCatalog.ITEM_FQN;
    }

    /**
     {@inheritDoc}
     **/
    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }
}