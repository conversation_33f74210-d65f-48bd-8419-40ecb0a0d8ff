package ru.naumen.core.server.advlist.export;

import java.util.Collection;
import java.util.stream.Collectors;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.core.shared.attr.FormatterService;
import ru.naumen.core.shared.attr.presentation.AttributePresentationExtensionService;
import ru.naumen.metainfo.server.spi.SystemNameService;
import ru.naumen.metainfo.shared.Constants.FileAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;

/**
 * <AUTHOR>
 * @since 15.12.2011
 *
 */
@CellCreatorComponent(codes = { IXlsCellCreatorsRegistry.DEFAULT_CREATOR })
public class DefaultXlsCellCreator extends AbstractXlsCellCreator
{
    @Inject
    private FormatterService formattersService;
    @Inject
    private SystemNameService systemNameService;
    @Inject
    private AttributePresentationExtensionService presentationExtensionService;

    @Override
    public void setCellValue(Cell cell, Object attributeValue, Workbook workbook, FormatterContext context)
    {
        String value;
        Attribute attribute = context.getAttribute();
        if (attribute != null
            && presentationExtensionService.mayHaveMultipleValues(attribute.getType(), attribute)
            && attributeValue instanceof Collection<?>
            && !attribute.getType().getCode().equals(FileAttributeType.CODE)
            && !ru.naumen.metainfo.shared.Constants.COLLECTION_ENTITY_TYPES
                .contains(attribute.getType().getCode()))
        {
            value = ((Collection<?>)attributeValue).stream().map(v -> trim(formattersService.format(context, v)))
                    .filter(StringUtilities::isNotEmpty)
                    .collect(Collectors.joining(", "));

            AttributeType attributeType = attribute.getType();
            if (attributeType.isAttributeOfRelatedObject()
                && attributeType.getRelatedObjectAttribute().equals(Constants.AUTHOR)
                && CollectionUtils.isEmpty((Collection<?>)attributeValue))
            {
                value = systemNameService.getSystemName();
            }
        }
        else
        {
            value = trim(formattersService.format(context, attributeValue));
        }

        cell.setCellValue(value);
    }
}
