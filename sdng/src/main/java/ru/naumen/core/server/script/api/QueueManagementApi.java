package ru.naumen.core.server.script.api;

import static ru.naumen.core.server.jms.Constants.Queues.QUEUE_ADVLIST_EXPORT;
import static ru.naumen.core.server.jms.Constants.Queues.QUEUE_DOCUMENT_FILE_INDEXER;
import static ru.naumen.core.server.jms.Constants.Queues.QUEUE_DOCUMENT_INDEXER;
import static ru.naumen.core.server.jms.Constants.Queues.QUEUE_EVENT_ACTION;
import static ru.naumen.core.server.jms.Constants.Queues.QUEUE_USER_EVENT_ACTION;
import static ru.naumen.core.server.jms.JMSUtil.getJMSListenerID;

import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import jakarta.jms.IllegalStateException;
import ru.naumen.core.server.advlist.export.DeferredExport.AdvExportStartedProcessesCacheService;
import ru.naumen.core.server.advlist.export.DeferredExport.AdvlistExportProcess;
import ru.naumen.core.server.jms.Constants.ContainerIDs;
import ru.naumen.core.server.jms.JMSManager;
import ru.naumen.core.server.jms.JmsListenerContainersEndpointRegistry;

/**
 * API для работы с очередью сообщений
 *
 * <AUTHOR>
 * @since 4.2.12
 */
@Component("queueManagement")
public class QueueManagementApi implements IQueueManagementApi
{
    private static final String MESSAGES_ADDED_ATTRIBUTE = "messagesAdded";
    private static final Logger LOG = LoggerFactory.getLogger(QueueManagementApi.class);

    private final JMSManager jmsManager;
    private final AdvlistExportProcess advlistExportProcess;
    private final JmsListenerContainersEndpointRegistry registry;
    private final AdvExportStartedProcessesCacheService advExportStartedProcessesCacheService;

    @Inject
    public QueueManagementApi(final JMSManager jmsManager,
            AdvlistExportProcess advlistExportProcess,
            final JmsListenerContainersEndpointRegistry registry,
            final AdvExportStartedProcessesCacheService advExportStartedProcessesCacheService)
    {
        this.jmsManager = jmsManager;
        this.advlistExportProcess = advlistExportProcess;
        this.registry = registry;
        this.advExportStartedProcessesCacheService = advExportStartedProcessesCacheService;
    }

    public void connectAddPlannedEvent()
    {
        jmsManager.connectAddPlannedEvent();
    }

    public void connectAdvimport()
    {
        jmsManager.connectAdvimport();
    }

    @Override
    public void connectAdvlistExport()
    {
        jmsManager.connectAdvlistExport();
    }

    public void connectBuildReport()
    {
        jmsManager.connectBuildReport();
    }

    public void connectDeleteAllPlannedEvents()
    {
        jmsManager.connectDeleteAllPlannedEvents();
    }

    public void connectDocumentFileIndexer()
    {
        jmsManager.connectDocumentFileIndexer();
    }

    public void connectDocumentIndexer()
    {
        jmsManager.connectDocumentIndexer();
    }

    public void connectLinkedDocumentIndexer()
    {
        jmsManager.connectLinkedDocumentIndexer();
    }

    public void connectEventAction()
    {
        jmsManager.connectEventAction();
    }

    public void connectPlannedEvents()
    {
        jmsManager.connectPlannedEvents();
    }

    public void connectUserEventAction()
    {
        jmsManager.connectUserEventAction();
    }

    @Override
    public void connectUserQueue(String queueName)
    {
        jmsManager.connectUserQueue(queueName);
    }

    public void connectEventActionChangeTracking()
    {
        jmsManager.connectEventActionChangeTracking();
    }

    public void connectEventActionEscalations()
    {
        jmsManager.connectEventActionEscalations();
    }

    public void connectEventActionExternal()
    {
        jmsManager.connectEventActionExternal();
    }

    public void connectEventActionNotifications()
    {
        jmsManager.connectEventActionNotifications();
    }

    public void connectEventActionPushes()
    {
        jmsManager.connectEventActionPushes();
    }

    public void connectPeBCleanerEvent()
    {
        jmsManager.connectPeBCleanerEvent();
    }

    public void connectPeByRuleAsync()
    {
        jmsManager.connectPeByRuleAsync();
    }

    public void connectPeForSubjectAsync()
    {
        jmsManager.connectPeForSubjectAsync();
    }

    public void connectPlannedEventsExecution()
    {
        jmsManager.connectPlannedEventsExecutionQueue();
    }

    public void connectPlannedEventsFastExecution()
    {
        jmsManager.connectPlannedEventsFastExecutionQueue();
    }

    public void disconnectAddPlannedEvent()
    {
        jmsManager.disconnectAddPlannedEvent();
    }

    public void disconnectAdvimport()
    {
        jmsManager.disconnectAdvimport();
    }

    @Override
    public void disconnectAdvlistExport()
    {
        jmsManager.disconnectAdvlistExport();
    }

    public void disconnectBuildReport()
    {
        jmsManager.disconnectBuildReport();
    }

    public void disconnectDeleteAllPlannedEvents()
    {
        jmsManager.disconnectDeleteAllPlannedEvents();
    }

    public void disconnectDocumentFileIndexer()
    {
        jmsManager.disconnectFileDocumentIndexer();
    }

    public void disconnectDocumentIndexer()
    {
        jmsManager.disconnectDocumentIndexer();
    }

    public void disconnectLinkedDocumentIndexer()
    {
        jmsManager.disconnectLinkedDocumentIndexer();
    }

    public void disconnectEventAction()
    {
        jmsManager.disconnectEventAction();
    }

    public void disconnectEventActionChangeTracking()
    {
        jmsManager.disconnectEventActionChangeTracking();
    }

    public void disconnectPlannedEvents()
    {
        jmsManager.disconnectPlannedEvents();
    }

    public void disconnectUserEventAction()
    {
        jmsManager.disconnectUserEventAction();
    }

    @Override
    public void disconnectUserQueue(String queueName)
    {
        jmsManager.disconnectUserQueue(queueName);
    }

    public void disconnectEventActionEscalations()
    {
        jmsManager.disconnectEventActionEscalations();
    }

    public void disconnectEventActionExternal()
    {
        jmsManager.disconnectEventActionExternal();
    }

    public void disconnectEventActionNotifications()
    {
        jmsManager.disconnectEventActionNotifications();
    }

    public void disconnectEventActionPushes()
    {
        jmsManager.disconnectEventActionPushes();
    }

    public void disconnectPeBCleanerEvent()
    {
        jmsManager.disconnectPeBCleanerEvent();
    }

    public void disconnectPeByRuleAsync()
    {
        jmsManager.disconnectPeByRuleAsync();
    }

    public void disconnectPeForSubjectAsync()
    {
        jmsManager.disconnectPeForSubjectAsync();
    }

    public void disconnectPlannedEventsExecution()
    {
        jmsManager.disconnectPlannedEventsExecutionQueue();
    }

    public void disconnectPlannedEventsFastExecution()
    {
        jmsManager.disconnectPlannedEventsFastExecutionQueue();
    }

    public String documentFileIndexerQueue()
    {
        return QUEUE_DOCUMENT_FILE_INDEXER;
    }

    public String documentIndexerQueue()
    {
        return QUEUE_DOCUMENT_INDEXER;
    }

    public Object emptyQueue(String queue) throws Exception
    {
        return jmsManager.emptyQueue(queue);
    }

    @Override
    public Object emptyAdvlistExport() throws Exception
    {
        final var messagesAsJson = listMessagesAsJSON(QUEUE_ADVLIST_EXPORT);
        final var result = emptyQueue(QUEUE_ADVLIST_EXPORT);
        advExportStartedProcessesCacheService.removeHashesAccordingToJmsMessages(messagesAsJson);
        return result;
    }

    @Override
    public void interruptAdvlistExport()
    {
        advlistExportProcess.interrupt();
    }

    @Override
    public void connectNdapAlert()
    {
        jmsManager.connectNdapAlert();
    }

    @Override
    public void disconnectNdapAlert()
    {
        jmsManager.disconnectNdapAlert();
    }

    public String eventActionQueue()
    {
        return QUEUE_EVENT_ACTION;
    }

    public Map<String, Integer> getAllContainersCacheLevel()
    {
        return jmsManager.getAllContainersCacheLevels();
    }

    public Integer getConsumersCount(String queue) throws Exception
    {
        return jmsManager.getConsumersCount(queue);
    }

    public int getContainerCacheLevel(String containerId)
    {
        return jmsManager.getContainerCacheLevel(containerId);
    }

    @Override
    public Integer getEventActionMessageCount()
    {
        return jmsManager.getQueueMessageCount(QUEUE_EVENT_ACTION);
    }

    @Override
    public Integer getUserEventActionMessageCount()
    {
        return jmsManager.getQueueMessageCount(QUEUE_USER_EVENT_ACTION);
    }

    @Override
    public Integer getUserQueueMessageCount(String queueName)
    {
        return jmsManager.getQueueMessageCount(queueName);
    }

    @Override
    public Integer getEventActionMessagesAdded()
    {
        return getUserQueueMessagesAdded(QUEUE_EVENT_ACTION);
    }

    @Override
    public Integer getUserQueueMessagesAdded(String queueName)
    {
        try
        {
            return jmsManager.getQueueLongAttribute(queueName, MESSAGES_ADDED_ATTRIBUTE).intValue();
        }
        catch (Exception e)
        {
            LOG.error(String.format("Error in jmsManager.getQueueAttribute(%s, %s): %s", queueName,
                    MESSAGES_ADDED_ATTRIBUTE, e.getMessage()), e);
        }
        return -1;
    }

    @Override
    public Integer getFileIndexerMessagesCount()
    {
        return jmsManager.getQueueMessageCount(QUEUE_DOCUMENT_FILE_INDEXER);
    }

    @Override
    public boolean getFileIndexerStatus()
    {
        return registry.getListenerContainer(ContainerIDs.FILE_INDEXER_CONTAINER_ID).isRunning();
    }

    @Override
    public Integer getIndexerMessagesCount()
    {
        return jmsManager.getQueueMessageCount(QUEUE_DOCUMENT_INDEXER);
    }

    @Override
    public boolean getIndexerStatus()
    {
        return registry.getListenerContainer(ContainerIDs.INDEXER_CONTAINER_ID).isRunning();
    }

    @Override
    public Object getQueueAttribute(String queueName, String attributeName)
    {
        try
        {
            return jmsManager.getQueueAttribute(queueName, attributeName);
        }
        catch (Exception e)
        {
            LOG.error(String.format("Error in jmsManager.getQueueAttribute(%s, %s): %s", queueName, attributeName,
                    e.getMessage()), e);
        }
        return null;
    }

    /**
     * Выводит имеющиеся сообщения в виде JSON-строки.
     *
     * @param queue имя очереди, для которой необходимо вывести сообщения.
     * @return сообщения в виде JSON строки.
     * @throws Exception
     */
    public String listMessagesAsJSON(String queue) throws Exception
    {
        return jmsManager.listMessagesAsJSON(queue);
    }

    /**
     * Удалить сообщение с определённым идентификатором из очереди.
     *
     * @param queue имя очереди, из которой требуется удалить сообщения;
     * @param id идентификатор сообщения к удалению.
     * @return результат операции.
     * @throws Exception
     */
    public Object removeMessage(String queue, String id) throws Exception
    {
        return jmsManager.removeMessage(queue, id);
    }

    @Override
    public void restartFileIndexer()
    {
        disconnectDocumentFileIndexer();
        connectDocumentFileIndexer();
    }

    @Override
    public void restartIndexer()
    {
        disconnectDocumentIndexer();
        connectDocumentIndexer();
    }

    @Override
    public void restartLinkedIndexer()
    {
        disconnectLinkedDocumentIndexer();
        connectLinkedDocumentIndexer();
    }

    @Override
    public void restartUserQueue(String queueName)
    {
        disconnectUserQueue(queueName);
        connectUserQueue(queueName);
    }

    public void setContainerCacheLevel(String containerId, int cacheLevel) throws IllegalStateException
    {
        jmsManager.setContainerCacheLevel(containerId, cacheLevel);
    }

    @Override
    public void setConcurrency(String queueName, int maxConcurrentConsumers)
    {
        jmsManager.setConcurrency(maxConcurrentConsumers, getJMSListenerID(queueName));
    }

    @Override
    public void setConcurrency(String queueName, String concurrency)
    {
        jmsManager.setConcurrency(concurrency, getJMSListenerID(queueName));
    }
}