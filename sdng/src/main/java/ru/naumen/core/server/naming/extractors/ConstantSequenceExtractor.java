package ru.naumen.core.server.naming.extractors;

import jakarta.inject.Inject;

import ru.naumen.core.server.SpringContext;

/**
 * Для любого ключа генерирует один и тот же идентификатор последовательности
 *
 * <AUTHOR>
 * @since 02.05.2012
 */
public class ConstantSequenceExtractor implements SequenceExtractor
{
    @Inject
    SpringContext springContext;

    private String sequenceId;

    public ConstantSequenceExtractor(String sequenceId)
    {
        this.sequenceId = sequenceId;
    }

    @Override
    public String getSequenceId(Object key)
    {
        return sequenceId;
    }
}
