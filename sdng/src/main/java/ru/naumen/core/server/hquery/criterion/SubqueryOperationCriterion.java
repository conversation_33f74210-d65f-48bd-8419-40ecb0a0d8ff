package ru.naumen.core.server.hquery.criterion;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractPropertySubCriteriaHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;

/**
 * Ограничение, которое выполняет операцию над подзапросом
 *
 * <AUTHOR>
 * @since 18.07.2021
 */
public class SubqueryOperationCriterion extends AbstractPropertySubCriteriaHCriterion
{
    private final String operator;

    public SubqueryOperationCriterion(HColumn property, String operator, HCriteria subCriteria)
    {
        super(property, subCriteria);
        this.operator = operator;
    }

    @Override
    protected void append(StringBuilder sb, HBuilder builder, String subQuery)
    {
        super.append(sb, builder, subQuery);
        sb.append(operator).append(" (").append(subQuery).append(')');
    }

    @Override
    public String toString()
    {
        return "SubqueryOperationCriterion{" +
               "property=" + property +
               ", operator='" + operator + '\'' +
               ", subCriteria=" + subCriteria +
               '}';
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new SubqueryOperationCriterion(getProperty(), operator, subCriteria);
    }
}
