package ru.naumen.core.server.script.libraries.applications;

import static ru.naumen.metainfo.shared.Constants.CODE_SPECIAL_CHARS_FOR_VENDOR_FOR_EMBEDDED_APPLICATION;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Iterator;
import java.util.Optional;
import java.util.function.Consumer;
import java.util.jar.JarEntry;
import java.util.jar.JarFile;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.MimeTypeRegistry;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationFileManipulationService;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.core.server.filestorage.FileUtils;
import ru.naumen.core.server.upload.NestedFileItem;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.metainfo.server.embeddedapplication.EmbeddedApplicationMetainfoService;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;
import ru.naumen.metainfo.server.spi.dispatch.embeddedapplication.SaveEmbeddedApplicationActionHandler;
import ru.naumen.metainfo.shared.Constants.LocalizedAttributeType;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.embeddedapplication.SaveEmbeddedApplicationAction;
import ru.naumen.metainfo.shared.embeddedapplication.ClientSideApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplicationType;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Работа с ВП в библиотеке
 *
 * <AUTHOR>
 * @since 28.09.2020
 */
@Component
public class EmbeddedApplicationLibraryService
{
    private static final Logger LOG = LoggerFactory.getLogger(EmbeddedApplicationLibraryService.class);
    private static final String SUFFIX_TEMP_FILE = EmbeddedApplicationLibraryService.class.getName();

    private final EmbeddedApplicationFileManipulationService embeddedApplicationFileManipulationService;
    private final EmbeddedApplicationMetainfoService embeddedApplicationMetainfoService;
    private final SaveEmbeddedApplicationActionHandler saveEmbeddedApplicationActionHandler;
    private final EmbeddedApplicationService embeddedApplicationService;
    private final UploadService uploadService;
    private final MessageFacade messages;

    @Inject
    public EmbeddedApplicationLibraryService(
            final EmbeddedApplicationFileManipulationService embeddedApplicationFileManipulationService,
            final EmbeddedApplicationMetainfoService embeddedApplicationMetainfoService,
            final SaveEmbeddedApplicationActionHandler saveEmbeddedApplicationActionHandler,
            final EmbeddedApplicationService embeddedApplicationService,
            final UploadService uploadService,
            final MessageFacade messages)
    {
        this.embeddedApplicationFileManipulationService = embeddedApplicationFileManipulationService;
        this.embeddedApplicationMetainfoService = embeddedApplicationMetainfoService;
        this.saveEmbeddedApplicationActionHandler = saveEmbeddedApplicationActionHandler;
        this.embeddedApplicationService = embeddedApplicationService;
        this.uploadService = uploadService;
        this.messages = messages;
    }

    private record EmbeddedApplicationData(String code, String fileName, InputStream content)
    {
    }

    /**
     * Сохранение всех ВП, которые есть в jar
     *
     * @param libraryPath путь до файла библиотеки на ФС
     * @param library JAR библиотека
     */
    public void upload(Path libraryPath, ScriptLibrary library)
    {
        executeForEmbeddedApplication(libraryPath, library, embeddedAppData ->
        {
            String embeddedAppCode = embeddedAppData.code;
            verifyEmbeddedAppCode(embeddedAppCode);

            try
            {
                SimpleDtObject applicationFile = saveApplicationFile(
                        embeddedAppData.fileName, embeddedAppData.content);
                uploadEmbeddedApp(embeddedAppCode, applicationFile, library);
            }
            catch (IOException | DispatchException ioe)
            {
                LOG.error("Failed to save application", ioe);
                throw new FxException(ioe);
            }
        });
    }

    /**
     * Выполнить действие для каждого ВП в библиотеке
     */
    private static void executeForEmbeddedApplication(
            Path libraryPath,
            ScriptLibrary library,
            Consumer<EmbeddedApplicationData> action)
    {
        try
        {
            Path tempFilePath = createTempFileIfNeed(libraryPath, library);
            try (var jarFile = new JarFile(toFile(libraryPath, tempFilePath)))
            {
                @SuppressWarnings("java:S5042")
                Iterator<JarEntry> jarEntryIterator = jarFile.entries().asIterator();
                while (jarEntryIterator.hasNext())
                {
                    JarEntry entry = jarEntryIterator.next();
                    Optional<String> filenameOptional = EmbeddedAppMetadataExtractor.getEmbeddedAppFilename(entry);
                    if (filenameOptional.isEmpty())
                    {
                        continue;
                    }
                    String filename = filenameOptional.get();
                    String embeddedAppCode = EmbeddedAppMetadataExtractor.getEmbeddedAppCodeByFilename(filename);
                    try (InputStream content = jarFile.getInputStream(entry))
                    {
                        action.accept(new EmbeddedApplicationData(
                                embeddedAppCode, filename, content));
                    }
                }
            }
            finally
            {
                if (tempFilePath != null)
                {
                    Files.deleteIfExists(tempFilePath);
                }
            }
        }
        catch (IOException ioe)
        {
            LOG.error("Failed to read library file", ioe);
            throw new FxException(ioe);
        }
    }

    private static File toFile(Path libraryPath, @Nullable Path tempFilePath)
    {
        return tempFilePath == null ? libraryPath.toFile() : tempFilePath.toFile();
    }

    /**
     * Создать временный файл, если отсутствует основной файл библиотеки
     * (обычно не должно такого происходить, если только вручную файл с ФС удалить)
     * @return путь до временного файла или null, если основной файл существует
     */
    @Nullable
    private static Path createTempFileIfNeed(Path libraryPath, ScriptLibrary library)
    {
        if (Files.exists(libraryPath))
        {
            return null;
        }
        try
        {
            Path path = Files.createTempFile(library.getName(), SUFFIX_TEMP_FILE);
            Files.write(path, library.getContent());
            return path;
        }
        catch (IOException e)
        {
            LOG.error("Application file is absent and can't create temp file", e);
            throw new FxException(e);
        }
    }

    /**
     * Удаление ВП из системы, которые есть в переданной библиотеке
     *
     * @param libraryPath путь до файла библиотеки на ФС
     * @param library JAR библиотека
     */
    public void delete(Path libraryPath, ScriptLibrary library)
    {
        executeForEmbeddedApplication(libraryPath, library, embeddedAppData ->
        {
            try
            {
                String embeddedAppCode = embeddedAppData.code;
                EmbeddedApplication application = embeddedApplicationService.getApplication(embeddedAppCode);
                if (application == null || application.getClientApplicationFile() == null)
                {
                    return;
                }
                embeddedApplicationMetainfoService.switchApplication(embeddedAppCode, false);
                /* (vpyzhyanov) искренне не понимаю почему мы удаляем именно файл ВП,
                   а не ВП целиком. Это не логично, т.к. у ВП типа
                   "Приложение исполняемое на стороне клиента" не может отсутствовать файл! */
                deleteEmbeddedAppFile(application);
            }
            catch (DispatchException e)
            {
                LOG.error("Failed to delete application", e);
                throw new FxException(e);
            }
        });
    }

    /**
     * Проверка кода ВП на валидность
     *
     * @param code код ВП
     */
    private void verifyEmbeddedAppCode(String code)
    {
        String specialSymbols = CurrentEmployeeContext.isVendor()
                ? CODE_SPECIAL_CHARS_FOR_VENDOR_FOR_EMBEDDED_APPLICATION
                : "";

        if (!MetainfoUtils.isValidMetainfoKeyCode(code, specialSymbols))
        {
            throw new FxException(messages.getMessage("SaveEmbeddedApplicationActionHandler.invalidCode",
                    code, ru.naumen.metainfo.shared.Constants.MAX_ID_LENGTH));
        }
    }

    /**
     * Сохранение файла ВП
     *
     * @param filename имя файла
     * @param contentFile контент данного файла
     * @return сущность сохраненного файла ВП
     */
    private SimpleDtObject saveApplicationFile(String filename, InputStream contentFile) throws IOException
    {
        var applicationFile = new SimpleDtObject();
        String mimeType = MimeTypeRegistry.getMimeTypeByFileExtension(FileUtils.getExtension(filename));
        var fileItem = new NestedFileItem(IOUtils.toByteArray(contentFile), filename, mimeType);
        String fileUuid = uploadService.add(fileItem, false);

        applicationFile.setUUID(fileUuid);
        applicationFile.setProperty(Constants.File.TITLE, filename);
        return applicationFile;
    }

    /**
     * Добавление в систему встроенного приложения
     *
     * @param embeddedAppCode код ВП
     * @param applicationFile файл ВП
     * @param library JAR библиотека
     */
    private void uploadEmbeddedApp(final String embeddedAppCode, final SimpleDtObject applicationFile,
            ScriptLibrary library) throws DispatchException
    {
        EmbeddedApplication application = embeddedApplicationService.getApplication(embeddedAppCode);
        SaveEmbeddedApplicationAction action = application == null
                ? getSaveNewEmbeddedAppAction(embeddedAppCode, applicationFile, library)
                : getUpdateEmbeddedAppAction(application, applicationFile, applicationFile.getUUID(), library);

        saveEmbeddedApplicationActionHandler.executeInTransaction(action, null);
    }

    /**
     * Удаление файла у встроенного приложения
     *
     * @param application удаляемое ВП
     */
    private void deleteEmbeddedAppFile(final EmbeddedApplication application) throws DispatchException
    {
        embeddedApplicationFileManipulationService.deleteEmbeddedApplicationFile(application);

        SaveEmbeddedApplicationAction updateAction = getUpdateEmbeddedAppAction(application, null, null, null);
        saveEmbeddedApplicationActionHandler.executeInTransaction(updateAction, null);
    }

    /**
     * Получение action, для сохранения нового встроенного приложения
     *
     * @param embeddedAppName название встроенного приложения
     * @param applicationFile сущность, которая представляет файл со ВП
     * @param library JAR библиотека
     * @return готовый action для сохранения ВП
     */
    private static SaveEmbeddedApplicationAction getSaveNewEmbeddedAppAction(
            final String embeddedAppName, final SimpleDtObject applicationFile, ScriptLibrary library)
    {
        var application = new ClientSideApplication();
        application.getTitle().add(new LocalizedString(LocalizedAttributeType.RU, embeddedAppName));
        application.setCode(embeddedAppName);
        application.setInitialHeight(EmbeddedApplication.DEFAULT_INITIAL_HEIGHT);
        application.setApplicationType(EmbeddedApplicationType.ClientSideApplication);
        application.setClientApplicationFile(applicationFile);
        application.setLibraryName(library.getName());
        application.setOn(false);

        return new SaveEmbeddedApplicationAction(application, true);
    }

    /**
     * Получение action, для обновления встроенного приложения
     *
     * @param application встроенного приложение, которое пересохраняем
     * @param applicationFile сущность, которая представляет новый файл со ВП
     * @param fileUuid uuid нового файла со ВП
     * @param library JAR библиотека
     * @return готовый action для сохранения ВП
     */
    private static SaveEmbeddedApplicationAction getUpdateEmbeddedAppAction(
            final EmbeddedApplication application, @Nullable final SimpleDtObject applicationFile,
            @Nullable final String fileUuid, @Nullable ScriptLibrary library)
    {
        //Важно создать клон объекта, чтобы мы не трогали объект из кеша
        EmbeddedApplication applicationForUpdate = application.clone();

        applicationForUpdate.setClientApplicationFile(applicationFile);
        applicationForUpdate.setFileUuid(fileUuid);
        applicationForUpdate.setLibraryName(library == null ? null : library.getName());

        return new SaveEmbeddedApplicationAction(applicationForUpdate, false);
    }
}
