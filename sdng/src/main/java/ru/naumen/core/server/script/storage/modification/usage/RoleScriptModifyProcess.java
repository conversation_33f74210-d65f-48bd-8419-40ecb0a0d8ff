package ru.naumen.core.server.script.storage.modification.usage;

import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_ACCESS_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_FAST_LINK_RIGHTS_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_LIST_FILTER_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_OWNERS_KEY;

import java.util.Collection;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.script.storage.ScriptUsageUtils;
import ru.naumen.core.shared.script.places.RoleCategories;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.sec.Role;

/**
 * Контракт поведения при изменении скрипта в роли пользователя
 * Переопределяет специфичные действия при редактировании.
 * Основная логика редактирования содержится в {@link ScriptModifyProcessBase}
 * <AUTHOR>
 * @since Dec 16, 2015
 */
@Component
public class RoleScriptModifyProcess extends ScriptModifyProcessSupport<Role>
{
    @Override
    protected String getLocation(Role holder, ScriptModifyContext context)
    {
        return holder.getCode();
    }

    @Override
    protected String getOldScriptCode(Role oldHolder, ScriptModifyContext context)
    {
        final RoleCategories category = (RoleCategories)context.getCategory();
        switch (category)
        {
            case ACCESS:
                return oldHolder.getProperties().getProperty(SCRIPT_ACCESS_KEY);
            case LIST_FILTER:
                return oldHolder.getProperties().getProperty(SCRIPT_LIST_FILTER_KEY);
            case FAST_LINK_RIGHTS:
                return oldHolder.getProperties().getProperty(SCRIPT_FAST_LINK_RIGHTS_KEY);
            case OWNERS:
                return oldHolder.getProperties().getProperty(SCRIPT_OWNERS_KEY);
        }
        throw new FxException("ScriptModify context has unidentified category: " + category);
    }

    @Override
    protected Collection<ClassFqn> getRelatedMetaClasses(Role holder, ScriptModifyContext context)
    {
        return ScriptUsageUtils.getRoleFqnHolders(holder);
    }

    @Override
    protected boolean isUsageChangeable()
    {
        return false;
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, Role holder, ScriptModifyContext context)
    {
        final RoleCategories category = (RoleCategories)context.getCategory();
        switch (category)
        {
            case ACCESS:
                holder.getProperties().setProperty(SCRIPT_ACCESS_KEY, newScriptCode);
                break;
            case LIST_FILTER:
                holder.getProperties().setProperty(SCRIPT_LIST_FILTER_KEY, newScriptCode);
                break;
            case FAST_LINK_RIGHTS:
                holder.getProperties().setProperty(SCRIPT_FAST_LINK_RIGHTS_KEY, newScriptCode);
                break;
            case OWNERS:
                holder.getProperties().setProperty(SCRIPT_OWNERS_KEY, newScriptCode);
                break;
        }
    }
}