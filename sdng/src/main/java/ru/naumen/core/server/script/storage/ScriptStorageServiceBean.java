package ru.naumen.core.server.script.storage;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.partitioningBy;
import static java.util.stream.Collectors.toMap;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import org.infinispan.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Provider;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.cache.HasISCache;
import ru.naumen.core.server.cache.infinispan.ISCacheProvider;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.script.crawler.ScriptUsageCacheVisitor;
import ru.naumen.core.server.script.crawler.ScriptsCrawler;
import ru.naumen.core.server.script.libraries.scripting.StorageService;
import ru.naumen.core.server.script.libraries.scripting.role.SecurityRoleScript;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;

/**
 * Сервис "Хранилище скриптов"
 * Надстройка над кэшем для работы со скриптами и модулями скриптов
 * Внимание! Лишних зависимостей не добавлять, либо добавлять с Lazy инициализацией - используется в многих сервисах
 * и грузится раньше MetainfoServiceBean, но позже MetaStorageService и тянет за собой все Inject, что может привести 
 * к циклическим зависимостям или нежелательным преждевременным инициализациям.
 *
 * <AUTHOR>
 * @since Jul 16, 2015
 */
@Component
@DependsOn({ "metainfoService" })
public class ScriptStorageServiceBean implements ScriptStorageService, HasISCache
{
    private static final String SCRIPT_STORAGE_CACHE_KEY = "scriptStorageCache";

    private static final Logger LOG = LoggerFactory.getLogger(ScriptStorageServiceBean.class);

    private final ScriptsCrawler crawler;
    private final Provider<ScriptUsageCacheVisitor> visitorProvider;
    private final MetaStorageService metaStorageService;
    private final StorageService<Script, Script> librariesScriptService;
    private final StorageService<Script, SecurityRoleScript> librariesRolesService;

    /**
     * Используется для хранения кеша скриптов, поднятых из метаинформации<br>
     * Обращаемся только по ключу {@link #SCRIPT_STORAGE_CACHE_KEY}
     */
    private final ISCacheProvider cacheProvider;
    /**
     * Прошла ли инициализация кеша скриптов
     */
    private volatile boolean isInitialised;

    @Inject
    public ScriptStorageServiceBean(final ISCacheProvider cacheProvider,
            final Provider<ScriptUsageCacheVisitor> visitorProvider,
            final MetaStorageService metaStorageService,
            @Named("libraryScriptStorageService") final StorageService<Script, Script> librariesScriptService,
            @Named("libraryRoleStorageService") final StorageService<Script, SecurityRoleScript> librariesRolesService,
            @Lazy final ScriptsCrawler crawler)
    {
        this.cacheProvider = cacheProvider;
        this.visitorProvider = visitorProvider;
        this.metaStorageService = metaStorageService;
        this.librariesScriptService = librariesScriptService;
        this.librariesRolesService = librariesRolesService;
        this.crawler = crawler;
    }

    @Override
    public void deleteScript(@Nullable String code)
    {
        if (StringUtilities.isEmpty(code))
        {
            return;
        }
        cacheProvider.remove(SCRIPT_STORAGE_CACHE_KEY, code);
    }

    @Override
    public void deleteScripts(Collection<String> scriptCodes)
    {
        for (final String script : scriptCodes)
        {
            deleteScript(script);
        }
    }

    @Override
    public <K, V> Cache<K, V> getCache()
    {
        return cacheProvider.getCache(SCRIPT_STORAGE_CACHE_KEY);
    }

    @Override
    public Script getScript(@Nullable String code)
    {
        if (StringUtilities.isEmpty(code))
        {
            return null;
        }
        if (isNotInitialized())
        {
            return getScriptFromDB(code);
        }
        Script script = cacheProvider.get(SCRIPT_STORAGE_CACHE_KEY, code);
        if (script != null)
        {
            return script;
        }

        return librariesScriptService.getScript(code).orElseGet(() ->
                librariesRolesService.getScript(code).orElse(getScriptFromDB(code)));
    }

    @Override
    public String getScriptBody(@Nullable String code)
    {
        final Script script = getScript(code);
        return script == null ? null : script.getBody();
    }

    @Override
    public Collection<Script> getScripts()
    {
        final Collection<Script> scripts =
                cacheProvider.<String, Script> getCache(SCRIPT_STORAGE_CACHE_KEY).values();
        Collection<Script> librariesScripts = librariesScriptService.getScripts();
        Collection<Script> librariesRolesScripts = librariesRolesService.getScripts();
        final List<Script> result = new ArrayList<>(
                scripts.size() + librariesScripts.size() + librariesRolesScripts.size());
        result.addAll(scripts);
        result.addAll(librariesScripts);
        result.addAll(librariesRolesScripts);
        return Collections.unmodifiableList(result);
    }

    /**
     * Инициализировать сервис. Заполняет кэш скриптов (из метаинформации).
     */
    public void init()
    {
        TransactionRunner.run(() ->
        {
            LOG.debug("Starting script storage cache node initialization");
            cacheProvider.initCache(SCRIPT_STORAGE_CACHE_KEY);
            fillCacheFromMetainfo();
            resetScriptUsageCache();
            isInitialised = true;
            LOG.debug("Script storage cache node initialization has finished");
        });
    }

    public void clearCache()
    {
        cacheProvider.clear(SCRIPT_STORAGE_CACHE_KEY);
    }

    @Override
    public void saveScript(Script script)
    {
        final String code = script.getCode();
        if (code != null && librariesRolesService.isScriptNotExist(code))
        {
            if (librariesScriptService.isScriptNotExist(code))
            {
                cacheProvider.put(SCRIPT_STORAGE_CACHE_KEY, code, script);
            }
            else
            {
                librariesScriptService.saveScript(script);
            }
        }
        else
        {
            librariesRolesService.saveScript(script);
        }
    }

    @Override
    public void saveScripts(Collection<Script> scripts)
    {
        final Map<Boolean, List<Script>> scriptsMap = scripts.stream()
                .collect(partitioningBy(script ->
                        script.getCode() != null
                        && (librariesScriptService.isScriptNotExist(script.getCode())
                            || librariesRolesService.isScriptNotExist(script.getCode()))));

        final Map<String, Script> genericScripts = scriptsMap.get(true).stream()
                .collect(toMap(Script::getCode, identity()));
        genericScripts.forEach((key, value) ->
                cacheProvider.put(SCRIPT_STORAGE_CACHE_KEY, key, value));

        librariesScriptService.saveScripts(scriptsMap.get(false));
    }

    /**
     * Получение скрипта по коду из бд, минуя кэш
     * @param code код искомого скрипта
     */
    @Nullable
    public Script getScriptFromDB(@Nullable String code)
    {
        if (code == null)
        {
            return null;
        }
        return metaStorageService.get(ScriptStorageConfiguration.SCRIPT_TYPE, code, null);
    }

    /**
     * Точечно переиницализировать кэши сервиса
     */
    public void reinitCache()
    {
        cacheProvider.clear(SCRIPT_STORAGE_CACHE_KEY);
        fillCacheFromMetainfo();
    }

    /**
     * Помещает скрипты и скриптовые модули из метаинфы в кэш.
     */
    private void fillCacheFromMetainfo()
    {
        LOG.debug("Process initializing scripts cache");
        initializeScripts();
        LOG.debug("Finished initializing scripts cache");
    }

    private void initializeScripts()
    {
        for (final Script script : metaStorageService.<Script> get(ScriptStorageConfiguration.SCRIPT_TYPE))
        {
            saveScript(script);
        }

        final Collection<Script> scripts = librariesScriptService.getScripts();
        for (final Script script : scripts)
        {
            saveScript(script);
        }
    }

    public void resetScriptUsageCache()
    {
        final ScriptUsageCacheVisitor visitor = visitorProvider.get();
        crawler.visit(visitor);
        final Multimap<String, ScriptUsagePoint> entries = visitor.getCacheEntries();
        for (final String scriptCode : entries.keySet())
        {
            final Script script = getScript(scriptCode);
            if (script != null)
            {
                script.setUsagePoints(Lists.newArrayList(entries.get(scriptCode)));
                saveScript(script);
            }
            else
            {
                LOG.debug("Somehow there's no script with the code [{}].", scriptCode);
            }
        }
    }

    /**
     * Кэши могут быть не готовы:
     * - во время старта, тк инициализируются уже после старта системы
     * - во время ошибок загрузки меты, когда они будут сброшены и восстановятся только
     * при корректной последующей заливке
     */
    private boolean isNotInitialized()
    {
        return !isInitialised || getCache().isEmpty();
    }
}