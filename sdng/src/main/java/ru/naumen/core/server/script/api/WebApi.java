package ru.naumen.core.server.script.api;

import static ru.naumen.core.server.script.api.ObjectListHelper.getAttrChain;
import static ru.naumen.core.server.script.api.ObjectListHelper.isShowNested;
import static ru.naumen.fts.shared.SearchFilters.SearchFilter;
import static ru.naumen.metainfo.shared.Constants.LINK_ATTRIBUTE_TYPES;
import static ru.naumen.metainfo.shared.Constants.LinkObjectType.CURRENT_USER_LOWER_CASE;
import static ru.naumen.metainfo.shared.Constants.UI.WINDOW_KEY;
import static ru.naumen.metainfo.shared.ui.ContentLinkConstants.CHILD_OBJECT_LIST;
import static ru.naumen.metainfo.shared.ui.ContentLinkConstants.OBJECT_LIST;
import static ru.naumen.metainfo.shared.ui.ContentLinkConstants.REL_OBJECT_LIST;
import static ru.naumen.metainfo.shared.ui.ListFilterHelper.createRestrictionStrategiesMap;
import static ru.naumen.metainfo.shared.ui.ListFilterHelper.createSort;
import static ru.naumen.metainfo.shared.ui.ListFilterHelper.parseAttrCodes;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.apache.commons.collections4.MapUtils;
import org.apache.commons.text.StringEscapeUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;
import org.springframework.web.util.UriComponentsBuilder;

import com.google.common.annotations.VisibleForTesting;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.advlist.templates.ListTemplateService;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.content.IContentLinkDefinition;
import ru.naumen.core.server.content.IHierarchyGridLinkDefinition;
import ru.naumen.core.server.net.BuildUrlHelper;
import ru.naumen.core.server.net.UrlUtils;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.IListLinkDefinition;
import ru.naumen.core.server.script.IListLinkDefinition.IAttrChain;
import ru.naumen.core.server.script.IListLinkDefinition.IFilter.IFilterAnd;
import ru.naumen.core.server.script.IListLinkDefinition.IFilter.IFilterOr;
import ru.naumen.core.server.script.JSONFilterConverter;
import ru.naumen.core.server.script.api.accesskeys.IAccessKeyWrapper;
import ru.naumen.core.server.script.api.content.HierarchyGridLinkDefinition;
import ru.naumen.core.server.script.api.web.objectlist.ListSettingsContext;
import ru.naumen.core.server.script.api.web.objectlist.converters.DescriptorToListLinkDefinitionConverter;
import ru.naumen.core.server.script.spi.IScriptDtObject;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.version.planned.PlannedVersionLinkToListLicenseChecker;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.AbstractStateResponsibleEvent;
import ru.naumen.core.shared.Constants.AdminLogRecord;
import ru.naumen.core.shared.Constants.AttributeLink;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.Event;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.Constants.GeoHistoryRecord;
import ru.naumen.core.shared.Constants.Mail;
import ru.naumen.core.shared.Constants.MailLogRecord;
import ru.naumen.core.shared.Constants.Push;
import ru.naumen.core.shared.Constants.PushMobile;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.Constants.SuperUser;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.RemovedMode;
import ru.naumen.core.shared.dispatch.AddFormParametersHolderImpl;
import ru.naumen.core.shared.dispatch.EditFormParametersHolderImpl;
import ru.naumen.core.shared.list.IListDescriptor;
import ru.naumen.core.shared.utils.ClassFqnHelper;
import ru.naumen.core.shared.utils.FormPlaceParameter;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.fts.shared.SearchFilters;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.ui.standalone.ContentLinkHelper;
import ru.naumen.metainfo.server.spi.ui.standalone.ContentLinkService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.IClassFqn;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.HasSearchable;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.SearchSetting;
import ru.naumen.metainfo.shared.ui.ChildObjectList;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ContentUtils;
import ru.naumen.metainfo.shared.ui.ListFilterAndElement;
import ru.naumen.metainfo.shared.ui.ListFilterHelper;
import ru.naumen.metainfo.shared.ui.ObjectList;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.RelObjectList;
import ru.naumen.metainfo.shared.ui.Tab;
import ru.naumen.sec.server.users.superuser.SuperUserEmployee;
import ru.naumen.sec.server.utils.AuthenticationUtils;

/**
 * API для формирования различных ссылок
 *
 * <AUTHOR>
 *
 */
@Component("web")
public class WebApi implements IWebApi
{
    private static final Logger LOG = LoggerFactory.getLogger(WebApi.class);

    private static final String UUID = "uuid";
    private static final String TEMPLATE = "%s!{%%22object%%22:%%22%s%%22}";
    private static final String TEMPLATE_OPEN_TAB = "%s!%%7B%%22tab%%22:%%22%s%%22%%7D";
    private static final String TEMPLATE_OPEN_TAB_WITH_TOKEN = "%%21%%7B%%22tab%%22:%%22%s%%22%%7D";
    private static final String TEMPLATE_OPEN_CONTENT = "%s!%%7B%%22tab%%22:%%22%s%%22%%2C%%22content%%22:"
                                                        + "%%22%s%%22%%7D";
    private static final String TEMPLATE_OPEN_CONTENT_WITH_TOKEN = "%%21%%7B%%22tab%%22%%3A%%22%s%%22%%2C"
                                                                   + "%%22content%%22%%3A%%22%s%%22%%7D";
    private static final String TEMPLATE_OPEN_SEARCH = "esearch:extended:%s:%s!%s";

    private static final String PROP_FILTER = "filter";
    private static final String PROP_SORT = "sort";
    private static final String PROP_ATTR_CODES = "attrCodes";
    private static final String PROP_ATTR_PAGING = "paging";

    /**
     * Классы атрибутов, наличие которых указывает на необходимость использования "нового" формата ссылки
     * Внимание! Класс String вынесен из списка, так как он может быть как в "новом" формате, так и в "старом"
     * (обрабатывается отдельно)
     */
    private static final List<Class<?>> NEW_URL_FORMAT_ATTRIBUTE_TYPES = Arrays.asList(Collection.class, Map.class,
            DateTimeInterval.class, Hyperlink.class, Integer.class, Long.class, BigDecimal.class, Double.class,
            Boolean.class, Date.class);

    /**
     * Классы атрибутов передача которых возможна в "старом" формате ссылки
     * Внимание! Класс String вынесен из списка, так как он может быть как в "новом" формате, так и в "старом"
     * (обрабатывается отдельно)
     */
    private static final List<Class<?>> OLD_URL_FORMAT_ATTRIBUTE_TYPES = Collections.singletonList(
            IUUIDIdentifiable.class);

    /**
     * Список кодов классов, на которые нельзя генерировать ссылки на отдельной странице и использовать их
     * настройки поиска при формировании ссылок на страницы с результатами расширенного поиска результаты
     */
    private static final Set<String> NOT_APPLICABLE_CLASS_CODES = Set.of(Comment.CLASS_ID, File.CLASS_ID,
            AdminLogRecord.CLASS_ID, MailLogRecord.CLASS_ID, AbstractStateResponsibleEvent.CLASS_ID,
            Mail.CLASS_ID, Event.CLASS_ID, SuperUser.CLASS_ID, Push.CLASS_ID, PushMobile.CLASS_ID,
            GeoHistoryRecord.CLASS_ID);

    private static final String OBJECT_LIST_NOT_FOUND_EXCEPTION_TEMPLATE = "Object list with content code '%s' not "
                                                                           + "found";
    private static final String PROPERTY_IS_NOT_APPLICABLE_EXCEPTION_TEMPLATE = "Property '%s' is not applicable for "
                                                                                + "simple list";
    private static final String NOT_APPLICABLE_CLASS_EXCEPTION_TEMPLATE = "Class '%s' is not applicable for list of "
                                                                          + "objects on a separate page";

    @Inject
    private IAuthenticationApi authApi;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private MessageFacade messages;
    @Inject
    private ApiUtils apiUtils;
    @Inject
    private AuthenticationUtils authenticationUtils;
    @Inject
    private BuildUrlHelper buildUrlHelper;
    @Inject
    private PlannedVersionLinkToListLicenseChecker plannedVersionLinkToListLicenseChecker;
    @Inject
    private ListTemplateService templateService;
    @Inject
    private ContentLinkService contentLinkService;
    @Inject
    private ContentLinkHelper contentLinkHelper;
    @Inject
    private ListFilterHelper listFilterHelper;
    @Inject
    private ObjectListHelper objectListHelper;
    @Inject
    private ConfigurationProperties configurationProperties;

    @VisibleForTesting
    Collection<FormPlaceParameter> convertAttributes(Map<String, Object> attributes, boolean checkExists)
    {
        return contentLinkHelper.convertAttributes(attributes, checkExists);
    }

    @VisibleForTesting
    void initFromTest(MetainfoService metainfoService, MessageFacade messages, ApiUtils apiUtils,
            IAuthenticationApi authApi, BuildUrlHelper buildUrlHelper, DaoFactory daoFactory)
    {
        this.authApi = authApi;
        this.metainfoService = metainfoService;
        this.messages = messages;
        this.apiUtils = apiUtils;
        this.buildUrlHelper = buildUrlHelper;
        this.contentLinkHelper = new ContentLinkHelper(messages, daoFactory, metainfoService, null, null,
                null);
    }

    @Override
    public String add(@Nullable Collection<Object> fqns)
    {
        if (fqns == null)
        {
            return logAndReturnEmptyString();
        }
        return addIntUnsafe(Sets.newHashSet(fqns), null, null, null);
    }

    @Override
    public String add(@Nullable Collection<Object> fqns, @Nullable IUUIDIdentifiable parent,
            @Nullable Map<String, Object> attributes)
    {
        if (fqns == null)
        {
            return logAndReturnEmptyString();
        }
        return add(fqns, null, parent, attributes);
    }

    @Override
    public String add(@Nullable Collection<Object> fqns, @Nullable String mobileForm,
            @Nullable IUUIDIdentifiable parent, @Nullable Map<String, Object> attributes)
    {
        if (fqns == null)
        {
            return logAndReturnEmptyString();
        }
        return addIntUnsafe(Sets.newHashSet(fqns), mobileForm, UuidHelper.getUUIDSafe(parent), attributes);
    }

    @Override
    public String add(@Nullable Collection<Object> fqns, @Nullable String parent,
            @Nullable Map<String, Object> attributes)
    {
        if (fqns == null)
        {
            return logAndReturnEmptyString();
        }
        return add(Sets.newHashSet(fqns), null, parent, attributes);
    }

    @Override
    public String add(@Nullable Collection<Object> fqns, @Nullable String mobileForm, @Nullable String parent,
            @Nullable Map<String, Object> attributes)
    {
        if (fqns == null)
        {
            return logAndReturnEmptyString();
        }
        return addIntUnsafe(Sets.newHashSet(fqns), mobileForm, parent, attributes);
    }

    @Override
    public String add(@Nullable IClassFqn fqn)
    {
        if (fqn == null)
        {
            return logAndReturnEmptyString();
        }
        return getAddFormLink(fqn, null, null, null, true);
    }

    @Override
    public String add(@Nullable IClassFqn fqn, @Nullable IUUIDIdentifiable parent,
            @Nullable Map<String, Object> attributes)
    {
        if (fqn == null)
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, parent, attributes, true);
    }

    @Override
    public String add(@Nullable IClassFqn fqn, @Nullable String mobileForm, @Nullable IUUIDIdentifiable parent,
            @Nullable Map<String, Object> attributes)
    {
        if (fqn == null)
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, mobileForm, parent, attributes, true);
    }

    @Override
    public String add(@Nullable IClassFqn fqn, @Nullable IUUIDIdentifiable parent,
            @Nullable Map<String, Object> attributes,
            boolean needCheckAttrs)
    {
        if (fqn == null)
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, null, parent, attributes, needCheckAttrs);
    }

    @Override
    public String add(@Nullable IClassFqn fqn, @Nullable String mobileForm, @Nullable IUUIDIdentifiable parent,
            @Nullable Map<String, Object> attributes, boolean needCheckAttrs)
    {
        if (fqn == null)
        {
            return logAndReturnEmptyString();
        }
        return getAddFormLink(fqn, mobileForm, UuidHelper.getUUIDSafe(parent), attributes, needCheckAttrs);
    }

    @Override
    public String add(@Nullable IClassFqn fqn, @Nullable String parent, @Nullable Map<String, Object> attributes)
    {
        if (fqn == null)
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, parent, attributes, true);
    }

    @Override
    public String add(@Nullable IClassFqn fqn, @Nullable String mobileForm, @Nullable String parent,
            @Nullable Map<String, Object> attributes)
    {
        if (fqn == null)
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, mobileForm, parent, attributes, true);
    }

    @Override
    public String add(@Nullable IClassFqn fqn, @Nullable String parent, @Nullable Map<String, Object> attributes,
            boolean needCheckAttrs)
    {
        if (fqn == null)
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, null, parent, attributes, needCheckAttrs);
    }

    @Override
    public String add(@Nullable IClassFqn fqn, @Nullable String mobileForm, @Nullable String parent,
            @Nullable Map<String, Object> attributes, boolean needCheckAttrs)
    {
        if (fqn == null)
        {
            return logAndReturnEmptyString();
        }
        return getAddFormLink(fqn, mobileForm, parent, attributes, needCheckAttrs);
    }

    @Override
    public String add(@Nullable String fqn)
    {
        if (StringUtilities.isEmpty(fqn))
        {
            return logAndReturnEmptyString();
        }
        return getAddFormLink(ClassFqn.parse(fqn), null, null, null, true);
    }

    @Override
    public String add(@Nullable String fqn, @Nullable IUUIDIdentifiable parent,
            @Nullable Map<String, Object> attributes)
    {
        if (StringUtilities.isEmpty(fqn))
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, parent, attributes, true);
    }

    @Override
    public String add(@Nullable String fqn, @Nullable String mobileForm, @Nullable IUUIDIdentifiable parent,
            @Nullable Map<String, Object> attributes)
    {
        if (StringUtilities.isEmpty(fqn))
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, mobileForm, parent, attributes, true);
    }

    @Override
    public String add(@Nullable String fqn, @Nullable IUUIDIdentifiable parent,
            @Nullable Map<String, Object> attributes,
            boolean needCheckAttrs)
    {
        if (StringUtilities.isEmpty(fqn))
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, null, parent, attributes, needCheckAttrs);
    }

    @Override
    public String add(@Nullable String fqn, @Nullable String mobileForm, @Nullable IUUIDIdentifiable parent,
            @Nullable Map<String, Object> attributes, boolean needCheckAttrs)
    {
        if (StringUtilities.isEmpty(fqn))
        {
            return logAndReturnEmptyString();
        }
        return getAddFormLink(ClassFqn.parse(fqn), mobileForm, UuidHelper.getUUIDSafe(parent), attributes,
                needCheckAttrs);
    }

    @Override
    public String add(@Nullable String fqn, @Nullable String parent, @Nullable Map<String, Object> attributes)
    {
        if (StringUtilities.isEmpty(fqn))
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, parent, attributes, true);
    }

    @Override
    public String add(@Nullable String fqn, @Nullable String mobileForm, @Nullable String parent,
            @Nullable Map<String, Object> attributes)
    {
        if (StringUtilities.isEmpty(fqn))
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, parent, attributes, true);
    }

    @Override
    public String add(@Nullable String fqn, @Nullable String parent, @Nullable Map<String, Object> attributes,
            boolean needCheckAttrs)
    {
        if (StringUtilities.isEmpty(fqn))
        {
            return logAndReturnEmptyString();
        }
        return add(fqn, null, parent, attributes, needCheckAttrs);
    }

    @Override
    public String add(@Nullable String fqn, @Nullable String mobileForm, @Nullable String parent,
            @Nullable Map<String, Object> attributes, boolean needCheckAttrs)
    {
        if (StringUtilities.isEmpty(fqn))
        {
            return logAndReturnEmptyString();
        }
        return getAddFormLink(ClassFqn.parse(fqn), mobileForm, parent, attributes, needCheckAttrs);
    }

    @Override
    public String asLink(String url)
    {
        return asLink(url, null);
    }

    @Override
    public String asLink(String url, @Nullable String title)
    {
        return String.format("<a href=\"%s\">%s</a>", url, StringUtilities.isEmpty(title) ? url : title);
    }

    @Override
    public String changeState(IUUIDIdentifiable object)
    {
        return changeState(object, Collections.emptyMap());
    }

    @Override
    public String changeState(IUUIDIdentifiable object, IAccessKeyWrapper accessKey)
    {
        return changeState(object, accessKey, Collections.emptyMap());
    }

    @Override
    public String changeState(@Nullable IUUIDIdentifiable object, @Nullable IAccessKeyWrapper accessKey,
            @Nullable Map<String, Object> values)
    {
        if (object == null || values == null)
        {
            return logAndReturnEmptyString();
        }
        Collection<FormPlaceParameter> formPlaceParameters = contentLinkHelper.convertAttributes(values, true, null);
        return buildUrlHelper.buildChangeStateLink(formPlaceParameters, open(object, accessKey));
    }

    @Override
    public String changeState(IUUIDIdentifiable object, Map<String, Object> values)
    {
        return changeState(object, null, values);
    }

    @Override
    public String content(IContentLinkDefinition<?> contentDefinition)
    {
        return contentLinkService.buildContentLink(contentDefinition, false);
    }

    @Override
    public IHierarchyGridLinkDefinition defineHierarchyGridLink()
    {
        return new HierarchyGridLinkDefinition();
    }

    @Override
    public IListLinkDefinition defineListLink(boolean onCard)
    {
        return new ListLinkDefinition(onCard);
    }

    @Override
    public IListLinkDefinition defineListLink(IListDescriptor listDescriptor, boolean onCard)
    {
        return DescriptorToListLinkDefinitionConverter.convert(listDescriptor, onCard);
    }

    @Override
    public IEditLinkDefinition defineEditLink()
    {
        return new EditLinkDefinition();
    }

    @Override
    public String edit(Object obj, IEditLinkDefinition definition)
    {
        String uuid = ApiUtils.getUuid(obj);
        if (uuid == null)
        {
            throw new FxException(String.format("Object %s not found", obj.toString()));
        }

        IAccessKeyWrapper accessKey = extractAccessKey(definition);

        String parameterAccessKey = accessKey == null ? null : toParameter(accessKey);
        Collection<FormPlaceParameter> parameters = convertAttributes(definition.getAttributes(), true);

        return buildUrlHelper.buildEditFormLink(new EditFormParametersHolderImpl(uuid, parameterAccessKey,
                definition.getMobileFormCode(), parameters));
    }

    @Nullable
    private IAccessKeyWrapper extractAccessKey(IEditLinkDefinition definition)
    {
        if (definition.getAccessKey() != null)
        {
            return definition.getAccessKey();
        }
        if (definition.getLogin() != null)
        {
            return authApi.getAccessKey(definition.getLogin());
        }
        if (definition.getUser() != null)
        {
            return authApi.getAccessKeyByUUID(definition.getUser());
        }
        return null;
    }

    @Override
    public String edit(@Nullable IUUIDIdentifiable obj)
    {
        if (obj == null)
        {
            return logAndReturnEmptyString();
        }
        return edit(obj.getUUID());
    }

    @Override
    public String edit(@Nullable IUUIDIdentifiable obj, @Nullable IAccessKeyWrapper accessKey)
    {
        if (obj == null)
        {
            return logAndReturnEmptyString();
        }
        return edit(obj, accessKey, null);
    }

    @Override
    public String edit(@Nullable IUUIDIdentifiable obj, @Nullable IAccessKeyWrapper accessKey,
            @Nullable String mobileCode)
    {
        if (obj == null)
        {
            return logAndReturnEmptyString();
        }
        return edit(obj.getUUID(), accessKey, mobileCode);
    }

    @Override
    public String edit(@Nullable IUUIDIdentifiable obj, @Nullable String username)
    {
        // AuthApi при вызове getAccessKey(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (obj == null || StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return edit(obj, username, null);
    }

    @Override
    public String edit(@Nullable IUUIDIdentifiable obj, @Nullable String username, @Nullable String mobileCode)
    {
        // AuthApi при вызове getAccessKey(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (obj == null || StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return edit(obj, authApi.getAccessKey(username), mobileCode);
    }

    @Override
    public String edit(String objUUID)
    {
        if (StringUtilities.isEmpty(objUUID))
        {
            return logAndReturnEmptyString();
        }
        return getBaseUrl() + "?" + toAnchor("edit:" + objUUID);
    }

    @Override
    public String edit(String objUUID, @Nullable IAccessKeyWrapper accessKey)
    {
        if (StringUtilities.isEmpty(objUUID))
        {
            return logAndReturnEmptyString();
        }

        return edit(objUUID, accessKey, null);
    }

    @Override
    public String edit(String objUUID, @Nullable IAccessKeyWrapper accessKey, @Nullable String mobileForm)
    {
        if (StringUtilities.isEmpty(objUUID))
        {
            return logAndReturnEmptyString();
        }

        String parameterAccessKey = accessKey == null ? null : toParameter(accessKey);
        return buildUrlHelper.buildEditFormLink(new EditFormParametersHolderImpl(objUUID, parameterAccessKey,
                mobileForm));
    }

    /**
     * Формирует ссылку на открытие формы редактирования объекта
     *
     * @param objUUID идентификатор объекта
     * @param accessKey ключ доступа
     * @param mobileCode код формы редактирования в мобильном клиенте. Может быть null.
     *                   Если он указан, то при открытии ссылки в МК будет использована указанная форма редактирования
     * @param attributes набор заполняемых на форме значений атрибутов
     *
     * @return строка со ссылкой на форму редактирования объекта
     *
     * @since 4.12.1
     */
    String edit(String objUUID, @Nullable IAccessKeyWrapper accessKey, @Nullable String mobileCode,
            @Nullable Map<String, Object> attributes)
    {
        if (StringUtilities.isEmpty(objUUID))
        {
            return logAndReturnEmptyString();
        }

        final StringBuilder url = new StringBuilder(getBaseUrl()).append('?');
        if (accessKey != null)
        {
            url.append(toParameter(accessKey)).append('&');
        }
        url.append(toAnchor("edit:" + objUUID));
        if (mobileCode != null)
        {
            url.append(UrlUtils.encodeUrlComponent("!{\"" + BuildUrlHelper.MOBILE_FORM + "\":\"" + mobileCode + "\"}"));
        }
        if (attributes != null)
        {
            if (mobileCode == null)
            {
                url.append(UrlUtils.encodeUrlComponent("!"));
            }
            String attributesStr = buildUrlHelper.getFormParametersAsString(contentLinkHelper.convertAttributes(
                    attributes, true));
            url.append(UrlUtils.encodeUrlComponent("!" + attributesStr));
        }
        return url.toString();
    }

    @Override
    public String edit(String objUUID, @Nullable String username)
    {
        // AuthApi при вызове getAccessKey(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return edit(objUUID, username, null);
    }

    @Override
    public String edit(@Nullable String objUUID, @Nullable String username, @Nullable String mobileCode)
    {
        // AuthApi при вызове getAccessKey(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (objUUID == null || StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return edit(objUUID, authApi.getAccessKey(username), mobileCode);
    }

    @Override
    public String editWithUserUUID(@Nullable IUUIDIdentifiable obj, @Nullable String userUuid)
    {
        // AuthApi при вызове getAccessKeyByUUID(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (obj == null || StringUtilities.isEmpty(userUuid))
        {
            return logAndReturnEmptyString();
        }
        return editWithUserUUID(obj, userUuid, null);
    }

    @Override
    public String editWithUserUUID(@Nullable IUUIDIdentifiable obj, @Nullable String userUuid,
            @Nullable String mobileCode)
    {
        // AuthApi при вызове getAccessKeyByUUID(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (obj == null || StringUtilities.isEmpty(userUuid))
        {
            return logAndReturnEmptyString();
        }
        return edit(obj.getUUID(), authApi.getAccessKeyByUUID(userUuid), mobileCode);
    }

    @Override
    public String editWithUserUUID(String objUUID, String userUuid)
    {
        // AuthApi при вызове getAccessKeyByUUID(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (StringUtilities.isEmpty(userUuid))
        {
            return logAndReturnEmptyString();
        }
        return editWithUserUUID(objUUID, userUuid, null);
    }

    @Override
    public String editWithUserUUID(String objUUID, String userUuid, @Nullable String mobileCode)
    {
        // AuthApi при вызове getAccessKeyByUUID(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (StringUtilities.isEmpty(userUuid))
        {
            return logAndReturnEmptyString();
        }
        return edit(objUUID, authApi.getAccessKeyByUUID(userUuid), mobileCode);
    }

    @Override
    public String encodeUrl(String url)
    {
        return BuildUrlHelper.encodeUrl(url);
    }

    @Override
    public String getBaseUrl()
    {
        return buildUrlHelper.getBaseUrl();
    }

    private Content getContentByFqnAndCode(ClassFqn fqn, String contentCode)
    {
        ContentInfo ci = metainfoService.getDeclaredNullableUiForm(fqn, WINDOW_KEY);
        if (ci == null)
        {
            ClassFqn parent = metainfoService.getParentClassFqn(fqn);
            return getContentByFqnAndCode(parent, contentCode);
        }
        return ContentUtils.findContentByUUID(ci.getContent(), contentCode);
    }

    @Override
    public String getPlace(@Nullable String url)
    {
        String marker = getBaseUrl() + "?" + toAnchor("");
        String result = url == null || !url.contains(marker) ? ""
                : url.substring(url.indexOf(marker) + marker.length());
        if (result.isEmpty())
        {
            LOG.warn(
                    "Unable to determine place. Url either contains access key or has no anchor.\nInput was: "
                    + "{}\nReturning empty string",
                    url);
        }
        return result;
    }

    @Override
    public String list(Object object, String listContentCode)
    {
        String objectUuid = null;
        if (object instanceof IUUIDIdentifiable identifiable)
        {
            objectUuid = identifiable.getUUID();
        }
        else if (object instanceof String uuid && (UuidHelper.isValid(uuid)))
        {
            objectUuid = uuid;
        }
        return list(defineListLink(true).setUuid(objectUuid).setListCode(listContentCode));
    }

    @Override
    public String list(String listTitle, String classFqn, String attrGroup)
    {
        return list(defineListLink(false).setTitle(listTitle).setClassCode(classFqn).setAttrGroup(attrGroup));
    }

    @Override
    public String list(IListLinkDefinition linkBuilder)
    {
        validateListLinkDefinition(linkBuilder);
        return linkBuilder.isOnCard() ? getListLinkOnCard(linkBuilder) : getListLinkOnNewWindow(linkBuilder);
    }

    /**
     * Получить ссылку на список объектов на отдельной странице
     *
     * @param linkBuilder конструктор ссылки на список объектов
     * @return ссылка на список объектов на отдельной странице либо пустая строка, если не определены обязательные
     * атрибуты
     */
    public String getListLinkOnNewWindow(final IListLinkDefinition linkBuilder)
    {
        final String listTitle = linkBuilder.getTitle();
        if (listTitle == null)
        {
            return logAndReturnEmptyString();
        }
        final String objectUuid = linkBuilder.getUuid();
        final ClassFqn fqnOfClass = ClassFqn.parse(linkBuilder.getClassCode());
        final List<String> possibleCases = linkBuilder.getCases();
        final MetaClass metaClass = objectListHelper.getMetaClass(fqnOfClass, possibleCases);
        final String attrGroup = linkBuilder.getAttrGroup();
        if (metaClass.getAttributeGroup(attrGroup) == null)
        {
            return logAndReturnEmptyString();
        }

        final IAttrChain attrChain = linkBuilder.attrChain();
        final List<String> attrsChain = ((AttrChain)attrChain).getAttributesChain();
        if (attrsChain.stream().anyMatch(this::checkComputableAttr))
        {
            String message =
                    "A computable attribute is specified as a link attribute. Specify a non-computable attribute.";
            LOG.error(message);
            throw new FxException(message);
        }

        final String typeList = linkBuilder.getListType() == null ? OBJECT_LIST : linkBuilder.getListType();
        if (checkCommonParametersBroken(attrChain, typeList, objectUuid))
        {
            logAndThrowAttrChainError();
        }

        if (REL_OBJECT_LIST.equalsIgnoreCase(typeList))
        {
            String attrCode = attrsChain.get(attrsChain.size() - 1);
            AttributeType attr = metainfoService.getAttribute(AttributeFqn.parse(attrCode)).getType();
            if (!LINK_ATTRIBUTE_TYPES.contains(attr.getCode()))
            {
                throw new FxException(messages.getMessage("listAttrChain.incorrectType"));
            }
            if (!metaClass.getFqn().fqnOfClass()
                    .equals(ClassFqn.parse(attr.getProperty(ObjectAttributeType.METACLASS_FQN)).fqnOfClass()))
            {
                throw new FxException(messages.getMessage("listAttrChain.incorrectAttr"));
            }
        }
        final boolean isMenuItem = linkBuilder.isMenuItem();
        final String templateCode = linkBuilder.getTemplate();
        if (!isMenuItem && templateCode != null && templateService.getTemplate(templateCode) == null)
        {
            throw new FxException(messages.getMessage("listTemplate.removed"));
        }
        //Учитываем, что контент может находиться на карточке типа
        final ClassFqn cardFqn = getCardClassFqn(objectUuid);

        final Integer linksLifetime = linkBuilder.getDaysToLive();
        final String branch = linkBuilder.getBranch();
        final String classFqnId = fqnOfClass.getId();

        //Получение объединенной фильтрации, включающую в себя сложную (видимую) и ограничивающую (не видимую)
        //фильтрацию, так как на отдельной странице сложная фильтрация используется как ограничение содержимого списка.
        final ListFilter filter = (ListFilter)linkBuilder.filter();
        final List<ListFilterAndElement> complexAndRestrictionFilters =
                listFilterHelper.createFilters(filter.listAndElements(), metaClass);
        complexAndRestrictionFilters.addAll(listFilterHelper.createFilters(filter.getRestrictionFilters(), metaClass));

        final Map<String, Object> listSettings = ListSettingsContext.newBuilder()
                .setTitle(listTitle)
                .setObjectFilter(complexAndRestrictionFilters)
                .setFastListFilter(listFilterHelper.createFastFilter(linkBuilder.getFastFilter(), metaClass))
                .setSort(createSort(linkBuilder.getSort(), metaClass))
                .setAttrCodes(parseAttrCodes(linkBuilder.getAttrCodes(), metaClass))
                .setAttrGroup(attrGroup)
                .setCases(possibleCases)
                .setPermittedUsers(linkBuilder.getUsers())
                .setClassFqn(classFqnId)
                .setListPresentation(linkBuilder.getListPresentationType())
                .setNavigationPosition(linkBuilder.getPaging())
                .setTemplateCode(templateCode)
                .setFastSubstringFilter(listFilterHelper.createFastSubstringFilter(
                        linkBuilder.getFastSubstringFilter(), metaClass))
                .setRestrictionFilterSettings(createRestrictionStrategiesMap(linkBuilder.getRestrictionFilter()))
                .setShowNested(Boolean.TRUE.equals(linkBuilder.getNested()))
                .setTypeList(typeList)
                .setObjectUuid(objectUuid)
                .setAttrChain(attrChain)
                .setCardFqn(cardFqn)
                .setLinksLifetime(linksLifetime)
                .setBranch(branch)
                .setBranchIgnored(plannedVersionLinkToListLicenseChecker.logIfBranchParameterIgnored(
                        objectUuid, branch))
                .setAddLinkDialogTitle(linkBuilder.getAddLinkDialogTitle())
                .setDefaultViewApplied(linkBuilder.isDefaultViewApplied())
                .build();

        final Collection<FormPlaceParameter> extraOptions = contentLinkHelper.convertAttributes(listSettings, false);
        return buildUrlHelper.buildObjectListLink(linksLifetime, extraOptions, true, isMenuItem);
    }

    @Nullable
    private ClassFqn getCardClassFqn(@Nullable String objectUuid)
    {
        if (objectUuid != null && !objectUuid.toLowerCase().startsWith(CURRENT_USER_LOWER_CASE))
        {
            return Root.CLASS_ID.equals(UuidHelper.toPrefix(objectUuid))
                    ? Root.FQN
                    : ((IHasMetaInfo)Objects.requireNonNull(apiUtils.load(objectUuid))).getMetaClass();
        }
        return null;
    }

    /**
     * Проверка заполненности необходимых данных для генерации ссылки на контент на новой странице
     * @param attrChain данные для связи через атрибут
     * @param typeList тип контента
     * @param objectUuid уникальный идентификатор объекта на карточке которого настроен список
     * @return true, если заполнены не все необходимые параметры для "Списка связанных объектов", либо
     * "Списка вложенных объектов"
     */
    private static boolean checkCommonParametersBroken(@Nullable IAttrChain attrChain, String typeList,
            @Nullable String objectUuid)
    {
        //Список объектов.
        if (OBJECT_LIST.equalsIgnoreCase(typeList))
        {
            return false;
        }
        //Список связанных объектов.
        if (REL_OBJECT_LIST.equalsIgnoreCase(typeList))
        {
            if (attrChain == null)
            {
                return true;
            }
            AttrChain chain = ((AttrChain)attrChain);
            return chain.getAttributesChain().isEmpty()
                   && (chain.getNestedHierarchyAttrFqn() == null
                       || chain.getNestedAttrLinkFqn() == null
                       || objectUuid == null);
        }
        //Список вложенных объектов.
        return CHILD_OBJECT_LIST.equalsIgnoreCase(typeList)
               && objectUuid == null
               && (attrChain == null || ((AttrChain)attrChain).getAttributesChain().isEmpty());
    }

    @Override
    public String open(@Nullable IUUIDIdentifiable obj)
    {
        if (obj == null)
        {
            return logAndReturnEmptyString();
        }

        return open(obj.getUUID());
    }

    @Override
    public String open(@Nullable IUUIDIdentifiable obj, @Nullable IAccessKeyWrapper accessKey)
    {
        if (obj == null)
        {
            return logAndReturnEmptyString();
        }

        return open(obj.getUUID(), accessKey);
    }

    @Override
    public String open(@Nullable IUUIDIdentifiable obj, @Nullable String username)
    {
        // AuthApi при вызове getAccessKey(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (obj == null || StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return open(obj.getUUID(), authApi.getAccessKey(username));
    }

    @Override
    public String open(String objUUID)
    {
        if (StringUtilities.isEmpty(objUUID))
        {
            return logAndReturnEmptyString();
        }
        return getBaseUrl() + "?" + toAnchor("uuid:" + objUUID);

    }

    @Override
    public String open(String objUUID, @Nullable IAccessKeyWrapper accessKey)
    {
        if (StringUtilities.isEmpty(objUUID))
        {
            return logAndReturnEmptyString();
        }

        StringBuilder url = new StringBuilder(getBaseUrl()).append('?');
        if (accessKey != null)
        {
            url.append(toParameter(accessKey)).append('&');
        }
        return url.append(toAnchor("uuid:" + objUUID)).toString();
    }

    @Override
    public String open(String objUUID, @Nullable String username)
    {
        // AuthApi при вызове getAccessKey(null) вызывает NPE.
        // После решения данной проблемы проверку необходимо убрать.
        if (StringUtilities.isEmpty(objUUID) || StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return open(objUUID, authApi.getAccessKey(username));
    }

    @Override
    public String openCommentInList(String objUUID, String commentUUID)
    {
        return String.format(TEMPLATE, open(objUUID), commentUUID);
    }

    @Override
    public String openFileInList(String objUUID, String fileUUID)
    {
        return String.format(TEMPLATE, open(objUUID), fileUUID);
    }

    /**
     * Генерация ссылки на контент на карточке объекта
     *
     * @param linkBuilder билдер для генерации ссылки на список объектов
     * @return ссылка на контент списка на карточке объекта
     */
    private String getListLinkOnCard(final IListLinkDefinition linkBuilder)
    {
        final String objectUuid = linkBuilder.getUuid();
        if (!UuidHelper.isValid(objectUuid, true))
        {
            throw new FxException(objectUuid + "is not valid UUID");
        }

        //Учитываем, что контент может находиться на карточке типа
        final IUUIDIdentifiable object = apiUtils.load(objectUuid);
        final ClassFqn objectFqn = ((IHasMetaInfo)object).getMetaClass();
        //При открытии списка на карточке объекта, нужен метакласс объектов списка
        final ObjectListBase content = (ObjectListBase)getContentByFqnAndCode(objectFqn, linkBuilder.getListCode());
        //В случае, если определены типы объектов, content.getClazz() вернет null
        final ClassFqn fqnOfClass = content.getFqnOfClass();
        final MetaClass metaclass = metainfoService.getMetaClass(Objects.requireNonNull(fqnOfClass));

        final String branch = linkBuilder.getBranch();
        final ListSettingsContext listContext = ListSettingsContext.newBuilder()
                .setListFilter(listFilterHelper.createFilters(linkBuilder.filter().listAndElements(), metaclass))
                .setObjectFilter(listFilterHelper.createFilters(
                        ((ListFilter)linkBuilder.filter()).getRestrictionFilters(), metaclass))
                .setFastListFilter(listFilterHelper.createFastFilter(linkBuilder.getFastFilter(), metaclass))
                .setSort(createSort(linkBuilder.getSort(), metaclass))
                .setAttrCodes(parseAttrCodes(linkBuilder.getAttrCodes(), metaclass))
                .setListContentCode(content.getUuid())
                .setFastSubstringFilter(listFilterHelper.createFastSubstringFilter(
                        linkBuilder.getFastSubstringFilter(), metaclass))
                .setRestrictionFilterSettings(createRestrictionStrategiesMap(linkBuilder.getRestrictionFilter()))
                .setLinksLifetime(linkBuilder.getDaysToLive())
                .setBranch(branch)
                .setBranchIgnored(plannedVersionLinkToListLicenseChecker.logIfBranchParameterIgnored(
                        objectUuid, branch))
                .setTypeList(StringUtilities.EMPTY)
                .setObjectUuid(objectUuid);

        return getListLinkOnCard(object, content, listContext, linkBuilder.getDaysToLive());
    }

    /**
     * Возвращает ссылку на контент списка на карточке объекта
     *
     * @param object объект карточке которого выведен список
     * @param content список объектов
     * @param listContext контекст с параметрами списка объектов
     * @param linksLifetime время жизни ссылки на список объектов
     * @return ссылка на контент списка на карточке объекта
     */
    public String getListLinkOnCard(
            final IUUIDIdentifiable object,
            final ObjectListBase content,
            final ListSettingsContext listContext,
            final @Nullable Integer linksLifetime)
    {
        if (!(content instanceof ObjectList)
            && !(content instanceof ChildObjectList)
            && !(content instanceof RelObjectList))
        {
            throw new FxException(String.format(OBJECT_LIST_NOT_FOUND_EXCEPTION_TEMPLATE, content.getUuid()));
        }

        final AttrChain attrChain = getAttrChain(content);
        final String listType = content.getClass().getSimpleName();
        if (REL_OBJECT_LIST.equalsIgnoreCase(listType) && checkCommonParametersBroken(attrChain, listType,
                object.getUUID()))
        {
            logAndThrowAttrChainError();
        }

        final String url = getObjectCardTabUrl(object, content);
        final String objectUuid = object.getUUID();
        final String contentUuid = content.getUuid();

        listContext.setScrollToElement("gwt-debug-" + listType + "." + contentUuid)
                .setListContentCode(contentUuid)
                .setShowNested(isShowNested(content))
                .setObjectUuid(objectUuid)
                .setAttrChain(attrChain)
                .setCardFqn(((IHasMetaInfo)object).getMetaClass());

        final Collection<FormPlaceParameter> extraOptions = contentLinkHelper.convertAttributes(listContext.build(),
                false);
        return buildUrlHelper.addExtraOptionsToUrl(url, extraOptions, true, linksLifetime, false);
    }

    /**
     * @param object  объект, на карточку которого будет получена ссылка
     * @param content список объектов на карточке объекта
     * @return ссылка на карточку объекта, либо на вкладку на карточке объекта
     */
    private String getObjectCardTabUrl(IUUIDIdentifiable object, ObjectListBase content)
    {
        List<String> tabs = new ArrayList<>();
        //Ищем табы для открытия
        Content parent = content.getParent();
        while (parent != null)
        {
            if (parent instanceof Tab)
            {
                tabs.add(parent.getUuid());
            }
            parent = parent.getParent();
        }
        String objectUuid = object.getUUID();
        return tabs.isEmpty() ? open(objectUuid) : openTab(objectUuid, String.join(StringUtilities.COMMA, tabs));
    }

    /**
     * Проверяет, что определены только доступные свойства для данного типа списка
     */
    private void validateListLinkDefinition(IListLinkDefinition builder)
    {
        if (ObjectListBase.PresentationType.DEFAULT.getCode().equals(builder.getListPresentationType()))
        {

            Map<Object, String> notPermittedProperties = Maps.newHashMapWithExpectedSize(4);
            notPermittedProperties.put(builder.filter().listAndElements(), PROP_FILTER);
            notPermittedProperties.put(builder.getSort(), PROP_SORT);
            notPermittedProperties.put(builder.getAttrCodes(), PROP_ATTR_CODES);
            notPermittedProperties.put(builder.getPaging(), PROP_ATTR_PAGING);
            notPermittedProperties.forEach((key, value) ->
            {
                if (key instanceof Collection && !((Collection<?>)key).isEmpty() ||
                    !(key instanceof Collection) && key != null)
                {
                    throw new FxException(String.format(PROPERTY_IS_NOT_APPLICABLE_EXCEPTION_TEMPLATE, value));
                }
            });
        }
        if (builder.getSort() != null)
        {
            //Необходимо исключить дубликаты (то есть сортировку по одному полю более одного раза)
            //Оставляем последний вариант.
            Map<String, IListLinkDefinition.ISort> map = new HashMap<>();
            for (IListLinkDefinition.ISort sort : builder.getSort())
            {
                map.put(sort.getAttrCode(), sort);
            }
            if (builder instanceof ListLinkDefinition)
            {
                ((ListLinkDefinition)builder).setSort(builder.getSort().stream()
                        .filter(map::containsValue)
                        .collect(Collectors.toList()));
            }
        }
        //Нельзя генерировать ссылку на некоторые классы (например, комментарии или файлы)
        if (!builder.isOnCard() && NOT_APPLICABLE_CLASS_CODES.contains(builder.getClassCode()))
        {
            throw new FxException(String.format(NOT_APPLICABLE_CLASS_EXCEPTION_TEMPLATE, builder.getClassCode()));
        }
        removeComputableAttr(builder);
    }

    /**
     * Необходимо исключить из фильтрации атрибуты  имеющие признак "Вычислимый"
     *
     * @param builder билдер для генерации ссылки на список объектов
     */
    private void removeComputableAttr(IListLinkDefinition builder)
    {
        List<IFilterAnd> iFilterAnds = builder.filter().listAndElements();
        List<IFilterOr> fastFilter = builder.getFastFilter();
        List<IFilterOr> fastSubstringFilter = builder.getFastSubstringFilter();

        if (!iFilterAnds.isEmpty())
        {
            List<IFilterAnd> newFilerAND = new ArrayList<>();

            iFilterAnds.forEach(and ->
            {
                List<IFilterOr> filterOrs = and.listOrElements()
                        .stream()
                        .filter(or -> !checkComputableAttr(or.getAttrCode()))
                        .collect(Collectors.toList());
                ListFilterAnd listFilterAnd = new ListFilterAnd(filterOrs);
                newFilerAND.add(listFilterAnd);
            });
            builder.filter().setListAndElements(newFilerAND);
        }

        if (!fastFilter.isEmpty())
        {
            fastFilter.removeIf(f -> checkComputableAttr(f.getAttrCode()));
        }
        if (!fastSubstringFilter.isEmpty())
        {
            fastSubstringFilter.removeIf(f -> checkComputableAttr(f.getAttrCode()));
        }
    }

    /**
     * Проверка что атрибут "Вычислимый"
     *
     * @param codeAttr код атрибута
     * @return true - "Вычислимый"
     */
    private boolean checkComputableAttr(String codeAttr)
    {
        String[] chainParts = codeAttr.split(AttributeLink.CHAIN_DELIMITER_REGEX);
        return Arrays.stream(chainParts).anyMatch(part -> AttributeFqn.isAttributeFqn(part)
                                                          && metainfoService.getAttribute(AttributeFqn.parse(part))
                                                                  .isComputable());
    }

    @Override
    public String openObjectInList(String listUUID, String objUUID)
    {
        return String.format(TEMPLATE, open(listUUID), objUUID);
    }

    @Override
    public String openTab(String objUUID, String tabId)
    {
        tabId = StringEscapeUtils.escapeJava(tabId);
        return String.format(TEMPLATE_OPEN_TAB, open(objUUID), UrlUtils.encodeUrlComponent(tabId));
    }

    @Override
    public String openTab(String objUUID, String tabId, @Nullable String username)
    {
        if (StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return openTab(objUUID, tabId, authApi.getAccessKey(username));
    }

    @Override
    public String openTab(String objUUID, String tabId, @Nullable IAccessKeyWrapper accessKey)
    {
        if (StringUtilities.isEmpty(objUUID))
        {
            return logAndReturnEmptyString();
        }
        final StringBuilder url = new StringBuilder(getBaseUrl()).append('?');
        if (accessKey != null)
        {
            url.append(toParameter(accessKey)).append('&');
        }
        url.append(toAnchor(UUID + ":" + objUUID))
                .append(String.format(TEMPLATE_OPEN_TAB_WITH_TOKEN, UrlUtils.encodeUrlComponent(tabId)));
        return url.toString();
    }

    @Override
    public String openContent(String objUUID, String tabId, @Nullable String contentId)
    {
        if (StringUtilities.isEmpty(contentId))
        {
            return openTab(objUUID, tabId);
        }
        tabId = StringEscapeUtils.escapeJava(tabId);
        contentId = StringEscapeUtils.escapeJava(contentId);
        return String.format(TEMPLATE_OPEN_CONTENT, open(objUUID), UrlUtils.encodeUrlComponent(tabId),
                UrlUtils.encodeUrlComponent(contentId));
    }

    @Override
    public String openContent(String objUUID, String tabId, @Nullable String contentId, String username)
    {
        if (StringUtilities.isEmpty(contentId))
        {
            return openTab(objUUID, tabId, username);
        }
        if (StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return openContent(objUUID, tabId, contentId, authApi.getAccessKey(username));
    }

    @Override
    public String openContent(String objUUID, String tabId, @Nullable String contentId,
            @Nullable IAccessKeyWrapper accessKey)
    {
        if (StringUtilities.isEmpty(contentId))
        {
            return openTab(objUUID, tabId, accessKey);
        }
        if (StringUtilities.isEmpty(objUUID))
        {
            return logAndReturnEmptyString();
        }
        final StringBuilder url = new StringBuilder(getBaseUrl()).append('?');
        if (accessKey != null)
        {
            url.append(toParameter(accessKey)).append('&');
        }
        url.append(toAnchor(UUID + ":" + objUUID))
                .append(String.format(TEMPLATE_OPEN_CONTENT_WITH_TOKEN, UrlUtils.encodeUrlComponent(tabId),
                        UrlUtils.encodeUrlComponent(contentId)));
        return url.toString();
    }

    @Override
    public String openSearch(Object classFqn, Map<String, Object> values, @Nullable IAccessKeyWrapper accessKey)
    {
        return openSearchInt(classFqn, values, RemovedMode.ACTIVE_OBJECTS_ONLY, accessKey);
    }

    @Override
    public String openSearch(Object classFqn, Map<String, Object> values)
    {
        return openSearchInt(classFqn, values, RemovedMode.ACTIVE_OBJECTS_ONLY, null);
    }

    @Override
    public String openSearch(Object classFqn, Map<String, Object> values, @Nullable String username)
    {
        if (StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return openSearchInt(classFqn, values, RemovedMode.ACTIVE_OBJECTS_ONLY, authApi.getAccessKey(username));
    }

    @Override
    public String openSearch(Object classFqn, Map<String, Object> values, String mode, @Nullable String username)
    {
        if (StringUtilities.isEmpty(username))
        {
            return logAndReturnEmptyString();
        }
        return openSearchInt(classFqn, values, ApiUtils.fromRemovedMode(mode), authApi.getAccessKey(username));
    }

    @Override
    public String openSearch(Object classFqn, Map<String, Object> values, String mode,
            @Nullable IAccessKeyWrapper accessKey)
    {
        if (values.isEmpty())
        {
            return logAndReturnEmptyString();
        }
        return openSearchInt(classFqn, values, ApiUtils.fromRemovedMode(mode), accessKey);
    }

    private String openSearchInt(Object fqn, Map<String, Object> values, RemovedMode mode,
            @Nullable IAccessKeyWrapper accessKey)
    {
        UriComponentsBuilder builder = UriComponentsBuilder.fromUriString(getBaseUrl());
        if (accessKey != null)
        {
            builder.query(toParameter(accessKey));
        }
        ClassFqn searchFqn = ApiUtils.extractFqn(fqn);
        MetaClass metaClass = metainfoService.getMetaClass(searchFqn);

        // в силу того, что метод metaClass.getSearchSetting() возвращает некорректные настройки,
        // получаем настройки поиска из списка со всеми настройками поиска
        Map<String, SearchSetting> searchSettings = metaClass.getAllSearchSettings().stream()
                .filter(searchSetting ->
                        !NOT_APPLICABLE_CLASS_CODES.contains(searchSetting.getDeclaredMetaClass().getId()))
                .filter(searchSetting -> values.containsKey(searchSetting.getAttrCode()))
                .collect(Collectors.toMap(SearchSetting::getAttrCode, Function.identity()));

        Set<SearchFilter> filters = new HashSet<>();
        for (Entry<String, Object> entry : values.entrySet())
        {
            String attributeCode = entry.getKey();
            HasSearchable searchSetting = searchSettings.get(attributeCode);
            if (searchSetting == null ||
                !(searchSetting.isSimpleSearchableForLicensed() || searchSetting.isSimpleSearchableForNotLicensed()
                  || searchSetting.isExtendedSearchableForLicensed()
                  || searchSetting.isExtendedSearchableForNotLicensed()))
            {
                throw new FxException(messages.getMessage("WebApi.wrongAttrSearchSettings", attributeCode), true);
            }

            String value = entry.getValue().toString();
            filters.add(SearchFilters.eq(searchFqn, attributeCode, attributeCode, value, searchFqn));
        }

        SearchFilter filter = filters.size() > 1 ? SearchFilters.and(filters) : filters.iterator().next();
        String fragmentUri = String.format(TEMPLATE_OPEN_SEARCH, searchFqn, mode.name(),
                JSONFilterConverter.toJSON(filter));

        return builder.fragment(fragmentUri).toUriString();
    }

    @Override
    public String openWithUserUUID(IUUIDIdentifiable obj, String userUuid)
    {
        return open(obj.getUUID(), authApi.getAccessKeyByUUID(userUuid));
    }

    @Override
    public String openWithUserUUID(String objUUID, String userUuid)
    {
        return open(objUUID, authApi.getAccessKeyByUUID(userUuid));
    }

    @Override
    public String toAnchor(String str)
    {
        return buildUrlHelper.toAnchor(str);
    }

    @Override
    public String toParameter(IAccessKeyWrapper accessKey)
    {
        return "accessKey=" + accessKey.getUuid();
    }

    @Override
    public String signIn(Object employee)
    {
        // Если метод выключен в параметрах, пробрасываем исключение
        if (!configurationProperties.isQuickSignInEnabled())
        {
            throw new FxException(messages.getMessage("WebApi.methodIsDisabled"));
        }

        // Если передан null, вернуть исключение
        if (employee == null)
        {
            throw new FxException(messages.getMessage("WebApi.employeeNotFound"));
        }

        // Если передана строка, проверяем, содержит ли она uuid и, если является, ищем сотрудника по uuid;
        // если же строка не содержит uuid, предполагаем, что там логин и ищем объект в системе по логину.
        // Если найти сотрудника не удалось - пробрасываем исключение.
        // Формируем ссылку используя найденный объект и возвращаем ее.
        if (employee instanceof String)
        {
            return buildSignInLink(findEmployee((String)employee));
        }

        // Если получен DtObject, извлекаем из него uuid, ищем в системе сотрудника по uuid и возвращаем
        // сформированную ссылку
        // Если найти сотрудника не удалось - пробрасываем исключение.
        if (employee instanceof IScriptDtObject)
        {
            String uuid = ((IScriptDtObject)employee).getProperty(AbstractBO.UUID);
            return buildSignInLink(findEmployee(uuid));
        }

        // Если получен IUUIDIdentifiable, формируем на его основе ссылку и возвращаем ее
        if (employee instanceof IUUIDIdentifiable)
        {
            return buildSignInLink((IUUIDIdentifiable)employee);
        }

        throw new FxException(messages.getMessage("WebApi.objectIsNotSuperuserOrEmployee"));
    }

    /**
     * Ищет сотрудника среди сотрудников и суперпользователей по uuid и логину
     *
     * @param uuidOrLogin uuid или логин сотрудника
     * @return найденного сотрудника
     * @throws UsernameNotFoundException если сотрудник не был найден
     */
    private IUUIDIdentifiable findEmployee(String uuidOrLogin) throws UsernameNotFoundException
    {
        if (UuidHelper.isValid(uuidOrLogin))
        {
            String code = ClassFqnHelper.toClassId(uuidOrLogin).getCode();
            if (!code.equals(Employee.getUUIDPrefix()) && !code.equals(SuperUserEmployee.getUUIDPrefix()))
            {
                throw new FxException(messages.getMessage("WebApi.objectIsNotSuperuserOrEmployee"));
            }

            IUUIDIdentifiable foundEmployee = apiUtils.getObjectSafe(uuidOrLogin);
            if (null != foundEmployee)
            {
                return foundEmployee;
            }

            throw new UsernameNotFoundException(messages.getMessage("WebApi.employeeNotFound"));
        }

        return authenticationUtils.findEmployee(uuidOrLogin)
                .orElseThrow(() -> new UsernameNotFoundException(messages.getMessage("WebApi.employeeNotFound")));
    }

    private String buildSignInLink(IUUIDIdentifiable employee)
    {
        // Если employee относится к типу Employee и сотрудник не является архивным, то
        // возвращаем ссылку с логином сотрудника, иначе пробрасываем исключение
        if (employee instanceof Employee)
        {
            if (((Employee)employee).isRemoved())
            {
                throw new FxException(messages.getMessage("WebApi.employeeIsArchived"));
            }

            String login = ((Employee)employee).getLogin();
            if (null == login)
            {
                throw new FxException(messages.getMessage("WebApi.employeeLoginIsNull"));
            }

            return buildSignInLink(login);
        }

        // Если employee относится к типу SuperUserEmployee и суперпользователь не ассоциирован с пользователем, то
        // возвращаем ссылку с логином суперпользователя, иначе пробрасываем исключение
        if (employee instanceof SuperUserEmployee)
        {
            String login = ((SuperUserEmployee)employee).getLogin();
            if (null == login)
            {
                throw new FxException(messages.getMessage("WebApi.employeeLoginIsNull"));
            }

            if (!authenticationUtils.hasAssociatedEmployee(login))
            {
                return buildSignInLink(login);
            }

            throw new FxException(messages.getMessage("WebApi.superuserIsLinkedToAccount"));
        }

        throw new FxException(messages.getMessage("WebApi.objectIsNotSuperuserOrEmployee"));
    }

    private String buildSignInLink(String login)
    {
        return authenticationUtils.buildUrl("?smp_login=" + login);
    }

    /**
     * Формирует ссылку для перехода на форму добавления объекта
     *
     * @param fqn {@link ClassFqn} добавляемого объекта. Если указан тип, то в контенте "Тип объекта" можно будет
     * выбрать только этот тип, если указан класс, то в поле "Тип объекта" можно будет выбрать все типы этого класса
     * (с учетом прав на выбор типа объекта и других ограничений)
     * @param parent родитель(для объектов, вложенных в другие) или клиент для запросов. Может быть null
     * @param attributes карта значений атрибутов по умолчанию для создаваемого объекта
     * <кода атрибута, значение атрибута>.
     * @return строка со ссылкой на форму добавления объекта
     */
    @VisibleForTesting
    protected String addInt(IClassFqn fqn, @Nullable String mobileCode, @Nullable String parent,
            @Nullable Map<String, Object> attributes)
    {
        List<String> possibleClasses = new ArrayList<>();
        if (fqn.isCase())
        {
            possibleClasses.add(fqn.getCase());
        }

        return addInternal(true, fqn, possibleClasses, mobileCode, parent, attributes);
    }

    /**
     * Формирует ссылку для перехода на форму добавления объекта без дополнительных проверок
     * на существование заданных атрибутов в метаклассе и корректности их значений
     *
     * @param fqns набор {@link ClassFqn} типов добавляемого объекта. Допускается указывать только типы объектов
     * одного класса.
     * @param parent родитель(для объектов, вложенных в другие) или клиент для запросов. Может быть null.
     * @param attributes карта значений атрибутов по умолчанию для создаваемого объекта
     * <кода атрибута, значение атрибута>.
     * @return строка со ссылкой на форму добавления объекта.
     */
    @VisibleForTesting
    protected <E> String addIntUnsafe(Set<E> fqns, @Nullable String mobileCode, @Nullable String parent,
            @Nullable Map<String, Object> attributes)
    {
        if (CollectionUtils.isEmpty(fqns))
        {
            throw new FxException(messages.getMessage("WebApi.fqnCollectionIsEmpty"), true);
        }

        Collection<ClassFqn> classFqns = ApiUtils.extractFqns(fqns);
        boolean isOnlyOneFqn = classFqns.size() == 1;
        ClassFqn classFqn = isOnlyOneFqn ? classFqns.iterator().next()
                : ClassFqn.parse(classFqns.iterator().next().getId());
        for (ClassFqn fqn : classFqns)
        {
            if (!isOnlyOneFqn && !classFqn.toString().equals(fqn.getId()))
            {
                throw new FxException(messages.getMessage("WebApi.fqnCollectionMustConsistOfFqnsWithSameClassId"),
                        true);
            }
        }
        List<String> possibleClasses = convertCases(classFqns);
        return addInternal(false, classFqn, possibleClasses, mobileCode, parent, attributes);
    }

    /**
     * Преобразует набор {@link ClassFqn} в отсортированный список названий типов
     *
     * @param fqns исходный набор
     * @return отсортированный список названий типов
     */
    public static List<String> convertCases(Collection<ClassFqn> fqns)
    {
        return fqns.stream().map(ClassFqn.CASE_EXTRACTOR).sorted().collect(Collectors.toList());
    }

    /**
     * Общий код методов {@link #addInt(IClassFqn, String, String, Map)} и
     * {@link #addIntUnsafe(Set, String, String, Map)}
     *
     * @param useValidation необходимость проверки существования атрибутов в метаклассе и объектов в значениях
     * @return ссылку на форму добавления
     */
    protected String addInternal(boolean useValidation, IClassFqn fqn, List<String> possibleClasses,
            @Nullable String mobileCode, @Nullable String parent, @Nullable Map<String, Object> attributes)
    {
        boolean isFastCreate = !MapUtils.isEmpty(attributes) && mobileCode != null || isNewUrlFormat(attributes);

        if (useValidation && !MapUtils.isEmpty(attributes))
        {
            apiUtils.checkMetaClassContainsAttibutes(attributes, metainfoService.getMetaClass(fqn));
        }
        Collection<FormPlaceParameter> parameters = contentLinkHelper.convertAttributes(attributes, useValidation, fqn);

        String result = buildUrlHelper.buildAddFormLink(new AddFormParametersHolderImpl((ClassFqn)fqn, parent,
                parameters, possibleClasses, isFastCreate, mobileCode));

        /*
         * До рефакторинга, в методе #addInt(IClassFqn, String, String, Map) перед возвращением
         * значения удалялись двоеточия при помощи StringUtilities.removeEndRecursively(String, String),
         * а в addIntUnsafe(Set, String, String, Map) - не удалялись.
         * Оставил как есть, так как иначе падали тесты.
         * Также был следующий комментарий: "Все двоеточия в конце нужно убрать, иначе портится все, что будет после url
         * Например: !{"fast":"true"} заменяется на !%7B%22fast%22:%22true%22%7D и кнопка Назад в браузере перестает
         * работать"
         */
        return useValidation ? StringUtilities.removeEndRecursively(result, StringUtilities.COLON) : result;
    }

    /**
     * Требуется ли для переданного списка атрибутов использовать новый формат ссылки?
     *
     * @param attributes - атрибуты
     * @return true, если среди переданных атрибутов есть атрибуты классов,
     * для передачи которых требуется использовать "новый" формата ссылки, иначе false
     */
    private static boolean isNewUrlFormat(@Nullable Map<String, Object> attributes)
    {
        // @formatter:off
        return null != attributes && null != attributes.values().stream().filter(obj ->
                    obj != null &&
                    null == OLD_URL_FORMAT_ATTRIBUTE_TYPES.stream().filter(cls -> cls.isAssignableFrom(obj.getClass())).findAny().orElse(null) &&
                    ((obj instanceof String) ?
                        !UuidHelper.isValid((String)obj, true) :
                            (null != NEW_URL_FORMAT_ATTRIBUTE_TYPES.stream().filter(cls -> cls.isAssignableFrom(obj.getClass())).findAny().orElse(null)))
                ).findAny().orElse(null);
        // @formatter:on
    }

    /**
     * Метод формирует ссылку для перехода на форму добавления объекта.
     *
     * @param fqn {@link ClassFqn} добавляемого объекта.
     * @param parent родитель(для объектов, вложенных в другие) или клиент для запросов.
     * @param attributes карта значений атрибутов по умолчанию для создаваемого объекта <кода атрибута, значение
     *                   атрибута>.
     * В качестве значения атрибута типа "Ссылка на БО" может выступать как объект, так и его UUID).
     * @param needCheckAttrs требуются ли проверки на существование заданных атрибутов в метаклассе и корректности их
     *                      значений.
     *
     * @return строка со ссылкой на форму добавления объекта.
     */
    private String getAddFormLink(IClassFqn fqn, @Nullable String mobileCode, @Nullable String parent,
            @Nullable Map<String, Object> attributes,
            boolean needCheckAttrs)
    {
        return needCheckAttrs ? addInt(fqn, mobileCode, parent, attributes)
                : addIntUnsafe(Sets.newHashSet(fqn), mobileCode, parent, attributes);
    }

    /**
     * Выполняет запись в лог информации о том что один из параметров метода был null или пуст и возвращает пустую
     * строку
     *
     * @return Пустая строка
     */
    private static String logAndReturnEmptyString()
    {
        LOG.warn("One or more arguments are null or empty. Returning empty string.");
        return StringUtilities.EMPTY;
    }

    private static void logAndThrowAttrChainError()
    {
        String message = "Property for attribute chain isn't correct";
        LOG.error(message);
        throw new FxException(message);
    }
}
