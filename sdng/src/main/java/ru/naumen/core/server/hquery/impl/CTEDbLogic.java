package ru.naumen.core.server.hquery.impl;

import java.util.List;
import java.util.Set;

import org.hibernate.Session;
import org.hibernate.dialect.Dialect;
import org.hibernate.dialect.OracleDialect;
import org.hibernate.dialect.PostgreSQLDialect;
import org.hibernate.dialect.SQLServerDialect;
import org.hibernate.engine.spi.SessionFactoryImplementor;

import com.google.common.base.Function;
import com.google.common.base.Predicate;
import com.google.common.collect.Iterables;
import com.google.common.collect.Sets;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.hquery.AfterQueryHandler;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Класс инкапсулирует специфику БД при построении HQL запросов использующих
 * Common Table Expression (CTE) а также подзапросов для выборки иерархии
 *
 * <AUTHOR>
 * @since 18 июня 2015 г.
 */
public class CTEDbLogic implements AfterQueryHandler
{
    static class ColumnAliasPredicate implements Predicate<HColumn>
    {
        private String alias;

        ColumnAliasPredicate(String alias)
        {
            this.alias = alias;
        }

        @Override
        public boolean apply(HColumn input)
        {
            return alias.equals(input.getAlias());
        }
    }

    ;

    /**
     * Абстракция инкапсулирующая специфику СУБД
     */
    abstract class DBSpecific implements AfterQueryHandler
    {
        @Override
        public void afterQuery(Session session)
        {

        }

        /**
         * Генерирует eHQL запрос
         * @return
         */
        abstract String createHierarchyCTE(Session session, HCriteriaDelegate delegate);

        void customize(HCriteriaDelegate original)
        {

        }

        String getCTEPrefix()
        {
            return "WITH";
        }

        String getCTESourceSuffix()
        {
            return StringUtilities.EMPTY;
        }
    }

    class MsSQLImpl extends DBSpecific
    {
        private String pathAlias = "path";

        @Override
        String createHierarchyCTE(Session session, HCriteriaDelegate delegate)
        {
            final String cteAlias = delegate.getPriorSource().getAlias();
            HCriterion startCriterion = delegate.changeStartCriterion(null);
            HCriterion connectCriterion = delegate.changeConnectCriterion(null);
            delegate.add(startCriterion);
            List<HColumn> criteriaColumns = null == delegate.getStartColumns()
                    ? null
                    : delegate.replaceColumns(delegate.getStartColumns());
            final String idCol = Iterables.find(delegate.getColumns(),
                    new ColumnAliasPredicate("id")).getHQL(new HBuilder(delegate, session));
            delegate.addColumn(new HColumnImpl("CAST(concat('/', CAST(" + idCol + " AS string)) AS text)", pathAlias));
            String startHQL = delegate.generateHQL(session);
            if (null != criteriaColumns)
            {
                delegate.replaceColumns(criteriaColumns);
            }

            delegate.addSource(delegate.getPriorSource());
            Iterables.removeIf(delegate.getColumns(), new ColumnAliasPredicate(pathAlias));
            delegate.addColumn(new HColumnImpl("CAST(concat(" + cteAlias + "." + pathAlias + ", '/', CAST(" + idCol
                                               + " AS string)) AS text)", pathAlias));
            delegate.getCriterions().remove(startCriterion);
            delegate.add(connectCriterion);
            // Дополнительное условие для исключения ошибки с достижением глубокого уровня рекурсии если есть
            // петли в зависимостях
            delegate.add(new AbstractHCriterion(null)
            {
                @Override
                public void append(StringBuilder sb, HBuilder builder,
                        NameGenerator parameterCounter)
                {
                    sb.append(cteAlias)
                            .append('.')
                            .append(pathAlias)
                            .append(" not like concat('%/', CAST(")
                            .append(idCol)
                            .append(" AS string), '/%')");
                }

                @Override
                protected HCriterion createCopyInstance()
                {
                    return this;
                }
            });
            String connectHQL = delegate.generateHQL(session);
            return String.format(" %s %nUNION ALL%n %s ", startHQL, connectHQL);
        }

        @Override
        void customize(HCriteriaDelegate original)
        {
            super.customize(original);
            Set<String> colAliases = Sets.newHashSet(Iterables.transform(original.getColumns(), COL_ALIAS_EXTRACTOR));
            if (colAliases.contains(pathAlias))
            {
                for (int i = 1; i < 100; ++i)
                {
                    String newAlias = "path" + i;
                    if (!colAliases.contains(newAlias))
                    {
                        this.pathAlias = newAlias;
                        break;
                    }
                }
            }
            original.addColumn(new HColumnImpl(pathAlias, pathAlias));
        }

    }

    abstract class CTEImplBase extends DBSpecific
    {
        @Override
        String createHierarchyCTE(Session session, HCriteriaDelegate delegate)
        {
            HCriterion startCriterion = delegate.changeStartCriterion(null);
            HCriterion connectCriterion = delegate.changeConnectCriterion(null);
            List<HColumn> criteriaColumns = null == delegate.getStartColumns()
                    ? null
                    : delegate.replaceColumns(delegate.getStartColumns());
            delegate.add(startCriterion);
            String startHQL = delegate.generateHQL(session);
            delegate.getCriterions().remove(startCriterion);
            if (null != criteriaColumns)
            {
                delegate.replaceColumns(criteriaColumns);
            }
            delegate.add(connectCriterion);
            delegate.addSource(delegate.getPriorSource());
            String connectHQL = delegate.generateHQL(session);
            return String.format(" %s %n%s%n %s ", startHQL, getUnionKeyword(), connectHQL);
        }

        abstract String getUnionKeyword();
    }

    class PostgresqlImpl extends CTEImplBase
    {
        @Override
        String getUnionKeyword()
        {
            return "UNION";
        }
    }

    class OracleImpl extends CTEImplBase
    {
        @Override
        String getUnionKeyword()
        {
            return "UNION ALL";
        }

        @Override
        String getCTESourceSuffix()
        {
            return "CYCLE id SET looped TO 1 DEFAULT 0";
        }
    }

    static Function<HColumn, String> COL_ALIAS_EXTRACTOR = new Function<HColumn, String>()
    {
        @Override
        public String apply(HColumn input)
        {
            return input.getAlias();
        }
    };

    private DBSpecific spec = null;

    @Override
    public void afterQuery(Session session)
    {
        ensureInitialized(session);
        spec.afterQuery(session);
    }

    public String createHierarchyHql(Session session, HCriteriaDelegate delegate)
    {
        ensureInitialized(session);
        HCriteriaDelegate copy = delegate.cloneOver();
        spec.customize(delegate);
        return spec.createHierarchyCTE(session, copy);
    }

    public String getCTEPrefix(Session session)
    {
        ensureInitialized(session);
        return spec.getCTEPrefix();
    }

    public String getCTESourceSuffix(Session session)
    {
        ensureInitialized(session);
        return spec.getCTESourceSuffix();
    }

    @SuppressWarnings("deprecation")
    private void ensureInitialized(Session session)
    {
        if (null == spec)
        {
            Dialect dialect = ((SessionFactoryImplementor)session.getSessionFactory()).getJdbcServices().getDialect();
            if (dialect instanceof PostgreSQLDialect)
            {
                spec = new PostgresqlImpl();
            }
            else if (dialect instanceof OracleDialect)
            {
                spec = new OracleImpl();
            }
            else if (dialect instanceof SQLServerDialect)
            {
                spec = new MsSQLImpl();
            }
            else
            {
                throw new FxException("Unsupported db with dialect: " + dialect);
            }
        }
    }
}
