package ru.naumen.core.server.script.storage.modification.edit;

import java.util.Collection;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.script.spi.ScriptServiceImpl;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants.Modules;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;
import ru.naumen.reports.server.ReportsStorageService;
import ru.naumen.reports.shared.ReportTemplate;
import ru.naumen.reports.shared.dispatch.GetReportParametersAction;
import ru.naumen.smp.report.specification.shared.Parameter;

/**
 * Стратегия обновления мест использования при изменении тела скрипта для категории {@link OtherCategories}
 * Происходит обновление сервисов для CTI и параметров скрипта для шаблонов отчетов.
 * Логика строится из расчета, что скрипт уже обновлен, выполняются сопутствующие
 * изменения в местах их использования.
 * <AUTHOR>
 * @since Oct 8, 2015
 */
@Lazy
@Component
public class OtherScriptBodyUpdateStrategy implements ScriptBodyUpdateStrategy
{
    @Lazy
    @Inject
    private ScriptServiceImpl scriptService;
    @Lazy
    @Inject
    private ReportsStorageService reportsStorageService;
    @Lazy
    @Inject
    private Dispatch dispatch;
    @Lazy
    @Inject
    private ScriptStorageService scriptStrorageService;

    @Override
    public void updateScriptUsagePoint(ScriptUsagePoint usagePoint)
    {
        OtherCategories otherCategory = (OtherCategories)usagePoint.getCategory();
        switch (otherCategory)
        {
            case CTI:
                scriptService.reloadModule(Modules.CTI);
                break;

            case REPORT_TEMPLATE:
                ReportTemplate template = reportsStorageService.getTemplate(usagePoint.getLocation());
                Script script = scriptStrorageService.getScript(template.getScript());

                GetReportParametersAction getReportParametersAction = new GetReportParametersAction(script);
                getReportParametersAction.setNeedReportParametersValidation(false);

                Collection<Parameter> parameters = dispatch
                        .executeExceptionSafe(getReportParametersAction)
                        .getParametersOrdered();
                template.getParameters().clear();
                template.getParameters().addAll(parameters);
                reportsStorageService.saveTemplate(template);
                break;
            default:
                break;
        }
    }
}
