package ru.naumen.core.server.script.dependencies;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.codehaus.groovy.transform.GroovyASTTransformationClass;

/**
 * Используется в скриптах для указания на наличие зависимости от значений одного или нескольких атрибутов
 * в контекстной переменной subject.
 * <AUTHOR>
 * @since Mar 25, 2016
 */
@Retention(RetentionPolicy.SOURCE)
@Target({ ElementType.LOCAL_VARIABLE, ElementType.FIELD, ElementType.METHOD, ElementType.TYPE,
        ElementType.CONSTRUCTOR })
@GroovyASTTransformationClass(classes = { SubjectDependencyExtractor.class })
public @interface DependsOn
{
    String[] value();
}
