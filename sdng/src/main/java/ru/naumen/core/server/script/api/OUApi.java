package ru.naumen.core.server.script.api;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.Sets;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.UUIDIdentifiableBase;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.bo.agreement.Agreement;
import ru.naumen.core.server.bo.agreement.HasAgreements;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.employee.EmployeeDao;
import ru.naumen.core.server.bo.ou.ISOU;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.bo.ou.OUDao;
import ru.naumen.core.server.bo.team.TeamDao;
import ru.naumen.core.server.cluster.readonlysettings.ReadOnlyClusterSettings;
import ru.naumen.core.server.cluster.readonlysettings.ReadOnlyClusterSettingsService;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.spi.IScriptDtObject;
import ru.naumen.core.server.script.spi.IScriptObjectBase;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.ICoreRemovable;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * API для работы с оргструктурой
 *
 * <AUTHOR>
 *
 * @since ******* (03.08.2012)

 * <AUTHOR>
 * @since 03.08.2012
 * @see IOuApi

 */
@Component("ou")
public class OUApi implements IOuApi
{
    private static final Logger LOG = LoggerFactory.getLogger(OUApi.class);

    private final OUDao ouDao;
    private final EmployeeDao<?> employeeDao;
    private final TeamDao teamDao;
    private final ScriptDtOHelper dtoHelper;
    private final CommonUtils commonUtils;
    private final MessageFacade messageFacade;
    private final SessionFactory sessionFactory;
    private final ReadOnlyClusterSettingsService settingsService;
    private final ApiUtils apiUtils;

    @Inject
    public OUApi(OUDao ouDao, EmployeeDao<?> employeeDao, TeamDao teamDao,
            ScriptDtOHelper dtoHelper, CommonUtils commonUtils, MessageFacade messageFacade,
            SessionFactory sessionFactory, ReadOnlyClusterSettingsService settingsService, ApiUtils apiUtils)
    {
        this.ouDao = ouDao;
        this.employeeDao = employeeDao;
        this.teamDao = teamDao;
        this.dtoHelper = dtoHelper;
        this.commonUtils = commonUtils;
        this.messageFacade = messageFacade;
        this.sessionFactory = sessionFactory;
        this.settingsService = settingsService;
        this.apiUtils = apiUtils;
    }

    @Override
    public void addToEntitiesWithWritePermissions(String uuid)
    {
        IUUIDIdentifiable ou = apiUtils.load(uuid);
        if (ou == null)
        {
            throw new FxException("Object with uuid '" + uuid + "' didn't find.", true);
        }
        settingsService.addOU(getOU(ou));
    }

    @Override
    public void addToEntitiesWithWritePermissions(IUUIDIdentifiable object)
    {
        IUUIDIdentifiable ou = apiUtils.getObject(object);
        if (ou == null)
        {
            throw new FxException("Ou is null!", true);
        }
        settingsService.addOU(getOU(ou));
    }

    @Override
    public void assignAgreementToEmployee(IUUIDIdentifiable employee, IUUIDIdentifiable agreement)
    {
        Employee empl = commonUtils.getByUUID(employee.getUUID());
        Agreement agr = commonUtils.getByUUID(agreement.getUUID());
        Collection<Agreement> agreements = empl.getRecipientAgreements();

        if (!agreements.contains(agr))
        {
            agreements.add(agr);
            IProperties properties = new MapProperties();
            properties.setProperty(Constants.Employee.RECIPIENT_AGREEMENTS, agreements);
            commonUtils.edit(empl, properties);
        }
    }

    @Override
    public void distributeAgreements(Object agreement, Collection<String> recipients)
    {
        distributeAgreements(agreement, recipients, true);
    }

    @Override
    public void distributeAgreements(Object agreement, Collection<String> recipients, boolean ignoreRemoved)
    {
        if (recipients.isEmpty())
        {
            return;
        }

        final Set<Object> agreementObjects = Sets.newHashSet(CollectionUtils.asCollection(agreement));
        final var session = sessionFactory.getCurrentSession();
        recipients.forEach(recipientUuid ->
        {
            IUUIDIdentifiable recipient = apiUtils.load(recipientUuid);
            /*
            если существует возможность добавить соглашение и в результате добавления коллекция соглашений клиента
            изменилась, то сохраняем изменения
            */
            if (isItPossibleToAddAgreement(recipient, recipientUuid, ignoreRemoved))
            {
                final Set<Agreement> agreements = agreementObjects.stream()
                        .map(agreementObject -> (Agreement)apiUtils.getObject(agreementObject))
                        .collect(Collectors.toSet());
                //если получилось добавить соглашения - то пересохраняем реципиента
                if (recipient != null && ((HasAgreements)recipient).getRecipientAgreements().addAll(agreements))
                {
                    session.merge(recipient);
                    LOG.debug("distribution of agreements: agreements added to recipient '{}' successfully.",
                            recipientUuid);
                }
            }
        });
    }

    @Override
    public void distributeAgreements(Object agreement, String rootRecipient)
    {
        distributeAgreements(agreement, rootRecipient, true);
    }

    @Override
    public void distributeAgreements(Object agreement, String rootRecipient, boolean ignoreRemoved)
    {
        Set<String> recipients = new HashSet<>();
        recipients.add(rootRecipient);
        if (rootRecipient.startsWith(OU.CLASS_ID) || rootRecipient.startsWith(Root.CLASS_ID))
        {
            recipients.addAll(nestedOUUuids(rootRecipient));
            recipients.addAll(nestedEmployeeUuids(rootRecipient));
        }
        if (rootRecipient.startsWith(Team.CLASS_ID))
        {
            recipients.addAll(teamDao.getTeamMembers(rootRecipient));
        }
        distributeAgreements(agreement, recipients, ignoreRemoved);
    }

    @Override
    public void deleteFromEntitiesWithWritePermissions(String uuid)
    {
        settingsService.removeOU(uuid);
    }

    @Override
    public void deleteFromEntitiesWithWritePermissions(IUUIDIdentifiable object)
    {
        IUUIDIdentifiable ou = apiUtils.getObject(object);
        if (ou == null)
        {
            throw new FxException("Ou is null!", true);
        }
        settingsService.removeOU(getOU(ou).getUUID());
    }

    @Override
    public Collection<IScriptObjectBase<ISOU>> listEntitiesWithWritePermissions()
    {
        ReadOnlyClusterSettings settings = settingsService.getSettings();
        if (settings == null)
        {
            return Collections.emptyList();
        }
        return dtoHelper.wrapLazy(settings.getOuWriters().stream().map(UUIDIdentifiableBase::getUUID).toList());
    }

    /**
     * Вспомогательный метод проверки на возможность добавления соглашений реципиенту
     * @param recipient объект для которого осуществляем проверку
     * @param recipientUuid uuid объекта, переданного на проверку, передается отдельно для логирования ошибки, на
     *        случай, если объект не существует
     * @param ignoreRemoved параметр проверки, если true - отбираем только активных, если false - то всех
     * @return true если возможно добавить соглашения, иначе false
     */
    private static boolean isItPossibleToAddAgreement(@Nullable IUUIDIdentifiable recipient, String recipientUuid,
            boolean ignoreRemoved)
    {
        if (null == recipient)
        {
            LOG.error("distribution of agreements: recipient uuid = '{}' not found.", recipientUuid);
            return false;
        }
        if (!(recipient instanceof HasAgreements))
        {
            LOG.error("distribution of agreements: object '{}' is not to be a recipient of agreement.", recipientUuid);
            return false;
        }
        if (ignoreRemoved)
        {
            return !((ICoreRemovable)recipient).isRemoved();
        }
        return true;
    }

    @Override
    public Collection<IScriptObjectBase<ISOU>> listNestedOUs(@Nullable String uuid)
    {
        return listNestedOUs(uuid, false);
    }

    @Override
    public Collection<IScriptObjectBase<ISOU>> listNestedOUs(@Nullable String uuid, boolean ignoreRemoved)
    {
        if (StringUtilities.isEmpty(uuid))
        {
            return Collections.emptyList();
        }
        try
        {
            Collection<String> nestedOUUuids = ouDao.getChildUUIDs(uuid, ignoreRemoved);
            return dtoHelper.wrapLazy(nestedOUUuids);
        }
        catch (FxException ex)
        {
            LOG.error(String.format("OUApi: error during getting nested ous - listNestedOUs(%s)", uuid), ex);

            throw new FxException(
                    messageFacade.getMessage("ScriptApi.call.error", "listNestedOUs", uuid), true);
        }
    }

    @Override
    public Collection<IScriptDtObject> nestedEmployees(Object object)
    {
        return nestedEmployees(object, false);
    }

    @Override
    public Collection<IScriptDtObject> nestedEmployees(Object object, boolean ignoreRemoved)
    {
        Collection<String> employeeUuids = nestedEmployeeUuids(object, ignoreRemoved);
        return dtoHelper.wrapLazy(employeeUuids);
    }

    @Override
    public Collection<String> nestedEmployeeUuids(Object object, boolean ignoreRemoved)
    {
        IUUIDIdentifiable ou = apiUtils.getObject(object);
        return ApiUtils.isRoot(ou)
                ? employeeDao.getAllUuids(ignoreRemoved)
                : ouDao.getChildEmployees(getOU(ou), ignoreRemoved);
    }

    @Override
    public Collection<String> nestedEmployeeUuids(Object object)
    {
        return nestedEmployeeUuids(object, false);
    }

    @Override
    public Collection<IScriptDtObject> nestedOUs(Object object)
    {
        return dtoHelper.wrapLazy(nestedOUUuids(object));
    }

    @Override
    public Collection<IScriptDtObject> nestedOUs(Object object, boolean ignoreRemoved)
    {
        Collection<String> nestedOUs = nestedOUUuids(object, ignoreRemoved);
        return dtoHelper.wrapLazy(nestedOUs);
    }

    @Override
    public Collection<String> nestedOUUuids(Object object)
    {
        return nestedOUUuids(object, false);
    }

    @Override
    public Collection<String> nestedOUUuids(Object object, boolean ignoreRemoved)
    {
        IUUIDIdentifiable ou = apiUtils.getObject(object);
        return ApiUtils.isRoot(ou)
                ? ouDao.getAllUuids(ignoreRemoved)
                : ouDao.getChildOus(getOU(ou), ignoreRemoved);
    }

    @Override
    public void removeAgreements(Object agreement, Collection<String> recipients)
    {
        removeAgreements(agreement, recipients, "active");
    }

    @Override
    public void removeAgreements(Object agreement, String rootRecipient)
    {
        removeAgreements(agreement, rootRecipient, "active");
    }

    @Override
    public void removeAgreements(Object agreement, Collection<String> recipients, String searchMode)
    {
        if (recipients.isEmpty())
        {
            return;
        }

        final Set<Object> agreementObjects = Sets.newHashSet(CollectionUtils.asCollection(agreement));
        final Set<Agreement> agreements = agreementObjects.stream()
                .map(object -> (Agreement)apiUtils.getObject(object))
                .collect(Collectors.toSet());
        final var session = sessionFactory.getCurrentSession();
        for (String recipientUUID : recipients)
        {
            AbstractBO recipient = apiUtils.load(recipientUUID);
            /*
            если существует возможность удалить соглашение, и реципиент удовлетворяет условию поиска и
            соглашения успешно удалены у реципиента
             */
            if (isItPossibleToRemoveAgreement(recipient)
                && isSearchModeMatchesWithObjectState(recipient, searchMode)
                && ((HasAgreements)recipient).getRecipientAgreements().removeAll(agreements))
            {
                session.merge(recipient);
                LOG.debug("Remove agreements: agreement for the object '{}' was successfully deleted.",
                        recipientUUID);
            }
        }
    }

    @Override
    public void removeAgreements(Object agreement, String rootRecipient, String searchMode)
    {
        Set<String> recipients = new HashSet<>();
        recipients.add(rootRecipient);
        if (rootRecipient.startsWith(OU.CLASS_ID) || rootRecipient.startsWith(Root.CLASS_ID))
        {
            recipients.addAll(nestedOUUuids(rootRecipient));
            recipients.addAll(nestedEmployeeUuids(rootRecipient));
        }
        if (rootRecipient.startsWith(Team.CLASS_ID))
        {
            recipients.addAll(teamDao.getTeamMembers(rootRecipient));
        }
        removeAgreements(agreement, recipients, searchMode);
    }

    /**
     * Вспомогательный метод проверки на возможность удаления соглашений у объекта/реципиента
     * @param recipient объект для проверки
     * @return true если существует возможность работы с соглашениями, иначе false
     */
    private static boolean isItPossibleToRemoveAgreement(@Nullable IUUIDIdentifiable recipient)
    {
        if (!(recipient instanceof HasAgreements))
        {
            LOG.error("Remove agreements: object '{}' is not to be a recipient of agreement.", recipient != null ?
                    recipient.getUUID() : "null");
            return false;
        }
        return true;
    }

    /**
     * Вспомогательный метод возвращающий значение статуса объекта(архивный/не архивный) в зависимости от заданного
     * типа поиска объектов,
     * @param recipient реципиент (объект) у которго происходит проверка на статус - архивный/не архивный
     * @param searchMode режим поиска объетов (active, removed или all)
     * @return true или false в зависимости от режима поиска и статуса объекта
     */
    private static boolean isSearchModeMatchesWithObjectState(ICoreRemovable recipient, String searchMode)
    {
        return switch (ApiUtils.fromRemovedMode(searchMode))
        {
            case ACTIVE_OBJECTS_ONLY -> !(recipient.isRemoved());
            case REMOVED_OBJECTS_ONLY -> recipient.isRemoved();
            //если searchMode=all
            default -> true;
        };
    }

    private static OU getOU(IUUIDIdentifiable object)
    {
        if (object instanceof OU ou)
        {
            return ou;
        }
        throw new FxException("OUApi: Input object is " + ((IHasMetaInfo)object).getMetaClass() + ", but OU expected");
    }
}
