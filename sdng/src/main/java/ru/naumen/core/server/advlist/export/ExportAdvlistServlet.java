package ru.naumen.core.server.advlist.export;

import java.io.IOException;
import java.util.List;
import java.util.Map;

import org.hibernate.CacheMode;
import org.hibernate.FlushMode;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.support.TransactionCallback;
import org.springframework.transaction.support.TransactionTemplate;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import jakarta.inject.Provider;
import jakarta.mail.internet.MimeUtility;
import jakarta.servlet.ServletOutputStream;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.advlist.AdvlistExportParameters;
import ru.naumen.core.server.advlist.export.DeferredExport.AdvlistExportService;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.server.hibernate.measurement.DBTimeMeasurementContainer;
import ru.naumen.core.server.hibernate.measurement.DBTimeMeasurementContainer.DBTimeMeasurement;
import ru.naumen.sec.server.servlets.requestqueue.AsyncServletWithQueue;
import ru.naumen.sec.server.servlets.requestqueue.RequestQueue;
import ru.naumen.sec.server.servlets.requestqueue.common.CommonQueue;
import ru.naumen.core.server.util.log.container.LogConfiguration;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.advlist.AdvlistConstants;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.objectlist.server.extension.ExportListMetainfoContext;
import ru.naumen.objectlist.server.extension.ObjectListMetainfoExtensionService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Сервлет выгрузки авдлиста в xls
 * <AUTHOR>
 * @since 01.12.2011
 *
 */
public class ExportAdvlistServlet extends AsyncServletWithQueue
{
    private static final Logger logger = LoggerFactory.getLogger(ExportAdvlistServlet.class);
    private static final String ADVLIST_EXPORT_WAS_NOT_FINISHED = "Advlist export was not finished";
    @Inject
    private transient Provider<GetAdvlistXlsFile> xlsFileProvider;
    @Inject
    private transient DaoFactory dao;
    @Inject
    private transient PlatformTransactionManager txManager;
    @Inject
    private transient AdvlistExportContextCache exportCache;
    @Inject
    private transient AdvlistExportParameters advlistParams;
    @Inject
    private transient ObjectListMetainfoExtensionService metainfoExtensionService;
    @Inject
    @Named("sessionFactory")
    private SessionFactory sessionFactory;

    @Inject
    private transient AdvlistExportService advlistExportService;
    @Inject
    private transient LogConfiguration logConfiguration;
    @Inject
    private transient CurrentEmployeeContext currentEmployeeContext;

    @Inject
    @Named("advlist-export-request-queue")
    private transient CommonQueue requestQueue;

    @Override
    protected void doGet(HttpServletRequest request, HttpServletResponse response)
    {
        doExportAdvlist(request, response);
    }

    @Override
    protected void doPost(HttpServletRequest request, HttpServletResponse response)
    {
        doExportAdvlist(request, response);
    }

    @Override
    protected RequestQueue getQueue()
    {
        return requestQueue;
    }

    /**
     * При вызове блокируется запись в кеш 2го уровня.
     */
    private void doExportAdvlist(final HttpServletRequest request, final HttpServletResponse response)
    {
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.setPropagationBehavior(TransactionDefinition.PROPAGATION_REQUIRES_NEW);
        tt.setTimeout(Constants.DEFAULT_TRANSACTION_TIMEOUT);
        tt.execute((TransactionCallback<Void>)status ->
        {
            try
            {
                doExportAdvlistActions(request, response);
            }
            catch (Exception e)
            {
                logger.error(ADVLIST_EXPORT_WAS_NOT_FINISHED, e);
                try
                {
                    response.sendRedirect(getServletContext().getContextPath() + "/operator");
                }
                catch (IOException e1)
                {
                    logger.error("Redirect failed : " + e1.getMessage(), e1);
                }
            }
            return null;
        });
    }

    private void doExportAdvlistActions(final HttpServletRequest request, final HttpServletResponse response)
    {
        try (DBTimeMeasurement advlistMeasure = DBTimeMeasurementContainer.measure())
        {
            int advlistSize = advlistParams.getAdvlistSize();
            String exportUUID = request.getParameter(AdvlistConstants.UUID);
            if (null == exportUUID)
            {
                throw new FxException("Export UUID is required.");
            }
            String userUUID = currentEmployeeContext.getCurrentUserUUID();
            if (userUUID == null)
            {
                throw new FxException("User is not authenticated.");
            }
            String storageKey = AdvlistExportUtils.createStorageKey(exportUUID, userUUID);
            DtoCriteria crit = exportCache.getCriteria(storageKey);
            Map<String, String> properties = exportCache.getProperties(storageKey);
            List<String> attrFqns = exportCache.getAttrFqns(storageKey);
            ExportListMetainfoContext context = metainfoExtensionService.prepareExport(crit,
                    new ExportListMetainfoContext(attrFqns, properties));
            exportCache.removeCriteriaAndProperties(storageKey);

            ClassFqn fqn = crit.getClassFqn();
            IDao<?> iDao = dao.get(fqn.fqnOfClass());
            int realCount = iDao.count(crit);
            crit.setMaxResults(advlistSize);
            final long minDuration = logConfiguration.getLogMinDuration();
            if (minDuration == 0)
            {
                logger.info("Start object export fqn: {}, max size: {}, total {}", fqn, advlistSize, realCount);
            }
            final long start = System.currentTimeMillis();
            List<String> uuids = iDao.listUuids(crit);

            // параметры для оптимизации hibernate-ом
            final Session session = getSession();
            session.setCacheMode(CacheMode.GET);
            session.setDefaultReadOnly(true);
            session.setHibernateFlushMode(FlushMode.MANUAL);
            GetAdvlistXlsFile xlsFile = xlsFileProvider.get();
            xlsFile.prepare(advlistParams.getExportBatchSize(), uuids, context.getAttributePresentations(),
                    context.getAttributeCodes(), null);
            xlsFile.createWorkbook();

            try (ServletOutputStream out = response.getOutputStream())
            {
                response.setContentType(ru.naumen.commons.shared.utils.MimeTypeRegistry
                        .getMimeTypeByFileExtension(advlistExportService.getFormat()));
                response.addHeader("Content-Disposition", "attachment; filename=\""
                                                          + MimeUtility.encodeText(advlistExportService.buildFileName(),
                        "utf-8", "B") + "\"");
                addNoCacheHeaders(request, response);
                xlsFile.write(out);
                out.flush();
            }
            catch (IOException e)
            {
                logger.error(ADVLIST_EXPORT_WAS_NOT_FINISHED, e);
            }
            finally
            {
                xlsFile.dispose();
                final long duration = System.currentTimeMillis() - start;
                if (duration >= minDuration)
                {
                    logger.info("SQL({}) Done({}):Finish object export {}{}", advlistMeasure.getJdbcTime(), duration,
                            fqn,
                            minDuration > 0 ? String.format(", max size %d, total %d", advlistSize, realCount) : "");
                }
            }
        }
    }

    private Session getSession()
    {
        return sessionFactory.getCurrentSession();
    }
}
