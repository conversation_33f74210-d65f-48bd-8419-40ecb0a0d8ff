package ru.naumen.core.server.catalog;

import jakarta.persistence.Cacheable;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.SystemIconCatalog;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.shared.ClassFqn;

//@formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Table(name = "tbl_systemicon", uniqueConstraints = { 
      @UniqueConstraint(name = "tbl_systemicon_code_key", columnNames = {"code"})}, indexes={@jakarta.persistence.Index(name = "idx_systemicon_parent", columnList="parent")})
@Cacheable
@DiscriminatorValue("-")
@DiscriminatorFormula(FlexHelper.NULL_DISCRIMINATOR_FORMULA)
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(SystemIconCatalogItem.CLASS_ID)
@Metaclass(id = SystemIconCatalogItem.CLASS_ID, 
         title = { @LStr(value = "Элемент справочника 'Иконки'"),
                 @LStr(lang = "en", value = "System Icon Catalog Item"),
                 @LStr(lang = "de", value = "Katalogartikel 'System-Symbole'") },
         withCase = false)
@Catalog(code = SystemIconCatalog.ITEM_CLASS_ID, 
      title = { @LStr(value = "Иконки"),
              @LStr(lang = "en", value = "System icons"),
              @LStr(lang = "de", value = "System-Symbole") },
      description = { @LStr(value = "Содержит иконки для изображения объектов на графических контентах (Схема связей, Схема сети)."), 
                      @LStr(lang = "en", value = "Contains icons for images of objects on the graphic contents (Relation schema, Network schema)."),
                      @LStr(lang = "de", value = "Enthält Symbole für  Objektbilder im grafischen Content ( Verbindungsplan,  Netzschaltbild).")})
//@formatter:on
public class SystemIconCatalogItem extends CatalogItem<SystemIconCatalogItem>
{
    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    public static final String CLASS_ID = SystemIconCatalog.ITEM_CLASS_ID;

    @Override
    public ClassFqn getMetaClass()
    {
        return SystemIconCatalog.ITEM_FQN;
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }
}