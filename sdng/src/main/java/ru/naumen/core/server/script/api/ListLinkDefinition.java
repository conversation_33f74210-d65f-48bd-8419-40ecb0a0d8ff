package ru.naumen.core.server.script.api;

import static ru.naumen.core.shared.utils.UuidHelper.SAFE_UUID_EXTRACTOR;

import java.util.ArrayList;
import java.util.List;
import java.util.function.Predicate;

import jakarta.annotation.Nullable;

import ru.naumen.common.shared.utils.ISProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.IListLinkDefinition;
import ru.naumen.core.server.script.IListLinkDefinition.IFilter.IFilterAnd;
import ru.naumen.core.server.script.IListLinkDefinition.IFilter.IFilterOr;
import ru.naumen.core.shared.Constants.Branch;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.ui.ObjectListBase;

/**
 * Билдер для генерации ссылки на список объектов 
 * Создан с целью избавиться от передачи в api.web.list() 11 параметров.
 *
 * <AUTHOR>
 * @since 19 июн. 2019 г.
 *
 */
public class ListLinkDefinition implements IListLinkDefinition
{
    /** Указатель на тип ссылки: true - на карточке объекта, false - на отдельной странице */
    private final boolean isOnCard;

    /** Название списка (Карточка объекта: не используется / Отдельная страница: обязательный) */
    @Nullable
    private String title;

    /** Хранит настроенную фильтрацию или ограничения содержимого списка */
    private final ListFilter listFilter;

    /**
     * Список элементов сортировки, содержащие код атрибута, по которому производиться сортировка списка и признак
     * направления сортировки
     * (Карточка объекта: опциональный / Отдельная страница: опциональный)
     */
    @Nullable
    private List<ISort> sort;

    /** Коды атрибутов, отображаемых в списке (Карточка объекта: опциональный / Отдельная страница: не используется) */
    @Nullable
    private List<String> attrCodes;

    /**
     * Коды типов класса, отображаемых в списке объектов
     * (Карточка объекта: не используется / Отдельная страница: опциональный)
     */
    @Nullable
    private List<String> cases;

    /**
     * UUID-ы пользователей, которым доступна страница
     * (Карточка объекта: не используется / Отдельная страница: опциональный)
     */
    @Nullable
    private List<String> permittedUserUuids;

    /** Код класса объектов списка (Карточка объекта: не используется / Отдельная страница: обязательный) */
    @Nullable
    private String classCode;

    /**
     * Код группы атрибутов, определённой во всех типах переданного класса, которая определяет колонки списка
     * (Карточка объекта: не используется / Отдельная страница: обязательный)
     */
    @Nullable
    private String attrGroup;

    /** Тип списка (по умолчанию "advlist") (Карточка объекта: не используется / Отдельная страница: опциональный) */
    @Nullable
    private String listPresentationType;

    /**
     * Расположение постраничной навигации (по умолчанию "bottom")
     * (Карточка объекта: не используется / Отдельная страница: опциональный)
     */
    @Nullable
    private String position;

    /**
     * Код шаблона списков, которому будет соответствовать отображение полученного списка
     * (Карточка объекта: не используется / Отдельная страница: опциональный)
     */
    @Nullable
    private String template;

    /** Код списочного контента (Карточка объекта: обязательный / Отдельная страница: не используется) */
    @Nullable
    private String listCode;

    @Nullable
    private Boolean nested;

    /**
     * Код типа списка. Допустимые значения:
     *  - "ChildObjectList" (список вложенных объектов)
     *  - "RelObjectList" (список связанных объектов)
     *  - "ObjectList" (список объектов)
     * Отдельная страница: Обязательный
     */
    @Nullable
    private String typeList;

    /** Описание "Цепи связей через атрибуты к списку Связанных объектов/Вложенных объектов" */
    private AttrChain attrChain;

    /** UUID объекта со списком на карточке (Карточка объекта: обязательный / Отдельная страница: не используется) */
    @Nullable
    private String objectUuid;

    /**
     * Продолжительность жизни ссылки в днях
     * (Карточка объекта: опциональный / Отдельная страница: опциональный)
     */
    @Nullable
    private Integer daysToLive;

    @Nullable
    private String branch;

    /** Название формы добавления связи (Карточка объекта: не используется / Отдельная страница: не используется) */
    @Nullable
    private String addLinkDialogTitle;

    /** Признак того, что ссылка используется для элемента навигационного меню */
    private final boolean isMenuItem;

    private boolean defaultViewApplied = false;

    public ListLinkDefinition(boolean onCard)
    {
        this(onCard, false, null, null);
    }

    public ListLinkDefinition(boolean onCard, boolean isMenuItem, @Nullable String listPresentationType,
            @Nullable AttrChain attrChain)
    {
        this(onCard, isMenuItem, listPresentationType, attrChain, List.of());
    }

    /**
     * Создание конструктора ссылки на основании списочного дескриптора.
     * Для конструктора ссылки будет установлена вся настроенная фильтрация и сортировки дескриптора.
     * (Для использования в скриптах)
     * @param onCard признак отображения на карточке или отдельной странице
     * @param isMenuItem признак того, что ссылка используется для элемента навигационного меню
     * @param presentationType внешний вид списка объектов
     * @param attrChain цепи связей через атрибуты к списку "Связанных объектов/Вложенных объектов"
     * @param restrictionFilters невидимая ограничительная фильтрация для списка объектов
     */
    public ListLinkDefinition(boolean onCard, boolean isMenuItem, @Nullable String presentationType,
            @Nullable AttrChain attrChain, List<IFilterAnd> restrictionFilters)
    {
        this.isOnCard = onCard;
        this.isMenuItem = isMenuItem;
        this.listPresentationType = presentationType;
        this.attrChain = attrChain;
        this.listFilter = new ListFilter(this, restrictionFilters);
    }

    @Override
    public boolean isOnCard()
    {
        return this.isOnCard;
    }

    @Override
    @Nullable
    public String getTitle()
    {
        return this.title;
    }

    @Override
    public IListLinkDefinition setTitle(String title)
    {
        this.title = title;
        return this;
    }

    @Override
    public IFilter filter()
    {
        return this.listFilter;
    }

    @Override
    public List<IFilterOr> getFastFilter()
    {
        return this.listFilter.getFastFilterElements();
    }

    @Override
    public List<IFilterOr> getFastSubstringFilter()
    {
        return this.listFilter.getFastSubstringFilters();
    }

    @Override
    public List<IFilterOr> getRestrictionFilter()
    {
        return this.listFilter.getRestrictionFilterSettings();
    }

    @Override
    public List<ISort> getSort()
    {
        if (this.sort == null)
        {
            this.sort = new ArrayList<>();
        }
        return this.sort;
    }

    public void setSort(List<ISort> sort)
    {
        this.sort = sort;
    }

    @Override
    public IListLinkDefinition sortAsc(String attrCode)
    {
        getSort().add(new ListSort(attrCode, true));
        return this;
    }

    @Override
    public IListLinkDefinition sortDesc(String attrCode)
    {
        getSort().add(new ListSort(attrCode, false));
        return this;
    }

    @Override
    @Nullable
    public List<String> getAttrCodes()
    {
        return this.attrCodes;
    }

    @Override
    public IListLinkDefinition setAttrCodes(List<String> attrCodes)
    {
        this.attrCodes = attrCodes;
        return this;
    }

    @Override
    @Nullable
    public List<String> getCases()
    {
        return this.cases;
    }

    @Override
    public IListLinkDefinition setCases(@Nullable List<String> cases)
    {
        this.cases = cases;
        return this;
    }

    @Override
    public List<String> getUsers()
    {
        return this.permittedUserUuids;
    }

    @Override
    public IListLinkDefinition setUsers(List<Object> users)
    {
        if (CollectionUtils.isEmpty(users))
        {
            return this;
        }
        this.permittedUserUuids = users.stream()
                .map(SAFE_UUID_EXTRACTOR)
                .filter(Predicate.not(StringUtilities::isEmptyTrim))
                .toList();
        return this;
    }

    @Override
    @Nullable
    public String getClassCode()
    {
        return this.classCode;
    }

    @Override
    public IListLinkDefinition setClassCode(String classCode)
    {
        this.classCode = classCode;
        return this;
    }

    @Override
    @Nullable
    public String getAttrGroup()
    {
        return this.attrGroup;
    }

    @Override
    public IListLinkDefinition setAttrGroup(String attrGroup)
    {
        this.attrGroup = attrGroup;
        return this;
    }

    @Override
    @Nullable
    public String getListPresentationType()
    {
        return this.listPresentationType;
    }

    @Override
    public IListLinkDefinition setSimple()
    {
        this.listPresentationType = ObjectListBase.PresentationType.DEFAULT.getCode();
        return this;
    }

    @Override
    public String getPaging()
    {
        return this.position;
    }

    @Override
    public IListLinkDefinition setPaging(@Nullable String position)
    {
        if (position != null)
        {
            position = position.toUpperCase().trim();
        }
        this.position = "BOTH".equals(position) ? "TOP_AND_BOTTOM" : position;
        return this;
    }

    @Override
    @Nullable
    public String getTemplate()
    {
        return this.template;
    }

    @Override
    public IListLinkDefinition setTemplate(String template)
    {
        this.template = template;
        return this;
    }

    @Override
    @Nullable
    public String getListCode()
    {
        return this.listCode;
    }

    @Override
    public IListLinkDefinition setListCode(String listCode)
    {
        this.listCode = listCode;
        return this;
    }

    @Override
    @Nullable
    public Boolean getNested()
    {
        return this.nested;
    }

    @Override
    public IListLinkDefinition setNested(boolean nested)
    {
        this.nested = nested;
        return this;
    }

    @Override
    @Nullable
    @Deprecated
    public String getTypeList()
    {
        return this.typeList;
    }

    @Override
    @Nullable
    public String getListType()
    {
        return this.typeList;
    }

    @Override
    public IListLinkDefinition setListType(String typeList)
    {
        this.typeList = typeList;
        return this;
    }

    @Override
    public IAttrChain attrChain()
    {
        if (attrChain == null)
        {
            attrChain = new AttrChain(this);
        }
        return attrChain;
    }

    @Override
    public void relatedWithNestedParams(List<String> attributeSelectBeforeHierarchy, String nestedAttrLinkFqn,
            List<String> attributeSelectAfterHierarchy)
    {
        this.attrChain = new AttrChain(attributeSelectBeforeHierarchy, nestedAttrLinkFqn,
                attributeSelectAfterHierarchy);
    }

    @Override
    public String getUuid()
    {
        return this.objectUuid;
    }

    @Override
    public IListLinkDefinition setObject(@Nullable IUUIDIdentifiable object)
    {
        this.objectUuid = object != null ? object.getUUID() : null;
        return this;
    }

    @Override
    public IListLinkDefinition setUuid(@Nullable String uuid)
    {
        this.objectUuid = uuid;
        return this;
    }

    @Override
    @Nullable
    public Integer getDaysToLive()
    {
        return this.daysToLive;
    }

    @Override
    public IListLinkDefinition setDaysToLive(int lifetime)
    {
        this.daysToLive = lifetime;
        return this;
    }

    @Nullable
    @Override
    public String getBranch()
    {
        return this.branch;
    }

    @Override
    public IListLinkDefinition setBranch(@Nullable Object branch)
    {
        if (branch == null)
        {
            this.branch = Branch.MASTER;
        }
        if (branch instanceof IUUIDIdentifiable identifiable)
        {
            this.branch = identifiable.getUUID();
        }
        if (branch instanceof String)
        {
            this.branch = branch.toString();
        }
        return this;
    }

    @Nullable
    @Override
    public String getAddLinkDialogTitle()
    {
        return this.addLinkDialogTitle;
    }

    @Override
    public IListLinkDefinition setAddLinkDialogTitle(@Nullable String addLinkDialogTitle)
    {
        this.addLinkDialogTitle = addLinkDialogTitle;
        return this;
    }

    @Override
    public boolean isMenuItem()
    {
        return isMenuItem;
    }

    @Override
    public boolean isDefaultViewApplied()
    {
        return defaultViewApplied;
    }

    @Override
    public IListLinkDefinition applyDefaultView(boolean applyDefaultView)
    {
        this.defaultViewApplied = applyDefaultView;
        return this;
    }

    @Override
    public ISProperties buildProperties()
    {
        return new MapProperties();
    }

    @Override
    public String toString()
    {
        return "ListLinkDefinition [isOnCard=" + isOnCard + ", uuid=" + objectUuid + ", listCode=" + listCode
               + ", fastFilter=" + this.listFilter.getFastFilterElements() + ", title=" + title
               + ", classCode=" + classCode + ", cases=" + cases + ", attrGroup=" + attrGroup + ", attrCodes="
               + attrCodes
               + ", listType=" + listPresentationType + ", position=" + position + ", template=" + template
               + ", filter=" + this.listFilter.listAndElements() + ", sort=" + sort + ", users=" + permittedUserUuids
               + ", lifetime=" + daysToLive + ", branch=" + branch + ", addLinkDialogTitle=" + addLinkDialogTitle
               + "]";
    }
}
