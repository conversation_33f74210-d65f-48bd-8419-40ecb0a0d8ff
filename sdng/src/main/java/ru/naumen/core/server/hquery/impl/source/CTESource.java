package ru.naumen.core.server.hquery.impl.source;

import java.util.Objects;

import org.hibernate.query.Query;
import org.hibernate.Session;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.HCriteriaDelegate;

/**
 *
 * Источник для Common Table Expression
 *
 * <AUTHOR>
 * @since 04 февр. 2016 г.
 */
public class CTESource extends TextSource
{
    protected HCriteriaDelegate cte;
    protected boolean visited = false;

    public CTESource(HCriteriaDelegate cte, String path, String alias)
    {
        super(path, alias);
        this.cte = cte;
        for (HColumn col : cte.getColumns())
        {
            if (ru.naumen.commons.shared.utils.StringUtilities.isEmpty(col.getAlias()))
            {
                throw new IllegalStateException("Columns aliases are required for CTE");
            }
        }
    }

    @Override
    public void afterQuery(Session session)
    {
        try
        {
            this.cte.afterQuery(session);
        }
        finally
        {
            this.visited = false;
        }
    }

    public HCriteriaDelegate getCTE()
    {
        return this.cte;
    }

    @Override
    public String getParentAlias()
    {
        return null;
    }

    @Override
    public void setParameters(Query q)
    {
        super.setParameters(q);
        this.cte.setParameters(q);
    }

    @Override
    public void visit(HBuilder parent)
    {
        if (!this.visited && !cte.wasVisited())
        {
            String hql = new HBuilder(parent, this.cte).build();
            String format = "%s AS (\n %s \n)";
            String cteString = String.format(format, this.getHQLSource(), hql);
            parent.commonTableExpression(cteString);
        }
        super.visit(parent);
        this.visited = true;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        CTESource cteSource = (CTESource)o;
        return Objects.equals(cte, cteSource.cte);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), cte);
    }
}
