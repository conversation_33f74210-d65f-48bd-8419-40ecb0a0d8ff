package ru.naumen.core.server.naming.spi;

import java.util.concurrent.Callable;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;

/**
 * Реализация {@link SequenceDao}
 *
 * <AUTHOR>
 *
 */
@Component
@Lazy
public class SequenceDaoBean implements SequenceDao
{
    @Inject
    @Named("sessionFactory")
    private SessionFactory sessionFactory;
    @Inject
    private SpringContext springContext;
    private SequenceDao sequenceDao;

    @Override
    public PeriodicalSequence createSequence(final String id)
    {
        return TransactionRunner.call(TransactionType.NEW, new Callable<PeriodicalSequence>()
        {
            @Override
            public PeriodicalSequence call() throws Exception
            {
                PeriodicalSequence seq = new PeriodicalSequence(id, 0, 0);
                Session session = getSession();
                session.persist(seq);
                return seq;
            }
        });
    }

    @Override
    public void delete(final String id)
    {
        TransactionRunner.run(TransactionType.CURRENT_OR_NEW, new Runnable()
        {
            @Override
            public void run()
            {
                Session session = getSession();
                Object seq;
                if ((seq = session.get(PeriodicalSequence.class, id)) != null)
                {
                    session.remove(seq);
                }
            }
        });
    }

    /**
     * Проверка на то, что последовательность с указанным идентификатором существует.
     *
     * @param id идентификатор проверяемой последовательности.
     * @return результат проверки.
     */
    public boolean exists(final String id)
    {
        return TransactionRunner.call(new Callable<Boolean>()
        {
            @Override
            public Boolean call() throws Exception
            {
                final Session session = getSession();
                return session.get(PeriodicalSequence.class, id) != null;
            }
        });
    }

    @Override
    public PeriodicalSequence get(final String id)
    {
        return TransactionRunner.call(TransactionType.CURRENT_OR_NEW, new Callable<PeriodicalSequence>()
        {
            @Override
            public PeriodicalSequence call() throws Exception
            {
                ensureInitialized();
                final Session session = getSession();
                final PeriodicalSequence seq = session.get(PeriodicalSequence.class, id);
                return seq != null ? seq : sequenceDao.createSequence(id);
            }
        });
    }

    @Override
    public void set(final String id, final long period, final int value)
    {
        TransactionRunner.run(TransactionType.CURRENT_OR_NEW, new Runnable()
        {
            @Override
            public void run()
            {
                final PeriodicalSequence seq = new PeriodicalSequence(id, period, value);
                final Session session = getSession();
                session.merge(seq);
            }
        });
    }

    //Выполняется в новой транзакции для возможности использовать последовательность сразу в
    //нескольких параллельных транзакциях
    @Override
    public void updateSequence(final String id, final long period, final int value)
    {
        TransactionRunner.run(TransactionType.NEW, new Runnable()
        {
            @Override
            public void run()
            {
                final PeriodicalSequence seq = new PeriodicalSequence(id, period, value);
                final Session session = getSession();
                session.merge(seq);
            }
        });
    }

    private void ensureInitialized()
    {
        if (null == sequenceDao)
        {
            sequenceDao = springContext.getBean(SequenceDao.class);
        }
    }

    private Session getSession()
    {
        return sessionFactory.getCurrentSession();
    }
}
