package ru.naumen.core.server.script.api;

import java.security.cert.X509Certificate;
import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.keystore.AfterImportCertificateActionEvent;
import ru.naumen.core.server.keystore.IX509Certificate;
import ru.naumen.core.server.keystore.KeyStoreService;
import ru.naumen.core.server.keystore.X509CertificateWrapper;
import ru.naumen.core.server.util.MessageFacade;

/**
 * API для работы с KeyStore
 * <AUTHOR>
 * @since 05.09.2019
 */
@Component("keystore")
public class KeyStoreApi implements IKeyStoreApi
{
    private final KeyStoreService keyStoreService;
    private final MessageFacade messages;
    private final ApplicationEventPublisher eventPublisher;

    @Inject
    public KeyStoreApi(KeyStoreService keyStoreService, MessageFacade messages,
            ApplicationEventPublisher eventPublisher)
    {
        this.keyStoreService = keyStoreService;
        this.messages = messages;
        this.eventPublisher = eventPublisher;
    }

    @Override
    public void importCertificate(String alias, String certificatePem)
    {
        if (!certificatePem.trim().isEmpty())
        {
            try
            {
                keyStoreService.addCertToAppKeyStore(alias, certificatePem);
                eventPublisher.publishEvent(new AfterImportCertificateActionEvent());
            }
            catch (Exception e)
            {
                throw new FxException(messages.getMessage("certificateErrorLoading"));
            }
        }
        else
        {
            throw new FxException(messages.getMessage("certificateErrorLoading"));
        }
    }

    @Override
    public IX509Certificate getCertificate(String alias)
    {
        X509Certificate certificate = keyStoreService.getCertFromAppKeyStore(alias);

        if (null != certificate)
        {
            return new X509CertificateWrapper(certificate);
        }
        else
        {
            throw new FxException(messages.getMessage("keystore.notfound"));
        }
    }

    @Override
    public List<IX509Certificate> getAllCertificates()
    {
        return keyStoreService.getAllCertificates().stream().map(X509CertificateWrapper::new).collect(
                Collectors.toList());
    }

    @Override
    public void deleteCertificate(String alias)
    {
        X509Certificate certificate = keyStoreService.getCertFromAppKeyStore(alias);
        if (null != certificate)
        {
            keyStoreService.deleteCertFromAppKeyStore(alias);
            eventPublisher.publishEvent(new AfterImportCertificateActionEvent());
        }
        else
        {
            throw new FxException(messages.getMessage("keystore.notfound"));
        }
    }
}
