package ru.naumen.core.server.script.storage.modification.usage;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import com.google.common.base.Joiner;
import com.google.common.collect.Maps;

import java.util.HashMap;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.customforms.CustomFormsService;
import ru.naumen.core.server.customforms.FormParameter;
import ru.naumen.core.server.script.ScriptService.Constants;
import ru.naumen.core.server.util.DateTimeAttrRestrictionHelper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.script.places.FormParameterScriptCategories;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Реализация {@link ScriptModifyProcess} для параметров настраиваемых форм
 *
 * <AUTHOR>
 * @since 6 мая 2016 г.
 */
@Component
public class FormParameterScriptModifyProcess extends AbstractAttributeScriptModifyProcess<FormParameter>
{

    private final CustomFormsService customFormsService;
    private final MessageFacade messages;
    private final DateTimeAttrRestrictionHelper dateTimeAttrRestrictionHelper;

    @Inject
    public FormParameterScriptModifyProcess(CustomFormsService customFormsService, MessageFacade messages,
            DateTimeAttrRestrictionHelper dateTimeAttrRestrictionHelper)
    {
        this.customFormsService = customFormsService;
        this.messages = messages;
        this.dateTimeAttrRestrictionHelper = dateTimeAttrRestrictionHelper;
    }

    @Override
    protected void dataUpdateSelfUsage(Script script, FormParameter newHolder, ScriptModifyContext context)
    {
        switch ((FormParameterScriptCategories)context.getCategory())
        {
            case PARAM_COMPUTABLE_ON_FORM:
                updateComputableOnForm(script, newHolder);
                break;
            case PARAM_FILTRATION:
                updateFiltration(script, newHolder);
                break;
            case PARAM_DATE_TIME_RESTRICTION:
                updateDateTimeRestriction(script, newHolder);
                break;
            default:
                break;
        }
    }

    @Override
    protected void dataUpdateSelfUsageAfterRemove(FormParameter holder, ScriptModifyContext context)
    {
        switch ((FormParameterScriptCategories)context.getCategory())
        {
            case PARAM_COMPUTABLE_ON_FORM:
                updateComputableOnFormAR(holder);
                break;
            case PARAM_FILTRATION:
                updateFiltrationAR(holder);
                break;
            case PARAM_DATE_TIME_RESTRICTION:
                updateDateTimeRestrictionAR(holder);
                break;
            case PARAM_COMPUTE_ANY_CATALOG_ELEMENTS:
                updateComputeAnyCatalogElementsAR(holder);
                break;
            default:
                break;
        }
    }

    @Override
    protected Map<String, Object> getBindingsForFiltration()
    {
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(Constants.FORM, null);
        return bindings;
    }

    @Override
    @Nullable
    protected String getOldScriptCode(FormParameter oldHolder, ScriptModifyContext context)
    {
        return switch ((FormParameterScriptCategories)context.getCategory())
        {
            case PARAM_COMPUTABLE_ON_FORM -> oldHolder.getComputableOnFormScript();
            case PARAM_FILTRATION -> oldHolder.getScriptForFiltration();
            case PARAM_DEFAULT_VALUE -> oldHolder.getScriptForDefault();
            case PARAM_DATE_TIME_RESTRICTION -> oldHolder.getDateTimeRestrictionScript();
            case PARAM_COMPUTE_ANY_CATALOG_ELEMENTS -> oldHolder.getComputeAnyCatalogElementsScript();
            default -> null;
        };
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, FormParameter holder, ScriptModifyContext context)
    {
        switch ((FormParameterScriptCategories)context.getCategory())
        {
            case PARAM_COMPUTABLE_ON_FORM:
                holder.setScriptForEditValue(newScriptCode);
                break;
            case PARAM_FILTRATION:
                holder.setScriptForFiltration(newScriptCode);
                break;
            case PARAM_DEFAULT_VALUE:
                holder.setScriptForDefault(newScriptCode);
                break;
            case PARAM_DATE_TIME_RESTRICTION:
                holder.setDateTimeRestrictionScript(newScriptCode);
                break;
            case PARAM_COMPUTE_ANY_CATALOG_ELEMENTS:
                holder.setComputeAnyCatalogElementsScript(newScriptCode);
                break;
        }
    }

    @Override
    protected void validateCOFScriptCodes(List<String> codes, FormParameter parameter)
    {
        assertCodes(codes, parameter, parameter.getCode(), "CustomForms.invalidParamsInCOFScript");
    }

    @Override
    protected void validateFiltrationScriptCodes(List<String> codes, FormParameter parameter)
    {
        assertCodes(codes, parameter, parameter.getCode(), "CustomForms.invalidParamsInFiltrationScript");
    }

    @Override
    protected void validateRestrictionScriptAttributes(List<String> attributesCodes, FormParameter parameter)
    {
        CustomForm form = customFormsService.getFormSafe(parameter.getFormCode());
        dateTimeAttrRestrictionHelper.validateRestrictionScriptParameters(attributesCodes, parameter, form);
    }

    private void assertCodes(Collection<String> codes, FormParameter parameter, String attrCode, String messageCode)
    {
        CustomForm form = customFormsService.getFormSafe(parameter.getFormCode());
        Set<String> allCodes = codes.stream().filter(code -> !code.equals(attrCode)).collect(Collectors.toSet());
        //@formatter:off
        Set<String> invalidCodes = form == null ? allCodes : allCodes.stream()
                .filter(code -> form.getAttribute(code) == null)
                .collect(Collectors.toSet());
        //@formatter:on
        if (!invalidCodes.isEmpty())
        {
            String message = messages.getMessage(messageCode, Joiner.on(", ").join(invalidCodes));
            throw new FxException(message, true);
        }
    }

    private void updateComputeAnyCatalogElementsAR(FormParameter holder)
    {
        holder.setComputeAnyCatalogElementsScript(null);
    }

    Map<String, Object> getBindingsForDateTimeRestriction()
    {
        Map<String, Object> bindings = Maps.newHashMapWithExpectedSize(3);
        bindings.put(Constants.FORM, null);
        return bindings;
    }
}
