package ru.naumen.core.server.script.libraries.scripting.module;

import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.embeddedapplication.ClientSideApplicationZipService;
import ru.naumen.core.server.script.libraries.LibrariesService;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.script.modules.storage.ScriptModuleModificationService;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;

/**
 * Обработчик результата сканирования аннотаций модулей в библиотеках
 */
@Component
class ModulesScannedScriptsSetProcessor
{
    private static final Logger LOG = LoggerFactory.getLogger(ModulesScannedScriptsSetProcessor.class);

    private final LibrariesService librariesService;
    private final ScriptModuleModificationService scriptModuleModificationService;
    private final ClientSideApplicationZipService applicationZipService;

    @Inject
    public ModulesScannedScriptsSetProcessor(LibrariesService librariesService,
            ScriptModuleModificationService scriptModuleModificationService,
            ClientSideApplicationZipService applicationZipService)
    {
        this.librariesService = librariesService;
        this.scriptModuleModificationService = scriptModuleModificationService;
        this.applicationZipService = applicationZipService;
    }

    @EventListener
    public void process(ModulesSetRefreshedEvent modulesSetRefreshedEvent)
    {
        final Map<String, Set<ScriptModule>> libraryPerModulesCodes = modulesSetRefreshedEvent.getLibraryPerModules();
        libraryPerModulesCodes.forEach((libraryName, scriptModules) ->
        {
            final ScriptLibrary library = librariesService.getLibrary(libraryName);
            if (library != null)
            {
                final Set<String> modules = library.getModules();
                modules.clear();
                modules.addAll(scriptModules.stream().map(ScriptModule::getCode).collect(Collectors.toSet()));

                modules.forEach(scriptModuleCode ->
                {
                    final Optional<ScriptModule> module = scriptModuleModificationService.getModule(scriptModuleCode);
                    if (module.isPresent()
                        && scriptModuleModificationService.deleteModule(scriptModuleCode))
                    {
                        LOG.info("Remove and replace the module '{}'.", scriptModuleCode);
                        applicationZipService.deleteScriptModuleIfNeed(module.get());
                    }
                });
                librariesService.saveLibrary(library);
            }
            else
            {
                LOG.warn("There's no library with name {} for modules' codes addition.", libraryName);
            }
        });
    }
}
