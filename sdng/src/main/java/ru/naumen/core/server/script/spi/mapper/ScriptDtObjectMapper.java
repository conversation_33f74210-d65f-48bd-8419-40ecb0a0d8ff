package ru.naumen.core.server.script.spi.mapper;

import java.util.Collection;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.gson.JsonObject;

import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.server.bo.ToJsonTransformer;
import ru.naumen.core.server.script.spi.AbstractScriptDtObject;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.metainfo.server.MetainfoService;

/**
 * Преобразует объекты AbstractScriptDtObject в JSON по тем же правилам, что RestAPI преобразует объект в JSON
 *
 * <AUTHOR>
 * @since 03.02.2020
 */
@Component
public class ScriptDtObjectMapper extends AbstractMapper<AbstractScriptDtObject, JsonObject>
{
    private MetainfoService metainfoService;
    private ToJsonTransformer helper;

    @Inject
    public ScriptDtObjectMapper(MetainfoService metainfoService, ToJsonTransformer helper)
    {
        super(AbstractScriptDtObject.class, JsonObject.class);
        this.metainfoService = metainfoService;
        this.helper = helper;
    }

    @Override
    public void transform(AbstractScriptDtObject from, JsonObject to, @Nullable DtoProperties properties)
    {
        boolean isHasProperties = properties != null && !properties.getProperties().isEmpty();

        Collection<String> attributeCodes = isHasProperties ? properties.getProperties() :
                metainfoService.getMetaClass(from).getAttributeCodes();

        attributeCodes.forEach(attrCode ->
                helper.transform(to, attrCode, from.getProperty(attrCode), true, true));
    }
}
