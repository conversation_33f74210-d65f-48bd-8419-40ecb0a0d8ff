package ru.naumen.core.server.script.api.criteria.criterion;

import ru.naumen.core.server.script.api.criteria.column.IApiCriteriaColumnInternal;
import ru.naumen.core.server.script.api.criteria.criterion.handler.PropertyIsNullApiCriterionHandler;

/**
 * Условие фильтрации, проверяющее значение свойства на null или not null
 *
 * <AUTHOR>
 * @since 06.03.2021
 * @see PropertyIsNullApiCriterionHandler
 */
public class PropertyIsNullApiCriterion extends AbstractPropertyApiCriterion
{

    /**
     * true, если значение свойства должно быть null, иначе false
     */
    private final boolean isNull;

    /**
     * Условие фильтрации, проверяющее значение свойства на null или not null
     * @param property свойство или выражение, на которое накадывается текущее условие
     * @param isNull true, если значение свойства должно быть null, иначе false
     * @throws NullPointerException если property равно null
     */
    public PropertyIsNullApiCriterion(IApiCriteriaColumnInternal property, boolean isNull) throws NullPointerException
    {
        super(property);
        this.isNull = isNull;
    }

    /**
     * @return true, если значение свойства должно быть null, иначе false
     */
    public boolean isNull()
    {
        return isNull;
    }
}
