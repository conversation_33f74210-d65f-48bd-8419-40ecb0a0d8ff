package ru.naumen.core.server.upload;

import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.util.Arrays;
import java.util.Objects;

import org.apache.commons.io.FileUtils;

import ru.naumen.commons.shared.utils.MimeTypeRegistry;

/**
 *
 * <AUTHOR>
 *
 */
public class NestedFileItem extends AbsrtractFileItem<NestedFileItem>
{
    private final transient byte[] bytes;

    public NestedFileItem(String name, String contentType)
    {
        this(new byte[0], name, contentType);
    }

    public NestedFileItem(byte[] content, String name, String contentType)
    {
        super();
        setName(name);
        setContentType(contentType);
        this.bytes = content;
    }

    public NestedFileItem(File file) throws IOException
    {
        this(FileUtils.readFileToByteArray(file),
                file.getName(),
                MimeTypeRegistry.getMimeTypeByFileExtension(
                        ru.naumen.core.server.filestorage.FileUtils.getExtension(file.getName())));
    }

    /**
     * @param content содержимое файла. В контексте s3 - хранилищ сюда помещается внешний
     *                идентификатор файла (полный путь к файлу в хранилище)
     * @param name имя файла
     * @param contentType mime-тип файла
     */
    public NestedFileItem(String content, String name, String contentType)
    {
        this(content.getBytes(StandardCharsets.UTF_8), name, contentType);
    }

    @Override
    public byte[] get()
    {
        return bytes;
    }

    @Override
    public InputStream getInputStream() throws IOException
    {
        return new ByteArrayInputStream(bytes);
    }

    @Override
    public long getSize()
    {
        return bytes.length;
    }

    @Override
    public String getString()
    {
        return new String(bytes, StandardCharsets.UTF_8);
    }

    @Override
    public String getString(Charset encoding) throws IOException
    {
        return new String(bytes, encoding);
    }

    @Override
    public boolean isInMemory()
    {
        return true;
    }

    @Override
    public boolean equals(final Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }

        final NestedFileItem that = (NestedFileItem)o;

        if (!getName().equals(that.getName()))
        {
            return false;
        }
        if (!Objects.equals(getContentType(), that.getContentType()))
        {
            return false;
        }
        return Arrays.equals(bytes, that.bytes);
    }

    @Override
    public int hashCode()
    {
        int result = getName().hashCode();
        result = 31 * result + (getContentType() != null ? getContentType().hashCode() : 0);
        result = 31 * result + Arrays.hashCode(bytes);
        return result;
    }
}
