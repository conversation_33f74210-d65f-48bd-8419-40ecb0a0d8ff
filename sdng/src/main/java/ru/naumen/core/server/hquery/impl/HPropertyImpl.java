package ru.naumen.core.server.hquery.impl;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HProperty;

public class HPropertyImpl extends HColumnImpl implements HProperty
{
    public HPropertyImpl(String property)
    {
        super(property);
    }

    public HPropertyImpl(String property, String alias)
    {
        super(property, alias);
    }

    HPropertyImpl(HColumn other, String property, @Nullable String alias)
    {
        super(other, property, alias);
    }

    @Override
    public String getProperty()
    {
        return super.column;
    }

    @Override
    public String toString()
    {
        return "HPropertyImpl{" +
               "other=" + other +
               ", column='" + column + '\'' +
               ", overrideHQL='" + overrideHQL + '\'' +
               ", alias=" + getAlias() +
               '}';
    }
}
