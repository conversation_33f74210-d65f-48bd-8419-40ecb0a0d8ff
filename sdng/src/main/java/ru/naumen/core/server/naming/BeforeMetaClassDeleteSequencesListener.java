package ru.naumen.core.server.naming;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.naming.spi.NamingService;
import ru.naumen.metainfo.server.BeforeMetaClassDeleteEvent;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Листенер, удаляющий PeriodicalSequence, созданные для удаляемого класса.
 * (можно создать снова класс с таким же кодом, и генерация начнется с нуля).
 *
 * <AUTHOR>
 * @since 03.05.2012
 */
@Component
public class BeforeMetaClassDeleteSequencesListener implements ApplicationListener<BeforeMetaClassDeleteEvent>
{
    @Inject
    SpringContext springContext;

    @Override
    public void onApplicationEvent(BeforeMetaClassDeleteEvent event)
    {
        ClassFqn classFqn = ((MetaClass)event.getSource()).getFqn();
        if (classFqn.isCase())
        {
            return;
        }
        NamingService service = springContext.getBean(NamingService.class);
        service.deleteNumericNamingRules(classFqn);
    }
}
