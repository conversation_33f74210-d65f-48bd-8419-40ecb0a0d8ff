package ru.naumen.core.server.script.api;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.shared.DtoCriteriaHelper;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.filters.AggregateAttrTitleNotContainsFilter;
import ru.naumen.core.shared.filters.AllFilter;
import ru.naumen.core.shared.filters.AndFilter;
import ru.naumen.core.shared.filters.BackLinkFilter;
import ru.naumen.core.shared.filters.BackLinkTitleFilter;
import ru.naumen.core.shared.filters.BetweenFilter;
import ru.naumen.core.shared.filters.CasesFilter;
import ru.naumen.core.shared.filters.ContainsFilter;
import ru.naumen.core.shared.filters.DateIntervalFilter;
import ru.naumen.core.shared.filters.DescendantsFilter;
import ru.naumen.core.shared.filters.ExceptFilter;
import ru.naumen.core.shared.filters.FileFilter;
import ru.naumen.core.shared.filters.Filters;
import ru.naumen.core.shared.filters.IFilter;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.InAttributeFilter;
import ru.naumen.core.shared.filters.InAttributesChainFilter;
import ru.naumen.core.shared.filters.InDirectAndBackLinkFilter;
import ru.naumen.core.shared.filters.InDisconnectingStatesFilter;
import ru.naumen.core.shared.filters.InequalityFilter;
import ru.naumen.core.shared.filters.IsNullFilter;
import ru.naumen.core.shared.filters.LastNDaysFilter;
import ru.naumen.core.shared.filters.NoneFilter;
import ru.naumen.core.shared.filters.NotFilter;
import ru.naumen.core.shared.filters.OrFilter;
import ru.naumen.core.shared.filters.ParentFilter;
import ru.naumen.core.shared.filters.SimpleFilter;
import ru.naumen.core.shared.filters.SimpleInFilter;
import ru.naumen.core.shared.filters.SimplePropertiesFilter;
import ru.naumen.core.shared.filters.SimpleSearchFilter;
import ru.naumen.core.shared.filters.StateTitleFilter;
import ru.naumen.core.shared.filters.TodayFilter;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.IAttrReference;
import ru.naumen.metainfo.shared.IAttributeFqn;
import ru.naumen.metainfo.shared.IClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * <AUTHOR>
 * @since Apr 23, 2014
 */
@Component("filters")
public class FiltersApi implements IFiltersApi
{
    private final MetainfoService metainfoService;
    private final DtoCriteriaHelper dtoCriteriaHelper;

    @Inject
    public FiltersApi(MetainfoService metainfoService, DtoCriteriaHelper dtoCriteriaHelper)
    {
        this.metainfoService = metainfoService;
        this.dtoCriteriaHelper = dtoCriteriaHelper;
    }

    @Override
    public IFilter aggregateAttrTitleNotContains(String name, String value, boolean ignoreCase)
    {
        return new AggregateAttrTitleNotContainsFilter(name, value, ignoreCase);
    }

    @Override
    public IFilter all()
    {
        return AllFilter.INSTANCE;
    }

    @Override
    public IFilter and(IFilter... filters)
    {
        return new AndFilter(
                Arrays.stream(filters)
                        .map(filter -> (IObjectFilter)filter)
                        .collect(Collectors.toList()));
    }

    @Override
    public IFilter attrContains(String name, Object value, boolean ignoreCase, boolean revert)
    {
        return new ContainsFilter(name, value, ignoreCase, revert);
    }

    @Override
    public IFilter attrsInequality(String name1, String op, String name2)
    {
        return new SimplePropertiesFilter(name1, op, name2);
    }

    @Override
    public IFilter attrValueEq(String name, Object value)
    {
        return new SimpleFilter<>(name, value);
    }

    @Override
    public IFilter attrValueIn(String name, Collection<Object> value, boolean not)
    {
        return new SimpleInFilter<>(name, value, not);
    }

    @Override
    public IFilter backLinkAttrContains(String directLinkAttrCode, String objectId, ArrayList<String> dtObjects,
            boolean contains)
    {
        return new BackLinkFilter(directLinkAttrCode, objectId, dtObjects, contains);
    }

    @Override
    public IFilter backLinkAttrTitleContains(String directLinkAttrCode, String objectId, String titleValue,
            boolean contains)
    {
        return new BackLinkTitleFilter(directLinkAttrCode, objectId, titleValue, contains);
    }

    @Override
    public IFilter between(String name, Object left, Object right)
    {
        return new BetweenFilter(name, left, right);
    }

    @Override
    public IFilter children()
    {
        return new ParentFilter();
    }

    @Override
    public IFilter dateAttrInInterval(String name, Date sdate, Date edate, boolean compareAsLong)
    {
        return new DateIntervalFilter(name, sdate, edate, compareAsLong);
    }

    @Override
    public IFilter dateEndingUpBack(String name, long length, @Nullable String intervalName)
    {
        return ApiUtils.createDateWithDirectionFilter(name, length, intervalName, false);
    }

    @Override
    public IFilter dateEndingUpForward(String name, long length, @Nullable String intervalName)
    {
        return ApiUtils.createDateWithDirectionFilter(name, Math.negateExact(length), intervalName, false);
    }

    @Override
    public IFilter dateStartWithBack(String name, long length, @Nullable String intervalName)
    {
        return ApiUtils.createDateWithDirectionFilter(name, length, intervalName, true);
    }

    @Override
    public IFilter dateStartWithForward(String name, long length, @Nullable String intervalName)
    {
        return ApiUtils.createDateWithDirectionFilter(name, Math.negateExact(length), intervalName, true);
    }

    @Override
    public IFilter eq(IClassFqn fqn, Map<String, Object> attributes)
    {
        if (ObjectUtils.isEmpty(attributes))
        {
            return Filters.NO_FILTER;
        }
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        DtoCriteria criteria = dtoCriteriaHelper.getDtoCriteria(metaClass, attributes);
        Collection<IObjectFilter> filters = criteria.getFilters();
        if (ObjectUtils.isEmpty(filters))
        {
            return Filters.NO_FILTER;
        }
        else if (filters.size() == 1)
        {
            return filters.iterator().next();
        }
        return Filters.and(filters);
    }

    @Override
    public IFilter eq(String fqn, Map<String, Object> attributes)
    {
        return eq(ApiUtils.extractFqn(fqn), attributes);
    }

    @Override
    public IFilter except(IUUIDIdentifiable object)
    {
        return new ExceptFilter(object);
    }

    @Override
    public IFilter except(String uuid)
    {
        return new ExceptFilter(uuid);
    }

    @Override
    public IFilter fqnAttrIsDescendantOf(String name, IClassFqn ancestorClassFqn, boolean includeAncestor)
    {
        return new DescendantsFilter(name, (ClassFqn)ancestorClassFqn, includeAncestor);
    }

    @Override
    public IFilter hasAnyFile(IAttributeFqn attributeFqn)
    {
        return new FileFilter((AttributeFqn)attributeFqn);
    }

    @Override
    public IFilter inAttributesChainAreObjects(List<IAttrReference> attrChain)
    {
        return new InAttributesChainFilter((List)attrChain, true);
    }

    @Override
    public IFilter inAttributesChainOfObjects(List<IAttrReference> attrChain)
    {
        return new InAttributesChainFilter((List)attrChain, false);
    }

    @Override
    public IFilter inAttrValueOf(String objectId, String attribute, boolean not)
    {
        return new InAttributeFilter(objectId, attribute, not);
    }

    @Override
    public IFilter inCases(String... caseIds)
    {
        return new CasesFilter(caseIds);
    }

    @Override
    public IFilter inDirectAndBackLink(String objectId, String attrCode, boolean showRelatedWithNested,
            IAttributeFqn linkAttrFqn, String hierarchyAttrFqn)
    {
        return new InDirectAndBackLinkFilter(objectId, attrCode, showRelatedWithNested, (AttributeFqn)linkAttrFqn);
    }

    @Override
    public IFilter inDisconnectingStates(IClassFqn masterSlaveFqn)
    {
        return new InDisconnectingStatesFilter((ClassFqn)masterSlaveFqn);
    }

    @Override
    public IFilter inequality(String name, String sign, Object value)
    {
        return new InequalityFilter(name, sign, value);
    }

    @Override
    public IFilter isNotNull(String name)
    {
        return new IsNullFilter(name, true);
    }

    @Override
    public IFilter isNull(String name)
    {
        return new IsNullFilter(name, false);
    }

    @Override
    public IFilter lastNDays(String name, long days)
    {
        return new LastNDaysFilter(name, days);
    }

    @Override
    public IFilter none()
    {
        return NoneFilter.INSTANCE;
    }

    @Override
    public IFilter not(IFilter filter)
    {
        return new NotFilter((IObjectFilter)filter);
    }

    @Override
    public IFilter or(IFilter... filters)
    {
        return new OrFilter(
                Arrays.stream(filters)
                        .map(filter -> (IObjectFilter)filter)
                        .collect(Collectors.toList()));
    }

    @Override
    public IFilter searchableAttributesContainString(String searchString, IClassFqn fqn)
    {
        return new SimpleSearchFilter(searchString, (ClassFqn)fqn);
    }

    @Override
    public IFilter stateTitleEq(Iterable<IClassFqn> fqns, String stateTitle)
    {
        return new StateTitleFilter((Iterable)fqns, stateTitle, false);
    }

    @Override
    public IFilter stateTitleLike(Iterable<IClassFqn> fqns, String stateTitle)
    {
        return new StateTitleFilter((Iterable)fqns, stateTitle, true);
    }

    @Override
    public IFilter stringAttrValueEq(String name, Object value, boolean ignoreCase)
    {
        return new SimpleFilter<>(name, value, ignoreCase);
    }

    @Override
    public IFilter today(String name)
    {
        return new TodayFilter(name);
    }
}
