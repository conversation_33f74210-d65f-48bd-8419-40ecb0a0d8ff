package ru.naumen.core.server.script.api.metainfo;

import java.util.Collection;
import java.util.List;

import com.google.common.base.Function;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.shared.HasAttributeFqn;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

public class AttributeWrapper implements IAttributeWrapper, HasAttributeFqn
{
    public static final Function<Attribute, IAttributeWrapper> WRAPPER = input -> null == input ? null
            : new AttributeWrapper(input);

    private static final Function<Attribute, List<ITagWrapper>> TAGS_EXTRACTOR = new Function<Attribute,
            List<ITagWrapper>>()
    {
        private ApiUtils apiUtils;

        @Override
        public List<ITagWrapper> apply(Attribute input)
        {
            ensureInitialized();
            return apiUtils.getElementTags(input);
        }

        private void ensureInitialized()
        {
            if (null == apiUtils)
            {
                apiUtils = SpringContext.getInstance().getBean(ApiUtils.class);
            }
        }
    };

    private static ScriptDtOHelper scriptDtOHelper;
    private static ResolverUtils resolver;
    private final Attribute attribute;

    public AttributeWrapper(Attribute attribute)
    {
        this.attribute = attribute;
    }

    @Override
    public Collection<String> getRelatedAttrsToExport()
    {
        return attribute.getRelatedAttrsToExport();
    }

    @Override
    public AttributeFqn getAttributeFqn()
    {
        return attribute.getFqn();
    }

    @Override
    public String getCode()
    {
        return attribute.getCode();
    }

    @Override
    public IMetaClassWrapper getDeclaredMetaClass()
    {
        return MetaClassWrapper.FQN_WRAPPER.apply(attribute.getDeclaredMetaClass());
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T> T getDefaultValue()
    {
        ensureInitialized();
        return (T)scriptDtOHelper.wrap(resolver.resolv(new ResolverContext(attribute, attribute.getDefaultValue())),
                attribute);
    }

    @Override
    public String getDescription()
    {
        return attribute.getDescription();
    }

    @Override
    public PresentationWrapper getEditPresentation()
    {
        return PresentationWrapper.WRAPPER.apply(attribute.getEditPresentation());
    }

    @Override
    public boolean getHasDefaultValue()
    {
        return attribute.getHasDefaultValue();
    }

    @Override
    public MetaClassWrapper getMetaClass()
    {
        return MetaClassWrapper.WRAPPER.apply(attribute.getMetaClass());
    }

    @Override
    public String getSearchAlias()
    {
        return attribute.getSearchAlias();
    }

    @Override
    public String getSearchAnalyzer()
    {
        return attribute.getSearchAnalyzer();
    }

    @Override
    public Float getSearchBoost()
    {
        return attribute.getSearchBoost();
    }

    @Override
    public List<ITagWrapper> getTags()
    {
        return TAGS_EXTRACTOR.apply(attribute);
    }

    @Override
    public String getTitle()
    {
        return attribute.getTitle();
    }

    @Override
    public AttributeTypeWrapper getType()
    {
        return AttributeTypeWrapper.WRAPPER.apply(attribute.getType());
    }

    @Override
    public PresentationWrapper getViewPresentation()
    {
        return PresentationWrapper.WRAPPER.apply(attribute.getViewPresentation());
    }

    @Override
    public boolean isComputable()
    {
        return Boolean.TRUE.equals(attribute.isComputable());
    }

    @Override
    public boolean isComputableOnForm()
    {
        return Boolean.TRUE.equals(attribute.isComputableOnForm());
    }

    @Override
    public boolean isEditable()
    {
        return Boolean.TRUE.equals(attribute.isEditable());
    }

    @Override
    public boolean isExportNDAP()
    {
        return Boolean.TRUE.equals(attribute.isExportNDAP());
    }

    @Override
    public boolean isExtendedSearchableForLicensed()
    {
        return attribute.isExtendedSearchableForLicensed();
    }

    @Override
    public boolean isExtendedSearchableForNotLicensed()
    {
        return attribute.isExtendedSearchableForNotLicensed();
    }

    @Override
    public boolean isFilteredByScript()
    {
        return Boolean.TRUE.equals(attribute.isFilteredByScript());
    }

    @Override
    public boolean isHardcoded()
    {
        return attribute.isHardcoded();
    }

    @Override
    public boolean isOverrided()
    {
        return attribute.isOverrided();
    }

    @Override
    public boolean isRequired()
    {
        return Boolean.TRUE.equals(attribute.isRequired());
    }

    @Override
    public boolean isRequiredInInterface()
    {
        return Boolean.TRUE.equals(attribute.isRequiredInInterface());
    }

    @Override
    public boolean isSimpleSearchableForLicensed()
    {
        return attribute.isSimpleSearchableForLicensed();
    }

    @Override
    public boolean isSimpleSearchableForNotLicensed()
    {
        return attribute.isSimpleSearchableForNotLicensed();
    }

    @Override
    public boolean isSystemEditable()
    {
        return Boolean.TRUE.equals(attribute.isSystemEditable());
    }

    @Override
    public boolean isUnique()
    {
        return Boolean.TRUE.equals(attribute.isUnique());
    }

    @Override
    public boolean isWithDefaultValue()
    {
        return Boolean.TRUE.equals(attribute.isWithDefaultValue());
    }

    @Override
    public String toString()
    {
        return "Attribute '" + this.getTitle() + "' (Metaclass: '" + this.getMetaClass() + "', Code: '" + this.getCode()
               + "')";
    }

    private void ensureInitialized()
    {
        if (null == resolver)
        {
            resolver = SpringContext.getInstance().getBean(ResolverUtils.class);
        }
        if (null == scriptDtOHelper)
        {
            scriptDtOHelper = SpringContext.getInstance().getBean(ScriptDtOHelper.class);
        }
    }

    @Override
    public String getInputMaskMode()
    {
        return attribute.getType().getProperty(StringAttributeType.INPUT_MASK_MODE);
    }

    @Override
    public String getInputMask()
    {
        return attribute.getType().getProperty(StringAttributeType.INPUT_MASK);
    }
}
