package ru.naumen.core.server.script.libraries;

import java.util.Set;

import ru.naumen.metainfo.server.libraries.ScriptLibrary;

/**
 * Сервис JAR библиотек
 * <AUTHOR>
 * @since 22.05.2020
 */
public interface LibrariesService
{
    /**
     * Создать JAR библиотеку
     * @param name имя библиотеки
     * @param content содержимое библиотеки
     */
    void createLibrary(String name, byte[] content);

    /**
     * Получить JAR библиотеку
     * @param name имя библиотеки
     */
    ScriptLibrary getLibrary(String name);

    /**
     * Сохранить библиотеку
     */
    void saveLibrary(ScriptLibrary library);

    /**
     * Удалить библиотеку с заданным именем
     * @param name имя библиотеки
     */
    void deleteLibrary(String name);

    /**
     * @return имена существующих библиотек
     */
    Set<String> getLibrariesNames();

    /**
     * Проверяет наличие загруженной библиотеки с идентичным содержимым.
     * @param library исходная библиотека
     * @return <code>true</code>, если библиотека с таким же содержимым уже есть, иначе <code>false</code>
     */
    boolean hasSameLibrary(ScriptLibrary library);

    /**
     * Перезагрузить сервис в ходе синхронизации меты на ноде, отличной от той, где эта мета изменялась
     * При перезагрузке будет вызвана синхронизация заданного
     * {@link ru.naumen.core.server.script.libraries.storage.LibraryStorage}
     */
    void reloadCluster();
}
