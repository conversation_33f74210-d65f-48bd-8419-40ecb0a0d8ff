package ru.naumen.core.server.script;

import java.io.Serial;
import java.io.Serializable;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.codehaus.groovy.control.customizers.CompilationCustomizer;

import com.google.gwt.user.client.rpc.IsSerializable;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.Constants;

/**
 * Контекст выполнения скрипта.
 * Нужен для удобного хранения и пробрасывания по методам параметров выполнения скрипта
 *
 * <AUTHOR>
 * @since 25 авг. 2016 г.
 */
public class ScriptExecutionContext implements IsSerializable, Serializable
{
    @Serial
    private static final long serialVersionUID = 1L;

    /**
     * Максимальное время определяется в com.arjuna.ats.internal.arjuna.coordinator.ReaperElement.ReaperElement
     * (Reapable, int)
     * (умножение на 1000 без приведения к длинному целому)
     */
    private static final int MAX_TRANSACTION_TIMEOUT = Integer.MAX_VALUE / 1000;

    /**
     * Таймаут транзакции выполнения скрипта
     */
    private int transactionTimeout = Constants.DEFAULT_TRANSACTION_TIMEOUT;
    /**
     * Таймаут выполнения скрипта, если в директивах скрипта не указано, то должно быть равно -1
     * (чтоб не было утечек памяти http://sd-jira.naumen.ru/browse/NSDPRD-4826)
     */
    private int scriptTimeout = Constants.DEFAULT_SCRIPT_TIMEOUT;

    /**
     * Признак необходимости скрыть тело скрипта при логировании("тихий" запуск скрипта)
     */
    private boolean isNeedSilentExec;
    /**
     * Признак необходимости конвертировать результат выполнения в JSON
     */
    private boolean isNeedConvertToJson;
    /**
     * Признак наличия директив(параметров выполнения) в скрипте
     */
    private boolean hasDirectives;
    /**
     * Признак того, что выполняемый скрипт - модуль, запущенный из REST API
     */
    private boolean isRunModuleFromRest = false;
    private boolean keepInvokedDataSourceTypeName = false;
    /**
     * Нужно ли ждать готовность скриптовых модулей<br>
     * Но при этом КП modules будет доступна, просто не будет ожидания готовности модулей.
     * То есть если модули готовы, то их можно использовать, если нет - то будет ошибка.
     */
    private boolean isNeedWaitModules = true;

    private transient Map<String, Object> customBindings = new HashMap<>();
    private List<String> nodes = new ArrayList<>();
    /**
     * Пользовательские конфигураторы процесса компиляции
     */
    private CompilationCustomizer[] compilationCustomizers = new CompilationCustomizer[0];

    public ScriptExecutionContext(boolean keepInvokedDataSourceTypeName, boolean isRunModuleFromRest)
    {
        this.keepInvokedDataSourceTypeName = keepInvokedDataSourceTypeName;
        this.isRunModuleFromRest = isRunModuleFromRest;
    }

    public ScriptExecutionContext(@Nullable byte[] script, ScriptExecutionParameters action,
            boolean keepInvokedDataSourceTypeName,
            boolean isRunModuleFromRest)
    {
        this(script, action);
        this.keepInvokedDataSourceTypeName = keepInvokedDataSourceTypeName;
        this.isRunModuleFromRest = isRunModuleFromRest;
    }

    public ScriptExecutionContext()
    { //NOPMD
    }

    public ScriptExecutionContext(@Nullable byte[] script)
    {
        this(script, Constants.DEFAULT_TRANSACTION_TIMEOUT);
    }

    public ScriptExecutionContext(@Nullable byte[] script, ScriptExecutionParameters action)
    {
        this(script);

        isNeedConvertToJson = action.needConvertToJSON();
        customBindings = action.getCustomBindings();
    }

    public ScriptExecutionContext(@Nullable byte[] script, int defaultTransactionTimeout,
            CompilationCustomizer... customizers)
    {
        transactionTimeout = defaultTransactionTimeout;
        this.compilationCustomizers = customizers;
        List<String> directives = new ArrayList<>(0);
        if (script != null)
        {
            nodes = ScriptHelper.extractNodes(script);
            directives = ScriptHelper.extractDirectives(script);
        }
        if (!directives.isEmpty())
        {
            try
            {
                transactionTimeout = Integer.parseInt(directives.getFirst());
                if (MAX_TRANSACTION_TIMEOUT < transactionTimeout)
                {
                    transactionTimeout = MAX_TRANSACTION_TIMEOUT;
                }
                scriptTimeout = transactionTimeout;
            }
            catch (NumberFormatException e)
            {
                // Ничего не делаем, если таймаут не указан, а указана друга директива,
                // то просто игнорируем это исключение, таймаут остается по умолчанию.
                transactionTimeout = defaultTransactionTimeout;
            }

            isNeedSilentExec = directives.contains("silent");
            hasDirectives = true;
        }
    }

    public Map<String, Object> getCustomBindings()
    {
        return customBindings;
    }

    public List<String> getNodes()
    {
        return nodes;
    }

    public int getScriptTimeout()
    {
        return scriptTimeout;
    }

    public int getTransactionTimeout()
    {
        return transactionTimeout;
    }

    public boolean isHasDirectives()
    {
        return hasDirectives;
    }

    public boolean isKeepInvokedDataSourceTypeName()
    {
        return keepInvokedDataSourceTypeName;
    }

    public boolean isNeedConvertToJson()
    {
        return isNeedConvertToJson;
    }

    public boolean isNeedSilentExec()
    {
        return isNeedSilentExec;
    }

    public boolean isRunModuleFromRest()
    {
        return isRunModuleFromRest;
    }

    public CompilationCustomizer[] getCompilationCustomizers()
    {
        return compilationCustomizers;
    }

    public boolean isNeedWaitModules()
    {
        return isNeedWaitModules;
    }

    public void setNeedWaitModules(boolean needWaitModules)
    {
        this.isNeedWaitModules = needWaitModules;
    }
}
