package ru.naumen.core.server.timing.calculate;

import java.util.Date;
import java.util.TimeZone;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.catalog.servicetime.ServiceTimeCatalogItem;

/**
 * Cервис для выполнения операций с датами, с учетом периодов сервисного обслуживания
 *
 * <AUTHOR>
 * @since 27.06.2018
 */
public interface IDateWithServiceTimeOperationsHelper
{
    /**
     * Добавить/отнять рабочие дни к/от текущей дате/даты
     * @param date дата-время. Если передан null, то пересчет применяется к текущей дате
     * @param amountOfDays количество добавляемых/отнимаемых(в случае отрицательного amountOfDays) рабочих дней
     * @return Пересчитанное значение даты-времени. Если время в полученном рабочем дне недоступно, то
     *         функция вернет первую доступную предшествующую/последующую секунду рабочего времени
     *
     */
    @Nullable
    Date addWorkingDays(@Nullable Date date, int amountOfDays);

    /**
     * Добавить/отнять рабочие часы
     * @param date дата-время. Если передан null, то пересчет применяется к текущей дате
     * @param hours количество добавляемых/отнимаемых(в случае отрицательного amountOfHours) рабочих часов
     * @return Пересчитанное значение даты-времени
     */
    @Nullable
    Date addWorkingHours(Date date, int amountOfHours);

    /**
     * Получить последнюю рабочую секунду для календарного поля(день, месяц, год)
     * @param date дата, для которой необходимо найти последнюю рабочую секунду
     * @param field календарное поле(день, месяц, год)
     * @return Последнюю рабочую секунду указанного календарного поля. Если рабочее время отсутствует, возвращает null. 
     */
    @Nullable
    Date getEndOfWorkingCalendarField(@Nullable Date date, int field);

    /**
     * Получить класс обслуживания
     */
    ServiceTimeCatalogItem getServiceTime();

    /**
     * Получить первую рабочую секунду для календарного поля(день, месяц, год)
     * @param date дата, для которой необходимо найти первую рабочую секунду
     * @param field календарное поле(день, месяц, год)
     * @return Первую рабочую секунду указанного календарного поля. Если рабочее время отсутствует, возвращает null. 
     */
    @Nullable
    Date getStartOfWorkingCalendarField(@Nullable Date date, int field);

    /**
     * Получить часововй пояс
     */
    TimeZone getTimeZone();

    /**
     * Установить класс обслуживания
     */
    void setServiceTime(ServiceTimeCatalogItem serviceTime);

    /**
     * Установить часововй пояс
     */
    void setTimeZone(TimeZone timeZone);
}
