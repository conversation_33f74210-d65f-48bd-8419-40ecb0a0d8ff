package ru.naumen.core.server.catalog;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Map;

import jakarta.persistence.AttributeOverride;
import jakarta.persistence.AttributeOverrides;
import jakarta.persistence.Column;
import jakarta.persistence.Embedded;
import jakarta.persistence.FetchType;
import jakarta.persistence.JoinColumn;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.MappedSuperclass;
import jakarta.persistence.OneToMany;
import jakarta.persistence.OrderBy;

import jakarta.annotation.Nonnull;
import jakarta.annotation.Nullable;
import jakarta.xml.bind.annotation.XmlTransient;
import ru.naumen.common.shared.utils.Color;
import ru.naumen.common.shared.utils.ILocalizedText;
import ru.naumen.common.shared.utils.LocalizedText;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.bo.CoreCatalogItem;
import ru.naumen.core.server.bo.AbstractSystemObject;
import ru.naumen.core.server.common.LocalizedTitleAccessor;
import ru.naumen.core.server.dependencies.DeleteAction;
import ru.naumen.core.server.dependencies.SetRemovedAction;
import ru.naumen.core.server.filestorage.CatalogItemIconAccessor;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.DependenceOperations;
import ru.naumen.core.shared.HasClone;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.annotations.Dependence;
import ru.naumen.metainfo.server.annotations.Dependencies;
import ru.naumen.metainfo.server.annotations.Group;
import ru.naumen.metainfo.server.annotations.Groups;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.server.annotations.RequireType;
import ru.naumen.metainfo.shared.Constants.AttrGroup;
import ru.naumen.metainfo.shared.Constants.FileAttributeType;
import ru.naumen.metainfo.shared.Constants.LocalizedAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StringAttributeType;
import ru.naumen.metainfo.shared.permissioncheck.AdminPermissionCategories;
import ru.naumen.metainfo.shared.permissioncheck.HasAdminPermissionCategory;

/**
 * Базовый абстрактный класс для всех элементов справочников.
 * <p>
 * Среди всех элементов справочника выделяются т.н. "Папки" (Folders),
 * являющиеся контейнерами для других элементов, и не имеющие специфичных
 * свойств элемента. Для отличия папок от обычных элементов введён признак -
 * атрибут класса {@link #isFolder()}, который для папок равен <code>true</code>
 * </p>
 * Класс предоставляет возможность организации иерархии элементов справочника.
 * <p>
 * Эта возможность может быть использована для представления иерархии обычных
 * элементов без папок, а также для вложения элементов и папок в другие папки.
 * Для этого в класс добавлен атрибут {@link #getParent()} - ссылка на родительский элемент.
 * У элементов плоских справочников без папок, значение этого атрибута равно <code>null</code>.
 * </p>
 * @param <T> тип элемента справочника наследующего данный класс
 * <AUTHOR>
 * @since 23.12.2010
 */
//@formatter:off
@MappedSuperclass
@Metaclass(id = CatalogItem.CLASS_ID, withCase = false, withUserClasses = true,
        title = { @LStr(value = "Элемент справочника"),
                @LStr(lang = "en", value = "Catalog item"),
                @LStr(lang = "de", value = "Katalogartikel") },
        attributes = {
            @Attribute(code = Constants.CatalogItem.ITEM_ICON, editable = true, type = FileAttributeType.CODE,
                    title = { @LStr(value = "Изображение"), @LStr(lang = "en", value = "Image") },
                    showPresentation = Presentations.FILE_IMAGE_VIEW,
                    editPresentation = Presentations.FILE_UPLOAD,
                    accessor = CatalogItemIconAccessor.NAME)
        }
    )
@Groups({@Group(code = AttrGroup.SYSTEM,  attrs = { "code", "folder", "parent", "color" }),
         @Group(code = AttrGroup.DISPLAY_GROUP, attrs = { "title", "color", "code" })})
//@formatter:on
public abstract class CatalogItem<T extends CatalogItem<T>> extends AbstractSystemObject implements ICatalogItem<T>,
        HasClone, CoreCatalogItem, HasAdminPermissionCategory
{
    public static final String CLASS_ID = Constants.CatalogItem.CLASS_ID;
    private static final long serialVersionUID = -324137814154989169L;

    //@formatter:off
    @Column(name = "removal_date")
    @Attribute(code=Constants.CatalogItem.ITEM_REMOVAL_DATE,
            editable = false, title = { @LStr(value = "Дата архивирования"),
            @LStr(lang = "en", value = "Archive date") }, example = @LStr(value = "2010-10-01 15:05:37"))
    private Date removalDate;

    @Column(name = "removed")
    @Attribute(code=Constants.CatalogItem.ITEM_IS_REMOVED,
            editable = false, title = { @LStr(value = "Признак архивирования"),
            @LStr(lang = "en", value = "In archive") }, example = @LStr(value = "true"))
    private boolean removed;

    @Embedded
    @AttributeOverrides({
                 @AttributeOverride(name="ru",    column = @Column(name = "title", length = DDLTool.STRING_COLUMN_LENGTH)),
                 @AttributeOverride(name="en",    column = @Column(name = "title_en", length = DDLTool.STRING_COLUMN_LENGTH) ),
                 @AttributeOverride(name="de",    column = @Column(name = "title_de", length = DDLTool.STRING_COLUMN_LENGTH)),
                 @AttributeOverride(name="uk",    column = @Column(name = "title_uk", length = DDLTool.STRING_COLUMN_LENGTH)),
                 @AttributeOverride(name="pl",    column = @Column(name = "title_pl", length = DDLTool.STRING_COLUMN_LENGTH)),
                 @AttributeOverride(name="client",column = @Column(name = "title_client", length = DDLTool.STRING_COLUMN_LENGTH) )
                })
    @Attribute(code=Constants.CatalogItem.ITEM_TITLE,
            accessor=LocalizedTitleAccessor.NAME,
            type = LocalizedAttributeType.CODE,
            title = { @LStr(value = "Название"), @LStr(lang = "en", value = "Title")},
            example = { @LStr(value = "Название элемента справочника"), @LStr(lang = "en", value = "Catalog item title") },
            required = RequireType.SYSTEM_REQUIRED)
    private LocalizedText title;

    @Column(name = "code", nullable = false)
    @Attribute(code=Constants.CatalogItem.ITEM_CODE, unique = false,//уникальность кода эл. справочника проверяется отдельно
            required = RequireType.SYSTEM_REQUIRED, editable = false, title = { @LStr(value = "Код элемента справочника"),
            @LStr(lang = "en", value = "Catalog item code") }, example = @LStr(value = "itemCode"))
    private String code;

    @Column(name = "settingsSet")
    @Attribute(editable = false,
            title = {@LStr(value = "Комплект"), @LStr(lang = "en", value = "Set")},
            type = StringAttributeType.CODE)
    private String settingsSet;

    @Column(name = "folder", nullable = false)
    @Attribute(code=Constants.CatalogItem.ITEM_IS_FOLDER,
            required = RequireType.SYSTEM_REQUIRED, editable = false, title = { @LStr(value = "Элемент является папкой"),
            @LStr(lang = "en", value = "Item is a folder") }, example = @LStr(value = "false"))
    private boolean folder = false;

    @Attribute(code=Constants.CatalogItem.ITEM_PARENT,
            editable = false, title = { @LStr(value = "Ссылка на родительский элемент"),
            @LStr(lang = "en", value = "Link to parent item") })
    @Dependencies(value = {
            @Dependence(operationId = DependenceOperations.DELETE, action = DeleteAction.class),
            @Dependence(operationId = DependenceOperations.REMOVE, action = SetRemovedAction.class) })
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "parent")
    private T parent;

    @Column(name = "pos")
    @Attribute(code=Constants.CatalogItem.ITEM_POSITION,
            editable = false, title = { @LStr(value = "Порядок среди сёстринских элементов"),
            @LStr(lang = "en", value = "Catalog item position among sibling") })
    private long position = 0;

    @Column(name = "color", length = 6)
    @Attribute(code=Constants.CatalogItem.ITEM_COLOR,
            editable = true, title = { @LStr(value = "Цвет фона элемента справочника"),
            @LStr(lang = "en", value = "Catalog item background color") })
    private Color color;

    @OneToMany(mappedBy = "parent", fetch = FetchType.LAZY)
    @OrderBy("position")
    private List<T> children;
    //@formatter:on

    @Override
    public CatalogItem<T> clone()
    {
        try
        {
            CatalogItem<T> copy = getClass().newInstance();

            copy.setId(getId());
            copy.setCode(code);
            copy.setColor(color);
            copy.setFolder(folder);
            copy.setPosition(position);
            copy.setRemovalDate(removalDate);
            copy.setRemoved(removed);
            copy.setLocalizedTitle(title.asMap());
            copy.setSettingsSet(settingsSet);

            ObjectUtils.cloneCollection(children, copy.children = new ArrayList<>());
            copy.children.forEach(child -> child.setParent((T)copy));

            return copy;
        }
        catch (InstantiationException | IllegalAccessException e)
        {
            throw new FxException(e);
        }
    }

    public @Nullable String getSettingsSet()
    {
        return settingsSet;
    }

    public void setSettingsSet(@Nullable String settingsSet)
    {
        this.settingsSet = settingsSet;
    }

    /**
     * @return the children
     */
    public List<T> getChildren()
    {
        return this.children;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getCode()
    {
        return this.code;
    }

    /**
     * @return the color
     */
    public Color getColor()
    {
        return this.color;
    }

    @Override
    @Nonnull
    public String getDisplayableTitle()
    {
        return getLocalizedTitle() == null ? StringUtilities.EMPTY
                : getLocalizedTitle().getText(LocaleUtils.getCurrentLocaleLang());
    }

    @Override
    @Nullable
    public ILocalizedText getLocalizedTitle()
    {
        return title;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public T getParent()
    {
        return this.parent;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public long getPosition()
    {
        return this.position;
    }

    @Override
    public Date getRemovalDate()
    {
        return removalDate;
    }

    @Override
    @Nonnull
    public String getTitle()
    {
        return getDisplayableTitle();
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public boolean isFolder()
    {
        return this.folder;
    }

    @Override
    public boolean isRemoved()
    {
        return removed;
    }

    /**
     * @param children the children to set
     */
    public void setChildren(List<T> children)
    {
        this.children = children;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void setCode(String code)
    {
        this.code = code;
    }

    /**
     * @param color the color to set
     */
    public void setColor(Color color)
    {
        this.color = color;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void setFolder(boolean folder)
    {
        this.folder = folder;
    }

    @Override
    public void setLocalizedTitle(@Nullable ILocalizedText title)
    {
        setLocalizedTitle(title == null ? null : title.asMap());
    }

    @Override
    public void setLocalizedTitle(@Nullable Map<String, String> titles)
    {
        if (title == null)
        {
            this.title = new LocalizedText(titles);
        }
        else
        {
            title.setFromMap(titles);
        }
    }

    @Override
    public void setLocalizedTitle(String locale, String title)
    {
        if (this.title == null)
        {
            this.title = new LocalizedText(locale, title);
        }
        else
        {
            this.title.setText(locale, title);
        }
    }

    @Override
    public String getElementCode()
    {
        return getCode();
    }

    @Override
    public String getElementType()
    {
        return Constants.CatalogItem.CLASS_ID;
    }

    @XmlTransient
    @Override
    public String getAdminPermissionCategory()
    {
        return AdminPermissionCategories.CATALOG_ITEM;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void setParent(T parent)
    {
        this.parent = parent;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public void setPosition(long position)
    {
        this.position = position;
    }

    @Override
    public void setRemovalDate(Date removalDate)
    {
        this.removalDate = removalDate;
    }

    @Override
    public void setRemoved(boolean removed)
    {
        this.removed = removed;
    }

    public void setTitle(String title)
    {
        setLocalizedTitle(LocaleUtils.getCurrentLocaleLang(), title);
    }
}
