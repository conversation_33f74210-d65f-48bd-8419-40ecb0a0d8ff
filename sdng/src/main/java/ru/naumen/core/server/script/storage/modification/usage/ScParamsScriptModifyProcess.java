package ru.naumen.core.server.script.storage.modification.usage;

import java.util.Collection;
import java.util.Collections;
import java.util.TreeSet;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.storage.ScriptUsageUtils;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Контракт поведения при изменении скрипта в SCParameters - параметрах запроса
 * Переопределяет специфичные действия при редактировании.
 * Основная логика редактирования содержится в {@link ScriptModifyProcessBase}
 * <AUTHOR>
 * @since Nov 19, 2015
 */
@Component
public class ScParamsScriptModifyProcess extends ScriptModifyProcessSupport<SCParameters>
{
    public static final String SELF_UPDATE = "selfUpdate";

    @Lazy
    @Inject
    private ScriptService scriptService;

    @Override
    protected void dataUpdateSelfUsage(Script script, SCParameters newHolder, ScriptModifyContext context)
    {
        Boolean updateSelfProperties = context.<Boolean> getProperty(SELF_UPDATE);
        if (updateSelfProperties == null || !updateSelfProperties)
        {
            return;
        }

        Script scriptForAgreement = scriptStorageService.getScript(newHolder.getAgreementsFiltrationScript());
        Script scriptForService = scriptStorageService.getScript(newHolder.getServicesFiltrationScript());
        Script scriptForCases = scriptStorageService.getScript(newHolder.getCasesFiltrationScript());

        TreeSet<String> attrs = Sets.newTreeSet();
        if (newHolder.isFilterAgreements() && null != scriptForAgreement)
        {
            attrs.addAll(getAttrsUsedInScript(scriptForAgreement));
        }
        if (newHolder.isFilterServices() && null != scriptForService)
        {
            attrs.addAll(getAttrsUsedInScript(scriptForService));
        }
        if (newHolder.isFilterCases() && null != scriptForCases)
        {
            attrs.addAll(getAttrsUsedInScript(scriptForCases));
        }

        newHolder.setAttrsUsedInScripts(Lists.newArrayList(attrs));
    }

    @Override
    protected String getLocation(SCParameters holder, ScriptModifyContext context)
    {
        return ScriptUsageUtils.getSCParamLocation();
    }

    @Override
    protected String getOldScriptCode(SCParameters oldHolder, ScriptModifyContext context)
    {
        return switch (context.getHolderType())
        {
            case SC_PARAMETERS_AGREEMENT -> oldHolder.getAgreementsFiltrationScript();
            case SC_PARAMETERS_SLM -> oldHolder.getServicesFiltrationScript();
            case SC_PARAMETERS_CASES -> oldHolder.getCasesFiltrationScript();
            default -> null;
        };
    }

    @Override
    protected Collection<ClassFqn> getRelatedMetaClasses(SCParameters holder, ScriptModifyContext context)
    {
        return ScriptUsageUtils.getSCParamFqns();
    }

    @Override
    protected boolean isUsageChangeable()
    {
        return false;
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, SCParameters holder, ScriptModifyContext context)
    {
        switch (context.getHolderType())
        {
            case SC_PARAMETERS_AGREEMENT:
                holder.setAgreementsFiltrationScript(newScriptCode);
                break;
            case SC_PARAMETERS_SLM:
                holder.setServicesFiltrationScript(newScriptCode);
                break;
            case SC_PARAMETERS_CASES:
                holder.setCasesFiltrationScript(newScriptCode);
                break;
            default:
                break;
        }
    }

    @SuppressWarnings("unchecked")
    private Collection<String> getAttrsUsedInScript(Script script)
    {
        return (Collection<String>)CollectionUtils.asCollection(scriptService.execute(script,
                Collections.singletonMap(ScriptService.Constants.SUBJECT, null)));
    }
}
