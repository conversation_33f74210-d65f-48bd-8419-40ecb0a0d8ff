package ru.naumen.core.server.script.api.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.TreeSet;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.sec.server.autorize.cache.AuthorizeServiceUtils;
import ru.naumen.core.server.license.LicensingPolicyService;
import ru.naumen.core.server.license.PolicyInitializer;
import ru.naumen.core.server.license.conf.PermissionSetUnlicUsers;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.core.shared.licensing.policy.LicensingPolicy;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix.Key;
import ru.naumen.metainfo.shared.elements.sec.Marker;
import ru.naumen.metainfo.shared.elements.sec.Profile;

/**
 * Класс для отображения всех маркеров прав в классах и типах, наследников "Основного класса",
 * которые содержат атрибуты и переходы между статусами,
 * ставшие доступными нелицензированному пользователю при смене набора прав.<br>
 * Маркеры отображаются в разбивке по классам и типам в виде HTML
 *
 * <AUTHOR>
 * @since 15.07.19
 */
@Component
public class ChangesInPermissionSetUnlicUsers
{
    private final MetainfoService metainfoService;
    private final SecurityServiceBean securityService;
    private final PolicyInitializer policyInitializer;
    private final LicensingPolicyService licensingPolicyService;
    private final AuthorizeServiceUtils authUtils;
    private final MessageFacade messages;

    @Inject
    public ChangesInPermissionSetUnlicUsers(MetainfoService metainfoService,
            SecurityServiceBean securityService, PolicyInitializer policyInitializer,
            LicensingPolicyService licensingPolicyService,
            AuthorizeServiceUtils authUtils, MessageFacade messages)
    {
        this.metainfoService = metainfoService;
        this.securityService = securityService;
        this.policyInitializer = policyInitializer;
        this.licensingPolicyService = licensingPolicyService;
        this.authUtils = authUtils;
        this.messages = messages;
    }

    /**
     * Показать все маркеры прав в классах и типах, наследников "Основного класса",
     * которые содержат атрибуты и переходы между статусами,
     * ставшие доступными нелицензированному пользователю при смене набора прав
     * c {@code from} на {@code to}.<br>
     * Маркеры отображаются в разбивке по классам и типам
     * @param from набор прав доступа из которого выполняется переход. Если null, то текущий.
     * @param to набор прав доступа в который выполняется переход
     * @return html текст с перечнем маркеров с атрибутами, сгруппированными по классам
     * и типам
     * @see #asHTML(PermissionSetUnlicUsers, PermissionSetUnlicUsers, String)
     */
    public String asHTML(@Nullable PermissionSetUnlicUsers from,
            PermissionSetUnlicUsers to)
    {
        return asHTML(from, to, Constants.AbstractBO.CLASS_ID);
    }

    /**
     * Показать все маркеры прав в классах и типах, наследников класса с кодом
     * {@code classFqn}, которые содержат атрибуты и переходы между статусами,
     * ставшие доступными нелицензированному пользователю при смене набора прав
     * c {@code from} на {@code to}.<br>
     * Маркеры отображаются в разбивке по классам и типам
     * @param from набор прав доступа из которого выполняется переход. Если null, то текущий.
     * @param to набор прав доступа в который выполняется переход
     * @param classFqn код метакласса
     * @return html текст с перечнем маркеров с атрибутами, сгруппированными по классам
     * и типам
     */
    public String asHTML(@Nullable PermissionSetUnlicUsers from,
            PermissionSetUnlicUsers to, String classFqn)
    {
        LicensingPolicy fromPolicy = from != null
                ? policyInitializer.build(from) : licensingPolicyService.getPolicy();
        LicensingPolicy toPolicy = policyInitializer.build(to);
        Pair<LicensingPolicy, LicensingPolicy> policies =
                new Pair<>(fromPolicy, toPolicy);
        ClassFqn fqn = ClassFqn.parse(classFqn);
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        StringBuilder result = new StringBuilder();
        doFor(policies, metaClass, result);
        if (result.length() == 0)
        {
            result.append("<h4>")
                    .append(messages.getMessage("ChangesInPermissionSetUnlicUsers.emptyResult"))
                    .append("</h4>");
        }
        return "<html>" + result.toString() + "</html>";
    }

    /**
     * Добавить в строку {@code stringBuilder} группу маркеров и её содержимое
     * @param stringBuilder строка для наполнения
     * @param fqn код метакласса
     * @param atts атрибуты, которые нужно добавить
     * @param groupTitle заголовок группы атрибутов
     * @param edit является ли группа группой на редактирование
     * @param includeRestGroup добавить группу атрибутов "Остальные атрибуты"
     * @return true - если добавлена полезная информация, false - иначе
     */
    private boolean appendMarkerGroup(StringBuilder stringBuilder, ClassFqn fqn,
            Collection<Attribute> atts, String groupTitle, boolean edit,
            boolean includeRestGroup)
    {
        SecDomainImpl domain = securityService.getDomain(fqn);
        AccessMatrix accessMatrix = domain.getAccessMatrix();
        List<? extends Profile> profiles = accessMatrix.getProfiles().stream()
                .filter(profile -> !profile.isForLicensedUsers())
                .collect(Collectors.toList());

        // Код маркера -> список атрибутов доступных для просмотра
        Map<Marker, List<Attribute>> attsToViewByMarkers = new HashMap<>();
        Set<String> selfMarkerCodes = domain.getMarkersWithoutParent()
                .stream().filter(marker -> edit
                        ? SecConstants.MarkerGroups.EDIT_ATTRS.equals(marker.getGroup())
                        : SecConstants.MarkerGroups.VIEW_ATTRS.equals(marker.getGroup()))
                .map(HasCode::getCode).collect(Collectors.toSet());
        if (includeRestGroup)
        {
            // Добавление специальной группы "Остальные атрибуты"
            selfMarkerCodes.add(edit ? SecConstants.AbstractBO.EDIT_REST_ATTRIBUTES
                    : SecConstants.AbstractBO.VIEW_REST_ATTRIBUTES);
        }
        Map<AccessMatrix.Key, Boolean> declaredData = accessMatrix.getDeclaredData();
        for (Attribute attribute : atts)
        {
            Set<String> markersForView = domain.getMarkerCodesForAttr(
                    attribute.getCode(), edit);
            for (String markerCode : markersForView)
            {
                Marker marker = domain.getMarker(markerCode);
                boolean needAddAttrToMarker = marker.isUnlicensedAllowed()
                                              && profiles.stream().anyMatch(
                        profile -> Boolean.TRUE.equals(accessMatrix.get(profile, marker)))
                                              && (selfMarkerCodes.contains(marker.getCode())
                                                  || profiles.stream().anyMatch(
                        profile -> declaredData.getOrDefault(
                                new Key(profile.getCode(), marker.getCode()),
                                Boolean.FALSE)));
                if (needAddAttrToMarker)
                {
                    attsToViewByMarkers.computeIfAbsent(
                                    marker, key -> new ArrayList<>())
                            .add(attribute);
                }
            }
        }

        boolean hasMarkers = !attsToViewByMarkers.isEmpty();
        if (hasMarkers)
        {
            stringBuilder.append(groupTitle)
                    .append("<br>\n")
                    .append("<ul style=\"list-style-type: disc;\">\n");
            for (Map.Entry<Marker, List<Attribute>> entry : attsToViewByMarkers
                    .entrySet())
            {
                Marker marker = entry.getKey();
                List<Attribute> attributes = entry.getValue();
                stringBuilder.append("\t<li>")
                        .append(messages.getMessage("ChangesInPermissionSetUnlicUsers.marker"))
                        .append(' ')
                        .append(StringUtils.wrap("<u>" + marker.getTitle() + "</u>",
                                '\"'))
                        .append(": ")
                        .append(attributes.stream()
                                .map(attribute -> StringUtils
                                        .wrap(attribute.getTitle(), '\"'))
                                .collect(Collectors.joining(", ")))
                        .append("</li>\n");
            }
            stringBuilder.append("</ul>\n");
        }
        return hasMarkers;
    }

    /**
     * Выполнить сбор маркеров для заданного класса и его наследников
     * @param metaClass метакласс
     * @param result строка, которая будет наполняться описанием маркеров
     */
    private void doFor(Pair<LicensingPolicy, LicensingPolicy> policies,
            MetaClass metaClass, StringBuilder result)
    {
        result.append(processClass(policies, metaClass));
        metaClass.getChildren().stream()
                .map(metainfoService::getMetaClass)
                .sorted(ITitled.COMPARATOR)
                .forEach(child -> doFor(policies, child, result));
    }

    private List<Attribute> getAttributesWithoutParent(MetaClass metaClass)
    {
        ClassFqn parentFqn = metaClass.getParent();
        if (parentFqn == null)
        {
            return metaClass.getAttributes();
        }
        else
        {
            MetaClass parent = metainfoService.getMetaClass(parentFqn);
            Collection<String> parentAttributeCodes = parent.getAttributeCodes();
            return metaClass.getAttributes().stream()
                    .filter(attribute -> !parentAttributeCodes
                            .contains(attribute.getCode()))
                    .collect(Collectors.toList());
        }
    }

    /**
     * Получить название родителя метакласса с указанием является он классом или типом.
     * @param metaClass метакласс для которого выбирается родитель
     */
    private String getParentTitle(MetaClass metaClass)
    {
        ClassFqn fqn = metaClass.getParent();
        return (fqn.isClass()
                ? messages.getMessage("ChangesInPermissionSetUnlicUsers.classPrefixLowercase")
                : messages.getMessage("ChangesInPermissionSetUnlicUsers.casePrefixLowercase"))
               + ' '
               + StringUtils.wrap(metainfoService.getMetaClassTitle(fqn), '\"');
    }

    private boolean hasPermission(MetaClass metaClass, Attribute attribute,
            LicensingPolicy licensingPolicy, boolean edit)
    {
        return authUtils.hasPermissionForUnlicensedUser(
                metaClass.getFqn(), attribute.getCode(), edit, licensingPolicy);
    }

    /**
     * Произошло ли изменение прав в сторону увеличения
     */
    private boolean isPermissionsChanged(Pair<LicensingPolicy, LicensingPolicy> policies,
            MetaClass metaClass, Attribute attribute, boolean edit)
    {
        return !hasPermission(metaClass, attribute, policies.getLeft(), edit)
               && hasPermission(metaClass, attribute, policies.getRight(), edit);
    }

    /**
     * Выполнить обработку метакласса с целью получить html содержимое с заголовком
     * класса/типа, группами маркеров прав доступа, маркерами и их атрибутами
     *
     * @param metaClass метакласс
     * @return html строка
     */
    private String processClass(Pair<LicensingPolicy, LicensingPolicy> policies,
            MetaClass metaClass)
    {
        ClassFqn fqn = metaClass.getFqn();
        StringBuilder stringBuilder = new StringBuilder()
                .append(fqn.isClass()
                        ? "<h3>" + messages.getMessage("ChangesInPermissionSetUnlicUsers.classPrefixFirstCapital")
                        : "<h4>" + messages.getMessage("ChangesInPermissionSetUnlicUsers.casePrefixFirstCapital"))
                .append(' ')
                .append(StringUtils.wrap(metaClass.getTitle(), '\"'))
                .append(fqn.isClass()
                        ? "</h3>"
                        : " (" + getParentTitle(metaClass) + ")</h4>")
                .append('\n');
        Set<Attribute> attsToView = new TreeSet<>(ITitled.COMPARATOR);
        Set<Attribute> attsToEdit = new TreeSet<>(ITitled.COMPARATOR);
        for (Attribute attribute : metaClass.getAttributes())
        {
            if (isPermissionsChanged(policies, metaClass, attribute, false))
            {
                attsToView.add(attribute);
            }
            if (isPermissionsChanged(policies, metaClass, attribute, true))
            {
                attsToEdit.add(attribute);
            }
        }
        String result = "";
        if (!attsToView.isEmpty() || !attsToEdit.isEmpty())
        {
            // если были добавлены атрибуты, то необходимо добавить группу атрибутов
            // "Остальные атрибуты"
            List<Attribute> selfAttributes = getAttributesWithoutParent(
                    metaClass);
            boolean appended = appendMarkerGroup(stringBuilder, fqn, attsToView,
                    messages.getMessage("ChangesInPermissionSetUnlicUsers.viewAttrGroup"),
                    false, selfAttributes.stream().anyMatch(attsToView::contains));
            appended |= appendMarkerGroup(stringBuilder, fqn, attsToEdit,
                    messages.getMessage("ChangesInPermissionSetUnlicUsers.editAttrGroup"),
                    true, selfAttributes.stream().anyMatch(attsToEdit::contains));
            if (appended)
            {
                result = stringBuilder.append('\n').toString();
            }
        }
        return result;
    }
}
