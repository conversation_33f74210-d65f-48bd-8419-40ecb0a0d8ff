package ru.naumen.core.server.catalog;

import jakarta.persistence.Cacheable;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants.ClosureCodeCatalog;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Элемент справочника "Коды закрытия"
 *
 * <AUTHOR>
 */
// @formatter:off
@Entity
@Table(name = "tbl_closurecode", uniqueConstraints = { 
        @UniqueConstraint(name = "tbl_closurecode_code_key", columnNames = {"code"})}, indexes={@jakarta.persistence.Index(name = "idx_closurecode_parent", columnList="parent")})
@Cacheable
@DiscriminatorValue("-")
@DiscriminatorFormula(FlexHelper.NULL_DISCRIMINATOR_FORMULA)
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(ClosureCodeCatalogItem.CLASS_ID)
@Metaclass(id = ClosureCodeCatalogItem.CLASS_ID,
        title = { @LStr(value = "Элемент справочника 'Коды закрытия'"),
                @LStr(lang = "en", value = "'Codes of closure' catalog item"),
                @LStr(lang = "de", value = "Katalogartikel 'Abschlusscodes'")}, withCase = false)
@Catalog(code = ClosureCodeCatalog.CODE, flat = false, withFolders = true, 
    title = { @LStr(value = "Коды закрытия"),
            @LStr(lang = "en", value = "Codes of closure"),
            @LStr(lang = "de", value = "Abschlusscodes") },
    description = { @LStr(value = "Содержит причины закрытия запросов."),
            @LStr(lang = "en", value = "Contains reasons of requests closure."),
            @LStr(lang = "de", value = "Gründe Anforderungen zu schliessen.") })
// @formatter:on
public class ClosureCodeCatalogItem extends CatalogItem<ClosureCodeCatalogItem>
{
    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    public static final String CLASS_ID = ClosureCodeCatalog.ITEM_CLASS_ID;

    @Override
    public ClassFqn getMetaClass()
    {
        return ClosureCodeCatalog.ITEM_FQN;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }
}
