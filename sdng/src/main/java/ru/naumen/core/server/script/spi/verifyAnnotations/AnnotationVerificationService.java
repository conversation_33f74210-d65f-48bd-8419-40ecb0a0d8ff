package ru.naumen.core.server.script.spi.verifyAnnotations;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.codehaus.groovy.control.customizers.ASTTransformationCustomizer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import io.github.classgraph.AnnotationInfo;
import io.github.classgraph.AnnotationParameterValueList;
import io.github.classgraph.ClassInfo;
import io.github.classgraph.ClassInfoList;
import io.github.classgraph.FieldInfo;
import io.github.classgraph.ScanResult;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.Attribute;
import ru.naumen.metainfo.server.Metaclass;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Сервис для валидации аннотаций {@link Metaclass} и {@link Attribute}
 *
 * <AUTHOR>
 * @since 28.06.2021
 */
@Service
public class AnnotationVerificationService
{
    private static final Logger LOG = LoggerFactory.getLogger(AnnotationVerificationService.class);

    private final MetainfoService metainfoService;
    private final MessageFacade messages;
    private final ScriptService scriptService;

    @Inject
    public AnnotationVerificationService(MetainfoService metainfoService, MessageFacade messages,
            ScriptService scriptService)
    {
        this.metainfoService = metainfoService;
        this.messages = messages;
        this.scriptService = scriptService;
    }

    /**
     * Провалидировать объектную модель на предмет соответствия аннотациям {@link Metaclass} и {@link Attribute}
     * @param scanResult результат сканирования библиотеки
     * @return список ошибок
     */
    public List<String> verifyScanResult(ScanResult scanResult)
    {
        return findFailures(findAnnotationPairs(scanResult));
    }

    /**
     * Провалидировать объектную модель на предмет соответствия аннотациям {@link Metaclass} и {@link Attribute}
     * @param module скриптовый модуль
     */
    public void verifyAnnotations(ScriptModule module)
    {
        Script script = new Script(module.getScript());
        script.setCode(module.getCode());
        final List<String> errors = new ArrayList<>();
        ScriptServiceException compilationError = scriptService.getCompilationError(script,
                module.isEmbeddedApplicationModule() ? module.getEmbeddedApplicationCode() : null,
                new ASTTransformationCustomizer(new VerifyAnnotationsASTTransformation(this, errors::addAll)));
        if (compilationError != null)
        {
            throw new FxException(
                    messages.getMessage("ScriptServiceException.errorCompilingModule", module.getCode(),
                            compilationError.getMessage()), compilationError);
        }
        if (!errors.isEmpty())
        {
            int total = errors.size();
            String title = String.format("Validation report: %d failures total\n", total);
            String report = IntStream.range(0, total)
                    .mapToObj(i -> String.format("%d/%d: %s", i + 1, total, errors.get(i)))
                    .collect(Collectors.joining("\n", title, ""));
            LOG.error(report);
            throw new FxException(
                    messages.getMessage("ScriptServiceException.invalidAnnotations", module.getCode()));
        }
    }

    /**
     * Проверяем список пар на корректность
     * @param pairs пары класс - поле с аннотацией @MetaClass или @Attribute
     * @return Ошибки использования аннотаций
     */
    public List<String> findFailures(List<ClassAndField> pairs)
    {
        final Set<String> failures = new HashSet<>();
        for (ClassAndField pair : pairs)
        {
            if (!pair.hasClassAnnotation)
            {
                failures.add(String.format("Metaclass annotation contains no FQN for class %s", pair.className));
                continue;
            }
            if (pair.classAnnotationValue == null)
            {
                failures.add(String.format("No FQN was defined in Metaclass annotation for class %s", pair.className));
                continue;
            }
            ClassFqn classFqn = ClassFqn.parse(pair.classAnnotationValue);
            tryCall(() -> metainfoService.getMetaClass(classFqn), failures);

            if (pair.hasFieldAnnotation)
            {
                String attributeFqnString =
                        pair.fieldAnnotationValue != null ? pair.fieldAnnotationValue : pair.fieldName;
                if (AttributeFqn.isAttributeFqn(attributeFqnString))
                {
                    failures.add(String.format("A short attribute code must be used instead of full FQN %s",
                            attributeFqnString));
                    continue;
                }
                AttributeFqn attributeFqn = AttributeFqn.create(classFqn, attributeFqnString);
                tryCall(() -> metainfoService.getAttribute(attributeFqn), failures);
            }
        }
        return new ArrayList<>(failures);
    }

    /**
     * Вспомогательный метод, который на основе результата сканирования библиотеки (jar) может провалидировать
     * объектную модель на предмет соответствия аннотациям {@link Metaclass} и {@link Attribute}
     * @param scanResult результат сканирования библиотеки
     * @return пары класс - поле над которыми установленны аннотации
     */
    private static List<ClassAndField> findAnnotationPairs(ScanResult scanResult)
    {
        final ClassInfoList classesWithAnnotation = scanResult.getClassesWithAnnotation(Metaclass.class.getName());
        final ClassInfoList classesWithFieldAnnotation =
                scanResult.getClassesWithFieldAnnotation(Attribute.class.getName());
        // обрабатываем classesWithAnnotation, с удалением дублирующих классов из classesWithFieldAnnotation
        final List<ClassAndField> pairs = findAnnotationPairsByClassesWithAnnotation(
                classesWithAnnotation,
                classesWithFieldAnnotation
        );
        // обрабатываем классы, над полями которых есть аннотация @Attribute, а над классом нет аннотации @Metaclass
        final List<ClassAndField> annotationPairsByClassesWithFieldAnnotation =
                findAnnotationPairsByClassesWithFieldAnnotation(classesWithFieldAnnotation);
        pairs.addAll(annotationPairsByClassesWithFieldAnnotation);
        return pairs;
    }

    /**
     * Находит пары (класс c аннотацией {@link Metaclass} + его поле с аннотацией {@link Attribute}).
     *
     * <ol>
     *     <li>Проходим по всему списку классов c аннотацией {@link Metaclass}</li>
     *     <li>Проходим по всем полям для каждого класса и ищем поле с аннотацией {@link Attribute}, если поле
     *     найдено, то формируем пару (класс + поле)</li>
     *     <li>Если исследовали все поля и не нашли поля над которым стояла бы аннотация {@link Attribute}, то
     *     добавляем на проверку только значение аннотации {@link Metaclass}</li>
     *     <li>Удаляем из classesWithFieldAnnotation обработанный класс с аннотацией.</li>
     * </ol>
     * @param classesWithAnnotation классы с аннотацией {@link Metaclass};
     * @param classesWithFieldAnnotation классы с аннотацией {@link Attribute};
     * @return пары класс с аннотацией {@link Metaclass} и его поле с аннотацией {@link Attribute};
     */
    private static List<ClassAndField> findAnnotationPairsByClassesWithAnnotation(ClassInfoList classesWithAnnotation,
            ClassInfoList classesWithFieldAnnotation)
    {
        final List<ClassAndField> pairs = new ArrayList<>();
        classesWithAnnotation.forEach(classWithAnnotation ->
        {
            pairs.addAll(findAnnotationPairsFromClassInfo(classWithAnnotation));
            classesWithFieldAnnotation.remove(classWithAnnotation);
        });
        return pairs;
    }

    /**
     * Обрабатывает классы у которых есть поля над которыми установлена аннотация {@link Attribute}, а над классом
     * нет аннотации {@link Metaclass}. !!! Не проверяет, что над классом не стоит аннотации {@link Metaclass}
     * @param classesWithFieldAnnotation список классов у которых есть поля над которыми установлена аннотация
     * {@link Attribute}
     * @return пары (класс ЕБЗ аннотации {@link Metaclass} + его поле с аннотацией {@link Attribute})
     */
    private static List<ClassAndField> findAnnotationPairsByClassesWithFieldAnnotation(
            ClassInfoList classesWithFieldAnnotation)
    {
        return classesWithFieldAnnotation.stream()
                .map(AnnotationVerificationService::findAnnotationPairsFromClassInfo)
                .flatMap(List::stream)
                .collect(Collectors.toList());
    }

    /**
     * Составляет пары (класс c аннотацией {@link Metaclass} + его поле с аннотацией {@link Attribute}).
     * @param classInfo информация о классе, поля которого нужно просканировать на наличие аннотации
     * {@link Attribute} и составить пары;
     * @return пары (класс c аннотацией {@link Metaclass} + его поле с аннотацией {@link Attribute});
     */
    private static List<ClassAndField> findAnnotationPairsFromClassInfo(ClassInfo classInfo)
    {
        final List<ClassAndField> pairs = new ArrayList<>();
        final String classAnnotationValue;
        final boolean hasClassAnnotation;
        AnnotationInfo annotationInfoMetaclass = classInfo.getAnnotationInfo(Metaclass.class.getName());
        if (annotationInfoMetaclass == null)
        {
            classAnnotationValue = null;
            hasClassAnnotation = false;
        }
        else
        {
            classAnnotationValue = (String)annotationInfoMetaclass.getParameterValues().getValue("value");
            hasClassAnnotation = true;
        }
        for (FieldInfo fieldInfo : classInfo.getFieldInfo())
        {
            AnnotationInfo annotationInfo = fieldInfo.getAnnotationInfo(Attribute.class.getName());
            if (annotationInfo == null)
            {
                // если не найдена информация об аннотации, то значит над полем она не установлена
                continue;
            }
            // может быть пустое. Это означает, что значение не переопределено - дефолтное пустая строка (см
            // аннотацию Attribute)
            AnnotationParameterValueList fieldAnnotationParameters = annotationInfo.getParameterValues();
            String fieldAnnotationValue = (String)fieldAnnotationParameters.getValue("value");

            var classAndField = new ClassAndField(classInfo.getName(), fieldInfo.getName(),
                    classAnnotationValue, hasClassAnnotation, fieldAnnotationValue, true);
            pairs.add(classAndField);
        }
        if (pairs.isEmpty() && hasClassAnnotation)
        {
            //над классом установлена аннотация @Metaclass, а над полем нет, надо проверить код метакласса
            var classAndField = new ClassAndField(classInfo.getName(), "", classAnnotationValue,
                    hasClassAnnotation, null, false);
            pairs.add(classAndField);
        }
        return pairs;
    }

    /**
     * Метод предназначен для того, чтобы проверять успешность выполнения задачи callable, если возникнет ошибка, то
     * ее текст дополнится в коллекцию ошибок report;
     * @param callable задача, которую нужно выполнить;
     * @param report коллекция ошибок, которую нужно пополнить если возникнет ошибка при выполнении задачи;
     */
    private static void tryCall(Callable<?> callable, Set<String> report)
    {
        try
        {
            callable.call();
        }
        catch (FxException e)
        {
            report.add(e.getLocalizedMessage());
        }
        catch (Exception e)
        {
            report.add(new FxException(e).getLocalizedMessage());
        }
    }
}