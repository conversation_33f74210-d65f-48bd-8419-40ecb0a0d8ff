package ru.naumen.core.server.script.spi;

import java.util.Collection;

import java.util.HashSet;

/**
 * Хранит в себе результаты выполнения скрипта такие как созданные или отредактированные им
 * объекты либо добавленные комментарии
 *
 * <AUTHOR>
 *
 */
public class ScriptExecutionResults
{
    private Collection<String> createdObjects = new HashSet<>();
    private Collection<String> addedComments = new HashSet<>();
    private Collection<String> editedComments = new HashSet<>();
    private Collection<String> editedObjects = new HashSet<>();

    public void addAddedCommentUuid(String uuid)
    {
        addedComments.add(uuid);
    }

    public void addEditedCommentUuid(String uuid)
    {
        editedComments.add(uuid);
    }

    public void addCreatedObjectUuid(String uuid)
    {
        createdObjects.add(uuid);
    }

    public void addEditedObjectUuid(String uuid)
    {
        editedObjects.add(uuid);
    }

    public void clear()
    {
        createdObjects.clear();
        addedComments.clear();
        editedComments.clear();
        editedObjects.clear();
    }

    public Collection<String> getAddedCommentUuids()
    {
        return addedComments;
    }

    public Collection<String> getEditedCommentUuids()
    {
        return editedComments;
    }

    public Collection<String> getCreatedObjectUuids()
    {
        return createdObjects;
    }

    public Collection<String> getEditedObjectUuids()
    {
        return editedObjects;
    }
}
