package ru.naumen.core.server.script.spi;

import static com.google.common.base.Preconditions.checkNotNull;
import static ru.naumen.commons.shared.utils.StringUtilities.join;
import static ru.naumen.core.shared.Constants.AUTHOR;
import static ru.naumen.core.shared.Constants.MAX_COLLECTION_OBJECTS;
import static ru.naumen.metainfo.shared.Constants.COLLECTION_ENTITY_TYPES;

import java.util.Collection;
import java.util.Objects;

import org.apache.commons.lang3.StringUtils;

import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Врапер для {@link DtObject} чтобы их можно было использовать в скриптах,
 * при этом если запрашиваемый атрибут отсутствует в объекте,
 * то поднимается оригинальный объект и значение получается из него
 *
 * <AUTHOR>
 * @since 09.11.2012
 */
public abstract class AbstractLazyScriptDtObject extends AbstractScriptDtObject<DtObject>
{
    private static final long serialVersionUID = 6770575127117989562L;
    private MetaClass metaClass;
    private ScriptUtils scriptUtils;
    DtObject delegate;

    AbstractLazyScriptDtObject(DtObject delegate, ScriptDtOHelper helper, ScriptUtils scriptUtils)
    {
        super(delegate, helper);
        this.scriptUtils = scriptUtils;
    }

    @SuppressWarnings("unchecked")
    @Override
    public Object get(Object key)
    {
        //В случае, если объект еще небыл записан в базу, не пытаемся его поднять
        if (delegate == null && !isTemplate() && (!getDelegate().containsKey(key) || isCollectionWithMaxSize(key
                .toString()) || (key.equals(AUTHOR)
                                 && ((DtObject)getDelegate().getProperty(AUTHOR)).getUUID() == null)))
        {
            delegate = loadObject();
            afterLoadRealObject(key.toString());
        }

        return extractValue(getDelegate(), key);
    }

    protected Object extractValue(IProperties source, Object key)
    {
        if (AbstractBO.UUID.equals(key))
        {
            return source.getProperty(key.toString());
        }

        if (Employee.LICENSE.equals(key) && isPropertyInstanceOfCollection(key.toString()))
        {
            return helper.wrap(join(Objects.requireNonNull(source.getProperty(key.toString())), ", "));
        }

        return helper.wrap(source.getProperty((String)key));
    }

    protected DtObject loadObject()
    {
        return scriptUtils.get(getDelegate().getUUID());
    }

    @Override
    public DtObject getDelegate()
    {
        if (delegate != null)
        {
            return delegate;
        }
        return super.getDelegate();
    }

    @Override
    public int getLevel()
    {
        return 0;
    }

    @Override
    public ClassFqn getMetainfo()
    {
        checkMetainfo();
        return getDelegate().getMetaClass();
    }

    @Override
    public Boolean hasPermission(String name)
    {
        return Boolean.TRUE;
    }

    protected void afterLoadRealObject(String name)
    {
    }

    @Override
    protected MetaClass metaclass()
    {
        if (metaClass == null)
        {
            checkMetainfo();
            metaClass = helper.getMetaClass(getDelegate());
        }
        return metaClass;
    }

    private void checkMetainfo()
    {
        checkNotNull(get(AbstractBO.METACLASS));
    }

    private boolean isCollectionWithMaxSize(String key)
    {
        boolean isLazyCollectionWithMaxSize = isPropertyInstanceOfCollection(key) && ((Collection<?>)getDelegate()
                .getProperty(key)).size() == MAX_COLLECTION_OBJECTS;
        if (null == helper || !isLazyCollectionWithMaxSize || null == get(AbstractBO.METACLASS))
        {
            return false;
        }
        MetaClass mc = helper.getMetaClass(getDelegate());
        return COLLECTION_ENTITY_TYPES.contains(mc.getAttribute(key).getType().getCode());
    }

    protected boolean isPropertyInstanceOfCollection(String key)
    {
        return getDelegate().getProperty(key) instanceof Collection<?>;
    }

    /**
     * В случае, если объект является шаблоном на форме добавления 
     * возвращает true
     * @return
     */
    private boolean isTemplate()
    {
        return getDelegate().getUUID() != null && getDelegate().getUUID().startsWith(Constants.TEMP_UUID);
    }

    @Override
    public boolean isEmpty()
    {
        return StringUtils.isEmpty(getUUID());
    }
}
