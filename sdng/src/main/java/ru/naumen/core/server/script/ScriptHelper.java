package ru.naumen.core.server.script;

import static ru.naumen.core.server.util.AttrGeneratedClassConverter.convertSafe;
import static ru.naumen.metainfo.shared.Constants.Presentations.DATE_EDIT;

import java.math.BigDecimal;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Pattern;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import groovy.lang.GString;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.common.shared.utils.DateWithoutTimeDto;
import ru.naumen.commons.server.utils.MessageDigestUtils;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.UUIDIdentifiableBase;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.catalog.CatalogItem;
import ru.naumen.core.server.customforms.AttrValueTransformer;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.script.spi.AbstractScriptDtObject;
import ru.naumen.core.server.script.spi.AggregateContainerWrapper;
import ru.naumen.core.server.script.spi.IScriptDtObject;
import ru.naumen.core.server.script.spi.IScriptObjectBase;
import ru.naumen.core.server.script.spi.LazyScriptDtObject;
import ru.naumen.core.server.script.spi.ScriptDate;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.shared.AggregateContainer;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.AbstractDtObject;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.dto.TreeDtObject;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.core.shared.script.places.ScriptCategoryFqn;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.BOLinksAttributeType;
import ru.naumen.metainfo.shared.Constants.BackLinkAttributeType;
import ru.naumen.metainfo.shared.Constants.CaseListAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemAttributeType;
import ru.naumen.metainfo.shared.Constants.CatalogItemsAttributeType;
import ru.naumen.metainfo.shared.Constants.DateAttributeType;
import ru.naumen.metainfo.shared.Constants.DateTimeAttributeType;
import ru.naumen.metainfo.shared.Constants.DoubleAttributeType;
import ru.naumen.metainfo.shared.Constants.FileAttributeType;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.Constants.ResponsibleAttributeType;
import ru.naumen.metainfo.shared.Constants.SecGroupsAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.sec.Group;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;

/**
 * Вспомогательные методы для работы со скриптами
 *
 * <AUTHOR>
 *
 */
@Component
public class ScriptHelper
{
    private static final Logger LOG = LoggerFactory.getLogger(ScriptHelper.class);
    private static final Pattern SCRIPT_CODE_PATTERN = Pattern.compile("^[a-zA-Z]+[0-9a-zA-Z]*");

    /**
     * Проверяет вхождениее массива b в массив a начиная с позиции fromA
     * @return true - есть вхождение, false - иначе
     */
    public static boolean equals(byte[] a, int fromA, byte[] b)
    {
        int length = b.length;
        for (int i = 0; i < length; ++i)
        {
            if (a[fromA + i] != b[i])
            {
                return false;
            }
        }
        return true;
    }

    /**
     * Возвращает содержимое первого комментария обрамленного тегами begin и end
     * @return
     */
    public static String extractComment(byte[] bytes, byte[] begin, byte[] end)
    {
        int startPos = -1;
        for (int i = 0; i < bytes.length - begin.length; ++i)
        {
            if (equals(bytes, i, begin))
            {
                startPos = i + begin.length;
                break;
            }
        }

        int endPos = -1;
        if (-1 != startPos)
        {
            for (int i = startPos; i < bytes.length - end.length; ++i)
            {
                if (equals(bytes, i, end))
                {
                    endPos = i - 1;
                    break;
                }
            }
        }

        if (-1 == startPos || -1 == endPos)
        {
            return null;
        }
        return new String(bytes, startPos, endPos - startPos + 1, StandardCharsets.UTF_8);
    }

    public static List<String> extractDirectives(byte[] bytes)
    {
        String value = extractComment(bytes, "/*&".getBytes(StandardCharsets.UTF_8),
                "*/".getBytes(StandardCharsets.UTF_8));
        if (value == null)
        {
            return Collections.emptyList();
        }
        List<String> res = new ArrayList<>(4);
        StringUtilities.splitByDelimiters(value, " ", res);
        return res;
    }

    /**
     * С версии 4.18 формат нод должен быть ru.naumen.cluster.node.role : -Dnaumen.cluster.node
     * например FRONTEND:1 или UNIVERSAL:2f9d324a-559c-4c69-adc0-b3aa53eda038
     */
    public static List<String> extractNodes(byte[] script)
    {
        String value = extractComment(script, "/*@".getBytes(StandardCharsets.UTF_8),
                "*/".getBytes(StandardCharsets.UTF_8));
        if (value == null)
        {
            return Collections.emptyList();
        }
        return Lists.newArrayList(StringUtilities.splitByDelimitersAndTrim(value, ","));
    }

    /**
     * Возвращает набор кодов всех категорий для заданного скрипта
     */
    public static Set<String> getAllCategoriesCodesForScript(Script script)
    {
        Set<String> uniqueCategories = new HashSet<>();
        uniqueCategories.addAll(script.getDefaultCategories());

        for (ScriptUsagePoint usage : script.getUsagePoints())
        {
            uniqueCategories.add(getCategoryCode(usage.getCategory()));
        }

        if (uniqueCategories.isEmpty())
        {
            uniqueCategories.add(getCategoryCode(OtherCategories.WITHOUT_CATEGORY));
        }
        return uniqueCategories;
    }

    /**
     * Возвращает набор всех категорий для заданного скрипта
     */
    public static Set<ScriptCategory> getAllCategoriesForScript(Script script)
    {
        Set<ScriptCategory> uniqueCategories = new HashSet<>();
        for (String categoryCode : script.getDefaultCategories())
        {
            uniqueCategories.add(ScriptHelper.getCategory(new ScriptCategoryFqn(categoryCode)));
        }

        for (ScriptUsagePoint usage : script.getUsagePoints())
        {
            uniqueCategories.add(usage.getCategory());
        }

        if (uniqueCategories.isEmpty())
        {
            uniqueCategories.add(OtherCategories.WITHOUT_CATEGORY);
        }
        return uniqueCategories;
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    public static ScriptCategory getCategory(ScriptCategoryFqn fqn)
    {
        try
        {
            Class clazz = Class.forName("ru.naumen.core.shared.script.places." + fqn.getClazz());
            return (ScriptCategory)Enum.valueOf(clazz, fqn.getName());
        }
        catch (ClassNotFoundException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Возвращает уникальный код для переданной категории
     */
    public static String getCategoryCode(ScriptCategory category)
    {
        return new ScriptCategoryFqn(category).toString();
    }

    /**
     * Возвращает кодировку скрипта
     *
     * @see Script
     *
     * @param bytes тело скрипта
     * @return название кодировки тела скрипта
     */
    public static String getEncoding(byte[] bytes)
    {
        String value = extractComment(bytes, "/*!".getBytes(StandardCharsets.UTF_8),
                "*/".getBytes(StandardCharsets.UTF_8));
        if (null == value)
        {
            return "UTF-8";
        }
        return value.trim();
    }

    /**
     * Проверяет что код скрипта валидный
     */
    public static Boolean isScriptCodeValid(String scriptCode)
    {
        return SCRIPT_CODE_PATTERN.matcher(scriptCode).matches();
    }

    /**
     * Определение принадлежит ли скрипт указанной категории
     */
    public static boolean isScriptHasCategory(Script script, ScriptCategory category)
    {
        if (category == OtherCategories.WITHOUT_CATEGORY)
        {
            return script.getUsagePoints().isEmpty() && script.getDefaultCategories().isEmpty();
        }

        String categoryCode = getCategoryCode(category);

        if (script.getDefaultCategories().contains(categoryCode))
        {
            return true;
        }

        for (ScriptUsagePoint usage : script.getUsagePoints())
        {
            if (category == usage.getCategory())
            {
                return true;
            }
        }

        return false;
    }

    @Inject
    private ConfigurationProperties configurationProperties;
    @Inject
    private MappingService mapperService;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private IPrefixObjectLoaderService objectLoaderService;
    @Inject
    private ScriptDtOHelper scriptDtOHelper;
    @Inject
    private SecurityService service;
    @Inject
    private SnapshotService snapshotService;
    @Inject
    private AttrValueTransformer transformer;
    @Inject
    private ResolverUtils resolverUtils;

    /**
     * Метод для формирования ссылки на карточку скрипта.
     * @param script скрипт, выполняющийся в {@link ScriptService} сервисе
     * @return строка - html тег c уникальным номером скрипта.
     */
    public String getScriptCodeAsLink(Script script)
    {
        String scriptCode = ru.naumen.commons.shared.utils.StringUtilities.EMPTY;
        if (!ru.naumen.commons.shared.utils.StringUtilities.isEmpty(script.getCode()))
        {
            String link = getScriptLink(script.getCode());
            scriptCode = String.format(ru.naumen.core.shared.Constants.Scripts.SCRIPT_LINK_TEMPLATE, link,
                    script.getCode());
        }
        return scriptCode;
    }

    /**
     * Метод для формирования url расположения скрипта
     */
    private String getScriptLink(String scriptCode)
    {
        String baseUrl = configurationProperties.getBaseUrl();
        baseUrl = baseUrl.endsWith("/") ? baseUrl + Constants.ADMIN_ALIAS + "/" : baseUrl + "/" + Constants.ADMIN_ALIAS
                                                                                  + "/";

        String target = String.format("#script:%s", scriptCode);

        return baseUrl + target;
    }

    private void transform(Object obj, DtObject dto)
    {
        DtoProperties itemProperties = new DtoProperties(null, Lists.<String> newArrayList(AbstractBO.METACLASS,
                AbstractBO.UUID, AbstractBO.TITLE));
        if (obj instanceof CatalogItem<?>)
        {
            itemProperties.add(Constants.CatalogItem.ITEM_COLOR, Constants.CatalogItem.ITEM_ICON);
        }
        mapperService.transform(obj, dto, itemProperties);
    }

    private Object transformAggrAttrValue(Object value)
    {
        if (value instanceof String)
        {
            IUUIDIdentifiable object = objectLoaderService.get((String)value);
            DtObject dto = new SimpleDtObject();
            transform(object, dto);
            return new SimpleTreeDtObject(null, dto);
        }

        if (value instanceof AggregateContainerWrapper)
        {
            AggregateContainerWrapper container = (AggregateContainerWrapper)value;
            if (container.getEmployee() != null)
            {
                value = Lists.newArrayList(container.getOu() != null ? container.getOu() : container.getTeam(),
                        container.getEmployee());
            }
            else
            {
                value = container.getOu() != null ? container.getOu() : container.getTeam();
            }
        }

        if (value instanceof AggregateContainer)
        {
            AggregateContainer container = (AggregateContainer)value;
            if (container.getEmployee() != null)
            {
                value = Lists.newArrayList(container.getOu() != null ? container.getOu() : container.getTeam(),
                        container.getEmployee());
            }
            else
            {
                value = container.getOu() != null ? container.getOu() : container.getTeam();
            }
        }

        if (value instanceof IUUIDIdentifiable)
        {
            DtObject dto = transformUUIDIdentifiable(value, false);
            if (!(dto instanceof TreeDtObject))
            {
                dto = new SimpleTreeDtObject(null, dto);
            }
            return dto;
        }

        if (!(value instanceof Collection<?>))
        {
            return null;
        }

        List<Object> pair = Lists.newArrayList((Collection<?>)value);
        if (pair.size() != 2 || pair.get(0) == null || pair.get(1) == null)
        {
            return null;
        }

        if (pair.get(0) instanceof IUUIDIdentifiable)
        {
            if (pair.get(0) instanceof AbstractDtObject)
            {
                return new SimpleTreeDtObject(new SimpleTreeDtObject(null, (DtObject)pair.get(0)),
                        (DtObject)pair.get(1));
            }

            DtObject parent = new SimpleDtObject();
            transform(scriptDtOHelper.unwrap((IUUIDIdentifiable)pair.get(0)), parent);

            DtObject child = new SimpleDtObject();
            transform(scriptDtOHelper.unwrap((IUUIDIdentifiable)pair.get(1)), child);

            return new SimpleTreeDtObject(new SimpleTreeDtObject(null, parent), child);
        }

        if (pair.get(0) instanceof String)
        {
            IUUIDIdentifiable parent = objectLoaderService.get((String)pair.get(0));
            IUUIDIdentifiable child = objectLoaderService.get((String)pair.get(1));

            DtObject parentDto = new SimpleDtObject();
            transform(parent, parentDto);

            DtObject childDto = new SimpleDtObject();
            transform(child, childDto);

            return new SimpleTreeDtObject(new SimpleTreeDtObject(null, parentDto), childDto);
        }

        return null;
    }

    public static Collection<ClassFqn> transformCaseListValue(@Nullable Object valueToTransform)
    {
        if (!(valueToTransform instanceof Collection<?>))
        {
            return null;
        }

        Collection<ClassFqn> dtos = Lists.newArrayListWithCapacity(((Collection<?>)valueToTransform).size());
        for (Object obj : (Collection<?>)valueToTransform)
        {
            if (obj instanceof String)
            {
                dtos.add(ClassFqn.parse((String)obj));
            }
            else if (obj instanceof ClassFqn)
            {
                dtos.add((ClassFqn)obj);
            }
            else if (obj instanceof Collection<?>)
            {
                Object values = transformCaseListValue(obj);
                if (values instanceof Collection<?>)
                {
                    dtos.addAll(transformCaseListValue(obj));
                }
            }
        }

        return dtos;
    }

    private Object transformCatalogAnyItemAttributeTypes(Object valueToTransform, Attribute attr)
    {
        return transformer.transform(resolverUtils.resolvAndValidate(new ResolverContext(attr, valueToTransform)),
                attr);
    }

    private Object transformDoubleValue(Object valueToTransform)
    {
        if (valueToTransform instanceof Double)
        {
            return valueToTransform;
        }
        if (valueToTransform instanceof BigDecimal bigDecimal)
        {
            return bigDecimal.doubleValue();
        }
        if (valueToTransform instanceof Float f)
        {
            return f.doubleValue(); //NOPMD
        }
        if (valueToTransform instanceof Long l)
        {
            return l.doubleValue(); //NOPMD
        }
        if (valueToTransform instanceof Integer integer)
        {
            return integer.doubleValue(); //NOPMD
        }
        return null;
    }

    private Object transformFileAttributeValue(Object valueToTransform, boolean isComputableAttr)
    {
        if (valueToTransform instanceof Collection<?>)
        {
            ArrayList<Object> list = new ArrayList<>();
            for (Object value : (Collection<?>)valueToTransform)
            {
                list.addAll(transformFileCollectionsValue(transformFileAttributeValue(value, isComputableAttr)));
            }
            return list;
        }
        return transformUUIDIdentifiable(valueToTransform, isComputableAttr);
    }

    private Collection<?> transformFileCollectionsValue(Object value)
    {
        return value instanceof Collection<?> ? (Collection<?>)value : Lists.newArrayList(value);
    }

    private Object transformIntegerValue(Object valueToTransform)
    {
        if (valueToTransform instanceof Long)
        {
            return valueToTransform;
        }
        if (valueToTransform instanceof Integer intValue)
        {
            return intValue.longValue();  //NOPMD
        }
        return null;
    }

    @SuppressWarnings("unchecked")
    private Object transformObjectsLinksValue(Object valueToTransform, boolean isComputableAttr)
    {
        if (!(valueToTransform instanceof Collection<?>))
        {
            return null;
        }

        Collection<DtObject> dtos = Lists.newArrayListWithCapacity(((Collection<?>)valueToTransform).size());
        for (Object obj : (Collection<?>)valueToTransform)
        {
            if (null == obj || (obj instanceof Collection<?> && ((Collection<DtObject>)obj).isEmpty()))
            {
                continue;
            }
            else if (obj instanceof Collection<?>)
            {
                //Если в obj находится коллекция, то повторяем выполнение метода и забираем результат выполнения
                Collection<DtObject> dtos2 = (Collection<DtObject>)transformObjectsLinksValue(obj, isComputableAttr);
                if (!dtos2.isEmpty())
                {
                    dtos.addAll(dtos2);
                }
            }
            else
            {
                dtos.add(transformUUIDIdentifiable(obj, isComputableAttr));
            }
        }

        return dtos;
    }

    private Object transformScriptDate2Date(Object object, String presentation)
    {
        if (object instanceof ScriptDate)
        {
            if (DATE_EDIT.equals(presentation))
            {
                return new DateWithoutTimeDto(new Date(((ScriptDate)object).getTime()));
            }
            return new Date(((ScriptDate)object).getTime());
        }
        return object;
    }

    @SuppressWarnings("unchecked")
    private Object transformSecGroupAttrValue(Object valueToTransform)
    {
        if (valueToTransform instanceof String)
        {
            return Lists.newArrayList(snapshotService.prepare(service.getGroup((String)valueToTransform)));
        }
        if (valueToTransform instanceof Collection<?>)
        {
            Collection<Group> groups = Lists.newArrayListWithCapacity(((Collection<?>)valueToTransform).size());
            for (String code : (Collection<String>)valueToTransform)
            {
                groups.add(snapshotService.prepare(service.getGroup(code)));
            }

            return groups;
        }

        return null;
    }

    private DtObject transformUUIDIdentifiable(Object object, boolean isComputableAttr)
    {
        if (object instanceof LazyScriptDtObject && ((LazyScriptDtObject)object).isUuidObject())
        {
            object = objectLoaderService.load(((LazyScriptDtObject)object).getUUID());
        }

        if (object instanceof String)
        {
            object = objectLoaderService.load((String)object);
        }

        if (!(object instanceof IUUIDIdentifiable))
        {
            return null;
        }

        if (!isComputableAttr && object instanceof IScriptDtObject
            && ((AbstractScriptDtObject<?>)object).getDelegate() instanceof AbstractDtObject)
        {
            return (DtObject)((AbstractScriptDtObject<?>)object).getDelegate();
        }

        DtObject dto;
        if (!isComputableAttr && object instanceof IHasMetaInfo
            && metainfoService.getMetaClass(((IHasMetaInfo)object).getMetaClass()).isHasParentRelation())
        {
            dto = new SimpleTreeDtObject(null, new SimpleDtObject());
        }
        else
        {
            dto = new SimpleDtObject();
        }

        transform(scriptDtOHelper.unwrap(object), dto);
        return dto;
    }

    /**
     * Преобразует значение, полученное в скрипте вычислимого атрибута или атрибута, вычислимого при редактировании,
     * для дальнейшей передачи на сторону клиента
     * @param valueToTransform значение, которое необходимо преобразовать
     * @param attr атрибут
     * @param isComputableAttr является ли атрибут вычислимым
     * @return
     */
    public Object transformValueForCompAttrAndCompOnFormAttr(Object valueToTransform, Attribute attr,
            boolean isComputableAttr)
    {
        valueToTransform = convertSafe(valueToTransform);
        String attrType = attr.getType().getCode();

        if (ru.naumen.metainfo.shared.Constants.AggregateAttributeType.CODE.equals(attrType)
            || ResponsibleAttributeType.CODE.equals(attrType))
        {
            return transformAggrAttrValue(valueToTransform);
        }
        if (FileAttributeType.CODE.equals(attrType))
        {
            return transformFileAttributeValue(valueToTransform, isComputableAttr);
        }
        if (ObjectAttributeType.CODE.equals(attrType) || CatalogItemAttributeType.CODE.equals(attrType))
        {
            return transformUUIDIdentifiable(valueToTransform, isComputableAttr);
        }

        if (BackLinkAttributeType.CODE.equals(attrType) || BOLinksAttributeType.CODE.equals(attrType)
            || CatalogItemsAttributeType.CODE.equals(attrType))
        {
            return transformObjectsLinksValue(valueToTransform, isComputableAttr);
        }

        if (SecGroupsAttributeType.CODE.equals(attrType))
        {
            return transformSecGroupAttrValue(valueToTransform);
        }

        if (DoubleAttributeType.CODE.equals(attrType))
        {
            return transformDoubleValue(valueToTransform);
        }

        if (IntegerAttributeType.CODE.equals(attrType))
        {
            return transformIntegerValue(valueToTransform);
        }

        if (CaseListAttributeType.CODE.equals(attrType))
        {
            return transformCaseListValue(valueToTransform);
        }

        if (AttrValueTransformer.isCatalogAnyItemTypes(attr))
        {
            return transformCatalogAnyItemAttributeTypes(valueToTransform, attr);
        }

        //Отлавливаем наиболее вероятные ошибки при составлении скрипта
        if (valueToTransform instanceof IScriptObjectBase<?> || valueToTransform instanceof UUIDIdentifiableBase)
        {
            LOG.error("Wrong type of data - {}", valueToTransform.getClass().getName());
            return null;
        }

        if (DateTimeAttributeType.CODE.equals(attrType) || DateAttributeType.CODE.equals(attrType))
        {
            return transformScriptDate2Date(valueToTransform,
                    isComputableAttr ? attr.getViewPresentation().getCode() : attr.getEditPresentation().getCode());
        }

        if (valueToTransform instanceof GString)
        {
            return ((GString)valueToTransform).toString();
        }

        // Все коллекции обрабатываются ранее.
        // Если до этого места доходит коллекция, заменяем ее на null, чтоб не возникало ClassCastException
        if (valueToTransform instanceof Collection<?>)
        {
            return null;
        }

        return valueToTransform;
    }

    /**
     * Вычисляет контрольную сумму для модуля
     * @param code код модуля
     * @param scriptBody тело модуля (groovy скрипт)
     * @param isSuperUserReadable флаг доступности на чтение для суперпользователей
     * @param isSuperUserWritable флаг доступности на редактирование для суперпользователей
     * @param isRestAllowed флаг доступности для REST-запросов
     * @return строка - контрольная сумма модуля
     */
    public static String generateModuleChecksum(String code, String scriptBody, boolean isSuperUserReadable,
            boolean isSuperUserWritable, boolean isRestAllowed)
    {
        // флаг доступности через REST учитываем только если выполнение через REST запрещено (для того, чтобы у
        // старых модулей до доработки чек сумма не изменилась)
        String isRestAllowedPart = isRestAllowed ? "" : "false";
        return MessageDigestUtils.sha256WithStandardSalt(
                code + scriptBody + isSuperUserReadable + isSuperUserWritable + isRestAllowedPart);
    }

    /**
     * Вычисляет контрольную сумму для модуля
     * @param module скриптовый модуль
     * @return строка - контрольная сумма модуля
     */
    public static String generateModuleChecksum(ScriptModule module)
    {
        return generateModuleChecksum(module.getCodeWithoutEmbeddedApplication(), module.getScript(),
                module.isSuperUserReadable(), module.isSuperUserWritable(), module.isRestAllowed());
    }
}
