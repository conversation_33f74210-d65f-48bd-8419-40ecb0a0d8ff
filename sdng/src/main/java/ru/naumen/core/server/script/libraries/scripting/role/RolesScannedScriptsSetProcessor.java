package ru.naumen.core.server.script.libraries.scripting.role;

import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_ACCESS_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_OWNERS_KEY;

import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.Action;
import net.customware.gwt.dispatch.shared.Result;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.i18n.LocaleInfoImpl;
import ru.naumen.core.server.script.libraries.LibrariesService;
import ru.naumen.core.server.script.libraries.registration.role.ScriptRoleCategory;
import ru.naumen.core.server.script.storage.ScriptStorageServiceBean;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.elements.sec.RoleImpl;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.dispatch2.AddSecurityRoleAction;
import ru.naumen.metainfo.shared.dispatch2.EditSecurityRoleAction;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * Обработчик результата сканирования аннотаций скриптовых ролей в библиотеках;
 */
@Service
class RolesScannedScriptsSetProcessor
{
    private static final Logger LOG = LoggerFactory.getLogger(RolesScannedScriptsSetProcessor.class);
    private final LibrariesService librariesService;
    private final ScriptStorageServiceBean scriptStorageService;
    private final MetainfoServicePersister metainfoService;
    private final Dispatch dispatch;
    private final ScriptDtoFactory scriptDtoFactory;
    private final LocaleInfoImpl localeInfo;
    private final SecurityService securityService;
    private final AuthorizationRunnerService authorizeRunner;

    @Inject
    public RolesScannedScriptsSetProcessor(LibrariesService librariesService,
            ScriptStorageServiceBean scriptStorageService,
            MetainfoServicePersister metainfoService, Dispatch dispatch,
            ScriptDtoFactory scriptDtoFactory, LocaleInfoImpl localeInfo,
            SecurityService securityService, AuthorizationRunnerService authorizeRunner)
    {
        this.librariesService = librariesService;
        this.scriptStorageService = scriptStorageService;
        this.metainfoService = metainfoService;
        this.dispatch = dispatch;
        this.scriptDtoFactory = scriptDtoFactory;
        this.localeInfo = localeInfo;
        this.securityService = securityService;
        this.authorizeRunner = authorizeRunner;
    }

    /**
     * Метод получения {@link MapProperties} с параметрами создаваемой или обновляемой роли
     * @param securityRoleScript прототип скриптовой роли
     * @param role роль, полученная из {@link SecurityService}
     * @return объект {@link MapProperties} с параметры создаваемой или обновляемой роли для передачи {@link Action}
     */
    private MapProperties getPropertiesForCreateOrUpdateRole(SecurityRoleScript securityRoleScript,
            @Nullable RoleImpl role)
    {
        final MapProperties properties = new MapProperties();
        properties.put(Constants.Role.ONLY_FOR_GLOBALLY_LICENSED_KEY, securityRoleScript.isOnlyForGloballyLicensed());
        final Script script = scriptStorageService.getScript(securityRoleScript.getScript().getCode());
        final ScriptDto scriptDto = scriptDtoFactory.create(script);
        final ScriptDto withoutScript = ScriptDtoFactory.createWithout();
        ScriptRoleCategory category = securityRoleScript.getCategory();
        if (role != null)
        {
            if (ScriptRoleCategory.ACCESS.equals(category))
            {
                properties.put(Constants.Role.SCRIPT_ACCESS_DTO_KEY, scriptDto);
                if (role.hasScriptedOwners())
                {
                    Script ownersScript =
                            scriptStorageService.getScript(role.getProperties().getProperty(SCRIPT_OWNERS_KEY));
                    properties.setProperty(Constants.Role.SCRIPT_OWNERS_DTO_KEY, scriptDtoFactory.create(ownersScript));
                }
                else
                {
                    properties.setProperty(Constants.Role.SCRIPT_OWNERS_DTO_KEY, withoutScript);
                }
            }
            else if (ScriptRoleCategory.OWNER.equals(category))
            {
                properties.put(Constants.Role.SCRIPT_OWNERS_DTO_KEY, scriptDto);
                if (role.hasScriptedAccess())
                {
                    Script accessScript =
                            scriptStorageService.getScript(role.getProperties().getProperty(SCRIPT_ACCESS_KEY));
                    properties.setProperty(Constants.Role.SCRIPT_ACCESS_DTO_KEY, scriptDtoFactory.create(accessScript));
                }
                else
                {
                    properties.setProperty(Constants.Role.SCRIPT_ACCESS_DTO_KEY, withoutScript);
                }
            }
        }
        else
        {
            if (ScriptRoleCategory.ACCESS.equals(category))
            {
                properties.put(Constants.Role.SCRIPT_ACCESS_DTO_KEY, scriptDto);
                properties.setProperty(Constants.Role.SCRIPT_OWNERS_DTO_KEY, withoutScript);
            }
            else if (ScriptRoleCategory.OWNER.equals(category))
            {
                properties.put(Constants.Role.SCRIPT_OWNERS_DTO_KEY, scriptDto);
                properties.setProperty(Constants.Role.SCRIPT_ACCESS_DTO_KEY, withoutScript);
            }
        }
        properties.setProperty(Constants.Role.SCRIPT_LIST_FILTER_DTO_KEY, withoutScript);
        properties.setProperty(Constants.Role.SCRIPT_FAST_LINK_RIGHTS_DTO_KEY, withoutScript);
        return properties;
    }

    /**
     * Создание или обновление скриптовой роли на основании информации из
     * прототипа скриптовой роли
     *
     * @param securityRoleScript прототип скриптовой роли для ее создания/обновления
     */
    private void createOrUpdateScriptRole(SecurityRoleScript securityRoleScript)
    {
        String roleCode = securityRoleScript.getCode();
        Script script = securityRoleScript.getScript();
        String scriptCode = script.getCode();
        scriptStorageService.saveScript(script);
        if (!MetainfoUtils.isValidRoleCode(roleCode, CurrentEmployeeContext.isVendor()))
        {
            LOG.error("Role code for script [" + scriptCode + "] is invalid. " +
                      "The \"Code\" must include at least one character, but not more than " +
                      ru.naumen.metainfo.shared.Constants.MAX_CODE_LENGTH + ", begin with the latin alphabet character "
                      +
                      "and consists of only of latin alphabet characters, numbers and dashes.");
            return;
        }
        RoleImpl role = (RoleImpl)securityService.getRole(roleCode);
        final MapProperties properties = getPropertiesForCreateOrUpdateRole(securityRoleScript, role);
        final List<LocalizedString> titles = securityRoleScript.getTitles();
        final String curLocaleRoleTitle = titles.stream()
                .filter(title -> title.getLang().equals(localeInfo.getCurrentLang()))
                .map(LocalizedString::getValue)
                .findAny()
                .orElse(null);
        final List<LocalizedString> otherLocaleTitles = titles.stream()
                .filter(title -> !title.getLang().equals(localeInfo.getCurrentLang()))
                .collect(Collectors.toList());
        final Action<? extends Result> action;
        if (role != null)
        {
            action = new EditSecurityRoleAction(roleCode, curLocaleRoleTitle, properties);
        }
        else
        {
            action = new AddSecurityRoleAction(curLocaleRoleTitle, roleCode, securityRoleScript.getClassFqn(),
                    Role.Type.SCRIPT, properties);

        }
        authorizeRunner.runAsSuperUser("script", () ->
        {
            try
            {
                dispatch.execute(action);
            }
            catch (Exception e)
            {
                LOG.warn("An error occured while create or update script role for script " + scriptCode, e);
            }
        });
        role = (RoleImpl)securityService.getRole(roleCode);
        if (role != null)
        {
            for (LocalizedString localizedTitle : otherLocaleTitles)
            {
                if (!role.getTitle(localizedTitle.getLang()).equals(localizedTitle.getValue()))
                {
                    role.addTitle(localizedTitle.getLang(), localizedTitle.getValue());
                }
            }
        }
    }

    @EventListener
    public void process(RolesSetRefreshedEvent rolesSetRefreshedEvent)
    {
        final Map<String, Set<SecurityRoleScript>> libraryPerScriptsCodes =
                rolesSetRefreshedEvent.getLibraryPerScripts();
        for (final Entry<String, Set<SecurityRoleScript>> entry : libraryPerScriptsCodes.entrySet())
        {
            final String libraryName = entry.getKey();
            final Set<SecurityRoleScript> securityRoleScriptSet = entry.getValue();
            final ScriptLibrary library = librariesService.getLibrary(libraryName);
            if (library != null)
            {
                final Set<String> scripts = library.getScripts();
                scripts.clear();
                scripts.addAll(securityRoleScriptSet.stream()
                        .map(roleScript -> roleScript.getScript().getCode())
                        .collect(Collectors.toSet()));
                scriptStorageService.deleteScripts(scripts);
                metainfoService.deleteScriptsByCode(scripts);
                librariesService.saveLibrary(library);
                scriptStorageService.resetScriptUsageCache();
                securityRoleScriptSet.forEach(this::createOrUpdateScriptRole);
            }
            else
            {
                LOG.warn("There's no library with name {} for scripts' codes addition.", libraryName);
            }
        }
    }
}
