package ru.naumen.core.server.script.api;

import jakarta.inject.Inject;

import org.apache.activemq.artemis.api.core.RoutingType;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import groovy.lang.Closure;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.jms.JMSManager;
import ru.naumen.core.server.jms.JMSUtil;
import ru.naumen.core.server.jms.services.UserJMSQueueService;
import ru.naumen.core.server.mq.Destination;
import ru.naumen.core.server.mq.MqDataSendingService;
import ru.naumen.core.server.mq.MqSettingsReloadService;
import ru.naumen.core.server.mq.MqIntegration;
import ru.naumen.core.server.util.MessageFacade;

/**
 * Реализация {@link IMqApi}
 *
 * <AUTHOR>
 * @since 23 мая 2017 г.
 */
@Component("mq")
public class MqApi implements IMqApi
{
    /**
     * Валидация кложуры на корректное количество параметров
     * @param messageEnhancer кложура
     */
    private static void validateMessageEnhancer(Closure<?> messageEnhancer)
    {
        if (messageEnhancer.getMaximumNumberOfParameters() != 1)
        {
            throw new IllegalArgumentException("messageEnhancer must take only one argument");
        }
    }

    private final MqDataSendingService mqDataSendingService;
    private final UserJMSQueueService userJMSQueueService;
    private final JMSManager jmsManager;
    private final MqSettingsReloadService mqManager;
    private final MessageFacade messages;

    @Inject
    public MqApi(
            @Lazy UserJMSQueueService userJMSQueueService,
            JMSManager jmsManager,
            MqSettingsReloadService mqManager,
            MqDataSendingService mqDataSendingService,
            MessageFacade messages)
    {
        this.userJMSQueueService = userJMSQueueService;
        this.jmsManager = jmsManager;
        this.mqManager = mqManager;
        this.mqDataSendingService = mqDataSendingService;
        this.messages = messages;
    }

    @Override
    public void changeConsumerSubscriptionType(String queueCode, String queueRoutingType) throws FxException
    {
        try
        {
            boolean pubSubDomain = RoutingType.MULTICAST.equals(RoutingType.valueOf(queueRoutingType.toUpperCase()));
            String containerId = JMSUtil.getJMSListenerID(queueCode);
            jmsManager.changeRoutingType(containerId, pubSubDomain);
        }
        catch (Exception ex)
        {
            throw new FxException(messages.getMessage("jmsQueue.setRoutingType.error"), true, ex);
        }
    }

    @Override
    public String setRoutingType(String jmsQueueCode, String routingType)
    {
        return userJMSQueueService.changeRoutingType(jmsQueueCode,
                RoutingType.valueOf(routingType.toUpperCase()));
    }

    @Override
    public boolean reload()
    {
        return mqManager.reload();
    }

    @Override
    public boolean reload(final Integration integration)
    {
        MqIntegration mqIntegration = fromIntegration(integration);
        return mqManager.reload(mqIntegration);
    }

    private static MqIntegration fromIntegration(Integration integration)
    {
        if (integration.equals(Integration.JMS))
        {
            return MqIntegration.JMS;
        }
        return MqIntegration.KAFKA;
    }

    @Override
    public void send(String jmsQueueCode, Object data)
    {
        userJMSQueueService.sendMessageToUserJMSQueue(jmsQueueCode, data);
    }

    @Override
    public void send(String jmsQueueCode, Closure<?> messageEnhancer)
    {
        validateMessageEnhancer(messageEnhancer);
        userJMSQueueService.sendMessageToUserJMSQueue(jmsQueueCode, messageEnhancer::call);
    }

    @Override
    public void send(String connectionCode, String destinationCode, Object data)
    {
        mqDataSendingService.send(new Destination(connectionCode, destinationCode), data);
    }

    @Override
    public void send(String connectionCode, String destinationCode, Closure<?> messageEnhancer)
    {
        validateMessageEnhancer(messageEnhancer);
        mqDataSendingService.send(new Destination(connectionCode, destinationCode), messageEnhancer::call);
    }
}
