package ru.naumen.core.server.upload;

import java.io.BufferedInputStream;
import java.io.BufferedOutputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;
import java.util.zip.GZIPOutputStream;

import org.apache.commons.io.IOUtils;

/**
 * Поток для чтения файлов с диска.
 * Есть следующие возможности
 *  - переоткрытие потока 
 *  - сжатие файла-источника
 *  - удалениe файла-источника
 *
 * <AUTHOR>
 *
 */
public class DiskFileInputStream extends InputStream
{
    private static final int BUFFER_SIZE = 1024 * 1024; //размер буфера копирования = 1MB
    private final Lock lock = new ReentrantLock();
    private InputStream stream;
    private String path;

    public DiskFileInputStream(String path) throws IOException
    {
        this.path = path;
        init();
    }

    @Override
    public void close() throws IOException
    {
        if (stream != null)
        {
            stream.close();
        }
    }

    public void compress() throws IOException
    {
        reopen();

        String zipPath = path + ".gz";

        try (OutputStream os = new BufferedOutputStream(new FileOutputStream(zipPath));
             GZIPOutputStream gzos = new GZIPOutputStream(os))
        {
            IOUtils.copyLarge(stream, gzos, new byte[BUFFER_SIZE]);
            gzos.finish();
        }
        deleteSource();
        path = zipPath;
        reopen();
    }

    public void deleteSource()
    {
        new java.io.File(path).delete();
    }

    public String getPath()
    {
        return path;
    }

    public long length()
    {
        return new File(path).length();
    }

    @Override
    public int read() throws IOException
    {
        return stream.read();
    }

    public void reopen() throws IOException
    {
        this.close();
        init();
    }

    protected void init() throws FileNotFoundException
    {
        stream = new BufferedInputStream(new FileInputStream(path));
    }

    @Override
    public void reset() throws IOException
    {
        lock.lock();
        try
        {
            stream.reset();
        }
        finally
        {
            lock.unlock();
        }
    }
}
