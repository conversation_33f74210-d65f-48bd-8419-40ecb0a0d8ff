package ru.naumen.core.server.script.api.injection;

import java.util.Objects;

import org.codehaus.groovy.ast.ASTNode;
import org.codehaus.groovy.ast.AnnotationNode;
import org.codehaus.groovy.ast.ClassHelper;
import org.codehaus.groovy.ast.ClassNode;
import org.codehaus.groovy.ast.MethodNode;
import org.codehaus.groovy.ast.Parameter;
import org.codehaus.groovy.ast.expr.ConstantExpression;
import org.codehaus.groovy.ast.stmt.BlockStatement;
import org.codehaus.groovy.ast.stmt.ReturnStatement;
import org.codehaus.groovy.control.SourceUnit;
import org.codehaus.groovy.transform.AbstractASTTransformation;
import org.codehaus.groovy.transform.GroovyASTTransformation;
import org.objectweb.asm.Opcodes;

import ru.naumen.core.server.script.modules.compile.NauGroovyClassLoader;

/**
 * Трансформация синтаксического дерева в груви-коде, добавляющая каждому внутреннему классу
 * <pre>implements {@link IHasApi}</pre>
 *
 * Данная трансформация вкупе с дефолтными трансформациями в груви позволяет использовать переменные контекста
 * (api, utils и т.д.) внутри груви-классов
 *
 * <AUTHOR>
 */
@GroovyASTTransformation
public class ModuleClassesApiInjectionASTTransformation extends AbstractASTTransformation
{
    private static final String GET_EMBEDDED_APPLICATION_CODE_METHOD_NAME =
            "IHasApi_getEmbeddedApplicationCode";

    @Override
    public void visit(ASTNode[] nodes, SourceUnit source)
    {
        if (nodes == null || nodes.length != 2)
        {
            return;
        }
        ASTNode node = nodes[1];
        if (!(node instanceof ClassNode classNode))
        {
            return;
        }
        if (InjectApiUtils.isNotScriptAndAnnotatedByInjectApi(classNode))
        {
            ClassNode interfaceNode = ClassHelper.make(IHasApi.class);
            classNode.addInterface(interfaceNode);
            overrideMethodGetEmbeddedApplicationCode(source, classNode,
                    interfaceNode);
        }
    }

    /**
     * Переопределить метод 'IHasApi_getEmbeddedApplicationCode' в интерфейсе {@link IHasApi},
     * который возвращает код встроенного приложения (код ВП определяется из контекста класс лоадера)
     */
    private void overrideMethodGetEmbeddedApplicationCode(SourceUnit source, ClassNode classNode,
            ClassNode interfaceNode)
    {
        if (source.getClassLoader() instanceof NauGroovyClassLoader nauGroovyClassLoader
            && nauGroovyClassLoader.getEmbeddedApplicationCode() != null)
        {
            MethodNode methodGetEmbeddedApplicationCode = interfaceNode.getDeclaredMethod(
                    GET_EMBEDDED_APPLICATION_CODE_METHOD_NAME, Parameter.EMPTY_ARRAY);
            Objects.requireNonNull(methodGetEmbeddedApplicationCode,
                    GET_EMBEDDED_APPLICATION_CODE_METHOD_NAME + "() method not found");
            String embeddedApplicationCode = nauGroovyClassLoader.getEmbeddedApplicationCode();
            BlockStatement blockStatement = new BlockStatement();
            blockStatement.addStatement(new ReturnStatement(
                    new ConstantExpression(embeddedApplicationCode)));
            MethodNode overrideMethod = new MethodNode(
                    GET_EMBEDDED_APPLICATION_CODE_METHOD_NAME,
                    Opcodes.ACC_PUBLIC | Opcodes.ACC_FINAL,
                    methodGetEmbeddedApplicationCode.getReturnType(),
                    methodGetEmbeddedApplicationCode.getParameters(),
                    methodGetEmbeddedApplicationCode.getExceptions(),
                    blockStatement);
            overrideMethod.addAnnotation(new AnnotationNode(new ClassNode(Override.class)));
            classNode.addMethod(overrideMethod);
        }
    }
}
