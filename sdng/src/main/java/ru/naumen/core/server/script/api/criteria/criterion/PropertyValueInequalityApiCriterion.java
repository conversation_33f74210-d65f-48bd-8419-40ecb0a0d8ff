package ru.naumen.core.server.script.api.criteria.criterion;

import java.util.Objects;

import ru.naumen.core.server.script.api.criteria.column.IApiCriteriaColumnInternal;
import ru.naumen.core.server.script.api.criteria.criterion.handler.PropertyValueInequalityApiCriterionHandler;

/**
 * Условие фильтрации, сравнивающее свойство с определённым значением
 *
 * <AUTHOR>
 * @since 06.03.2021
 * @see PropertyValueInequalityApiCriterionHandler
 */
public class PropertyValueInequalityApiCriterion extends AbstractPropertyInequalityApiCriterion
{

    /**
     * Значение для сравнения. Не null.
     */
    private final Object value;

    /**
     * Условие фильтрации, сравнивающее свойство с определённым значением
     *
     * @param property сравниваемое свойство или выражение
     * @param operation операция сравнения
     * @param value значение для сравнения
     * @throws NullPointerException если один из аргументов null
     */
    public PropertyValueInequalityApiCriterion(IApiCriteriaColumnInternal property,
            ApiCriterionComparisonOperation operation,
            Object value) throws NullPointerException
    {
        super(property, operation);
        this.value = Objects.requireNonNull(value);
    }

    /**
     * @return значение для сравнения
     */
    public Object getValue()
    {
        return value;
    }
}
