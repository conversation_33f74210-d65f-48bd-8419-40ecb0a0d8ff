package ru.naumen.core.server.script.spi.mapper;

import org.springframework.stereotype.Component;

import com.google.gson.JsonObject;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.bo.ToJsonTransformer;
import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.server.script.spi.ScriptDtOMapEntry;
import ru.naumen.core.shared.criteria.DtoProperties;

/**
 * Преобразует объекты ScriptDtOMapEntry в JSON по тем же правилам, что RestAPI преобразует объект в JSON
 *
 * <AUTHOR>
 * @since 03.02.2020
 */
@Component
public class ScriptDtOMapEntryMapper extends AbstractMapper<ScriptDtOMapEntry, JsonObject>
{
    private final ToJsonTransformer helper;

    @Inject
    public ScriptDtOMapEntryMapper(ToJsonTransformer helper)
    {
        super(ScriptDtOMapEntry.class, JsonObject.class);
        this.helper = helper;
    }

    @Override
    public void transform(ScriptDtOMapEntry from, JsonObject to, @Nullable DtoProperties properties)
    {
        boolean isHasProperties = properties != null && !properties.getProperties().isEmpty();
        if (!isHasProperties || properties.getProperties().contains(from.getKey().toString()))
        {
            to.add(from.getKey().toString(), helper.resolveValue(from.getValue(), true, true));
        }
    }
}
