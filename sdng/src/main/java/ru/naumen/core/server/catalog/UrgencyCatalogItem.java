package ru.naumen.core.server.catalog;

import jakarta.persistence.Cacheable;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.UrgencyCatalog;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Элемент справочника "Уровни срочности"
 *
 * <AUTHOR>
 */
// @formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Table(name = "tbl_urgency", uniqueConstraints = { 
        @UniqueConstraint(name = "tbl_urgency_code_key", columnNames = {"code"})}, indexes={@jakarta.persistence.Index(name = "idx_urgency_parent", columnList="parent")})
@Cacheable
@DiscriminatorValue("-")
@DiscriminatorFormula(FlexHelper.NULL_DISCRIMINATOR_FORMULA)
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(UrgencyCatalogItem.CLASS_ID)
@Metaclass(id = UrgencyCatalogItem.CLASS_ID,
        title = { @LStr(value = "Элемент справочника 'Уровни срочности'"),
                @LStr(lang = "en", value = "'Urgency levels' catalog item"),
                @LStr(lang = "de", value = "Katalogartikel 'Dringlichkeit'") },
        withCase = false)
@Catalog(code = UrgencyCatalog.CODE,
        title = { @LStr(value = "Уровни срочности"),
                @LStr(lang = "en", value = "Urgency levels"),
                @LStr(lang = "de", value = "Dringlichkeit") },
        description = { @LStr(value = "Содержит уровни срочности, показывающие насколько скоро инцидент начнет оказывать влияние на бизнес."),
                        @LStr(lang = "en", value = "Contains urgency levels, indicating how soon the incident will have an impact on the business."),
                        @LStr(lang = "de", value = "Enthält den Fristigkeitsgrad, der zeigt, wie bald der Einfluss des Incidentes auf das Geschäft eintritt.")})
//@formatter:on
public class UrgencyCatalogItem extends CatalogItem<UrgencyCatalogItem>
{
    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    public static final String CLASS_ID = UrgencyCatalog.ITEM_CLASS_ID;

    @Override
    public ClassFqn getMetaClass()
    {
        return UrgencyCatalog.ITEM_FQN;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }
}
