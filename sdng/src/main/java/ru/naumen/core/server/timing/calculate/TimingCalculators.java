/**
 */
package ru.naumen.core.server.timing.calculate;

import java.util.Date;
import java.util.TimeZone;

import ru.naumen.core.server.timing.Timesheet;
import ru.naumen.core.server.timing.calculate.scheme.SchemeTimingCalculator;
import ru.naumen.core.server.timing.calculate.scheme.TimingScheme;

/**
 * Компонент хранит ссылку на калькулятор сервисного времени, используемый в системе по умолчанию.
 *
 * @deprecated TimingScheme является частью устаревшего механизма SchemeTimingCalculator. 
 * На данный момент используется усовершенствованная версия калькулятора - {@link ServiceTimeCalculator}.
 */
@Deprecated
public class TimingCalculators
{
    /**
     * Получение калькулятора сервисного времени, используемого в системе, с предустановленной
     * временной зоной, используемой в системе по умолчанию
     * @param timesheet схема периодов обслуживания
     * @return калькулятор
     */
    public ITimingCalculator getCalculator(Timesheet timesheet)
    {
        return getCalculator(timesheet, TimeZone.getDefault());
    }

    /**
     * Получение калькулятора сервисного времени, используемого в системе
     * @param timesheet схема периодов обслуживания
     * @param start календарная дата
     * @param timeZone временная зона
     * @return калькулятор
     */
    public ITimingCalculator getCalculator(Timesheet timesheet, Date start, TimeZone timeZone)
    {
        ITimingCalculator calculator = new SchemeTimingCalculator(timeZone, TimingScheme.buildScheme(timesheet));
        calculator.setStartDate(start);
        return calculator;
    }

    /**
     * Получение калькулятора сервисного времени, используемого в системе
     * @param timesheet схема периодов обслуживания
     * @param timeZone временная зона
     * @return калькулятор
     */
    public ITimingCalculator getCalculator(Timesheet timesheet, TimeZone timeZone)
    {
        return new SchemeTimingCalculator(timeZone, TimingScheme.buildScheme(timesheet));
    }
}
