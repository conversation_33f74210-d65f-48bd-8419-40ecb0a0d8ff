package ru.naumen.core.server.script.api;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.common.collect.Sets;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.UUIDIdentifiableBase;
import ru.naumen.core.server.bo.team.Team;
import ru.naumen.core.server.bo.team.TeamDao;
import ru.naumen.core.server.cluster.readonlysettings.ReadOnlyClusterSettings;
import ru.naumen.core.server.cluster.readonlysettings.ReadOnlyClusterSettingsService;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.spi.IScriptDtObject;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * API для работы с командами
 *
 * <AUTHOR>
 *
 * @since ******* (14.11.2012)
 *
 */
@Component("team")
public class TeamApi implements ITeamApi
{
    private static Team getTeam(IUUIDIdentifiable object)
    {
        if (object instanceof Team team)
        {
            return team;
        }
        throw new FxException("TeamApi: Input object is " + ((IHasMetaInfo)object).getMetaClass()
                              + ", but Team expected", true);
    }

    private final TeamDao teamDao;
    private final ApiUtils apiUtils;
    private final MessageFacade messages;
    private final ScriptDtOHelper dtoHelper;
    private final ReadOnlyClusterSettingsService settingsService;

    @Inject
    public TeamApi(
            TeamDao teamDao,
            ApiUtils apiUtils, MessageFacade messages,
            ScriptDtOHelper dtoHelper,
            ReadOnlyClusterSettingsService settingsService)
    {
        this.teamDao = teamDao;
        this.apiUtils = apiUtils;
        this.messages = messages;
        this.dtoHelper = dtoHelper;
        this.settingsService = settingsService;
    }

    @Override
    public void addToEntitiesWithWritePermissions(String uuid)
    {
        IUUIDIdentifiable team = apiUtils.load(uuid);
        if (team == null)
        {
            throw new FxException("Object with uuid '" + uuid + "' didn't find.", true);
        }
        settingsService.addTeam(getTeam(team));
    }

    @Override
    public void addToEntitiesWithWritePermissions(IUUIDIdentifiable object)
    {
        IUUIDIdentifiable team = apiUtils.getObject(object);
        if (team == null)
        {
            throw new FxException("Team is null!", true);
        }
        settingsService.addTeam(getTeam(team));
    }

    @Override
    public void deleteFromEntitiesWithWritePermissions(String uuid)
    {
        settingsService.removeTeam(uuid);
    }

    @Override
    public void deleteFromEntitiesWithWritePermissions(IUUIDIdentifiable object)
    {
        IUUIDIdentifiable team = apiUtils.getObject(object);
        if (team == null)
        {
            throw new FxException("Team is null!", true);
        }
        settingsService.removeTeam(getTeam(team).getUUID());
    }

    @Override
    public Collection<IScriptDtObject> listEntitiesWithWritePermissions()
    {
        ReadOnlyClusterSettings settings = settingsService.getSettings();
        if (settings == null)
        {
            return Collections.emptyList();
        }
        return dtoHelper.wrapLazy(settings.getTeamWriters().stream().map(UUIDIdentifiableBase::getUUID).toList());
    }

    @Override
    public Collection<IScriptDtObject> getTeamMembers(Collection<String> teamUUIDs, boolean removed)
    {
        return dtoHelper.wrapLazy(getTeamMemberUUIDs(teamUUIDs, removed));
    }

    private Collection<String> getTeamMemberUUIDs(Collection<String> teamUUIDs, boolean removed)
    {
        Collection<String> exists = teamDao.getExists(teamUUIDs);
        if (exists.size() != teamUUIDs.size())
        {
            Collection<String> notExists = Sets.newHashSet(teamUUIDs);
            notExists.removeAll(exists);
            throw new FxException(messages.getMessage("TeamApi.UUIDsNotExists", notExists.toString()), true);
        }
        Set<String> employees = new HashSet<>();
        for (String teamUUID : teamUUIDs)
        {
            employees.addAll(teamDao.getTeamMembers(teamUUID, removed));
        }
        return employees;
    }
}