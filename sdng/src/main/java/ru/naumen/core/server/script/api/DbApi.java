package ru.naumen.core.server.script.api;

import java.util.Collection;
import java.util.concurrent.atomic.AtomicBoolean;

import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.db.DbLockService;
import ru.naumen.core.server.hibernate.dialect.json.JsonFiledDataType;
import ru.naumen.core.server.hibernate.dialect.json.JsonIndexingService;
import ru.naumen.core.server.script.GroovyUsage;
import ru.naumen.core.server.script.api.criteria.ApiCriteria;
import ru.naumen.core.server.script.api.criteria.ApiCriteriaUtils;
import ru.naumen.core.server.script.api.criteria.IApiCriteria;
import ru.naumen.core.server.script.api.db.DbApiHelper;
import ru.naumen.core.server.script.api.db.DbQuery;
import ru.naumen.core.server.script.api.db.DbQueryForEvent;
import ru.naumen.core.server.script.api.db.DbSql;
import ru.naumen.core.server.script.spi.IScriptDtObject;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * API для работы с БД.
 * @see IDbApi
 *
 * <AUTHOR>
 * @since ********
 */
@Component("db")
public class DbApi implements IDbApi
{
    private static final String DELETE_FROM_COMMENT_PATTERN = """
            DELETE FROM comment \
            WHERE creationDate < :commentCreationDate AND source IN (\
                 SELECT 'serviceCall$' || cast(id as string) \
                 FROM serviceCall WHERE%s creationDate < :scCreationDate\
            )""";

    private final SessionFactory sessionFactory;
    private final MetainfoService metainfoService;
    private final ScriptDtOHelper wrapper;
    private final ApiCriteriaUtils apiCriteriaUtils;
    private final DbLockService dbLockService;
    private final JsonIndexingService jsonIndexingService;
    private final DbApiHelper dbHelper;
    private final DbSql dbSql;

    private final AtomicBoolean autocachable;

    @Inject
    public DbApi(final SessionFactory sessionFactory,
            final MetainfoService metainfoService,
            final ScriptDtOHelper wrapper,
            final ApiCriteriaUtils apiCriteriaUtils,
            final DbLockService dbLockService,
            final JsonIndexingService jsonIndexingService,
            final DbApiHelper dbHelper,
            final DbSql dbSql,
            @Value("${ru.naumen.api.db.query.cachable}") final boolean cachable)
    {
        this.sessionFactory = sessionFactory;
        this.wrapper = wrapper;
        this.metainfoService = metainfoService;
        this.apiCriteriaUtils = apiCriteriaUtils;
        this.dbLockService = dbLockService;
        this.jsonIndexingService = jsonIndexingService;
        this.dbHelper = dbHelper;
        this.dbSql = dbSql;

        this.autocachable = new AtomicBoolean(cachable);
    }

    @Override
    @SuppressWarnings("deprecation")
    public IApiCriteria createCriteria()
    {
        return new ApiCriteria(apiCriteriaUtils);
    }

    @Override
    public void createJsonIndex(String attributeFqn, String jsonQuery, String dataType)
    {
        Attribute attribute = metainfoService.getAttribute(AttributeFqn.parse(attributeFqn));
        jsonIndexingService.createJsonIndex(attribute, jsonQuery, JsonFiledDataType.fromString(dataType));
    }

    @Override
    public void dropJsonIndex(String attributeFqn, String jsonQuery, String dataType)
    {
        Attribute attribute = metainfoService.getAttribute(AttributeFqn.parse(attributeFqn));
        jsonIndexingService.dropJsonIndex(attribute, jsonQuery, JsonFiledDataType.fromString(dataType));
    }

    @Override
    public int deleteComments(String dateTimeFormatter, String scCreationDate, String commentCreationDate,
            Collection<String> scStates)
    {
        if (StringUtilities.isEmptyTrim(dateTimeFormatter))
        {
            throw new FxException("Date format can't be null", true);
        }
        if (StringUtilities.isEmptyTrim(scCreationDate) || StringUtilities.isEmptyTrim(commentCreationDate))
        {
            throw new FxException("Date can't be null", true);
        }

        StringBuilder querySB = new StringBuilder();
        if (CollectionUtils.isNotEmpty(scStates))
        {
            querySB.append(" state IN :states AND");
        }
        IQuery query = query(DELETE_FROM_COMMENT_PATTERN.formatted(querySB.toString()));

        if (CollectionUtils.isNotEmpty(scStates))
        {
            query.set("states", scStates);
        }
        DateTimeFormatter formatter = DateTimeFormat.forPattern(dateTimeFormatter);
        query.set("scCreationDate", formatter.parseDateTime(scCreationDate).toDate());
        query.set("commentCreationDate", formatter.parseDateTime(commentCreationDate).toDate());

        return query.executeUpdate();
    }

    @Override
    public String getDBType()
    {
        return dbHelper.getDbType();
    }

    @Override
    public String getDBName()
    {
        return dbHelper.getDbName();
    }

    @Override
    @SuppressWarnings({ "SqlSourceToSinkFlow", "deprecation" }) // часть API
    public IQuery query(String query)
    {
        return new DbQuery(wrapper, sessionFactory.getCurrentSession().createQuery(query))
                .setCachable(autocachable.get());
    }

    @Override
    public IQuery query(IApiCriteria criteria)
    {
        Query<?> query = ((ApiCriteria)criteria).getCriteria().createQuery(sessionFactory.getCurrentSession());
        return new DbQuery(wrapper, query).setCachable(autocachable.get());
    }

    @Override
    @SuppressWarnings({ "SqlSourceToSinkFlow", "deprecation" }) // часть API
    public IQuery queryForEvent(String query)
    {
        return new DbQueryForEvent(wrapper, dbHelper.getSessionFactoryForEvent().getCurrentSession().createQuery(query))
                .setCachable(autocachable.get());
    }

    @Override
    public IQuery queryForEvent(IApiCriteria criteria)
    {
        Query<?> query = ((ApiCriteria)criteria).getCriteria()
                .createQuery(dbHelper.getSessionFactoryForEvent().getCurrentSession());
        return new DbQueryForEvent(wrapper, query).setCachable(autocachable.get());
    }

    @GroovyUsage
    @SuppressWarnings("unused")
    public boolean isQueryCachable()
    {
        return autocachable.get();
    }

    @GroovyUsage
    @SuppressWarnings("unused")
    public void setQueryCachable(boolean value)
    {
        autocachable.set(value);
    }

    @Override
    public void removedFromSystemFlushWorkaround()
    {
        sessionFactory.getCurrentSession().flush();
    }

    @Override
    public void clear()
    {
        sessionFactory.getCurrentSession().clear();
    }

    @Override
    public void detach(IScriptDtObject dto)
    {
        dbHelper.detach(dto);
    }

    @Override
    public void lock(String uuid)
    {
        dbLockService.lock(uuid);
    }

    @Override
    public boolean isReadOnly()
    {
        return AppContext.isReadOnly();
    }

    @Override
    public IDbSql getSql()
    {
        return dbSql;
    }
}
