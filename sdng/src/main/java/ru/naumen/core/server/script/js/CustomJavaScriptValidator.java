package ru.naumen.core.server.script.js;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

/**
 * Валидатор файлов кастомизации.
 * На текущий момент, используется только для проверки подписи.
 *
 * <AUTHOR>
 * @since Nov 29, 2017
 */
@Component
public class CustomJavaScriptValidator
{
    @Inject
    private ScriptSignatureVerifier signatureVerifier;

    public void validate(byte[] content)
    {
        signatureVerifier.verify(content);
    }
}
