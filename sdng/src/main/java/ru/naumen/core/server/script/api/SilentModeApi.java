package ru.naumen.core.server.script.api;

import static ru.naumen.core.server.cluster.external.Constants.CLUSTER;
import static ru.naumen.core.server.script.spi.ScriptServiceImpl.getClusterEventAttribute;

import java.util.Set;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.config.SilentModeConfiguration;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.cluster.external.ClusterInfoService;
import ru.naumen.core.server.cluster.external.ClusterServiceManager;
import ru.naumen.core.server.cluster.external.events.SetSuitableIPsEvent;

/**
 *
 * <AUTHOR>
 * @since 31 марта 2016 г.
 */
@Component("silentMode")
public class SilentModeApi implements ISilentModeApi
{
    private static final Logger LOG = LoggerFactory.getLogger(SilentModeApi.class);
    private final SilentModeConfiguration configuration;
    private final ClusterInfoService clusterInfoService;
    private final ClusterServiceManager serviceManager;

    @Inject
    public SilentModeApi(
            SilentModeConfiguration configuration,
            ClusterInfoService clusterInfoService,
            ClusterServiceManager serviceManager)
    {
        this.configuration = configuration;
        this.clusterInfoService = clusterInfoService;
        this.serviceManager = serviceManager;
    }

    @Override
    public Set<String> getSuitableIPs()
    {
        return configuration.getSuitableIps();
    }

    @Override
    public boolean isSilent()
    {
        return configuration.isSilent();
    }

    @Override
    public void setNotSilent()
    {
        setSilent(false);

    }

    @Override
    public void setSilent()
    {
        setSilent(true);
    }

    @Override
    public void setSilent(String... ips)
    {
        setSilent(true);
        setSuitableIPs(ips);
    }

    /**
     * После вызова данного api-метода будет установлен новый список адресов-исключений на текущей ноде, после чего,
     * в случае возможности, производится синхронизацию списка в кластере
     */
    @Override
    public void setSuitableIPs(@Nullable String... ips)
    {
        //Если приложение запущено на тестовом стенде, то адреса-исключения не устанавливаем
        if (AppContext.isTestStand())
        {
            LOG.atInfo().setMessage("Installation SuitableIPs:{}, - unsupported on TestStand")
                    .addArgument(ips).log();
            return;
        }
        if (ips == null)
        {
            ips = new String[0];
        }
        configuration.setSuitableIPs(ips);
        /*
        Если возможно, производим синхронизацию списка в кластере
         */
        if (isNeedSyncSuitableIPsInCluster())
        {
            SetSuitableIPsEvent event = new SetSuitableIPsEvent(getSuitableIPs());
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Send list SuitableIPs from {} to the Cluster: {}",
                        serviceManager.getNodeAddress(), serviceManager.getReachableNodes());
            }
            serviceManager.sendEvent(event);
        }
    }

    /**
     * Проверяем необходимость запуска синхронизации списка в кластере, для метода setSuitableIPs():
     * если скрипт уже был разослан в кластере, после чего был вызван api-метод setSuitableIPs(), то для потока
     * обработки кластерных событий должна быть установлена ThreadLocal переменная в методе bindToCluster() класса
     * {@link ru.naumen.core.server.script.spi.ScriptServiceImpl}. Следовательно повторная рассылка не нужна.
     *
     * Иначе, если нода работает в кластере, производим рассылку.
     */
    private boolean isNeedSyncSuitableIPsInCluster()
    {
        String clusterEvent = getClusterEventAttribute().get();
        if (clusterEvent != null && clusterEvent.equals(CLUSTER))
        {
            return false;
        }
        return clusterInfoService.isNormalClusterMode();
    }

    @Override
    public void useLegacy(boolean use)
    {
        configuration.setUseLegacy(use);
    }

    private void setSilent(boolean silent) //NOPMD
    {
        configuration.setEnabled(silent);
    }
}
