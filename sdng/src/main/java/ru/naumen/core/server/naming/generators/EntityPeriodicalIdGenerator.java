package ru.naumen.core.server.naming.generators;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.naming.extractors.PeriodExtractor;
import ru.naumen.core.server.naming.extractors.SequenceExtractor;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.utils.UuidHelper;

/**
 * Абстрактная реализация {@link PeriodicalIDGenerator} для сущностей из хранилища метаинформации,
 * то есть объектов, которые не хранятся в базе данных. 
 * Содержит логику обновления значений последовательностей для приведения их в соответствие со значениями пришедшими
 * из другой системы
 *
 * @param <T> - тип сущности
 *
 * <AUTHOR>
 * @since 2 июня 2016 г.
 */
public abstract class EntityPeriodicalIdGenerator<T extends HasCode> extends PeriodicalIDGenerator
{
    /**
     * Класс для обновления значения последовательности, используемой данным id генератором
     * Значение последовательности обновляется, если оно меньше значения идентификатора переданного объекта obj 
     *
     * @param <T> тип объектов, чей счётчик обновляется. 
     *
     * <AUTHOR>
     * @since 24.06.2015
     */
    public class SequenceUpdater
    {
        private final Lock lock = new ReentrantLock();

        public void execute(HasCode obj)
        {
            TransactionRunner.run(TransactionType.NEW, () ->
            {
                lock.lock();
                try
                {
                    int objCodeValue = Integer.parseInt(UuidHelper.toIdStr(obj.getCode()));
                    int sequenceValue = sequenceProvider
                            .getSequenceValue(getSequenceExtractor().getSequenceId(obj));
                    if (objCodeValue > sequenceValue)
                    {
                        restart(objCodeValue, obj);
                    }
                }
                finally
                {
                    lock.unlock();
                }
            });
        }

        public void execute(List<HasCode> objs)
        {
            if (!objs.isEmpty())
            {
                //сортируем объекты по убыванию их идентификатора
                objs.sort((o1, o2) ->
                {
                    int io1 = Integer.parseInt(UuidHelper.toIdStr(o1.getCode()));
                    int io2 = Integer.parseInt(UuidHelper.toIdStr(o2.getCode()));
                    return Integer.compare(io2, io1);
                });

                //проверяем необходимость обновления последовательности только один раз: для объекта с максимальным
                // идентификатором
                execute(objs.iterator().next());
            }
        }
    }

    public EntityPeriodicalIdGenerator(SequenceExtractor sequenceExtractor, PeriodExtractor<T> periodExtractor)
    {
        super(sequenceExtractor, periodExtractor);
    }

    /**
     * @return средство обновления (актуализации) последовательностей для объектов.
     */
    public SequenceUpdater getSequenceUpdater()
    {
        return new SequenceUpdater();
    }

    /**
     * @param obj объект, для которого нужно построить UUID.
     * @param prefix специфичный для объекта префикс UUID.
     * @return UUID для переданного объекта с учётом специфичного префикса.
     */
    public String getUuid(Object obj, String prefix)
    {
        String id = Long.toString(getId(obj));
        return UuidHelper.toUuid(id, prefix);
    }

    /**
     * @return список всех сущностей в системе
     */
    protected abstract List<T> getAllEntities();

    @Override
    protected void updateSequenceTable()
    {
        getSequenceUpdater().execute(new ArrayList<>(getAllEntities()));
    }
}
