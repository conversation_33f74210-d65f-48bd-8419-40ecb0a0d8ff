package ru.naumen.core.server.script.storage.modification.edit;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import java.util.HashMap;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.customforms.CustomFormImpl;
import ru.naumen.core.server.customforms.CustomFormsService;
import ru.naumen.core.server.customforms.FormParameter;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants.Scripts;
import ru.naumen.core.shared.script.places.FormParameterScriptCategories;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;

/**
 * Стратегия обновления мест использования при изменении тела скрипта для категории параметры ДПС
 * Происходит вычисление атрибутов используемых в скриптах и запись в места использования.
 * Логика строится из расчета, что скрипт уже обновлен, выполняются сопутствующие
 * изменения в местах их использования.
 * <AUTHOR>
 * @since 23.04.2020
 */
@Lazy
@Component
public class FormParameterScriptBodyUpdateStrategy implements ScriptBodyUpdateStrategy
{
    private static final Set<ScriptCategory> WITH_DEPENDENCIES = Set.of(
            FormParameterScriptCategories.PARAM_FILTRATION,
            FormParameterScriptCategories.PARAM_COMPUTABLE_ON_FORM,
            FormParameterScriptCategories.PARAM_DATE_TIME_RESTRICTION);

    private final MetainfoModification metainfoModification;
    private final CustomFormsService customFormsService;
    private final ScriptStorageService scriptStorageService;
    private final ScriptService scriptService;

    @Lazy
    @Inject
    public FormParameterScriptBodyUpdateStrategy(MetainfoModification metainfoModification,
            ScriptStorageService scriptStorageService, ScriptService scriptService,
            CustomFormsService customFormsService)
    {
        this.metainfoModification = metainfoModification;
        this.scriptStorageService = scriptStorageService;
        this.scriptService = scriptService;
        this.customFormsService = customFormsService;
    }

    @Override
    public void updateScriptUsagePoint(ScriptUsagePoint usagePoint)
    {
        FormParameterScriptCategories category = (FormParameterScriptCategories)usagePoint.getCategory();
        if (WITH_DEPENDENCIES.contains(category))
        {
            updateAttrsUsedInAttributesScriptCustomForm(usagePoint, category);
        }
    }

    private void updateAttrsUsedInAttributesScriptCustomForm(ScriptUsagePoint usagePoint,
            FormParameterScriptCategories category)
    {
        metainfoModification.modify(MetainfoRegion.EVENT_ACTIONS);
        final String formCode = usagePoint.getRelatedMetaClassFqns().iterator().next().getCode();
        final CustomFormImpl form = customFormsService.getForm(formCode).clone();
        final String attrCode = AttributeFqn.parse(usagePoint.getLocation()).getCode();
        FormParameter formParameter = form.getAttributes().stream()
                .filter(param -> Objects.equals(attrCode, param.getCode()))
                .findFirst().orElse(null);
        if (formParameter == null)
        {
            return;
        }

        switch (category) //NOSONAR NOPMD
        {
            case PARAM_FILTRATION ->
            {
                Script filtrationScript = scriptStorageService.getScript(formParameter.getScriptForFiltration());
                formParameter.setAttrsUsedInScript(getAttrsUsedInScriptWithAttrCode(filtrationScript, attrCode));
            }
            case PARAM_COMPUTABLE_ON_FORM ->
            {
                Script computationScript = scriptStorageService.getScript(formParameter.getComputableOnFormScript());
                formParameter.setAttrsUsedInEditValueScript(
                        getAttrsUsedInScriptWithAttrCode(computationScript, attrCode));
            }
            case PARAM_DATE_TIME_RESTRICTION ->
            {
                Script restrictionScript = scriptStorageService.getScript(formParameter.getDateTimeRestrictionScript());
                formParameter.setAttrsForDateTimeRestrictionScript(
                        getAttrsUsedInScriptWithAttrCode(restrictionScript, attrCode));
            }
        }

        customFormsService.saveForm(form);
    }

    private List<String> getAttrsUsedInScriptWithAttrCode(@Nullable Script script, String attrCode)
    {
        if (script == null)
        {
            return Collections.emptyList();
        }
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.FORM, null);
        bindings.put(Scripts.ATTR_CODE, attrCode);
        Collection<String> result = scriptService.execute(script, bindings);
        return result == null ? Collections.emptyList() : new ArrayList<>(result);
    }
}
