package ru.naumen.core.server.script.api.eventaction;

import static ru.naumen.metainfo.shared.eventaction.ActionType.NotificationEventAction;
import static ru.naumen.metainfo.shared.eventaction.ActionType.PushEventAction;
import static ru.naumen.metainfo.shared.eventaction.ActionType.PushMobileEventAction;
import static ru.naumen.metainfo.shared.eventaction.ActionType.PushPortalEventAction;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.eventaction.EventAction;

/**
 * API для работы с {@link EventAction}
 * <AUTHOR>
 * @since 10.03.2021
 */
@Component("eventAction")
public class EventActionApi implements IEventActionApi
{
    private final EventActionService eventActionService;
    private final MessageFacade messages;

    @Inject
    public EventActionApi(EventActionService eventActionService)
    {
        this.eventActionService = eventActionService;
        messages = SpringContext.getInstance().getBean(MessageFacade.class);
    }

    @Override
    public List<IEvenActionWrapper> getEventActions()
    {
        return eventActionService.getEventActions().stream()
                .map(EventActionApi::wrap)
                .collect(Collectors.toList());
    }

    @Override
    public List<INotificationEventActionWrapper> getNotificationEventActions()
    {
        return eventActionService.getEventActions(NotificationEventAction).stream()
                .map(NotificationEventActionWrapper::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<IPushWebEventActionWrapper> getPushWebEventActions()
    {
        return eventActionService.getEventActions(PushEventAction).stream()
                .map(PushWebEventActionWrapper::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<IPushMobileEventActionWrapper> getPushMobileEventActions()
    {
        return eventActionService.getEventActions(PushMobileEventAction).stream()
                .map(PushMobileEventActionWrapper::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<IPushPortalEventActionWrapper> getPushPortalEventActions()
    {
        return eventActionService.getEventActions(PushPortalEventAction).stream()
                .map(PushPortalEventActionWrapper::new)
                .collect(Collectors.toList());
    }

    @Override
    public IEvenActionWrapper getEventAction(String code)
    {
        code = code.startsWith("eventAction$") ? code : "eventAction$" + code;
        EventAction eventAction = eventActionService.getEventAction(code);

        if (eventAction == null)
        {
            throw new FxException(messages.getMessage("EventActionApi.absentEventAction", code));
        }

        return wrap(eventAction);
    }

    /**
     * Поместить ДПС в обёртку в соответствии с его типом
     */
    private static EventActionWrapper wrap(EventAction eventAction)
    {
        switch (eventAction.getAction().getActionType())
        {
            case NotificationEventAction:
                return new NotificationEventActionWrapper(eventAction);
            case PushEventAction:
                return new PushWebEventActionWrapper(eventAction);
            case PushMobileEventAction:
                return new PushMobileEventActionWrapper(eventAction);
            case PushPortalEventAction:
                return new PushPortalEventActionWrapper(eventAction);
            case ChangeTrackingEventAction:
                return new ChangeTrackingEventActionWrapper(eventAction);
            default:
                return new EventActionWrapper(eventAction);
        }
    }
}
