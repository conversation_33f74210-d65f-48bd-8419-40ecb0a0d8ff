package ru.naumen.core.server.script.libraries.validation;

import java.util.Collections;
import java.util.List;

import ru.naumen.commons.shared.FxException;

/**
 * Ошибка валидации, создается со списком ошибок или с единственной ошибкой
 * Ошибки соединяются через <code>\n</code> при {@link #getMessage()}
 * <AUTHOR>
 * @since 26.05.2020
 */
public class ValidationException extends FxException
{
    private final List<String> validationMessages;

    public ValidationException(Throwable cause, String validationMessage)
    {
        super("", cause);
        this.validationMessages = Collections.singletonList(validationMessage);
    }

    public ValidationException(List<String> validationMessages)
    {
        this.validationMessages = validationMessages;
    }

    public ValidationException(String validationMessage)
    {
        this.validationMessages = Collections.singletonList(validationMessage);
    }

    @Override
    public String getMessage()
    {
        return String.join("\n", validationMessages);
    }
}
