package ru.naumen.core.server.script.api;

import static ru.naumen.advimport.server.engine.datasource.LdapDataSourceProviderBean.INITCTX;
import static ru.naumen.advimport.server.engine.datasource.LdapDataSourceProviderBean.LDAP_READ_TIMEOUT;
import static ru.naumen.advimport.server.engine.datasource.LdapDataSourceProviderBean.TIMEOUT_1M;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Hashtable;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import javax.naming.Context;
import javax.naming.NamingEnumeration;
import javax.naming.NamingException;
import javax.naming.directory.Attribute;
import javax.naming.directory.BasicAttribute;
import javax.naming.directory.BasicAttributes;
import javax.naming.directory.DirContext;
import javax.naming.directory.InitialDirContext;
import javax.naming.directory.ModificationItem;
import javax.naming.directory.SearchControls;
import javax.naming.directory.SearchResult;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import java.util.HashMap;

import ru.naumen.advimport.server.engine.datasource.LdapDataSourceProviderBean;
import ru.naumen.core.server.ldap.LdapAuthenticationService;
import ru.naumen.core.server.ldap.LdapDummySSLSocketFactory;
import ru.naumen.core.server.ldap.LdapUnitedSSLSocketFactory;

/**
 * API для работы с LDAP
 *
 * <AUTHOR>
 *
 * @since *******
 */
@Component("ldap")
public class LdapApi implements ILdapApi
{
    public static class Connection implements IConnection
    {
        private final String url;
        private final String user;
        private final String passwd;
        private boolean skipCertVerification;

        protected Connection(String url, String user, String passwd)
        {
            this.url = url;
            this.user = user;
            this.passwd = passwd;
        }

        @Override
        public void create(String dn, Collection<String> cls, Map<String, Object> attributes) throws NamingException
        {
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Creating " + dn + " " + attributes);
            }

            Attribute objectClass = new BasicAttribute("objectClass");
            for (String cl : cls)
            {
                objectClass.add(cl);
            }

            BasicAttributes values = toAttributes(attributes);
            values.put(objectClass);

            DirContext ctx = createContext();
            try
            {
                ctx.bind(dn, ctx, values);
            }
            finally
            {
                ctx.close();
            }
        }

        @Override
        public void delete(String dn) throws NamingException
        {
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Deleting " + dn);
            }

            DirContext ctx = createContext();
            try
            {
                ctx.destroySubcontext(dn);
            }
            finally
            {
                ctx.close();
            }
        }

        @Override
        public void edit(String dn, Map<String, Object> attributes) throws NamingException
        {
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Editing " + dn + " " + attributes);
            }

            List<ModificationItem> modification = new ArrayList<>();
            for (Entry<String, Object> attr : attributes.entrySet())
            {
                if (null == attr.getValue())
                {
                    modification
                            .add(new ModificationItem(DirContext.REMOVE_ATTRIBUTE, new BasicAttribute(attr.getKey())));
                }
                else
                {
                    modification.add(new ModificationItem(DirContext.REPLACE_ATTRIBUTE,
                            getAttribute(attr.getKey(), attr.getValue())));
                }
            }

            DirContext ctx = createContext();
            try
            {
                ctx.modifyAttributes(dn, modification.toArray(new ModificationItem[modification.size()]));
            }
            finally
            {
                ctx.close();
            }
        }

        @Override
        public void rename(String oldDn, String newDn) throws NamingException
        {
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Rename '" + oldDn + "' to '" + newDn + "'");
            }

            DirContext ctx = createContext();
            try
            {
                ctx.rename(oldDn, newDn);
            }
            finally
            {
                ctx.close();
            }
        }

        @Override
        public Collection<Map<String, Object>> search(String baseDn, Map<String, Object> search) throws NamingException
        {
            return search(baseDn, search, null);
        }

        @Override
        public Collection<Map<String, Object>> search(String baseDn, Map<String, Object> search,
                @Nullable Iterable<String> attributesToReturn) throws NamingException
        {
            return search(baseDn, search, attributesToReturn, ATTR_VALUE_DELIMITER);
        }

        @Override
        public Collection<Map<String, Object>> search(String baseDn, Map<String, Object> search,
                @Nullable Iterable<String> attributesToReturn, String attrValueDelimiter) throws NamingException
        {
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Searching " + baseDn + " " + search);
            }

            DirContext ctx = createContext();
            try
            {
                String[] attrsToReturn = toArray(attributesToReturn);
                Collection<Map<String, Object>> result = toDto(ctx.search(baseDn, toAttributes(search), attrsToReturn),
                        attrValueDelimiter);
                if (LOG.isDebugEnabled())
                {
                    LOG.debug("Search result: " + result);
                }
                return result;
            }
            finally
            {
                ctx.close();
            }
        }

        @Override
        public Collection<Map<String, Object>> search(String baseDn, String search) throws NamingException
        {
            return search(baseDn, search, ATTR_VALUE_DELIMITER);

        }

        @Override
        public Collection<Map<String, Object>> search(String baseDn, String search, String attrValueDelimiter)
                throws NamingException
        {
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Searching " + baseDn + " " + search);
            }

            DirContext ctx = createContext();
            try
            {
                Collection<Map<String, Object>> result = toDto(ctx.search(baseDn, search, SEARCH_CONTROLS),
                        attrValueDelimiter);
                if (LOG.isDebugEnabled())
                {
                    LOG.debug("Search result: " + result);
                }
                return result;
            }
            finally
            {
                ctx.close();
            }
        }

        @Override
        public void setSkipCertVerification(boolean skipCertVerification)
        {
            this.skipCertVerification = skipCertVerification;
        }

        private DirContext createContext() throws NamingException
        {
            Hashtable<String, String> env = new Hashtable<>(); //NOPMD

            env.put(Context.INITIAL_CONTEXT_FACTORY, INITCTX);
            env.put(LDAP_READ_TIMEOUT, TIMEOUT_1M);
            env.put(Context.PROVIDER_URL, url);
            env.put(Context.SECURITY_AUTHENTICATION, "simple"); // "none", "simple", "strong"
            env.put(Context.SECURITY_PRINCIPAL, user);
            env.put(Context.SECURITY_CREDENTIALS, passwd);
            if (url.startsWith("ldaps"))
            {
                String factory = skipCertVerification
                        ? LdapDummySSLSocketFactory.class.getName()
                        : LdapUnitedSSLSocketFactory.class.getName();
                env.put(LdapDataSourceProviderBean.LDAP_SOCKET_FACTORY, factory);
            }
            return new InitialDirContext(env);
        }

        @SuppressWarnings("rawtypes")
        private Attribute getAttribute(String name, Object value)
        {
            BasicAttribute attribute = new BasicAttribute(name);
            if (null == value)
            {
                return attribute;
            }
            if (value instanceof Iterable)
            {
                for (Object v : (Iterable)value)
                {
                    attribute.add(v);
                }
            }
            else
            {
                attribute.add(value);
            }
            return attribute;
        }

        private String[] toArray(@Nullable Iterable<String> attributesToReturn)
        {
            return null == attributesToReturn ? null : Lists.newArrayList(attributesToReturn).toArray(new String[0]);
        }

        private BasicAttributes toAttributes(Map<String, Object> attributes)
        {
            BasicAttributes values = new BasicAttributes(true);
            for (Entry<String, Object> e : attributes.entrySet())
            {
                values.put(getAttribute(e.getKey(), e.getValue()));
            }
            return values;
        }

        private Collection<Map<String, Object>> toDto(NamingEnumeration<SearchResult> values, String attrValueDelimiter)
                throws NamingException
        {
            List<Map<String, Object>> result = new ArrayList<>();
            while (values.hasMore())
            {
                SearchResult item = values.next();
                NamingEnumeration<? extends Attribute> attributes = item.getAttributes().getAll();

                Map<String, Object> converted = new HashMap<>();
                while (attributes.hasMore())
                {
                    Attribute attribute = attributes.next();

                    NamingEnumeration<?> attrValues = attribute.getAll();
                    StringBuilder value = new StringBuilder();
                    value.append(attrValues.next());
                    while (attrValues.hasMore())
                    {
                        value.append(attrValueDelimiter).append(attrValues.next());
                    }
                    converted.put(attribute.getID(), value.toString());
                }
                result.add(converted);
            }
            return result;
        }
    }

    private static final String ATTR_VALUE_DELIMITER = "; ";

    private static final SearchControls SEARCH_CONTROLS = new SearchControls(SearchControls.SUBTREE_SCOPE, 0, 0, null,
            false, false);

    private static final Logger LOG = LoggerFactory.getLogger(LdapApi.class);

    private final LdapAuthenticationService service;

    @Inject
    public LdapApi(LdapAuthenticationService service)
    {
        this.service = service;
    }

    @Override
    public IConnection open(String url, String user, String passwd) throws NamingException
    {
        Connection connection = new Connection(url, user, passwd);
        return connection;
    }

    @Override
    public void reload()
    {
        service.reload();
    }
}
