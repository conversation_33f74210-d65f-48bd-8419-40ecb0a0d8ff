package ru.naumen.core.server.script.api.criteria.column;

import static ru.naumen.core.shared.Constants.AbstractBO.METACASE_ID;
import static ru.naumen.core.shared.Constants.AbstractBO.TITLE;
import static ru.naumen.core.shared.Constants.AbstractBO.UUID;
import static ru.naumen.core.shared.Constants.IDIdentifiableBase.ID;

import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Optional;
import java.util.function.BiFunction;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import ru.naumen.common.server.utils.localization.LocalizedTitleChecker;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.LocalizationHelper;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.server.script.api.criteria.ApiCriteria;
import ru.naumen.core.server.script.api.criteria.ApiCriteriaJoinType;
import ru.naumen.core.server.script.api.criteria.criterion.IApiCriterionInfoProvider;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.AttributeLink;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.Constants.LocalizedAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Колонка, соответствующая определенному свойству метакласса
 *
 * <AUTHOR>
 * @since 06.04.20
 */
public class ApiCriteriaPropertyColumn implements IApiCriterionInfoProvider
{

    /**
     * Критерия, в рамках которой нужно искать текущее свойство.
     * <br>
     * Может быть null.
     */
    @Nullable
    private final ApiCriteria optCriteria;

    /**
     * Цепочка свойств, конечное из которых необходимо вернуть в качестве значения
     */
    private final List<AttrReference> properties;

    public ApiCriteriaPropertyColumn(String[] propertyNames)
    {
        this(null, propertyNames);
    }

    public ApiCriteriaPropertyColumn(@Nullable ApiCriteria criteria, String[] propertyNames)
    {
        this.optCriteria = criteria;
        if (propertyNames.length == 0)
        {
            throw new IllegalArgumentException("There has to be at least one property name.");
        }
        this.properties = Arrays.stream(propertyNames)
                .flatMap(propName -> Arrays.stream(propName.split(AttributeLink.CHAIN_DELIMITER_REGEX)))
                .map(AttrReference::parse).collect(Collectors.toList());
    }

    /**
     * Мэппинг идентификаторов атрибутов, которые обрабатываются нестандартно, на функцию получения их column
     * expression.
     * <br>
     * Особенность: все эти атрибуты могут быть только в конце цепочки, т.к. не являются ссылочными.
     */
    private static final Map<String, BiFunction<String, MetaClass, String>> MAGIC_ATTR_EXPRESSIONS = Map.of(
            ID, (alias, metaClass) -> alias + "." + ID,
            "metaClassFqn", (alias, metaClass) -> String.format("concat('%s', '$', %s.%s)",
                    metaClass.getFqn().getId(), alias, METACASE_ID),
            METACASE_ID, (alias, metaClass) -> alias + "." + METACASE_ID);

    /**
     * Преобразование атрибута title в hibernate свойство в зависимости от локали.
     * <br>
     * Например, для локали ru, получится свойство titleRu.
     * <br>
     * Метод предназначен для обработки атрибута title, который локализуется с помощью flex-свойств. Не
     * применять для атрибута {@link LocalizedAttributeType}
     * @param locale локаль атрибута title
     * @param currentCriteria текущая критерия
     * @param handler обработчик, возвращающий объект необходимого на выходе типа
     * @return свойство критерии, соответствующее атрибуту title в локали <code>locale</code>
     */
    private static <T> T handleLocalizedTitleAsFlexProperty(final String locale, final ApiCriteria currentCriteria,
            final ILastInChainAttrHandler<T> handler)
    {
        if (!SpringContext.getInstance().getBean(LocalizedTitleChecker.class).isTitleLocalizationEnabled())
        {
            throw new FxException(SpringContext.getInstance().getBean(MessageFacade.class)
                    .getMessage("ApiCriteriaPropertyColumn.localizationDisabled", locale), true);
        }
        if (!SpringContext.getInstance().getBean(LocalizationHelper.class).isLocaleValid(locale))
        {
            throw new FxException(SpringContext.getInstance().getBean(MessageFacade.class)
                    .getMessage("ApiCriteriaPropertyColumn.localeIsNotAvailable", locale), true);
        }
        String titleAttrFqn = locale.equals(ILocaleInfo.BASE) ? TITLE :
                LocalizationHelper.getLocalizationPropName(TITLE, locale);
        return handler.getCriteriaProperty(currentCriteria, titleAttrFqn);
    }

    /**
     * Валидация обращения к локали атрибута title элемента справочника.
     * <br>
     * Метод бросает {@link FxException}, если переданная локаль не входит в список поддерживаемых
     * или переданная локаль - базовая (у элементов справочника нет базовой локали).
     * @param locale локаль, к которой идет обращение
     */
    private static void validateCatalogItemTitle(String locale)
    {
        if (locale.equals(ILocaleInfo.BASE))
        {
            throw new FxException(SpringContext.getInstance().getBean(MessageFacade.class)
                    .getMessage("ApiCriteriaPropertyColumn.baseLocaleIsNotAvailable", locale), true);
        }
        if (!ILocaleInfo.AVAILABLE_LOCALES.contains(locale))
        {
            throw new FxException(SpringContext.getInstance().getBean(MessageFacade.class)
                    .getMessage("ApiCriteriaPropertyColumn.localeIsNotAvailable", locale), true);
        }
    }

    /**
     * Присоединяет цепочку атрибутов текущей колонки (если цепочка вообще есть) и возвращает объект, соответствующий
     * последнему (или единственному) атрибуту в цепочке. Тип возвращаемого объекта определяется обработчиком handler.
     *
     * @param currentCriteria текущая критерия
     * @param currentAttrRef присоединяемый атрибут
     * @param previousAttrRef атрибут, который предшествует атрибуту <code>currentAttrRef</code> в цепочке атрибутов
     * @param handler обработчик, возвращающий объект необходимого на выходе типа
     * @param <T> тип возвращаемого объекта, соответствующего последнему атрибуту в цепочке
     * @return объект, соответствующий последнему атрибуту в цепочке
     */
    private static <T> T getPropertyColumnExpression(final ApiCriteria currentCriteria,
            final AttrReference currentAttrRef, final @Nullable AttrReference previousAttrRef,
            final ILastInChainAttrHandler<T> handler)
    {
        if (previousAttrRef != null && TITLE.equals(previousAttrRef.getAttrCode()))
        {
            String currentAttrCode = currentAttrRef.getAttrCode();
            Attribute titleAttr = currentCriteria.getCurrentMetaClass().getAttribute(previousAttrRef.toFqnString());
            Objects.requireNonNull(titleAttr);

            if (LocalizedAttributeType.CODE.equals(titleAttr.getType().getCode()))
            {
                validateCatalogItemTitle(currentAttrCode);
            }

            if (SpringContext.getInstance().getBean(LocalizedTitleChecker.class)
                    .metaClassHasTitleAsFlexProperty(currentCriteria.getCurrentMetaClass().getFqn()))
            {
                return handleLocalizedTitleAsFlexProperty(currentAttrCode, currentCriteria, handler);
            }
        }
        String currentAttrFqn = currentAttrRef.toFqnString();
        if (currentCriteria.isUnjoinable())
        {
            return handler.handleNoJoinApiCriteriaAttr(currentCriteria, currentAttrFqn);
        }
        if (MAGIC_ATTR_EXPRESSIONS.containsKey(currentAttrFqn))
        {
            return handler.handleMagicAttr(currentCriteria, currentAttrFqn);
        }

        // Обработка случая атрибутов связанного - т.к. они, фактически, сами являются цепочками атрибутов
        Attribute attr = currentCriteria.getCurrentMetaClass().getAttribute(currentAttrFqn);
        AttributeType attributeType = attr.getType();
        if (attributeType.isAttributeOfRelatedObject())
        {
            List<AttrReference> attrChain = attributeType.getAttrChain()
                    .stream()
                    .map(item -> new AttrReference(item.getClassFqn(), item.getCode()))
                    .collect(Collectors.toList());
            return getPropertyColumnExpression(currentCriteria.joinChain(attrChain, ApiCriteriaJoinType.LEFT),
                    new AttrReference(attributeType.getRelatedObjectMetaClass(),
                            attributeType.getRelatedObjectAttribute()), currentAttrRef, handler);
        }
        // текущий атрибут - не связанный, и присоединять его не надо
        return handler.handleSimpleAttr(currentCriteria, attr);
    }

    private <T> T getPropertyColumnExpression(final ApiCriteria criteria, final ILastInChainAttrHandler<T> handler)
    {
        ApiCriteria currentCriteria = Optional.ofNullable(optCriteria).orElse(criteria)
                .joinChain(properties.subList(0, properties.size() - 1), ApiCriteriaJoinType.LEFT);
        AttrReference lastInChainProperty = properties.get(properties.size() - 1);
        AttrReference previousProperty = properties.size() >= 2
                ? properties.get(properties.size() - 2) : null;
        return getPropertyColumnExpression(currentCriteria, lastInChainProperty, previousProperty, handler);
    }

    @Override
    public HColumn getColumnExpression(ApiCriteria criteria, @Nullable String alias)
    {
        return getPropertyColumnExpression(criteria, new HColumnLastInChainAttrHandler(alias));
    }

    @Override
    public Optional<ApiCriterionPropertyInfo> getPropertyInfo(ApiCriteria criteria)
    {
        return getPropertyColumnExpression(criteria, new PropertyInfoLastInChainAttrHandler());
    }

    @Override
    public boolean isMetacaseColumn()
    {
        return properties.get(properties.size() - 1).getAttrCode().equals(METACASE_ID);
    }

    @Override
    public boolean isIdColumn()
    {
        return properties.get(properties.size() - 1).getAttrCode().equals("id");
    }

    /**
     * Интерфейс обработчиков, возвращающих объект указанного типа по данным о последнем в цепочке атрибуте
     *
     * <AUTHOR>
     * @since 12.03.2021
     * @param <R> тип возвращаемого объекта
     */
    private interface ILastInChainAttrHandler<R>
    {
        /**
         * Возвращает объект указанного типа для случая обработки атрибута, относящегося к корню
         * @param criteria текущая
         * @param attrCode код атрибута
         */
        R handleNoJoinApiCriteriaAttr(ApiCriteria criteria, String attrCode);

        /**
         * Возвращает объект указанного типа для случая, когда атрибут не является атрибутом мета-класса и должен
         * по-особому обрабатываться.
         * @param currentCriteria текущая критерия
         * @param attrCode код атрибута
         */
        R handleMagicAttr(ApiCriteria currentCriteria, String attrCode);

        /**
         * Возвращает объект указанного типа для случая, когда атрибут - обычный атрибут метакласса
         * @param currentCriteria текущая критерия
         * @param attr код атрибута
         */
        R handleSimpleAttr(ApiCriteria currentCriteria, Attribute attr);

        /**
         * Возвращает свойство из критерии с указанным кодом атрибута
         *
         * @param currentCriteria текущая критерия
         * @param attrCode код атрибута
         */
        R getCriteriaProperty(ApiCriteria currentCriteria, String attrCode);
    }

    /**
     * Обработчик, возвращающий HColumn-представление последнего в цепочке атрибута, пригодное для вставки в HQL
     *
     * <AUTHOR>
     * @since 12.03.2021
     */
    private static class HColumnLastInChainAttrHandler implements ILastInChainAttrHandler<HColumn>
    {
        /**
         * Псевдоним возвращаемой колонки
         */
        private final String alias;

        public HColumnLastInChainAttrHandler(@Nullable String alias)
        {
            this.alias = alias;
        }

        @Override
        public HColumn handleNoJoinApiCriteriaAttr(ApiCriteria criteria, String attrCode)
        {
            return HHelper.getColumn(criteria.getAlias() + '.' + attrCode, alias);
        }

        @Override
        public HColumn handleMagicAttr(ApiCriteria criteria, String attrCode)
        {
            return HHelper.getColumn(MAGIC_ATTR_EXPRESSIONS.get(attrCode)
                    .apply(criteria.getAlias(), criteria.getCurrentMetaClass()), alias);
        }

        @Override
        public HColumn handleSimpleAttr(ApiCriteria criteria, Attribute attr)
        {
            if (SpringContext.getInstance().getBean(LocalizedTitleChecker.class)
                    .isAttrLocalizedTitle(attr.getCode(), criteria.getCurrentMetaClass().getFqn()))
            {
                return HHelper.createCoalesceColumn(List.of(
                        HHelper.getColumn(criteria.getAlias() + '.'
                                          + LocalizationHelper.getPropNameForCurrentLocale(attr.getPropertyFqn())),
                        HHelper.getColumn(criteria.getAlias() + '.' + attr.getPropertyFqn())
                ), alias);
            }
            else if (LocalizedAttributeType.CODE.equals(attr.getType().getCode()))
            {
                List<HColumn> columns = LocaleUtils.getAvailableLanguages().stream()
                        .map(lang -> HHelper.getColumn(criteria.getAlias() + '.' + attr.getPropertyFqn() + '.' + lang))
                        .collect(Collectors.toList());
                return HHelper.createCoalesceColumn(columns, alias);
            }
            else if (UUID.equals(attr.getCode()))
            {
                String column = String.format("concat('%s%s', cast(%s.id as string))",
                        criteria.getCurrentMetaClass().getFqn().getId(),
                        UuidHelper.DELIMITER, criteria.getAlias());
                return HHelper.getColumn(column, alias);
            }
            return getCriteriaProperty(criteria, attr.getPropertyFqn());
        }

        @Override
        public HColumn getCriteriaProperty(ApiCriteria currentCriteria, String attrCode)
        {
            return currentCriteria.getCriteria().getProperty(attrCode, alias);
        }
    }

    /**
     * Обработчик, возвращающий объектное представление
     * ({@link ru.naumen.core.server.script.api.criteria.criterion.IApiCriterionInfoProvider.ApiCriterionPropertyInfo})
     * последнего в цепочке атрибута
     *
     * <AUTHOR>
     * @since 12.03.2021
     */
    private static final class PropertyInfoLastInChainAttrHandler implements ILastInChainAttrHandler<Optional<ApiCriterionPropertyInfo>>
    {
        @Override
        public Optional<ApiCriterionPropertyInfo> handleNoJoinApiCriteriaAttr(ApiCriteria criteria,
                String attrCode)
        {
            return Optional.of(new ApiCriterionPropertyInfo(criteria.getCriteria(), criteria.getUnjoinableAttr(),
                    attrCode));
        }

        @Override
        public Optional<ApiCriterionPropertyInfo> handleMagicAttr(ApiCriteria currentCriteria, String attrCode)
        {
            return Optional.empty();
        }

        @Override
        public Optional<ApiCriterionPropertyInfo> handleSimpleAttr(ApiCriteria currentCriteria, Attribute attr)
        {
            return Optional.of(new ApiCriterionPropertyInfo(currentCriteria.getCriteria(), attr, null));
        }

        @Override
        public Optional<ApiCriterionPropertyInfo> getCriteriaProperty(ApiCriteria currentCriteria, String attrCode)
        {
            return Optional.empty();
        }
    }
}
