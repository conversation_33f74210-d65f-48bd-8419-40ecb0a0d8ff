package ru.naumen.core.server.script.spi;

import java.util.List;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;

import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.ClassFqn;

import com.google.common.collect.Lists;

/**
 * Обертка вокруг значения аггрегирующего атрибута используемого в скриптах.
 * <p>
 * Лучше всегда явно обращаться к аггрегируемым значениям (методы {@link #getEmployee()}, {@link #getOu()},
 * {@link #getTeam()}), а не обращаться к аггрегируемому атрибуту по порядковому номеру (порядок не определен явно).
 *
 * <AUTHOR>
 */
public class AggregateContainerWrapper implements IUUIDIdentifiable, IHasMetaInfo, ITitled
{
    @CheckForNull
    private final ScriptDtObject employee;
    @CheckForNull
    private final ScriptDtObject ou;
    @CheckForNull
    private final ScriptDtObject team;

    public AggregateContainerWrapper(@Nullable ScriptDtObject employee, @Nullable ScriptDtObject ou,
            @Nullable ScriptDtObject team)
    {
        this.employee = employee;
        this.ou = ou;
        this.team = team;
    }

    public ScriptDtObject get(int index)
    {
        return asList().get(index);

    }

    public Object get(String key)
    {
        return getMain().get(key);
    }

    @CheckForNull
    public ScriptDtObject getEmployee()
    {
        return employee;
    }

    @Override
    public ClassFqn getMetaClass()
    {
        ScriptDtObject obj = getMain();
        return null == obj ? null : obj.getMetaClass();
    }

    @CheckForNull
    public ScriptDtObject getOu()
    {
        return ou;
    }

    @CheckForNull
    public ScriptDtObject getTeam()
    {
        return team;
    }

    @Override
    public String getTitle()
    {
        ScriptDtObject obj = getMain();
        return null == obj ? null : obj.getTitle();
    }

    @Override
    public String getUUID()
    {
        ScriptDtObject obj = getMain();
        return null == obj ? null : obj.getUUID();
    }

    private List<ScriptDtObject> asList()
    {
        List<ScriptDtObject> result = Lists.newArrayListWithCapacity(3);
        if (null != employee)
        {
            result.add(employee);
        }
        if (null != ou)
        {
            result.add(ou);
        }
        if (null != team)
        {
            result.add(team);
        }
        return result;
    }

    private ScriptDtObject getMain()
    {
        if (null != employee)
        {
            return employee;
        }
        if (null != ou)
        {
            return ou;
        }
        return team;
    }
}
