package ru.naumen.core.server.advlist.export.DeferredExport;

import java.util.concurrent.atomic.AtomicBoolean;

import javax.annotation.concurrent.NotThreadSafe;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.monitoring.JMSStatistics;

/**
 * Инкапсуляция обработки сообщения о построении отложенного экспорта адвлиста, который можно прервать
 * <AUTHOR>
 * @since 20.08.2020
 **/
@NotThreadSafe
@Component
public class AdvlistExportProcess
{
    private static final Logger LOG = LoggerFactory.getLogger(AdvlistExportProcess.class);

    private final AtomicBoolean interrupted = new AtomicBoolean(false);

    private final JMSStatistics jmsStatistics;
    private final AdvlistExportPerformer advlistExportPerformer;
    private final AdvExportStartedProcessesCacheService startedProcessCacheService;

    @Inject
    public AdvlistExportProcess(JMSStatistics jmsStatistics,
            AdvlistExportPerformer advlistExportPerformer,
            AdvExportStartedProcessesCacheService startedProcessCacheService)
    {
        this.jmsStatistics = jmsStatistics;
        this.advlistExportPerformer = advlistExportPerformer;
        this.startedProcessCacheService = startedProcessCacheService;
    }

    /**
     * Построить файл с данными адвлиста и отправить на него ссылку
     */
    protected void startExport(AdvlistExportMessage message)
    {
        try
        {
            interrupted.set(false);
            advlistExportPerformer.createSendAdvlistExportFile(message, this);
        }
        catch (AdvlistExportInterruptedException e)
        {
            LOG.warn(e.getMessage()); //NOPMD
        }
    }

    protected void removeCache(String hash)
    {
        startedProcessCacheService.remove(hash);
    }

    protected void clearCache()
    {
        startedProcessCacheService.clear();
    }

    protected void addStatistics(long waitTime, long processingTime)
    {
        jmsStatistics.addDataAdvlistExportProcessing(waitTime, processingTime);
    }

    public void interrupt()
    {
        interrupted.set(true);
    }

    public boolean isInterrupted()
    {
        return interrupted.get();
    }
}