package ru.naumen.core.server.script.api;

import java.util.Map;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.Color;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.common.shared.utils.ILocalizedText;
import ru.naumen.common.shared.utils.LocalizedText;
import ru.naumen.common.shared.utils.SourceCode;
import ru.naumen.core.server.bo.ISimpleBO;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.IAttrReference;
import ru.naumen.metainfo.shared.IClassFqn;

/**
 * API для создания примитивных объектов системы, таких как {@link Hyperlink}
 *
 * <AUTHOR>
 *
 * @since *******
 */
@Component("types")
public class TypesApi implements ITypesApi
{
    private final ApiUtils apiUtils;

    @Inject
    public TypesApi(ApiUtils apiUtils)
    {
        this.apiUtils = apiUtils;
    }

    @Override
    public IAttrReference newAttrReference(String fqn, String code)
    {
        return new AttrReference(ClassFqn.parse(fqn), code);
    }

    @Override
    public ClassFqn newClassFqn(String code)
    {
        return ClassFqn.parse(code);
    }

    @Override
    public ClassFqn newClassFqn(String id, String caseCode)
    {
        return ClassFqn.parse(id, caseCode);
    }

    @Override
    public Color newColor(int intValue)
    {
        return new Color(intValue);
    }

    @Override
    public Color newColor(String value)
    {
        return new Color(value);
    }

    @Override
    public DateTimeInterval newDateTimeInterval(long length, String intervalName)
    {
        return new DateTimeInterval(length, intervalName);
    }

    @Override
    public Hyperlink newHyperlink(String url)
    {
        return new Hyperlink(url, url);
    }

    @Override
    public Hyperlink newHyperlink(String text, String url)
    {
        return new Hyperlink(text, url);
    }

    @Override
    public ILocalizedText newLocalizedText(Map<String, String> localizedStrings)
    {
        return new LocalizedText(localizedStrings);
    }

    @Override
    public ILocalizedText newLocalizedText(String locale, String text)
    {
        return new LocalizedText(locale, text);
    }

    @Override
    public SourceCode newSourceCode(String text)
    {
        return new SourceCode(text, "");
    }

    @Override
    public SourceCode newSourceCode(String text, String lang)
    {
        return new SourceCode(text, lang);
    }

    @Override
    public IUUIDIdentifiable newTreeDtObject(Object parent)
    {
        DtObject parentDtObject = convertToDtObject(parent);
        Preconditions.checkArgument(parentDtObject != null, "Ou or team can not be null");

        return new SimpleTreeDtObject(null, parentDtObject);
    }

    @Override
    public IUUIDIdentifiable newTreeDtObject(Object parent, Object employee)
    {
        DtObject parentDtObject = convertToDtObject(parent);
        DtObject employeeDtObject = convertToDtObject(employee);
        Preconditions.checkArgument(employeeDtObject != null, "Employee can not be null");

        return new SimpleTreeDtObject(parentDtObject, employeeDtObject);
    }

    @Nullable
    private DtObject convertToDtObject(@Nullable Object value)
    {
        if (value instanceof DtObject asDto)
        {
            return asDto;
        }

        IUUIDIdentifiable uuidIdentifiable = apiUtils.getObject(value);
        if (uuidIdentifiable == null)
        {
            return null;
        }

        Preconditions.checkArgument(uuidIdentifiable instanceof ISimpleBO,
                "Object '%s' must be ISimpleBO".formatted(uuidIdentifiable));

        String uuid = uuidIdentifiable.getUUID();
        String title = uuidIdentifiable instanceof ITitled titled ? titled.getTitle() : "";
        ClassFqn classFqn = ((ISimpleBO)uuidIdentifiable).getMetaClass();

        return new SimpleDtObject(uuid, title, classFqn);
    }

    @Override
    public IClassFqn extractFqn(Object fqn)
    {
        return ApiUtils.extractFqn(fqn);
    }
}
