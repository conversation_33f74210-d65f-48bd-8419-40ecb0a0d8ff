package ru.naumen.core.server.script.api;

/**
 * Методы для использования в скриптах с контекстной переменной sp (SearchParams)
 *
 * <AUTHOR>
 */
public final class SearchParams implements ISearchParams
{
    public static SearchParams createInstance()
    {
        return new SearchParams();
    }

    private int limit;
    private int offset;
    private boolean ignoreCase;

    private SearchParams()
    {
    }

    @Override
    public boolean getIgnoreCase()
    {
        return ignoreCase;
    }

    @Override
    public int getLimit()
    {
        return limit;
    }

    @Override
    public int getOffset()
    {
        return offset;
    }

    @Override
    public SearchParams ignoreCase()
    {
        ignoreCase = true;
        return this;
    }

    @Override
    public SearchParams limit(int limit)
    {
        this.limit = limit;
        return this;
    }

    @Override
    public SearchParams offset(int offset)
    {
        this.offset = offset;
        return this;
    }
}