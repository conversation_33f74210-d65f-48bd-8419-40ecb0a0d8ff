package ru.naumen.core.server.advlist.export;

import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;

import java.util.ArrayList;

/**
 * <AUTHOR>
 * @since 15.12.2011
 *
 */
@CellCreatorComponent(codes = { Constants.SlmService.CALL_CASES })
public class CallCasesXlsCellCreator extends AbstractXlsCellCreator
{
    @Inject
    MetainfoService metainfoService;

    @Override
    public void setCellValue(Cell cell, Object attributeValue, Workbook workbook, FormatterContext context)
    {
        List<String> caseTitles = new ArrayList<>();
        for (ClassFqn callCase : (Collection<ClassFqn>)attributeValue)
        {
            caseTitles.add(metainfoService.getMetaClass(callCase).getTitle());
        }
        String value = trim(StringUtilities.join(caseTitles));
        cell.setCellValue(value);
    }

}
