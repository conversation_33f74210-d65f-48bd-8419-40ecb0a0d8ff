package ru.naumen.core.server.orders;

import java.util.Collection;

import jakarta.inject.Inject;

import ru.naumen.core.server.filters.attribute.AttributeResolverHelper;
import ru.naumen.core.server.filters.handlers.restrictions.RestrictionsFactory;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HOrder;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.criteria.Order;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * <AUTHOR>
 * @since 05.10.2011
 */
public class DefaultOrderHandler implements OrderHandler
{
    protected final Order ord;
    protected final MetaClass metaClass;
    boolean needAddColumn;

    @Inject
    RestrictionsFactory restrictionsFactory;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private AttributeResolverHelper resolverHelper;

    public DefaultOrderHandler(MetaClass metaClass, Order ord, boolean needAddColumn)
    {
        this.metaClass = metaClass;
        this.ord = ord;
        this.needAddColumn = needAddColumn;
    }

    @Override
    public void apply(HCriteria criteria)
    {
        Attribute attribute = resolverHelper.getAttribute(metaClass, ord.name);
        if (attribute.getType().isAttributeOfRelatedObject())
        {
            for (AttributeFqn attrInChain : attribute.getType().getAttrChain())
            {
                criteria = criteria.addLeftJoin(metainfoService.getAttribute(attrInChain).getPropertyFqn());
            }

            for (int i = 0; i < attribute.getType().getRelatedObjectHierarchyLevel(); i++)
            {
                criteria = criteria.addLeftJoin(Constants.PARENT_ATTR);
            }

            String attrCode = attribute.getType().getRelatedObjectAttribute().equals(Constants.PARENT_ATTR)
                              && attribute.getType().getRelatedObjectHierarchyLevel() != 0
                    ? attrCode = AbstractBO.UUID : attribute.getType().getRelatedObjectAttribute();

            attribute = metainfoService
                    .getAttribute(new AttributeFqn(attribute.getType().getRelatedObjectMetaClass(), attrCode));
        }

        Collection<HOrder> orders = restrictionsFactory.getStrategy(attribute).order(criteria, attribute, ord.asc);
        for (HOrder o : orders)
        {
            criteria.addOrder(o);
            if (needAddColumn)
            {
                criteria.addColumn(o.getProperty());
            }
        }
    }
}
