package ru.naumen.core.server.script.storage.modification.usage;

import java.util.HashSet;
import java.util.List;

import org.codehaus.groovy.control.customizers.CompilationCustomizer;

import java.util.ArrayList;

import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Контекст редактирования скрипта, содержит все необходимые
 * для редактирования переменные среды.
 * Наследник {@link MapProperties}, что позволяет передавать специфичные
 * параметры для контрактов редактирования скрипта.
 * <AUTHOR>
 * @since Oct 23, 2015
 */
public class ScriptModifyContext extends MapProperties
{
    private static final long serialVersionUID = -1224155625627188020L;

    private ScriptCategory category;
    private ScriptHolders holderType;

    /**
     * Флаг изменения свойств скрипта, которые сохраняются в БД
     * инициализируется в процессе работы алгоритма, чтобы избежать повторных вычислений
     */
    private boolean scriptBDPropertiesChanged = false;

    /**
     * Флаг отложенного сохранения скриптов в БД, скрипты сохраняются в массив scriptsForSave
     */
    private boolean defferedPersist = false;

    /**
     * Коллекция скриптов для отложенного сохранения в БД
     */
    private HashSet<Script> scriptsForSave;

    /**
     * Информация об изменениях скриптов для логирования
     */
    private List<ScriptAdminLogInfo> scriptsLogInfo;

    /**
     * Флаг срочного снятия свойств для лога. Используется при удалении скриптовой настройки -
     * после удаления не получить свойств этой настройки.
     */
    private boolean immediateLogSnapshot = false;

    /**
     * Пользовательские конфигураторы процесса компиляции
     */
    private CompilationCustomizer[] compilationCustomizers = new CompilationCustomizer[0];

    public ScriptModifyContext(ScriptCategory category, ScriptHolders holderType)
    {
        this.category = category;
        this.holderType = holderType;
    }

    public ScriptModifyContext(ScriptHolders holderType)
    {
        this.holderType = holderType;
    }

    public void addScriptLogInfo(ScriptAdminLogInfo log)
    {
        getScriptsLogInfo().add(log);
    }

    public ScriptCategory getCategory()
    {
        return category;
    }

    public ScriptHolders getHolderType()
    {
        return holderType;
    }

    public HashSet<Script> getScriptsForSave()
    {
        if (scriptsForSave == null)
        {
            scriptsForSave = new HashSet<>();
        }
        return scriptsForSave;
    }

    public void setCompilationCustomizers(CompilationCustomizer... compilationCustomizers)
    {
        this.compilationCustomizers = compilationCustomizers;
    }

    public CompilationCustomizer[] getCompilationCustomizers()
    {
        return compilationCustomizers;
    }

    public List<ScriptAdminLogInfo> getScriptsLogInfo()
    {
        if (scriptsLogInfo == null)
        {
            scriptsLogInfo = new ArrayList<>();
        }
        return scriptsLogInfo;
    }

    public boolean isDefferedPersist()
    {
        return defferedPersist;
    }

    public boolean isScriptBDPropertiesChanged()
    {
        return scriptBDPropertiesChanged;
    }

    public void putScriptForDefferedPersist(Script script)
    {
        getScriptsForSave().add(script);
    }

    public void setCategory(ScriptCategory category)
    {
        this.category = category;
    }

    public void setDefferedPersist(boolean defferedPersist)
    {
        this.defferedPersist = defferedPersist;
    }

    public void setHolderType(ScriptHolders holderType)
    {
        this.holderType = holderType;
    }

    public void setScriptBDPropertiesChanged(boolean scriptBDPropertiesChanged)
    {
        this.scriptBDPropertiesChanged = scriptBDPropertiesChanged;
    }

    @Override
    public String toString()
    {
        return "[category=" + this.category + ", holder=" + this.holderType + ", properties=" + super.toString() + "]";
    }

    public boolean isImmediateLogSnapshot()
    {
        return immediateLogSnapshot;
    }

    public void setImmediateLogSnapshot(boolean immediateLogSnapshot)
    {
        this.immediateLogSnapshot = immediateLogSnapshot;
    }
}
