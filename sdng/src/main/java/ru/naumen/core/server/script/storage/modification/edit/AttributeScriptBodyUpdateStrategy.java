package ru.naumen.core.server.script.storage.modification.edit;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TreeSet;

import org.springframework.context.annotation.DependsOn;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.componform.ComputableOnFormHelper;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.server.util.DateTimeAttrRestrictionHelper;
import ru.naumen.core.shared.Constants.Scripts;
import ru.naumen.core.shared.script.places.AttributeCategories;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.settings.Settings;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.elements.AbstractAttributeInfo;
import ru.naumen.metainfo.server.spi.elements.AttributeImpl;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;

/**
 * Стратегия обновления мест использования при изменении тела скрипта для категории {@link AttributeCategories}
 * Происходит вычисление атрибутов используемых в скриптах и запись в места использования.
 * Логика строится из расчета, что скрипт уже обновлен, выполняются сопутствующие
 * изменения в местах их использования.
 * <AUTHOR>
 * @since Oct 8, 2015
 */
@Lazy
@Component
@DependsOn({ "metainfoService" })
public class AttributeScriptBodyUpdateStrategy implements ScriptBodyUpdateStrategy
{
    @Lazy
    @Inject
    private MetainfoServiceBean metainfoService;
    @Lazy
    @Inject
    private MetainfoServicePersister persister;
    @Lazy
    @Inject
    private ScriptStorageService scriptStorageService;
    @Lazy
    @Inject
    private ScriptService scriptService;
    @Lazy
    @Inject
    private SettingsStorage settingsStorage;
    @Lazy
    @Inject
    private ComputableOnFormHelper computableOnFormHelper;
    @Lazy
    @Inject
    private DateTimeAttrRestrictionHelper dateTimeAttrRestrictionHelper;
    @Lazy
    @Inject
    private MetainfoModification metainfoModification;

    @Override
    public void updateScriptUsagePoint(ScriptUsagePoint usagePoint)
    {
        ScriptHolders holderType = usagePoint.getHolderType();
        if (holderType == ScriptHolders.SC_PARAMETERS_AGREEMENT
            || holderType == ScriptHolders.SC_PARAMETERS_SLM
            || holderType == ScriptHolders.SC_PARAMETERS_CASES)
        {
            updateSCParametersScripts();
            return;
        }

        AttributeCategories category = (AttributeCategories)usagePoint.getCategory();
        if (category == AttributeCategories.FILTRATION
            || category == AttributeCategories.COMPUTABLE_ON_FORM
            || category == AttributeCategories.DATE_TIME_RESTRICTION)
        {
            updateAttrsUsedInAttributesScript(usagePoint);
        }
    }

    private Collection<String> getAttrsUsedInScript(Script script)
    {
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.SUBJECT, null);
        return scriptService.execute(script, bindings);
    }

    private Collection<String> getAttrsUsedInScriptWithAttrCode(Script script, String attrCode)
    {
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.SUBJECT, null);
        bindings.put(Scripts.ATTR_CODE, attrCode);
        return scriptService.execute(script, bindings);
    }

    private void setAttrsUsedInSCParamScript(SCParameters parameters)
    {
        Script scriptForAgreement = scriptStorageService.getScript(parameters.getAgreementsFiltrationScript());
        Script scriptForService = scriptStorageService.getScript(parameters.getServicesFiltrationScript());
        Script scriptForCases = scriptStorageService.getScript(parameters.getCasesFiltrationScript());

        TreeSet<String> attrs = new TreeSet<>();
        if (parameters.isFilterAgreements() && null != scriptForAgreement)
        {
            attrs.addAll(getAttrsUsedInScript(scriptForAgreement));
        }
        if (parameters.isFilterServices() && null != scriptForService)
        {
            attrs.addAll(getAttrsUsedInScript(scriptForService));
        }
        if (parameters.isFilterCases() && null != scriptForCases)
        {
            attrs.addAll(getAttrsUsedInScript(scriptForCases));
        }

        parameters.setAttrsUsedInScripts(new ArrayList<>(attrs));
    }

    @SuppressWarnings("incomplete-switch")
    private void updateAttrsUsedInAttributesScript(ScriptUsagePoint usagePoint)
    {
        metainfoModification.modify(MetainfoRegion.METACLASS_DECLARATIONS);
        MetaClassImpl metaClass = metainfoService.getMetaClass(usagePoint.getRelatedMetaClassFqns().iterator().next());
        AttributeFqn attrFqn = AttributeFqn.parse(usagePoint.getLocation());
        final String attrCode = attrFqn.getCode();
        AttributeImpl attribute = metaClass.getAttribute(attrCode);

        AbstractAttributeInfo attrInfo = metaClass.getDeclaredAttribute(attrCode);
        if (attribute.isHardcoded() || attrInfo == null)
        {
            attrInfo = metaClass.getAttributeOverride(attrCode);
        }

        Preconditions.checkArgument(attrInfo != null, "Wrong script location: %s", usagePoint.getLocation());

        AttributeCategories category = (AttributeCategories)usagePoint.getCategory();
        switch (category) //NOPMD NOSONAR
        {
            case COMPUTABLE_ON_FORM:
                Script computableScript = scriptStorageService.getScript(attrInfo.getComputableOnFormScript());
                Map<String, Object> bindings = new HashMap<>();
                bindings.put(ScriptService.Constants.SUBJECT, null);
                bindings.put("form", null);

                List<String> scriptResult = null;
                scriptResult = scriptService.execute(computableScript, bindings);
                if (null == scriptResult)
                {
                    scriptResult = Collections.emptyList();
                }

                attrInfo.setAttrsUsedInEditValueScript(scriptResult);
                computableOnFormHelper.assertNoLoopDependency(attrInfo);
                break;
            case FILTRATION:
                Script filtrationScript = scriptStorageService.getScript(attrInfo.getScriptForFiltration());
                List<String> attrsUsedInScript = CollectionUtils
                        .asArrayList(getAttrsUsedInScriptWithAttrCode(filtrationScript, attrCode));
                if (null == attrsUsedInScript)
                {
                    attrsUsedInScript = Collections.emptyList();
                }
                attrInfo.setAttrsUsedInScript(attrsUsedInScript);
                break;
            case DATE_TIME_RESTRICTION:
                Script restrictionScript = scriptStorageService.getScript(attrInfo.getDateTimeRestrictionScript());
                List<String> attrsUsedInRestrScript = CollectionUtils
                        .asArrayList(getAttrsUsedInScriptWithAttrCode(restrictionScript, attrCode));
                dateTimeAttrRestrictionHelper.validateRestrictionScriptAttributes(attrsUsedInRestrScript, attribute);
                if (null == attrsUsedInRestrScript)
                {
                    attrsUsedInRestrScript = Collections.emptyList();
                }
                attrInfo.setAttrsForDateTimeRestrictionScript(attrsUsedInRestrScript);
                metainfoModification.modify(MetainfoRegion.METACLASS_DECLARATIONS);
                break;
        }

        metaClass.travels(input ->
        {
            input.getAttribute(attrCode).recalc();
            persister.persist(input);
        });
    }

    private void updateSCParametersScripts()
    {
        metainfoModification.modify(MetainfoRegion.COMMON_SETTINGS);
        Settings settings = settingsStorage.getSettings().clone();
        SCParameters parameters = settings.getScParameters();
        setAttrsUsedInSCParamScript(parameters);
        settingsStorage.saveSettings(settings);
    }
}
