package ru.naumen.core.server.script.spi.mapper;

import org.springframework.stereotype.Component;

import com.google.gson.JsonArray;
import com.google.gson.JsonElement;
import com.google.gson.JsonObject;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.bo.ToJsonTransformer;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.server.script.spi.ScriptDtOCollection;
import ru.naumen.core.server.script.spi.ScriptObjectBase;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.criteria.DtoProperties;

/**
 * Преобразует объекты ScriptDtOCollection в JSON по тем же правилам, что RestAPI преобразует объект в JSON
 *
 * <AUTHOR>
 * @since 03.02.2020
 */
@Component
public class ScriptDtOCollectionMapper extends AbstractMapper<ScriptDtOCollection, JsonArray>
{
    private final MappingService mappingService;
    private final ToJsonTransformer helper;

    @Inject
    public ScriptDtOCollectionMapper(MappingService mappingService, ToJsonTransformer helper)
    {
        super(ScriptDtOCollection.class, JsonArray.class);
        this.mappingService = mappingService;
        this.helper = helper;
    }

    @Override
    public void transform(ScriptDtOCollection from, JsonArray to, @Nullable DtoProperties properties)
    {
        from.forEach(obj -> to.add(mapToJson(obj, properties)));
    }

    /**
     * Если объект унаследован от ScriptObjectBase или IUUIDIdentifiable, тогда он сериализуется в JSON в соответствии
     * с правилами RestApi.
     *
     * @param object сериализуемый объект
     * @param properties данные, которые необходимо извлечь из этого объекта
     * @return JsonElement - результат сериализации
     */
    private JsonElement mapToJson(Object object, @Nullable DtoProperties properties)
    {
        if (object instanceof ScriptObjectBase || object instanceof IUUIDIdentifiable)
        {
            JsonObject jsonObject = new JsonObject();
            mappingService.transform(object, jsonObject, properties);
            return jsonObject;
        }
        else if (object.getClass().isArray())
        {
            JsonArray jsonArray = new JsonArray();
            for (Object o : (Object[])object)
            {
                jsonArray.add(helper.resolveValue(o, true, true));
            }
            return jsonArray;
        }
        return helper.resolveValue(object, true, true);
    }
}
