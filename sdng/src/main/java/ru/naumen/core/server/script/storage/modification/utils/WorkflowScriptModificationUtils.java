package ru.naumen.core.server.script.storage.modification.utils;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

import jakarta.inject.Inject;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.base.Predicates;
import com.google.common.collect.FluentIterable;
import com.google.common.collect.Lists;

import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.server.script.storage.modification.usage.WfActionConditionScriptModifyProcess;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.core.shared.script.places.WorkflowCategory;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.wf.StateDeclarationImpl;
import ru.naumen.metainfo.server.spi.elements.wf.WorkflowImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.HasHardcoded;
import ru.naumen.metainfo.shared.elements.wf.Action;
import ru.naumen.metainfo.shared.elements.wf.Action.ActionType;
import ru.naumen.metainfo.shared.elements.wf.Condition;
import ru.naumen.metainfo.shared.elements.wf.Condition.ConditionType;
import ru.naumen.metainfo.shared.elements.wf.WfActionCondition;

/**
 * Утилитарные методы редактирования скриптов жизненного цикла метакласса
 * содержит логику создания, редактирования, удаления скриптов
 * <AUTHOR>
 * @since 12.09.2015
 */
@Component
public class WorkflowScriptModificationUtils
{
    @Lazy
    @Inject
    private ScriptModifyRegistry scriptModifyRegistry;

    public List<ScriptAdminLogInfo> processDeleteScripts(ClassFqn metaClassFqn, String stateCode,
            WorkflowCategory category, WfActionCondition holder)
    {
        ScriptModifyProcess<WfActionCondition> process = scriptModifyRegistry.getProcess(holder);
        ScriptModifyContext context = new ScriptModifyContext(category, ScriptHolders.WORKFLOW);
        context.setProperty(WfActionConditionScriptModifyProcess.META_CLASS_FQN, metaClassFqn.asString());
        context.setProperty(WfActionConditionScriptModifyProcess.STATE_CODE, stateCode);
        process.deleteHolder(holder, context);
        return context.getScriptsLogInfo();
    }

    public void processDeleteWfStateScripts(WorkflowImpl workflow, String stateCode,
            List<ScriptAdminLogInfo> scriptsLog)
    {
        Collection<Action> actions = new ArrayList<>();
        actions.addAll(workflow.getDeclaredActions(stateCode, true));
        actions.addAll(workflow.getDeclaredActions(stateCode, false));
        for (Action action : actions)
        {
            if (action.getType() != ActionType.SCRIPT)
            {
                continue;
            }
            scriptsLog.addAll(processDeleteScripts(workflow.getMetaClass().getFqn(), stateCode,
                    WorkflowCategory.WF_SCRIPT_ACTION, action));
        }

        Collection<Condition> conditions = new ArrayList<>();
        conditions.addAll(workflow.getDeclaredConditions(stateCode, true));
        conditions.addAll(workflow.getDeclaredConditions(stateCode, false));
        for (Condition condition : conditions)
        {
            if (condition.getType() != ConditionType.SCRIPT)
            {
                continue;
            }
            scriptsLog.addAll(processDeleteScripts(workflow.getMetaClass().getFqn(), stateCode,
                    WorkflowCategory.WF_SCRIPT_CONDITION, condition));
        }
    }

    public void processDeleteWorkflowScripts(MetaClassImpl metaClass, List<ScriptAdminLogInfo> scriptsLog)
    {
        WorkflowImpl workflow = metaClass.getWorkflow();
        //@formatter:off
        List<StateDeclarationImpl> stateDeclarations = Lists.newArrayList(
            stateDeclarations = FluentIterable.from(workflow.getStateDeclarations())
            .filter(Predicates.not(HasHardcoded.IS_HARDCODED))
            .toList());
        //@formatter:on
        stateDeclarations.addAll((workflow.getStateOverrides()));
        for (StateDeclarationImpl stateDeclaration : stateDeclarations)
        {
            processDeleteWfStateScripts(workflow, stateDeclaration.getCode(), scriptsLog);
        }
    }

    public void processUpdateWorkflowsScriptsAfterCopy(MetaClassImpl metaClass, List<ScriptAdminLogInfo> scriptLog)
    {
        WorkflowImpl workflow = metaClass.getWorkflow();

        List<StateDeclarationImpl> stateDeclarations = Lists.newArrayList(workflow.getStateDeclarations());
        stateDeclarations.addAll((workflow.getStateOverrides()));
        Collection<String> definedStates = CollectionUtils.transform(stateDeclarations, HasCode.CODE_EXTRACTOR);

        for (String stateCode : definedStates)
        {
            Collection<Action> actions = new ArrayList<>();
            actions.addAll(workflow.getDeclaredActions(stateCode, true));
            actions.addAll(workflow.getDeclaredActions(stateCode, false));

            for (Action action : actions)
            {
                if (action.getType() != ActionType.SCRIPT)
                {
                    continue;
                }
                updateScriptAfterCopy(metaClass.getFqn(), stateCode, WorkflowCategory.WF_SCRIPT_ACTION, action,
                        scriptLog);
            }

            Collection<Condition> conditions = new ArrayList<>();
            conditions.addAll(workflow.getDeclaredConditions(stateCode, true));
            conditions.addAll(workflow.getDeclaredConditions(stateCode, false));
            for (Condition condition : conditions)
            {
                if (condition.getType() != ConditionType.SCRIPT)
                {
                    continue;
                }
                updateScriptAfterCopy(metaClass.getFqn(), stateCode, WorkflowCategory.WF_SCRIPT_CONDITION, condition,
                        scriptLog);
            }
        }
    }

    private void updateScriptAfterCopy(ClassFqn metaClassFqn, String stateCode, WorkflowCategory category,
            WfActionCondition holder, List<ScriptAdminLogInfo> scriptLog)
    {
        ScriptModifyProcess<WfActionCondition> process = scriptModifyRegistry.getProcess(holder);
        ScriptModifyContext context = new ScriptModifyContext(category, ScriptHolders.WORKFLOW);
        context.setProperty(WfActionConditionScriptModifyProcess.META_CLASS_FQN, metaClassFqn.asString());
        context.setProperty(WfActionConditionScriptModifyProcess.STATE_CODE, stateCode);
        process.copyHolder(holder, context);
        scriptLog.addAll(context.getScriptsLogInfo());
    }
}
