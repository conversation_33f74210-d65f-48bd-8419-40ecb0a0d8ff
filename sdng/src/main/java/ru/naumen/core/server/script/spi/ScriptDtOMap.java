package ru.naumen.core.server.script.spi;

import java.util.Collection;
import java.util.Map;
import java.util.Set;

/**
 * Реализация {@link Map} для использования в скриптах
 * <AUTHOR>
 *
 * @param <K> тип объектов ключа для {@link Map}
 * @param <V> тип объектов значения для {@link Map}
 */
public class ScriptDtOMap<K, V> extends ScriptObjectBase<Map<Object, Object>> implements Map<K, V>
{
    public ScriptDtOMap(Map<Object, Object> rawMap, ScriptDtOHelper helper)
    {
        super(rawMap, helper);
    }

    @Override
    public void clear()
    {
        helper.unmodify();
    }

    @Override
    public boolean containsKey(Object key)
    {
        return delegate.containsKey(helper.unwrap(key));
    }

    @Override
    public boolean containsValue(Object value)
    {
        return delegate.containsValue(helper.unwrap(value));
    }

    @Override
    public Set<Entry<K, V>> entrySet()
    {
        return helper.wrapSet((Set)delegate.entrySet());
    }

    @Override
    public V get(Object key)
    {
        return helper.<V> wrap(delegate.get(helper.unwrap(key)));
    }

    @Override
    public boolean isEmpty()
    {
        return delegate.isEmpty();
    }

    @Override
    public Set<K> keySet()
    {
        return helper.wrapSet(delegate.keySet());
    }

    @Override
    public V put(K key, V value)
    {
        return helper.<V> unmodify();
    }

    @Override
    public void putAll(Map<? extends K, ? extends V> m)
    {
        helper.unmodify();
    }

    @Override
    public V remove(Object key)
    {
        return helper.<V> unmodify();
    }

    @Override
    public int size()
    {
        return delegate.size();
    }

    @Override
    public Collection<V> values()
    {
        return helper.wrapCollection(delegate.values());
    }

}
