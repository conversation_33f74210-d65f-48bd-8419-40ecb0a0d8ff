package ru.naumen.core.server.script.libraries;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import io.github.classgraph.ScanResult;
import ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessor;

/**
 * Результат сканирования {@link ClassPathScanProcessor#process(ScanResult, ClassLoader, String)}
 *
 * <AUTHOR>
 * @since 24.06.2021
 */
public class ScanProcessResult<V>
{
    // код скрипта -> скрипт как объект
    private final Map<String, V> scriptCodeToScriptObject;
    // имя библиотеки -> коллекция кодов скриптов
    private final Map<String, Set<String>> libraryNameToScriptCodes;

    public ScanProcessResult(Map<String, V> scriptCodeToScriptObject,
            Map<String, Set<String>> libraryNameToScriptInCurrentLibrary)
    {
        this.scriptCodeToScriptObject = scriptCodeToScriptObject;
        this.libraryNameToScriptCodes = libraryNameToScriptInCurrentLibrary;
    }

    /**
     * @return Ассоциативный массив (карту): ключ — код скрипта; значение — скрипт как объект.
     */
    public Map<String, V> getCodeScriptToScriptObject()
    {
        return scriptCodeToScriptObject;
    }

    /**
     * Получить ассоциативный массив (карту): ключ — код скрипта; значение — скрипт как объект для хранилища.
     * @param scannedScriptConverter функция по конвертации
     * @param <T> тип скрипта для хранения;
     * @return ассоциативный массив (карту): ключ — код скрипта; значение — скрипт как объект для хранилища.
     */
    public <T> Map<String, T> getCodeScriptToScriptForStorage(Function<V, T> scannedScriptConverter)
    {
        return scriptCodeToScriptObject.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> scannedScriptConverter.apply(entry.getValue())
                ));
    }

    /**
     * @return ассоциативный массив (карту): ключ — имя библиотеки; значение — коды скриптов, хранящиеся в этой
     * библиотеке.
     */
    public Map<String, Set<String>> getLibraryNameToCodeScripts()
    {
        return libraryNameToScriptCodes;
    }

    /**
     * @return ассоциативный массив (карту): ключ — имя библиотеки; значение — скрипты, как объекты, из этой библиотеки.
     */
    public Map<String, Set<V>> getLibraryNameToScriptObjects()
    {
        Map<String, Set<V>> result = new HashMap<>();
        for (Map.Entry<String, Set<String>> libraryToScriptCode : libraryNameToScriptCodes.entrySet())
        {
            final String libraryName = libraryToScriptCode.getKey();
            final Set<V> scripts = libraryToScriptCode.getValue().stream()
                    .map(scriptCodeToScriptObject::get)
                    .collect(Collectors.toSet());
            result.put(libraryName, scripts);
        }
        return result;
    }
}
