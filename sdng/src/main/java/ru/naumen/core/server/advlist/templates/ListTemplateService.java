package ru.naumen.core.server.advlist.templates;

import java.util.List;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.templates.list.ListTemplate;
import ru.naumen.metainfo.shared.templates.list.usage.ListTemplateUsagePoint;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ObjectListBase;

/**
 * Сервис для работы с шаблонами списков
 * <AUTHOR>
 * @since 06.04.2018
 */
public interface ListTemplateService
{
    /**
     * Сохраняет новый шаблон списков. Проверяет код шаблона на уникальность.
     * @param template шаблон списков
     */
    void addTemplate(ListTemplate template);

    /**
     * Добавляет для указанного шаблон списка место использования
     * @param template шаблон списка
     * @param usagePoint место использования
     */
    void addUsagePointInListTemplate(ListTemplate template, ListTemplateUsagePoint usagePoint);

    /**
     * Удаляет шаблоны списков с указанными кодами.
     * @param codes коды шаблонов списков
     */
    void deleteTemplates(String... codes);

    /**
     * Возвращает шаблоны списков, подходящих по классу, типу и группе
     * @return шаблоны списков
     */
    List<ListTemplate> findByFqns(ClassFqn classFqn, List<ClassFqn> casesFqns, String attrGroupCode);

    /**
     * Возвращает все имеющиеся в системе шаблоны списков
     * @return все шаблоны списков
     */
    List<ListTemplate> getAll();

    /**
     * Возвращает шаблон списка, имеющий указанный код.
     * @param code код шаблона
     * @return шаблон списка или null, если шаблона с указанным кодом не существует
     */
    @Nullable
    ListTemplate getTemplate(String code);

    /**
     * Подходящий ли шаблон template списка по классу, типу и группе
     */
    boolean isSuitableTemplate(ListTemplate template, ClassFqn classFqn, List<ClassFqn> casesFqns,
            String attrGroupCode);

    /**
     * Удаляет из шаблонов списков все места использования данного списка
     * @param fqn класса
     * @param formCode код формы
     * @param deleteContent контент для которого необходимо удалить все места использования для всех списков
     */
    void removeAllUsagePointInListTemplates(ClassFqn fqn, String formCode, Content deleteContent);

    /**
     * Удаляет из шаблона списков место использования
     * @param template шаблон списка
     * @param usagePoint место использования
     */
    void removeUsagePointInListTemplates(ListTemplate template, ListTemplateUsagePoint usagePoint);

    /**
     * Сохраняет указанный шаблон списка
     * @param template шаблон списка
     */
    void saveTemplate(ListTemplate template);

    /**
     * Сериализует список в String
     * @param listTemplate список
     */
    String serializeListTemplate(ObjectListBase listTemplate);
}
