package ru.naumen.core.server.script.api.injection;

import org.codehaus.groovy.transform.GroovyASTTransformationClass;

/**
 * Аннотация маркер, вспомогательная, чтобы отрабатывала AST трансформация
 * {@link ModuleClassesApiInjectionASTTransformation}, которая в свою очередь внедряет наши контекстные переменные
 * (api, utils и т.д.) как свойства класса над которыми установлена аннотация {@link InjectApi}
 *
 * <AUTHOR>
 * @since 19.07.2021
 */
@GroovyASTTransformationClass({
        "ru.naumen.core.server.script.api.injection.ModuleClassesApiInjectionASTTransformation" })
public @interface InjectApiLocal
{
}
