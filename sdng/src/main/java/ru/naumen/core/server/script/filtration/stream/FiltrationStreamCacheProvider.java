package ru.naumen.core.server.script.filtration.stream;

import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Set;
import java.util.concurrent.TimeUnit;

import org.infinispan.Cache;
import org.infinispan.commons.dataconversion.MediaType;
import org.infinispan.configuration.cache.Configuration;
import org.infinispan.configuration.cache.ConfigurationBuilder;
import org.infinispan.manager.EmbeddedCacheManager;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.InfinispanCacheManager;
import ru.naumen.core.server.script.IQueryDto;

/**
 * Управление и настройка конфигурации кэша для хранения selectable-элементов фильтрованных списков и деревьев
 * <AUTHOR>
 * @since 1.04.2018
 */
@Component
public class FiltrationStreamCacheProvider
{
    private final Configuration defConfig;
    private final EmbeddedCacheManager embeddedCacheManager;

    @Inject
    public FiltrationStreamCacheProvider(InfinispanCacheManager cacheManager)
    {
        this.embeddedCacheManager = cacheManager.getManager();
        defConfig = defaultCachesConfiguration();
    }

    public Cache<Object, Object> getCache(String filterKey)
    {
        Cache<Object, Object> cache = embeddedCacheManager.getCache(filterKey, false);
        if (null != cache)
        {
            return cache;
        }
        embeddedCacheManager.defineConfiguration(filterKey, defConfig);
        cache = embeddedCacheManager.getCache(filterKey);
        cache.start();
        return cache;
    }

    /**
     * Получение кэша selectable-элементов, соответствующего стриму фильтрации
     * @param stream стрим фильтрации
     * @return кэш selectable-элементов
     */
    public Cache<Object, Object> getCacheForStream(IQueryDto stream)
    {
        Cache<Object, Object> cache = embeddedCacheManager.getCache(stream.getFilterTitle(), false);
        if (null != cache)
        {
            cache.startBatch();
            Set<Object> keys = Set.copyOf(cache.keySet());
            if (!keys.isEmpty())
            {
                keys.forEach(cache::remove);
            }
            cache.endBatch(true);
        }
        else
        {
            embeddedCacheManager.defineConfiguration(stream.getFilterTitle(), defConfig);
            cache = embeddedCacheManager.getCache(stream.getFilterTitle());
            cache.start();
        }
        return cache;
    }

    /**
     * Настройка конфигурации кэшей selectable-элементов по умолчанию
     */
    private static Configuration defaultCachesConfiguration()
    {
        final Path storageDir = Paths.get(System.getProperty("java.io.tmpdir"))
                .resolve("filtrationStreamCache");
        return new ConfigurationBuilder()
                .memory().maxCount(1)
                .expiration().lifespan(1, TimeUnit.HOURS).maxIdle(1, TimeUnit.HOURS)
                .persistence()
                .passivation(true)
                .addSoftIndexFileStore()
                .dataLocation(storageDir.resolve("dataStore").toString())
                .indexLocation(storageDir.resolve("indexStore").toString())
                .purgeOnStartup(false)
                .encoding()
                .mediaType(MediaType.APPLICATION_SERIALIZED_OBJECT)
                .invocationBatching()
                .enable().build();
    }
}
