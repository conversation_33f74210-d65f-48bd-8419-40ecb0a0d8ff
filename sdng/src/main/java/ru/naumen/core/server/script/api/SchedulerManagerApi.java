package ru.naumen.core.server.script.api;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.core.server.scheduler.service.QuartzRecoveryService;

/**
 * API Работы с планировщиком
 * <AUTHOR>
 * @since 4.5.4
 * @see ISchedulerManagerApi
 *
 */
@Component("schedulerManager")
public class SchedulerManagerApi implements ISchedulerManagerApi
{
    @Inject
    private QuartzRecoveryService recoverService;

    @Override
    public void recover()
    {
        recoverService.executeRecovery();
    }

}
