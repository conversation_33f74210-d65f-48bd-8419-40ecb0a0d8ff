package ru.naumen.core.server.script.storage.modification.usage;

import java.util.Collection;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.storage.ScriptUsageUtils;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Контракт поведения при изменении скрипта в правиле обработки почты
 * Переопределяет специфичные действия при редактировании.
 * Основная логика редактирования содержится в {@link ScriptModifyProcessBase}
 * <AUTHOR>
 * @since Nov 5, 2015
 */
@Component
public class MailScriptModifyProcess extends ScriptModifyProcessSupport<MailProcessorRule>
{
    @Override
    protected String getLocation(MailProcessorRule holder, ScriptModifyContext context)
    {
        return ScriptUsageUtils.getMailProcessorRuleLocation(holder);
    }

    @Override
    protected String getOldScriptCode(@Nullable MailProcessorRule oldHolder, ScriptModifyContext context)
    {
        return oldHolder == null ? null : oldHolder.getScript();
    }

    @Override
    protected Collection<ClassFqn> getRelatedMetaClasses(MailProcessorRule holder, ScriptModifyContext context)
    {
        return ScriptUsageUtils.getMailProcessorRuleFqns();
    }

    @Override
    protected boolean isUsageChangeable()
    {
        return false;
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, MailProcessorRule holder,
            ScriptModifyContext context)
    {
        holder.setScript(newScriptCode);
    }
}
