package ru.naumen.core.server.script.api;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringWriter;

import jakarta.inject.Inject;

import javax.xml.parsers.ParserConfigurationException;
import javax.xml.transform.OutputKeys;
import javax.xml.transform.Result;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerConfigurationException;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.TransformerFactoryConfigurationError;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;

import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.xml.sax.SAXException;

import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.ConfigurationProperties;

/**
 * API для работы с XML документами
 *
 * <AUTHOR>
 * @since 4.3.11
 */
@Component("xml")
public class XMLApi implements IXMLApi
{
    private static final String YES = "yes";

    private final ConfigurationProperties configurationProperties;

    @Inject
    public XMLApi(ConfigurationProperties configurationProperties)
    {
        this.configurationProperties = configurationProperties;
    }

    @Override
    public String convertDocToString(Document doc)
    {
        Transformer transformer = getTransformer();
        transformer.setOutputProperty(OutputKeys.METHOD, "xml");
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, YES);

        StringWriter writer = new StringWriter();
        DOMSource source = new DOMSource(doc);
        StreamResult result = new StreamResult(writer);

        try
        {
            transformer.transform(source, result);
        }
        catch (TransformerException e)
        {
            throw new FxException(e);
        }
        return writer.toString();
    }

    @Override
    public Document formatDocument(Document doc)
    {
        Transformer transformer = getTransformer();
        transformer.setOutputProperty(OutputKeys.METHOD, "xml");
        transformer.setOutputProperty(OutputKeys.INDENT, YES);
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, YES);
        transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

        DOMSource source = new DOMSource(doc);

        try (ByteArrayOutputStream os = new ByteArrayOutputStream())
        {
            Result result = new StreamResult(os);
            transformer.transform(source, result);
            return getDocument(os.toByteArray());
        }
        catch (TransformerException | IOException e)
        {
            throw new FxException(e);
        }
    }

    @Override
    public InputStream formatXML(InputStream is)
    {
        Document doc = null;
        try
        {
            doc = XmlUtils.getDocument(is, configurationProperties.isProcessingExternalEntityInXML());
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }

        Transformer transformer = getTransformer();
        transformer.setOutputProperty(OutputKeys.METHOD, "xml");
        transformer.setOutputProperty(OutputKeys.INDENT, YES);
        transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, YES);
        transformer.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");

        DOMSource source = new DOMSource(doc);

        try (ByteArrayOutputStream os = new ByteArrayOutputStream())
        {
            Result result = new StreamResult(os);
            transformer.transform(source, result);
            return new ByteArrayInputStream(os.toByteArray());
        }
        catch (TransformerException | IOException e)
        {
            throw new FxException(e);
        }
    }

    @Override
    public Document getDocument(byte[] xml)
    {
        try
        {
            return XmlUtils.getDocument(xml, configurationProperties.isProcessingExternalEntityInXML());
        }
        catch (SAXException | IOException | ParserConfigurationException e)
        {
            throw new FxException(e);
        }
    }

    @Override
    public Document getDocument(InputStream in)
    {
        try
        {
            return XmlUtils.getDocument(in, configurationProperties.isProcessingExternalEntityInXML());
        }
        catch (SAXException | IOException | ParserConfigurationException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Получить класс трансформации объектов одного типа в другой.
     * @return transformer
     */
    private Transformer getTransformer()
    {
        Transformer transformer = null;
        try
        {
            transformer = TransformerFactory.newInstance().newTransformer();
        }
        catch (TransformerFactoryConfigurationError | TransformerConfigurationException e)
        {
            throw new FxException(e);
        }
        return transformer;
    }
}
