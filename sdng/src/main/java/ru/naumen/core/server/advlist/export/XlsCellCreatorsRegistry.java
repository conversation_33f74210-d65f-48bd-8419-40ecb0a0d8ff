package ru.naumen.core.server.advlist.export;

import java.util.Map;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.SpringContext;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @since 15.12.2011
 *
 */
@Component
public class XlsCellCreatorsRegistry implements IXlsCellCreatorsRegistry
{
    @Inject
    SpringContext ctx;
    Map<String, XlsCellCreator> creators = new HashMap<>();

    @Override
    public XlsCellCreator getCellCreator(String attrCode, String attrTypeCode, String presentationCode)
    {
        XlsCellCreator creator = creators.get(getCellCreatorKey(attrCode, attrTypeCode, presentationCode));
        if (null == creator)
        {
            return creators.get(DEFAULT_CREATOR);
        }
        return creator;
    }

    @Override
    public XlsCellCreator getDefaultCellCreator()
    {
        return creators.get(DEFAULT_CREATOR);
    }

    @PostConstruct
    void init()
    {
        for (XlsCellCreator creator : ctx.getBeans(XlsCellCreator.class).values())
        {
            CellCreatorComponent cmp = creator.getClass().getAnnotation(CellCreatorComponent.class);
            if (null == cmp)
            {
                throw new FxException("Illegal cellCreator: " + creator.getClass());
            }
            for (String code : cmp.codes())
            {
                creators.put(code, creator);
            }
        }
    }

    private String getCellCreatorKey(String attrCode, String attrTypeCode, String presentationCode)
    {
        if (creators.containsKey(attrCode))
        {
            return attrCode;
        }
        String typePresentation = attrTypeCode + PRESENTATION_SEPARATOR + presentationCode;
        if (creators.containsKey(typePresentation))
        {
            return typePresentation;
        }
        return attrTypeCode;
    }
}
