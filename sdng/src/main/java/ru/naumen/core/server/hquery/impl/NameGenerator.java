package ru.naumen.core.server.hquery.impl;

/**
 * <AUTHOR>
 * @since 01.11.2011
 */
public class NameGenerator
{
    private final String prefix;
    private int counter;
    private final int max;

    public NameGenerator(NameGenerator other)
    {
        this(other.prefix, other.counter, other.max);
    }

    public NameGenerator(String prefix)
    {
        this(prefix, 0, -1);
    }

    public NameGenerator(String prefix, int max)
    {
        this(prefix, 0, max);
    }

    public NameGenerator(String prefix, int counter, int max)
    {
        this.prefix = prefix;
        this.counter = counter;
        this.max = max;
    }

    public String next()
    {
        if (this.max >= 0 && this.counter >= this.max)
        {
            throw new IllegalStateException("Names counter range exceeded: " + max);
        }
        return prefix + counter++;
    }
}
