package ru.naumen.core.server.script.libraries.storage;

import java.nio.file.Path;
import java.util.Collection;
import java.util.List;
import java.util.Set;
import java.util.stream.Stream;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;

/**
 * Хранилище библиотек
 * <AUTHOR>
 * @since 20.05.2020
 */
public interface LibraryStorage
{

    /**
     * Получение библиотеки по её имени.
     * @param name имя получаемой библиотеки;
     * @return желаемая библиотека;
     */
    @Nullable
    ScriptLibrary getLibrary(String name);

    /**
     * Получить путь до файла для указанной библиотеки
     */
    Path getLibraryPath(ScriptLibrary library);

    /**
     * Получить поток путей сохраненных непросроченных библиотек
     */
    Stream<ScriptLibrary> getNonExpiredLibraries();

    /**
     * Сохранить заданную библиотеку
     * @param newLibrary библиотека для сохранения;
     * @return путь сохраненной библиотеки.
     */
    Path save(ScriptLibrary newLibrary);

    /**
     * Удалить библиотеку по заданному имени библиотеки
     * @param library имя библиотеки
     *
     * @return true - произошло удаление, false - ничего не изменилось.
     */
    boolean delete(ScriptLibrary library);

    /**
     * @return набор имен сохраненных библиотек
     */
    Set<String> getLibrariesNames();

    /**
     * Возвращает информацию обо всех сохраненных библиотеках.
     * @return коллекция с информацией обо всех библиотеках
     */
    Collection<ScriptLibrary> getAllLibraries();

    /**
     * Перезагрузить хранилище библиотек
     * Вычитываются все имеющиеся библиотеки из основного хранилища (БД) и сохраняются на диск
     * @return список путей сохраненных библиотек
     */
    List<Path> reload();

    /**
     * Перезагрузить хранилище библиотек
     * Вычитываются все имеющиеся библиотеки из основного хранилища (БД) и сохраняются на диск
     * От метода reload() отличается тем, что удаляет все с диска по старым путям, тк у нас нет иного способа
     * понять, что библитеку удалили или загрузили новую версия, как только все очистить и загрузить новое
     * @return список путей сохраненных библиотек
     */
    List<Path> reset();
}
