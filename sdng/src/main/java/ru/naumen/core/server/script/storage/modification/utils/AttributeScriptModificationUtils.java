package ru.naumen.core.server.script.storage.modification.utils;

import java.util.List;
import java.util.Set;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.customforms.FormParameter;
import ru.naumen.core.server.flex.attr.AttrStrategyFactory;
import ru.naumen.core.server.script.storage.modification.usage.AbstractAttributeScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.shared.script.places.AttributeCategories;
import ru.naumen.core.shared.script.places.FormParameterScriptCategories;
import ru.naumen.core.shared.script.places.ScriptCategory;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.metainfo.server.spi.dispatch.HandlerUtils;
import ru.naumen.metainfo.server.spi.elements.AbstractAttributeInfo;
import ru.naumen.metainfo.server.spi.elements.AttributeImpl;
import ru.naumen.metainfo.server.spi.elements.DeclaredAttributeImpl;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants.Accessors;
import ru.naumen.metainfo.shared.elements.HasDateTimeRestriction.RestrictionType;
import ru.naumen.metainfo.shared.elements.HasScripts;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;

/**
 * Утилитарные методы редактирования скриптов атрибутов метакласса
 * содержат логику создания, редактирования, удаления скриптов
 * <AUTHOR>
 * @since 12.09.2015
 */
@Component
public class AttributeScriptModificationUtils
{
    @Inject
    private AttrStrategyFactory attrStrategyFactory;
    @Inject
    private ResolverUtils resolverUtils;
    @Inject
    private HandlerUtils handlerUtils;
    @Inject
    private ScriptModifyRegistry scriptModifyRegistry;

    public void processDeleteAttributeScripts(@Nullable HasScripts attr, List<ScriptAdminLogInfo> scriptsLog)
    {
        if (attr != null)
        {
            scriptsLog.addAll(resetAttributeScripts(attr));
        }
    }

    public void processDeleteMetaClassAttributesScripts(MetaClassImpl metaClass, List<ScriptAdminLogInfo> scriptsLog)
    {
        for (AbstractAttributeInfo attribute : metaClass.getDefinedAttributes())
        {
            processDeleteAttributeScripts(attribute, scriptsLog);
        }
    }

    public void processUpdateAttributesScriptsAfterCopy(Set<AbstractAttributeInfo> attrs, MetaClassImpl metaClass,
            List<ScriptAdminLogInfo> scriptsLog)
    {
        for (AbstractAttributeInfo attr : attrs)
        {
            updateScriptAfterCopy(attr, metaClass, AttributeCategories.COMPUTABLE_ON_FORM, scriptsLog);
            updateScriptAfterCopy(attr, metaClass, AttributeCategories.DEFAULT_VALUE, scriptsLog);
            updateScriptAfterCopy(attr, metaClass, AttributeCategories.FILTRATION, scriptsLog);
            updateScriptAfterCopy(attr, metaClass, AttributeCategories.COMPUTABLE, scriptsLog);
            updateScriptAfterCopy(attr, metaClass, AttributeCategories.DATE_TIME_RESTRICTION, scriptsLog);
        }
    }

    public List<ScriptAdminLogInfo> resetAttributeScripts(HasScripts attr)
    {
        ScriptModifyProcess<HasScripts> process = scriptModifyRegistry.getProcess(attr);
        ScriptModifyContext context = new ScriptModifyContext(ScriptHolders.ATTRIBUTE);

        AttributeFqn attrFqn = new AttributeFqn(attr.getMetaClass().getFqn(), attr.getCode());
        context.put(AbstractAttributeScriptModifyProcess.ATTR_FQN, attrFqn.toString());

        if (Boolean.TRUE.equals(attr.isDefaultByScript()))
        {
            if (attr instanceof FormParameter)
            {
                context.setCategory(FormParameterScriptCategories.PARAM_DEFAULT_VALUE);
            }
            else
            {
                context.setCategory(AttributeCategories.DEFAULT_VALUE);
            }
            process.deleteHolder(attr, context);
        }
        if (Boolean.TRUE.equals(attr.isFilteredByScript()))
        {
            if (attr instanceof FormParameter)
            {
                context.setCategory(FormParameterScriptCategories.PARAM_FILTRATION);
            }
            else
            {
                context.setCategory(AttributeCategories.FILTRATION);
            }
            process.deleteHolder(attr, context);
        }
        if (Boolean.TRUE.equals(attr.isComputableOnForm()))
        {
            if (attr instanceof FormParameter)
            {
                context.setCategory(FormParameterScriptCategories.PARAM_COMPUTABLE_ON_FORM);
            }
            else
            {
                context.setCategory(AttributeCategories.COMPUTABLE_ON_FORM);
            }
            process.deleteHolder(attr, context);
        }
        if (Boolean.TRUE.equals(attr.isComputable()))
        {
            context.setCategory(AttributeCategories.COMPUTABLE);
            process.deleteHolder(attr, context);
        }
        if (attr.getDateTimeRestrictionType() == RestrictionType.RESTRICTION_BY_SCRIPT)
        {
            context.setCategory(attr instanceof FormParameter
                    ? FormParameterScriptCategories.PARAM_DATE_TIME_RESTRICTION
                    : AttributeCategories.DATE_TIME_RESTRICTION);
            process.deleteHolder(attr, context);
        }
        if (Boolean.TRUE.equals(attr.isComputableByAnyCatalogElementsScript()))
        {
            context.setCategory(FormParameterScriptCategories.PARAM_COMPUTE_ANY_CATALOG_ELEMENTS);
            process.deleteHolder(attr, context);
        }
        return context.getScriptsLogInfo();
    }

    public void setAttrComputedOnForm(boolean isComputableOnForm, ScriptDto computableOnFormScript,
            AbstractAttributeInfo attr, List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        setComputedOnForm(isComputableOnForm, computableOnFormScript, AttributeCategories.COMPUTABLE_ON_FORM, attr,
                scriptsForSave, scriptsLog);
    }

    public void setAttrDefaultValue(AttributeImpl attribute, AbstractAttributeInfo attr, Boolean defaultByScript,
            boolean hasDefaultValue, @Nullable Object defaultValue, ScriptDto scriptForDefault,
            List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        attr.setDefaultByScript(Boolean.TRUE.equals(defaultByScript));

        if (hasDefaultValue && !attr.isDefaultByScript())
        {
            defaultValue = handlerUtils
                    .transferDefaultValue(resolverUtils.resolv(new ResolverContext(attribute, defaultValue)));
        }
        else
        {
            defaultValue = null;
        }
        attr.setDefaultValue(defaultValue);

        setScriptValueWithCondition(attr.isDefaultByScript(), attr, scriptForDefault, AttributeCategories.DEFAULT_VALUE,
                scriptsForSave, scriptsLog);
    }

    public void setAttrFiltrationByScript(boolean isFilteredByScript, ScriptDto scriptForFiltration, HasScripts attr,
            List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        setAttrFiltrationByScript(isFilteredByScript, scriptForFiltration, AttributeCategories.FILTRATION, attr,
                scriptsForSave, scriptsLog);
    }

    public void setAttrScriptOnAdd(boolean isComputable, ScriptDto script, DeclaredAttributeImpl attr,
            AttributeImpl attribute, List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        attr.setComputable(isComputable);

        if (attr.isComputable())
        {
            attr.setAccessor(Accessors.COMPUTABLE);
        }
        else
        {
            attr.setAccessor(attrStrategyFactory.getStrategy(attribute).getAccessor());
        }

        setScriptValueWithCondition(attr.isComputable(), attr, script, AttributeCategories.COMPUTABLE, scriptsForSave,
                scriptsLog);
    }

    public void setAttrScriptOnEdit(boolean isComputable, ScriptDto script, MetaClassImpl metaClass,
            AbstractAttributeInfo attr, List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        attr.setComputable(isComputable);

        setScriptValueWithCondition(attr.isComputable(), attr, script, AttributeCategories.COMPUTABLE, scriptsForSave,
                scriptsLog);
    }

    /**
     * Установить или убрать проверку значения даты/времени скриптом
     *
     * @param isDateTimeRestrictionAttr - признак валидации атрибута Дата/Время скриптом
     * @param dateTimeRestrictionScript - скрипт проверки занчения
     * @param attr - модель атрибута
     * @param scriptsForSave - список скриптов для сохранения (куда будет добавлен сохраняемый скрипт)
     * @param scriptsLog - записи лога скриптов
     */
    public void setDateTimeRestrictionScript(boolean isDateTimeRestrictionAttr,
            @Nullable ScriptDto dateTimeRestrictionScript,
            AbstractAttributeInfo attr, List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        setScriptValueWithCondition(isDateTimeRestrictionAttr, attr, dateTimeRestrictionScript,
                AttributeCategories.DATE_TIME_RESTRICTION, scriptsForSave, scriptsLog);
    }

    /**
     * Установить или убрать вычислимое значение по-умолчанию для параметра настраиваемой формы
     *
     * @param isComputableAnyCatalogElements - вычислимый скриптом вычисления элементов справочника
     * @param parameter - параметр
     * @param script - скрипт вычисления элементов справочника
     * @param scriptsForSave - список скриптов для сохранения (куда будет добавлен сохраняемый скрипт)
     * @param scriptsLog - записи лога скриптов
     */
    public void setParamComputeCatalogAnyElementsScript(boolean isComputableAnyCatalogElements, FormParameter parameter,
            @Nullable ScriptDto script, List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        ScriptDto nullCheckedScript = null == script ? new ScriptDto() : script;
        setScriptValueWithCondition(isComputableAnyCatalogElements, parameter, nullCheckedScript,
                FormParameterScriptCategories.PARAM_COMPUTE_ANY_CATALOG_ELEMENTS, scriptsForSave, scriptsLog);
    }

    /**
     * Установить или убрать вычисление значений для параметра настраиваемой формы
     *
     * @param isFilteredByScript - вычислимый на форме
     * @param parameter - параметр
     * @param script - скрипт вычисление значения
     * @param scriptsForSave - список скриптов для сохранения (куда будет добавлен сохраняемый скрипт)
     * @param scriptsLog - записи лога скриптов
     */
    public void setParamComputedOnForm(boolean isComputableOnForm, ScriptDto computableOnFormScript,
            FormParameter param, List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        setComputedOnForm(isComputableOnForm, computableOnFormScript,
                FormParameterScriptCategories.PARAM_COMPUTABLE_ON_FORM, param, scriptsForSave, scriptsLog);
    }

    public void setParamDateTimeRestriction(boolean condition, FormParameter parameter,
            ScriptDto dateTimeRestrictionScript, List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        setScriptValueWithCondition(condition, parameter, dateTimeRestrictionScript,
                FormParameterScriptCategories.PARAM_DATE_TIME_RESTRICTION, scriptsForSave, scriptsLog);
    }

    /**
     * Установить или убрать вычислимое значение по-умолчанию для параметра настраиваемой формы
     *
     * @param defaultByScript - вычислимый по-умолчанию
     * @param parameter - параметр
     * @param script - скрипт вычисления значения по-умолчанию
     * @param scriptsForSave - список скриптов для сохранения (куда будет добавлен сохраняемый скрипт)
     * @param scriptsLog - записи лога скриптов
     */
    public void setParamDefaultByScript(boolean defaultByScript, FormParameter parameter, ScriptDto script,
            List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        parameter.setDefaultByScript(defaultByScript);
        setScriptValueWithCondition(parameter.isDefaultByScript(), parameter, script,
                FormParameterScriptCategories.PARAM_DEFAULT_VALUE, scriptsForSave, scriptsLog);
    }

    /**
     * Установить или убрать фильтрацию значений для параметра настраиваемой формы
     *
     * @param isFilteredByScript - фильтруемый на форме
     * @param parameter - параметр
     * @param script - скрипт фильтрации
     * @param scriptsForSave - список скриптов для сохранения (куда будет добавлен сохраняемый скрипт)
     * @param scriptsLog - записи лога скриптов
     */
    public void setParamFiltrationByScript(boolean isFilteredByScript, ScriptDto scriptForFiltration, HasScripts param,
            List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        setAttrFiltrationByScript(isFilteredByScript, scriptForFiltration,
                FormParameterScriptCategories.PARAM_FILTRATION, param, scriptsForSave, scriptsLog);
    }

    private void setAttrFiltrationByScript(boolean isFilteredByScript, ScriptDto scriptForFiltration,
            ScriptCategory category, HasScripts attr, List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        attr.setFilteredByScript(isFilteredByScript);
        setScriptValueWithCondition(attr.isFilteredByScript(), attr, scriptForFiltration, category, scriptsForSave,
                scriptsLog);
    }

    private void setComputedOnForm(boolean isComputableOnForm, ScriptDto computableOnFormScript,
            ScriptCategory category, HasScripts holder, List<Script> scriptsForSave,
            List<ScriptAdminLogInfo> scriptsLog)
    {
        holder.setComputableOnForm(isComputableOnForm);

        setScriptValueWithCondition(holder.isComputableOnForm(), holder, computableOnFormScript, category,
                scriptsForSave, scriptsLog);
    }

    private void setScriptValueWithCondition(boolean condition, HasScripts attr, ScriptDto script,
            ScriptCategory category, List<Script> scriptsForSave, List<ScriptAdminLogInfo> scriptsLog)
    {
        ScriptModifyProcess<HasScripts> process = scriptModifyRegistry.getProcess(attr);
        ScriptModifyContext context = new ScriptModifyContext(category, ScriptHolders.ATTRIBUTE);

        AttributeFqn attrFqn = new AttributeFqn(attr.getMetaClass().getFqn(), attr.getCode());
        context.put(AbstractAttributeScriptModifyProcess.ATTR_FQN, attrFqn.toString());

        if (condition)
        {
            context.setDefferedPersist(true);
            process.save(attr, attr, script, context);
            scriptsForSave.addAll(context.getScriptsForSave());
        }
        else
        {
            process.save(attr, attr, ScriptDtoFactory.createWithout(), context);
        }
        scriptsLog.addAll(context.getScriptsLogInfo());
    }

    private void updateScriptAfterCopy(AbstractAttributeInfo attr, MetaClassImpl metaClass,
            AttributeCategories category, List<ScriptAdminLogInfo> scriptsLog)
    {
        ScriptModifyProcess<AbstractAttributeInfo> process = scriptModifyRegistry.getProcess(attr);
        ScriptModifyContext context = new ScriptModifyContext(category, ScriptHolders.ATTRIBUTE);

        AttributeFqn attrFqn = new AttributeFqn(metaClass.getFqn(), attr.getCode());
        context.put(AbstractAttributeScriptModifyProcess.ATTR_FQN, attrFqn.toString());
        process.copyHolder(attr, context);
        scriptsLog.addAll(context.getScriptsLogInfo());
    }

}
