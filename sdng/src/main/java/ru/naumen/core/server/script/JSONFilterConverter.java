package ru.naumen.core.server.script;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;

import ru.naumen.core.server.util.JsonUtils;
import ru.naumen.core.shared.activity.AbstractTokenizer;
import ru.naumen.fts.shared.SearchFilters;
import ru.naumen.metainfo.shared.AttributeFqn;

/**
 * Реализация конвертации {@link SearchFilters.SearchFilter SearchFilter} в JSON для серверного кода.
 *
 * Есть отдельная реализация для клиентского кода
 * {@link ru.naumen.fts.client.extended.result.ExtendedSearchResultsPlace.JSONFilterConverter JSONFilterConverter}..
 *
 * <AUTHOR>
 * @since 07.10.20
 */
public class JSONFilterConverter implements AbstractTokenizer.Converter<SearchFilters.SearchFilter>
{
    private static final String TYPE_CODE = "type";
    private static final String FILTER_TYPE_AND = "and";
    private static final String FILTER_TYPE_OR = "or";
    private static final String FILTER_TYPE_EQ = "eq";
    private static final String FILTERS_CODE = "filters";
    private static final String ATTRIBUTE_CODE = "attr";
    private static final String VALUE_CODE = "value";
    private static final String METACLASS_CODE = "mcls";

    public static String toJSON(SearchFilters.SearchFilter filter)
    {
        return JsonUtils.toJson(JsonUtils.MAPPER, toJSONObject(filter));
    }

    private static ObjectNode toJSONObject(SearchFilters.SearchFilter filter)
    {
        ObjectMapper mapper = JsonUtils.MAPPER;
        ObjectNode obj = mapper.createObjectNode();

        if (filter instanceof SearchFilters.And)
        {
            obj.put(TYPE_CODE, FILTER_TYPE_AND);
            ArrayNode arr = mapper.createArrayNode();
            int i = 0;
            for (SearchFilters.SearchFilter f : ((SearchFilters.And)filter).getFilters())
            {
                arr.add(toJSONObject(f));
            }
            obj.set(FILTERS_CODE, arr);
        }
        else if (filter instanceof SearchFilters.Or)
        {
            obj.put(TYPE_CODE, FILTER_TYPE_OR);
            ArrayNode arr = mapper.createArrayNode();
            int i = 0;
            for (SearchFilters.SearchFilter f : ((SearchFilters.Or)filter).getFilters())
            {
                arr.add(toJSONObject(f));
            }
            obj.set(FILTERS_CODE, arr);
        }
        else if (filter instanceof SearchFilters.Eq)
        {
            SearchFilters.Eq eq = (SearchFilters.Eq)filter;
            obj.put(TYPE_CODE, FILTER_TYPE_EQ);
            obj.put(ATTRIBUTE_CODE, new AttributeFqn(eq.getAttrMetaClass(), eq.getAttribute()).toString());
            obj.put(METACLASS_CODE, ((SearchFilters.Eq)filter).getMetaClass().toString());
            obj.put(VALUE_CODE, eq.getValue());
        }
        return obj;
    }

    @Override
    public SearchFilters.SearchFilter toObject(String value)
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public String toString(SearchFilters.SearchFilter obj)
    {
        return toJSON(obj);
    }
}
