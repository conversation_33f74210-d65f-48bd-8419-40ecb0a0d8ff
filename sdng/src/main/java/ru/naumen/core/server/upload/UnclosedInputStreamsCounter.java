package ru.naumen.core.server.upload;

/**
 * Счётчик потоков InputStream, которые не были закрыты должным образом. Учитывает только обёрнутые в
 * {@link UnclosedInputStreamsCounter} потоки.
 *
 * @see InputStreamWithLogger
 * <AUTHOR>
 *
 */
public class UnclosedInputStreamsCounter
{
    private static final UnclosedInputStreamsCounter INSTANCE = new UnclosedInputStreamsCounter();

    public static UnclosedInputStreamsCounter get()
    {
        return INSTANCE;
    }

    private int counter = 0;
    private String firstStack = null;

    public int countUnclosedStream(String stackTrace)
    {
        counter++;
        if (firstStack == null)
        {
            firstStack = stackTrace;
        }
        return counter;
    }

    public int getCounter()
    {
        return counter;
    }
}
