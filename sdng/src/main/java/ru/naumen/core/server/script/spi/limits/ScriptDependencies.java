package ru.naumen.core.server.script.spi.limits;

import java.util.Collection;
import java.util.List;
import java.util.Map;

/**
 * Содержит список импортов и вызовов методов из скрипта.
 */
public class ScriptDependencies
{
    /**
     * Типы логируемых вызовов.
     */
    public static enum CallTypes
    {
        /**
         * Вызовы методов на биндингах и динамических переменных.
         */
        DYNAMIC,
        /**
         * Другие вызовы.
         */
        OTHER,
        /**
         * Вызовы статических методов.
         */
        STATIC,
        /**
         * Вызовы конструкторов. 
         */
        CONSTRUCTOR,
        /**
         * Вызовы атрибутов.
         */
        ATTRIBUTE,
        /**
         * Вызовы свойств.
         */
        PROPERTY,
        /**
         * Вызовы указателей на методы.
         */
        METHOD_POINTER;
    }

    /**
     * Типы логируемых импортов.
     */
    public static enum ImportTypes
    {
        /**
         * Обычные импорты. 
         */
        USUAL,
        /**
         * Импорты со '*'. 
         */
        STAR,
        /**
         * Статические импорты. 
         */
        STATIC,
        /**
         * Статические импорты со '*'. 
         */
        STATIC_STAR;
    }

    private Map<CallTypes, Map<String, Collection<CallInfo>>> calls;
    private Map<ImportTypes, List<String>> imports;

    public ScriptDependencies()
    {

    }

    public ScriptDependencies(Map<CallTypes, Map<String, Collection<CallInfo>>> calls,
            Map<ImportTypes, List<String>> imports)
    {
        this.calls = calls;
        this.setImports(imports);
    }

    public Map<CallTypes, Map<String, Collection<CallInfo>>> getCalls()
    {
        return calls;
    }

    public Map<ImportTypes, List<String>> getImports()
    {
        return imports;
    }

    public void setCalls(Map<CallTypes, Map<String, Collection<CallInfo>>> calls)
    {
        this.calls = calls;
    }

    public void setImports(Map<ImportTypes, List<String>> imports)
    {
        this.imports = imports;
    }

    @Override
    public String toString()
    {
        return "ScriptInfo [calls=" + calls + ", imports=" + imports + "]";
    }

}
