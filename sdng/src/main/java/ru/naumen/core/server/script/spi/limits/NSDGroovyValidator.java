package ru.naumen.core.server.script.spi.limits;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import org.codehaus.groovy.control.CompilationFailedException;

import ru.naumen.core.server.script.spi.limits.ScriptDependencies.CallTypes;
import ru.naumen.core.server.script.spi.limits.ScriptDependencies.ImportTypes;

import java.util.ArrayList;

/**
 * Проверяет информацию о скрипте на предмет нарушения ограничений (на вызовы, импорты и т.п.) в
 * скриптах.
 */
public class NSDGroovyValidator
{
    /**
     * Разрешенный импорты
     */
    private volatile Collection<ScriptImportTemplate> importsWhitelist = new ArrayList<>();

    /**
     * Разрешенные вызовы методов
     */
    private volatile Collection<ScriptMethodCallTemplate> receiversClassesWhiteList = new ArrayList<>();

    public NSDGroovyValidator()
    {
        // Настройка разрешений по-умолчанию
        importsWhitelist.add(new ScriptImportTemplate("java.lang.Object", true));

        importsWhitelist.add(new ScriptImportTemplate("org.springframework.beans.factory.BeanFactory", true));
        importsWhitelist.add(new ScriptImportTemplate("org.codehaus.groovy.runtime.InvokerHelper", true));
        importsWhitelist.add(new ScriptImportTemplate("ru.naumen.core.server.script.spi.limits", true));

        // allow any call on dynamic variable (java.lang.Object)
        receiversClassesWhiteList.add(new ScriptMethodCallTemplate("java.lang.Object", null));

        // allow toString on everywhere
        receiversClassesWhiteList.add(new ScriptMethodCallTemplate(null, "toString"));
        receiversClassesWhiteList.add(new ScriptMethodCallTemplate("none", "setBinding"));

        receiversClassesWhiteList.add(new ScriptMethodCallTemplate("api", null));
        receiversClassesWhiteList.add(new ScriptMethodCallTemplate("utils", null));
        receiversClassesWhiteList.add(new ScriptMethodCallTemplate("subject", null));
        receiversClassesWhiteList.add(new ScriptMethodCallTemplate("name", null));
        receiversClassesWhiteList.add(new ScriptMethodCallTemplate("org.codehaus.groovy.runtime.InvokerHelper",
                "runScript"));
        receiversClassesWhiteList.add(new ScriptMethodCallTemplate("this", "null"));
    }

    public Collection<ScriptMethodCallTemplate> getAllowedClassMethods()
    {
        return this.receiversClassesWhiteList;
    }

    public Collection<ScriptImportTemplate> getImportsWhiteList()
    {
        return this.importsWhitelist;
    }

    public void setAllowedClassMethods(Collection<ScriptMethodCallTemplate> acmc)
    {
        this.receiversClassesWhiteList = acmc;
    }

    public void setImportsWhiteList(Collection<ScriptImportTemplate> imports)
    {
        this.importsWhitelist = imports;
    }

    public void validate(ScriptDependencies info) throws CompilationFailedException
    {
        // JMM final-tricks
        final Collection<ScriptImportTemplate> importsWhitelist = this.importsWhitelist;
        final Collection<ScriptMethodCallTemplate> receiversClassesWhiteList = this.receiversClassesWhiteList;
        final Collection<SecurityException> violations = new ArrayList<>();

        try
        {
            Map<ImportTypes, List<String>> imports = info.getImports();
            Map<CallTypes, Map<String, Collection<CallInfo>>> calls = info.getCalls();

            assertImports(importsWhitelist, imports);
            assertIndirectImports(importsWhitelist, calls);
            assertMethodCalls(receiversClassesWhiteList, calls);
        }
        catch (SecurityException ex)
        {
            violations.add(ex);
        }

        if (!violations.isEmpty())
        {
            StringBuilder sb = new StringBuilder();
            for (SecurityException ex : violations)
            {
                sb.append(ex.getMessage());
                sb.append('\n');
            }

            throw new VerifyError(sb.toString());
        }
    }

    private void assertImport(ScriptImportTemplate impord, Collection<ScriptImportTemplate> importsWhitelist)
            throws SecurityException
    {
        if (!importsWhitelist.contains(impord))
        {
            throw new SecurityException(impord.getName() + " not allowed");
        }
    }

    private void assertImports(final Collection<ScriptImportTemplate> importsWhitelist,
            Map<ImportTypes, List<String>> imports)
    {
        for (ImportTypes type : ImportTypes.values())
        {
            List<String> typedImports = imports.get(type);
            for (String impord : typedImports)
            {
                ScriptImportTemplate importTemplate = new ScriptImportTemplate(impord, true);
                assertImport(importTemplate, importsWhitelist);
            }
        }
    }

    private void assertIndirectImports(final Collection<ScriptImportTemplate> importsWhitelist,
            Map<CallTypes, Map<String, Collection<CallInfo>>> calls)
    {
        CallTypes[] typesToValidate = { CallTypes.CONSTRUCTOR, CallTypes.STATIC, CallTypes.METHOD_POINTER };
        for (CallTypes callType : typesToValidate)
        {
            Set<Entry<String, Collection<CallInfo>>> typedCalls = calls.get(callType).entrySet();
            for (Entry<String, Collection<CallInfo>> typedCallEntry : typedCalls)
            {
                ScriptImportTemplate importTemplate = new ScriptImportTemplate(typedCallEntry.getKey(), false);
                assertImport(importTemplate, importsWhitelist);
            }
        }
    }

    private void assertMethodCall(final Collection<ScriptMethodCallTemplate> receiversClassesWhiteList,
            ScriptMethodCallTemplate methodCallTemplate)
    {
        if (!receiversClassesWhiteList.contains(methodCallTemplate))
        {
            throw new SecurityException("Call of " + methodCallTemplate.getMethodName() + " method is not allowed on ["
                                        + methodCallTemplate.getClassName() + "]");
        }
    }

    private void assertMethodCalls(final Collection<ScriptMethodCallTemplate> receiversClassesWhiteList,
            Map<CallTypes, Map<String, Collection<CallInfo>>> calls)
    {
        for (CallTypes type : CallTypes.values())
        {
            Set<Entry<String, Collection<CallInfo>>> typedCalls = calls.get(type).entrySet();
            assertMethodCallType(receiversClassesWhiteList, typedCalls);
        }
    }

    private void assertMethodCallType(final Collection<ScriptMethodCallTemplate> receiversClassesWhiteList,
            Set<Entry<String, Collection<CallInfo>>> typedCalls)
    {
        for (Entry<String, Collection<CallInfo>> typedCall : typedCalls)
        {
            Collection<CallInfo> methodCalls = typedCall.getValue();
            String receiver = typedCall.getKey();
            for (CallInfo callInfo : methodCalls)
            {
                ScriptMethodCallTemplate methodCallTemplate = new ScriptMethodCallTemplate(receiver,
                        callInfo.getMethodName());
                assertMethodCall(receiversClassesWhiteList, methodCallTemplate);
            }
        }
    }
}
