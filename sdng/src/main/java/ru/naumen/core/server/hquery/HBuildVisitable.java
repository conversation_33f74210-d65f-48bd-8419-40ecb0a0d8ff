package ru.naumen.core.server.hquery;

import ru.naumen.core.server.hquery.impl.HBuilder;

/**
 * Интерфейс объектов HCriteria участвующих в построении HQL запроса
 * Реализации интерфейса не должны опираться на последовательность обхода, она не определена
 *
 * <AUTHOR>
 * @since 04 февр. 2016 г.
 */
public interface HBuildVisitable
{
    /**
     * Метод вызывается при обходе объектов до формирования HQL
     * Предназначен для установки в экземпляре {@link HBuilder} элементов из которых
     * будет сформирован запрос.
     *
     * @param builder построитель запроса
     */
    void visit(HBuilder builder);

    /**
     * Построить HQL<br>
     * Нужен для построения вложенных элементов, когда их не нужно добавлять в builder.
     * И удобно использовать внутри метода {@link #visit(HBuilder)}
     * @param builder построитель запроса
     */
    String getHQL(HBuilder builder);
}
