package ru.naumen.core.server.hquery.criterion;

import java.util.Objects;

import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * Произвольный HQL
 */
public final class HQLCriterion extends AbstractHCriterion
{
    private final String hql;

    public HQLCriterion(String hql)
    {
        super(null);
        this.hql = hql;
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        sb.append(hql);
    }

    @Override
    public String toString()
    {
        return "HQLCriterion{" +
               "hql='" + hql + '\'' +
               '}';
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new HQLCriterion(hql);
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        HQLCriterion that = (HQLCriterion)o;
        return hql.equals(that.hql);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), hql);
    }
}
