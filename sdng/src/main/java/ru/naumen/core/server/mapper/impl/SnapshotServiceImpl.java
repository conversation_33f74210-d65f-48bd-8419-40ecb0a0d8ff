package ru.naumen.core.server.mapper.impl;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.ConcurrentMap;
import java.util.function.Function;

import org.springframework.beans.factory.NoSuchBeanDefinitionException;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.server.EntityReferenceResolver;
import ru.naumen.common.server.snapshot.SnapshotContext;
import ru.naumen.common.server.snapshot.SnapshotFactory;
import ru.naumen.common.server.snapshot.SnapshotService;
import ru.naumen.common.shared.Snapshotable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.TreeUUIDIdentifiable;
import ru.naumen.core.server.common.AggregateValueResolver;
import ru.naumen.core.server.dispatch.TreeSelectionProcessor;
import ru.naumen.core.server.mapper.MappingService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.util.AttrGeneratedClassConverter;
import ru.naumen.core.server.util.HierarchyUtils;
import ru.naumen.core.shared.AggregateValue;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.IEntityReference;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.customforms.CustomForm;
import ru.naumen.core.shared.customforms.CustomFormFakeMetaClass;
import ru.naumen.core.shared.customforms.CustomForm_SnapshotObject;
import ru.naumen.core.shared.dispatch.FastSelectionChangesWithValue;
import ru.naumen.core.shared.dispatch.FastSelectionDtObjectTreeSelectionChange;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.core.shared.utils.LazyCollection;
import ru.naumen.metainfo.server.MetaClassTitleRemovedExtractor;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.TitledClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.Attribute_SnapshotObject;
import ru.naumen.metainfo.shared.elements.HasAttributes;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassForOperator_SnapshotObject;
import ru.naumen.metainfo.shared.elements.MetaClassLite_SnapshotObject;
import ru.naumen.metainfo.shared.elements.MetaClass_SnapshotObject;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

/**
 * <AUTHOR>
 */
@Component
public class SnapshotServiceImpl implements SnapshotService
{
    private static final DtoProperties REQUIRED_ENTITY_PROPERTIES = new DtoProperties(null,
            Sets.newHashSet(Constants.AbstractBO.TITLE, Constants.CatalogItem.ITEM_CODE,
                    Constants.CatalogItem.ITEM_COLOR, Constants.CatalogItem.ITEM_ICON));

    @SuppressWarnings("rawtypes")
    static Collection newCollection(Collection<?> from)
    {
        if (from instanceof List)
        {
            return Lists.newArrayListWithCapacity(from.size());
        }
        if (from instanceof Set)
        {
            return Sets.newHashSetWithExpectedSize(from.size());
        }
        return Lists.newArrayListWithCapacity(from.size());
    }

    @Inject
    SpringContext springContext;
    @Inject
    EntityReferenceResolver referenceResolver;
    @Inject
    MappingService mappingService;
    @Inject
    MetainfoService metainfoService;
    @Inject
    IPrefixObjectLoaderService loaderService;
    @Inject
    MetaClassTitleRemovedExtractor metaclassTitleRemovedExtractor;

    ConcurrentMap<Class<?>, Object> factoryCache = Maps.newConcurrentMap();

    Object NULL_CACHE_VALUE = new Object();

    @Inject
    AggregateValueResolver aggregateValueResolver;

    /**
     * {@inheritDoc}
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T> T prepare(@Nullable T value)
    {
        Class<?> snType = guessSnapshotableType(value);
        return prepare(value, (Class<T>)(snType != null ? snType : Object.class));
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <T> T prepare(@Nullable T value, Class<T> to)
    {
        return prepare(value, to, new SnapshotContext());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public <T> T prepare(@Nullable T value, Class<T> to, SnapshotContext context)
    {
        return prepare(value, to, context, false);
    }

    @Override
    public <T> T prepareStrictType(T value, Class<T> to)
    {
        return prepare(value, to, new SnapshotContext(), true);
    }

    @Override
    public <T> T prepareStrictType(T value, Class<T> to, SnapshotContext context)
    {
        return prepare(value, to, context, true);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T, C extends Collection<T>> C processCollection(Collection<? extends T> from, final Class<T> toClass)
    {
        return (C)processCollection(from, toClass, new SnapshotContext());
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T, C extends Collection<T>> C processCollection(Collection<? extends T> from, final Class<T> toClass,
            final SnapshotContext context)
    {
        return (C)processCollection(from, context, new Function<T, T>()
        {
            @Override
            public T apply(T val)
            {
                return prepare(val, toClass, context);
            }
        });
    }

    /**
     * {@inheritDoc}
     */
    @Override
    @SuppressWarnings("unchecked")
    public <T, C extends Collection<T>> C processCollection(Collection<? extends T> from, final SnapshotContext context)
    {
        return (C)processCollection(from, context, new Function<T, T>()
        {
            @Override
            public T apply(T val)
            {
                return val;
            }
        });
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T, C extends Collection<T>> C processCollectionStrictType(final Collection<? extends T> from,
            final Class<T> toClass)
    {
        final SnapshotContext context = new SnapshotContext();
        return (C)processCollectionStrictType(from, toClass, context);
    }

    @SuppressWarnings("unchecked")
    @Override
    public <T, C extends Collection<T>> C processCollectionStrictType(final Collection<? extends T> from,
            final Class<T> toClass, final SnapshotContext context)
    {
        return (C)processCollection(from, context, new Function<T, T>()
        {
            @Override
            public T apply(T val)
            {
                return prepare(val, toClass, context, true);
            }
        });
    }

    @SuppressWarnings({ "unchecked", "rawtypes" })
    protected <T> T prepare(@Nullable T value, Class<T> to, SnapshotContext context, boolean strictType)
    {
        if (null == value)
        {
            return null;
        }
        if (value instanceof Collection)
        {
            return (T)processCollection((Collection)value, (Class)to, context);
        }
        if (value instanceof AggregateValue)
        {
            return (T)aggregateValueResolver.resolve((AggregateValue)value);
        }
        if (value instanceof IEntityReference)
        {
            return (T)prepareEntityReference((IEntityReference)value);
        }
        if (value instanceof ClassFqn && metainfoService.isMetaclassExists((ClassFqn)value))
        {
            value = (T)new TitledClassFqn((ClassFqn)value, metainfoService.getMetaClass((ClassFqn)value).getTitle());
        }
        T prepared = context.getPrepared(value);
        if (null != prepared)
        {
            return prepared;
        }

        SnapshotFactory factory = getFactory(strictType ? null : value, to);
        if (factory == null)
        {
            if (value instanceof Snapshotable)
            {
                throw new FxException("Has no SnapshotFactory for " + to);
            }
            else
            {
                return value;
            }
        }
        else
        {
            prepared = (T)factory.instance();
            context.prepare(value, prepared);
            // не делаем повторно snapshot для объектов для которых уже сделан snapshot
            context.prepare(prepared, prepared);
            factory.init(value, prepared, context);
            if (prepared instanceof MetaClass_SnapshotObject || prepared instanceof CustomForm_SnapshotObject)
            {
                substituteGeneratedClassDefaultValue((HasAttributes)prepared);
                substituteFastSelectionDefaultValue((HasAttributes)prepared);
            }
            if (prepared instanceof MetaClassForOperator_SnapshotObject)
            {
                substituteGeneratedClassDefaultValueForOperator((MetaClassForOperator_SnapshotObject)prepared);
            }
            if (prepared instanceof Attribute_SnapshotObject)
            {
                substituteGeneratedClassDefaultValue((Attribute)prepared);
            }
            if (prepared instanceof MetaClass_SnapshotObject && context.hasProfile("admin"))
            {
                MetaClass_SnapshotObject mc = (MetaClass_SnapshotObject)prepared;
                mc.__init__title(metaclassTitleRemovedExtractor.apply(mc));
            }
            if (prepared instanceof MetaClassLite_SnapshotObject && context.hasProfile("admin"))
            {
                MetaClassLite_SnapshotObject mc = (MetaClassLite_SnapshotObject)prepared;
                mc.__init__title(metaclassTitleRemovedExtractor.apply(mc));
            }
            return prepared;
        }
    }

    /**
     *
     * @param reference
     * @return
     */
    Object prepareEntityReference(IEntityReference reference)
    {
        IUUIDIdentifiable entity = referenceResolver.resolve(reference);
        if (null == entity)
        {
            return null;
        }

        DtObject dto = hasParent(entity) ? new SimpleTreeDtObject(null, new SimpleDtObject()) : new SimpleDtObject();
        return mappingService.transform(entity, dto, REQUIRED_ENTITY_PROPERTIES);
    }

    /**
     *
     * @param <T>
     * @param <C>
     * @param from
     * @param context
     * @param func
     * @return
     */
    @SuppressWarnings("unchecked")
    <T, C extends Collection<T>> C processCollection(Collection<? extends T> from, SnapshotContext context,
            Function<T, T> func)
    {
        if (null == from)
        {
            return null;
        }
        C to = (C)context.getPrepared(from);
        if (null != to)
        {
            return to;
        }
        to = (C)newCollection(from);
        for (T f : from)
        {
            T t = func.apply(f);
            /*
             * t==null для коллекций ссылок на объекты, которых нет в системе
             * Например, после загрузки метаинфы с устаревшим значением по умолчанию у атрибутов типа "Набор ссылок
             * на БО"
             * В таком случае получается коллекция с значениями null, которая неверно обрабатывается на клиенте
             */
            if (t != null)
            {
                to.add(t);
            }
        }
        context.prepare(from, to);
        return to;
    }

    /**
     * @param <T>
     * @param to класс необходимого snapshot-а
     * @return фабрику создающую snapshot-ы заданного типа
     */
    @SuppressWarnings("unchecked")
    private <T, O extends T> SnapshotFactory<T, O> getFactory(T value, Class<? super T> to)
    {
        Class<?> snType = guessSnapshotableType(value);
        snType = snType != null && to.isAssignableFrom(snType) ? snType : to;
        Object cached = factoryCache.get(snType);
        if (NULL_CACHE_VALUE.equals(cached))
        {
            return null;
        }
        if (null != cached)
        {
            return (SnapshotFactory)cached;
        }
        try
        {
            String factoryClassName = snType.getName().replace(".shared.", ".server.") + SNAPSHOT_FACTORY_SUFFIX;
            Class<?> clazz = Class.forName(factoryClassName);
            SnapshotFactory<T, O> factoryBean = (SnapshotFactory<T, O>)springContext.getBean(clazz);
            factoryCache.put(snType, factoryBean);
            return factoryBean;
        }
        catch (NoSuchBeanDefinitionException e)
        {
            factoryCache.put(snType, NULL_CACHE_VALUE);
            return null;
        }
        catch (ClassNotFoundException e)
        {
            factoryCache.put(snType, NULL_CACHE_VALUE);
            return null;
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    private Class<?> guessSnapshotableType(Object value)
    {
        if (value instanceof Snapshotable)
        {
            Class<?> clz = value.getClass();
            while (clz != null)
            {
                for (Class<?> intf : clz.getInterfaces())
                {
                    if (Snapshotable.class.isAssignableFrom(intf))
                    {
                        return intf;
                    }
                }
                clz = HierarchyUtils.getParentClass(clz);
            }
        }
        return null;
    }

    private boolean hasParent(IUUIDIdentifiable entity)
    {
        if (entity instanceof TreeUUIDIdentifiable)
        {
            return true;
        }

        if (entity instanceof IHasMetaInfo)
        {
            ClassFqn fqn = ((IHasMetaInfo)entity).getMetaClass().fqnOfClass();
            return metainfoService.getMetaClass(fqn).hasAttribute(Constants.PARENT_ATTR);
        }

        return false;
    }

    private void substituteFastSelectionDefaultValue(HasAttributes prepared)
    {
        for (Attribute attr : prepared.getAttributes())
        {
            if (!(attr.getDefaultValue() instanceof ArrayList))
            {
                continue;
            }
            ArrayList defValue = attr.getDefaultValue();
            if (defValue.isEmpty() || !(defValue.get(0) instanceof FastSelectionDtObjectTreeSelectionChange))
            {
                continue;
            }

            substituteFastSelectionDefaultValue(prepared, (Attribute_SnapshotObject)attr,
                    (ArrayList<FastSelectionDtObjectTreeSelectionChange>)defValue);
        }
    }

    private void substituteFastSelectionDefaultValue(HasAttributes hasAttributes, Attribute_SnapshotObject attr,
            ArrayList<FastSelectionDtObjectTreeSelectionChange> defValue)
    {
        TreeSelectionProcessor processor = springContext.getBean(TreeSelectionProcessor.NAME,
                TreeSelectionProcessor.class);
        try
        {
            ClassFqn fqn = null;
            if (hasAttributes instanceof MetaClass)
            {
                fqn = ((MetaClass)hasAttributes).getFqn();
            }
            else if (hasAttributes instanceof CustomForm)
            {
                fqn = new CustomFormFakeMetaClass(((CustomForm)hasAttributes).getCode()).getFqn();
            }
            Set<String> uuids = processor.getAttributeValue(defValue,
                    attr.getType().<ObjectAttributeType> cast().getRelatedMetaClass(), Constants.TEMP_UUID, fqn,
                    attr.getCode(), "", IProperties.EMPTY);

            LazyCollection lazyCollection = new LazyCollection("", attr.getCode(),
                    uuids.size() <= Constants.MAX_COLLECTION_OBJECTS);
            Iterator<String> iterator = uuids.iterator();
            for (int i = 0; i < Math.min(uuids.size(), Constants.MAX_COLLECTION_OBJECTS); i++)
            {
                IUUIDIdentifiable object = loaderService.getL10n(iterator.next());
                DtObject dtObject = mappingService.transform(object, new SimpleDtObject(), new DtoProperties(null,
                        Lists.<String> newArrayList(AbstractBO.METACLASS, AbstractBO.UUID, AbstractBO.TITLE)));
                lazyCollection.add(dtObject);
            }
            lazyCollection.sort(ITitled.COMPARATOR);
            attr.__init__defaultValue(new FastSelectionChangesWithValue(defValue, lazyCollection));
        }
        catch (DispatchException e)
        {
            attr.__init__defaultValue(
                    new FastSelectionChangesWithValue(defValue, new LazyCollection("", attr.getCode(), true)));
        }
    }

    private static void substituteGeneratedClassDefaultValue(HasAttributes prepared)
    {
        substituteGeneratedClassDefaultValue(prepared.getAttributes());
    }

    private static void substituteGeneratedClassDefaultValueForOperator(MetaClassForOperator_SnapshotObject prepared)
    {
        substituteGeneratedClassDefaultValue(prepared.getAttributes());
        substituteGeneratedClassDefaultValue(prepared.getAttributesWithoutParentAttr());
    }

    private static void substituteGeneratedClassDefaultValue(@Nullable Collection<? extends Attribute> attributes)
    {
        if (attributes != null)
        {
            for (Attribute attr : attributes)
            {
                substituteGeneratedClassDefaultValue(attr);
            }
        }
    }

    private static void substituteGeneratedClassDefaultValue(Attribute attr)
    {
        Object value = AttrGeneratedClassConverter.convert(attr.getDefaultValue());
        if (value != null)
        {
            ((Attribute_SnapshotObject)attr).__init__defaultValue(value);
        }
    }
}
