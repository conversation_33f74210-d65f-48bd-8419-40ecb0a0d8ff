package ru.naumen.core.server.script.storage.modification.usage;

import java.util.Collection;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.script.storage.ScriptUsageUtils;
import ru.naumen.core.shared.timer.definition.ScriptTimerCondition;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Контракт поведения при изменении скрипта счетчиках времени
 * Переопределяет специфичные действия при редактировании.
 * Основная логика редактирования содержится в {@link ScriptModifyProcessBase}
 * <AUTHOR>
 * @since Dec 22, 2015
 */
@Component
public class TimerDefinitionScriptModifyProcess extends ScriptModifyProcessSupport<TimerDefinition>
{
    public static final String TIMER_CONDITION_CODE = "timerConditionCode";
    public static final String START_CONDITION_CODE = "startConditionScript";
    public static final String STOP_CONDITION_CODE = "stopConditionScript";
    public static final String PAUSE_CONDITION_CODE = "pauseConditionScript";
    public static final String RESUME_CONDITION_CODE = "resumeConditionScript";

    @Override
    protected String getLocation(TimerDefinition holder, ScriptModifyContext context)
    {
        return ScriptUsageUtils.getTimerDefinitionLocation(holder);
    }

    @Override
    protected String getOldScriptCode(@Nullable TimerDefinition oldHolder, ScriptModifyContext context)
    {
        if (oldHolder == null)
        {
            return null;
        }
        ScriptTimerCondition timerCondition = (ScriptTimerCondition)oldHolder.getTimerCondition();
        String scriptCode = null;
        switch (context.<String> getProperty(TIMER_CONDITION_CODE))
        {
            case START_CONDITION_CODE:
                scriptCode = timerCondition.getStartCondition();
                break;
            case STOP_CONDITION_CODE:
                scriptCode = timerCondition.getStopCondition();
                break;
            case PAUSE_CONDITION_CODE:
                scriptCode = timerCondition.getPauseCondition();
                break;
            case RESUME_CONDITION_CODE:
                scriptCode = timerCondition.getResumeCondition();
                break;
            default:
                break;
        }
        return scriptCode;
    }

    @Override
    protected Collection<ClassFqn> getRelatedMetaClasses(TimerDefinition holder, ScriptModifyContext context)
    {
        return holder.getTargetTypes();
    }

    @Override
    protected boolean isUsageChangeable()
    {
        return false;
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, TimerDefinition holder, ScriptModifyContext context)
    {
        ScriptTimerCondition timerCondition = (ScriptTimerCondition)holder.getTimerCondition();
        switch (context.<String> getProperty(TIMER_CONDITION_CODE))
        {
            case START_CONDITION_CODE:
                timerCondition.setStartCondition(newScriptCode);
                break;
            case STOP_CONDITION_CODE:
                timerCondition.setStopCondition(newScriptCode);
                break;
            case PAUSE_CONDITION_CODE:
                timerCondition.setPauseCondition(newScriptCode);
                break;
            case RESUME_CONDITION_CODE:
                timerCondition.setResumeCondition(newScriptCode);
                break;
            default:
                break;
        }
    }
}
