package ru.naumen.core.server.script.spi.limits;

import java.util.ArrayList;
import java.util.Collection;
import java.util.EnumMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import org.codehaus.groovy.ast.ASTNode;
import org.codehaus.groovy.ast.ClassNode;
import org.codehaus.groovy.ast.ImportNode;
import org.codehaus.groovy.ast.ModuleNode;
import org.codehaus.groovy.control.CompilePhase;
import org.codehaus.groovy.control.SourceUnit;
import org.codehaus.groovy.transform.ASTTransformation;
import org.codehaus.groovy.transform.GroovyASTTransformation;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.spi.limits.ScriptDependencies.CallTypes;
import ru.naumen.core.server.script.spi.limits.ScriptDependencies.ImportTypes;

/**
 * Трансформация для получения списка вызовов на биндингах.
 */
@GroovyASTTransformation(phase = CompilePhase.CANONICALIZATION)
public class CallsASTTransform implements ASTTransformation
{
    private final Map<ImportTypes, List<String>> importsMap = new EnumMap<>(ImportTypes.class);
    private final Map<CallTypes, Map<String, Collection<CallInfo>>> calls = new EnumMap<>(CallTypes.class);
    private final ScriptDependencies scriptDependencies = new ScriptDependencies(calls, importsMap);

    /**
     * Позволяет получить список вызовов на биндингах
     * после выполнения компиляции скрипта.
     *
     * @return список вызовов.
     */
    public ScriptDependencies getScriptDependencies()
    {
        return scriptDependencies;
    }

    @Override
    public void visit(ASTNode[] nodes, SourceUnit source)
    {
        ModuleNode ast = source.getAST();

        List<ImportNode> imports = ast.getImports();
        List<ImportNode> starImports = ast.getStarImports();
        Map<String, ImportNode> staticImports = ast.getStaticImports();
        Map<String, ImportNode> staticStarImports = ast.getStaticStarImports();

        List<String> importStrings = new ArrayList<>(imports.size());
        for (ImportNode node : imports)
        {
            importStrings.add(node.getClassName());
        }
        List<String> starImportStrings = new ArrayList<>(starImports.size());
        for (ImportNode node : starImports)
        {
            starImportStrings.add(StringUtilities.removeLastCharacter(node.getPackageName()));
        }
        List<String> staticImportStrings = new ArrayList<>(staticImports.size());
        for (Entry<String, ImportNode> entry : staticImports.entrySet())
        {
            staticImportStrings.add(entry.getValue().getClassName() + "." + entry.getKey());
        }
        List<String> staticStarImportStrings = new ArrayList<>(staticStarImports.size());
        for (String entry : staticStarImports.keySet())
        {
            staticStarImportStrings.add(entry);
        }
        importsMap.put(ImportTypes.USUAL, importStrings);
        importsMap.put(ImportTypes.STAR, starImportStrings);
        importsMap.put(ImportTypes.STATIC, staticImportStrings);
        importsMap.put(ImportTypes.STATIC_STAR, staticStarImportStrings);

        ASTVisitor crawler = new ASTVisitor(source);
        List<ClassNode> clss = ast.getClasses();
        for (ClassNode node : clss)
        {
            node.visitContents(crawler);
        }
        calls.put(CallTypes.DYNAMIC, crawler.getDynamicCalls().asMap());
        calls.put(CallTypes.STATIC, crawler.getStaticCalls().asMap());
        calls.put(CallTypes.CONSTRUCTOR, crawler.getConstructorCalls().asMap());
        calls.put(CallTypes.OTHER, crawler.getOtherCalls().asMap());
        calls.put(CallTypes.ATTRIBUTE, crawler.getAttributeCalls().asMap());
        calls.put(CallTypes.PROPERTY, crawler.getPropertyCalls().asMap());
        calls.put(CallTypes.METHOD_POINTER, crawler.getMethodPointerCalls().asMap());
    }
}
