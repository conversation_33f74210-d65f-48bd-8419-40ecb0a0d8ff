package ru.naumen.core.server.hquery.impl;

import java.util.LinkedHashMap;
import java.util.Map;

import jakarta.annotation.Nullable;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HColumn;

/**
 * Колонка для простого выражения с условием<br>
 * Имеет HQL вид: case ... when ... then ... else ... end
 *
 * <AUTHOR>
 * @since 21.07.2021
 */
public class HSimpleCaseColumn extends AbstractHColumn
{
    private final HColumn expression;
    private final Map<HColumn, HColumn> conditions;
    private final HColumn elseResult;

    /**
     * Колонка для простого выражения с условием
     * @param expression колонка/выражение с которым сравнивается значение из условий
     * @param conditions условия в виде упорядоченного Map<значение выражения, результат при выполнении условия в
     *                   виде колонки>
     * @param elseResult результат, когда не выполнилось ни одно условие
     * @param alias псевдоним
     */
    public HSimpleCaseColumn(
            HColumn expression, LinkedHashMap<HColumn, HColumn> conditions,
            HColumn elseResult, @Nullable String alias)
    {
        super(alias);
        this.expression = expression;
        this.conditions = conditions;
        this.elseResult = elseResult;
    }

    @Override
    public void setParameters(Query query)
    {
        expression.setParameters(query);
        conditions.forEach((expValue, result) ->
        {
            expValue.setParameters(query);
            result.setParameters(query);
        });
        elseResult.setParameters(query);
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        StringBuilder sb = new StringBuilder("case ")
                .append(expression.getHQL(builder));
        conditions.forEach((key, value) ->
                sb.append(" when ").append(key.getHQL(builder))
                        .append(" then ").append(value.getHQL(builder)));
        sb.append(" else ").append(elseResult.getHQL(builder));
        sb.append(" end");
        return sb.toString();
    }

    @Override
    public String toString()
    {
        return "HSimpleCaseColumn{" +
               "expression=" + expression +
               ", conditions=" + conditions +
               ", elseResult=" + elseResult +
               ", alias=" + getAlias() +
               '}';
    }
}
