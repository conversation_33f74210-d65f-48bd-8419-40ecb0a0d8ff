package ru.naumen.core.server.catalog;

import java.util.Comparator;
import java.util.List;
import java.util.Set;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.Constants.CatalogItem;
import ru.naumen.core.shared.criteria.Order;
import ru.naumen.core.shared.dto.DtObject;

import com.google.common.collect.ImmutableSet;
import com.google.common.collect.Iterables;

/**
 * <AUTHOR>
 * @since 25.01.2013
 */
public class CatalogItemComparators
{
    /**
     * Компаратор, который умеет сортировать {@link DtObject}
     * сравнивая их названия или коды
     */
    private static class ItemDtObjectComparator implements Comparator<DtObject>
    {
        //@formatter:off
        private static final Set<String> SORTABLE_FIELDS = ImmutableSet.of(
                CatalogItem.ITEM_TITLE,
                CatalogItem.ITEM_CODE);
        //@formatter:on

        Order order;

        ItemDtObjectComparator(List<Order> orders)
        {
            Order order = Iterables.get(orders, 0, null);

            //@formatter:off
            this.order = order != null && SORTABLE_FIELDS.contains(order.getName())
                    ? order : new Order(CatalogItem.ITEM_TITLE);
            //@formatter:on
        }

        @Override
        public int compare(DtObject o1, DtObject o2)
        {
            String v1 = o1.getProperty(order.getName());
            String v2 = o2.getProperty(order.getName());
            int sign = order.asc ? 1 : -1;
            return sign * StringUtilities.compareTo(v1, v2);
        }
    }

    public static Comparator<DtObject> getItemDtoComparator(List<Order> orders)
    {
        return new ItemDtObjectComparator(orders);
    }
}
