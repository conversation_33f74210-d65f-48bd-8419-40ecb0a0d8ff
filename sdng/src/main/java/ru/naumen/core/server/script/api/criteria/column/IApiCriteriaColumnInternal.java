package ru.naumen.core.server.script.api.criteria.column;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.script.api.criteria.ApiCriteria;
import ru.naumen.core.server.script.api.criteria.IApiCriteriaColumn;

/**
 * Базовый класс объектов-колонок для {@link ApiCriteria}.
 * <br>
 * Фактически, представляет собой "внутреннее api" для работы с колонками-объектами: предполагается, что все
 * объекты-колонки должны наследоваться от данного класса.
 *
 * <AUTHOR>
 * @since 08.04.20
 */
public interface IApiCriteriaColumnInternal extends IApiCriteriaColumn
{
    /**
     * Возвращает строковое представление колонки, которое "как есть" попадёт в критерию (и потом в HQL-запрос)
     * @param criteria текущая критерия, для которой создаём колонку
     */
    default HColumn getColumnExpression(ApiCriteria criteria)
    {
        return getColumnExpression(criteria, null);
    }

    /**
     * Возвращает строковое представление колонки, которое "как есть" попадёт в критерию (и потом в HQL-запрос)
     * @param criteria текущая критерия, для которой создаём колонку
     * @param alias псевдоним колонки (может быть null, если он не нужен)
     */
    HColumn getColumnExpression(ApiCriteria criteria, @Nullable String alias);

    default boolean isMetacaseColumn()
    {
        return false;
    }

    default boolean isIdColumn()
    {
        return false;
    }
}
