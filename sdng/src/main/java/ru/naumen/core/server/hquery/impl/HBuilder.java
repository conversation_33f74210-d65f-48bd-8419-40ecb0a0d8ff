package ru.naumen.core.server.hquery.impl;

import java.util.ArrayList;
import java.util.List;

import org.apache.commons.collections4.CollectionUtils;
import org.hibernate.Session;

import com.google.common.base.Joiner;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.hquery.HBuildVisitable;
import ru.naumen.core.server.hquery.HPredicate;
import ru.naumen.core.server.hquery.HQueryException;

/**
 * Построитель HQL запроса из {@link HCriteriaDelegate}
 * <p>
 * Использует паттерн visitor для обхода всех {@link HBuildVisitable объектов} участвующих в формировании HQL запроса.
 * Только после обхода формирует текст HQL
 * </p>
 * Это позволяет не заботится о последовательности обхода
 *
 * <AUTHOR>
 * @since 04 февр. 2016 г.
 */
public class HBuilder
{
    private final HCriteriaDelegate criteria;
    private final Session session;
    private final HBuilder parent;

    private List<String> selectStrings;
    private List<String> fromStrings;
    private List<Boolean> isJoins;

    private List<String> whereStrings;
    private List<String> orderByStrings;
    private List<String> groupByStrings;

    private CTEDbLogic dbLogic;

    /**
     * Набор т.н. Common Table Expressions (CTE) - подзапросов,
     * которые выводятся в начале SQL в выражении <code>WITH ...</code> перед
     * основным запросом
     */
    private List<String> cTEs;
    private boolean enableCTEs = true;

    public HBuilder(HBuilder parent)
    {
        this(parent, parent.criteria);
    }

    public HBuilder(HBuilder parent, HCriteriaDelegate delegate)
    {
        this(parent, delegate, null);
    }

    public HBuilder(HCriteriaDelegate delegate, Session session)
    {
        this(null, delegate, session);
    }

    /**
     * Создание объекта построителя
     * @param parent
     * @param delegate общая критерия
     * @param session
     */
    private HBuilder(HBuilder parent, HCriteriaDelegate delegate, Session session)
    {
        this.parent = parent;
        this.criteria = delegate;
        this.session = session;
    }

    /**
     * Основной метод builder'а
     * Метод предназначен для построения HQL запроса
     *
     * @return текст готового HQL запроса
     */
    public String build()
    {
        if (criteria.isHierarchyCriteria())
        {
            return buildHierarchy();
        }
        criteria.visit(this);
        StringBuilder sb = new StringBuilder();
        if (cTEs != null && !this.cTEs.isEmpty())
        {
            sb.append(getDbLogic().getCTEPrefix(getSession())).append(' ');
            Joiner.on(",\n").appendTo(sb, this.cTEs).append('\n');
            appendHQL(sb);
        }
        else
        {
            appendHQL(sb);
        }
        return sb.toString();
    }

    public String buildHierarchy()
    {
        HBuilder f = new HBuilder(this);
        // Собираем CTE
        criteria.visit(f);
        if (null != criteria.getStartCriterion())
        {
            criteria.getStartCriterion().visit(f);
        }
        if (null != criteria.getConnectCriterion())
        {
            criteria.getConnectCriterion().visit(f);
        }
        return getDbLogic().createHierarchyHql(getSession(), this.criteria);
    }

    /**
     * Добавляет в builder т.н. Common Table Expression (CTE) - подзапрос,
     * который выводится в начале SQL в выражении <code>WITH ...</code> перед
     * основным запросом
     *
     * @param cte
     */
    public void commonTableExpression(String cte)
    {
        if (!enableCTEs)
        {
            return;
        }
        if (parent != null)
        {
            // Все CTE должны быть на верхнем уровне
            parent.commonTableExpression(cte);
            return;
        }
        if (null == cTEs)
        {
            this.cTEs = new ArrayList<>();
        }
        String suffix = getDbLogic().getCTESourceSuffix(getSession());
        cTEs.add(suffix.isEmpty() ? cte : cte + ' ' + suffix);
    }

    /**
     * Добавить элемент для формирования выражения FROM в запросе,
     * такой элемент будет выведен в этом выражении через запятую
     * @param fromString
     */
    public void from(String fromString)
    {
        from(fromString, false);
    }

    /**
     * Добавить элемент для формирования выражения FROM в запросе
     * Если это join, то не надо добавлять перед ним запятую
     * @param fromString
     * @param join
     */
    public void from(String fromString, boolean join)
    {
        if (null == fromStrings)
        {
            this.fromStrings = new ArrayList<>();
            this.isJoins = new ArrayList<>();
        }
        fromStrings.add(fromString);
        isJoins.add(join);
    }

    /**
     * @return the criteria
     */
    public HCriteriaDelegate getCriteria()
    {
        return criteria;
    }

    public List<String> getSelectStrings()
    {
        return selectStrings;
    }

    /**
     * @return the session
     */
    public Session getSession()
    {
        return session != null ? session : parent.getSession();
    }

    public List<String> getWhereStrings()
    {
        return this.whereStrings;
    }

    public List<String> getOrderByStrings()
    {
        return orderByStrings;
    }

    /**
     * Добавить элемент для формирования выражения SELECT в запросе
     */
    public void select(String selectString)
    {
        if (null == selectStrings)
        {
            this.selectStrings = new ArrayList<>();
        }
        selectStrings.add(selectString);
    }

    public HBuilder setCTEsEnabled(boolean enabled)
    {
        this.enableCTEs = enabled;
        return this;
    }

    /**
     * Добавить элемент для формирования выражения WHERE в запросе
     */
    public void where(String whereString)
    {
        if (null == whereStrings)
        {
            this.whereStrings = new ArrayList<>();
        }
        whereStrings.add(whereString);
    }

    /**
     * Добавить элемент для формирования выражения GROUP BY в запросе
     */
    public void groupBy(String groupBy)
    {
        if (null == groupByStrings)
        {
            this.groupByStrings = new ArrayList<>();
        }
        groupByStrings.add(groupBy);
    }

    /**
     * Добавить элемент для формирования выражения ORDER BY в запросе
     */
    public void orderBy(String orderBy)
    {
        if (null == orderByStrings)
        {
            this.orderByStrings = new ArrayList<>();
        }
        orderByStrings.add(orderBy);
    }

    private void appendFromClause(StringBuilder sb)
    {
        sb.append(" FROM "); //$NON-NLS-1$

        if (fromStrings == null || fromStrings.isEmpty())
        {
            throw new HQueryException("HCriteria.ErrorSourceIsNotIndicated");
        }
        for (int i = 0; i < fromStrings.size(); ++i)
        {
            if (i > 0)
            {
                sb.append(Boolean.TRUE.equals(isJoins.get(i)) ? " " : ", ");
            }
            sb.append(fromStrings.get(i));
        }
    }

    private void appendGroupClause(StringBuilder sb)
    {
        if (groupByStrings != null)
        {
            sb.append(" GROUP BY ");
            Joiner.on(", ").appendTo(sb, groupByStrings);
        }
    }

    private void appendHavingClause(StringBuilder sb)
    {
        if (StringUtilities.isNotEmpty(criteria.getHaving()))
        {
            sb.append(" HAVING "); //$NON-NLS-1$
            sb.append(criteria.getHaving());
        }
    }

    private void appendHQL(StringBuilder sb)
    {
        appendSelectClause(sb);
        appendFromClause(sb);
        appendWhereClause(sb);
        appendGroupClause(sb);
        appendHavingClause(sb);
        appendOrderClause(sb);
    }

    private void appendOrderClause(StringBuilder sb)
    {
        HPredicate predicate = criteria.getPredicate();
        /*
         Нет необходимости в сортировке при запросе с агрегатной функцией count
         (проверка обусловлена особенностью использования функции count в системе,
         другие агрегатные функции в такой проверке не нуждаются)
        */
        if (predicate == HPredicate.COUNT_ALL
            || (selectStrings != null && selectStrings.size() == 1
                && selectStrings.get(0).toLowerCase().startsWith("count")))
        {
            return;
        }

        if (orderByStrings != null)
        {
            sb.append(" ORDER BY ");
            Joiner.on(", ").appendTo(sb, orderByStrings);
        }
    }

    private void appendSelectClause(StringBuilder sb)
    {
        if (criteria.getPredicate() == null
            && (selectStrings == null || selectStrings.isEmpty()))
        {
            return;
        }

        sb.append("SELECT "); //$NON-NLS-1$

        if (criteria.getPredicate() != null)
        {
            sb.append(criteria.getPredicate().getHql());
            sb.append(' ');
        }
        if (CollectionUtils.isNotEmpty(selectStrings))
        {
            Joiner.on(", ").appendTo(sb, selectStrings);
        }
    }

    private void appendWhereClause(StringBuilder sb)
    {
        if (whereStrings != null && !whereStrings.isEmpty())
        {
            sb.append(" WHERE "); //$NON-NLS-1$
            Joiner.on(" AND ").appendTo(sb, whereStrings);
        }
    }

    private CTEDbLogic getDbLogic()
    {
        if (null == this.dbLogic)
        {
            this.dbLogic = new CTEDbLogic();
        }
        return this.dbLogic;
    }
}
