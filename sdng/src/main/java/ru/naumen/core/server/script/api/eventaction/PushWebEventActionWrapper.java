package ru.naumen.core.server.script.api.eventaction;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.push.PushEventAction;

/**
 * Обертка {@link EventAction} Уведомление в веб-интерфейсе для использования в скриптах
 * <AUTHOR>
 * @since 12.03.2021
 */
public class PushWebEventActionWrapper extends PushEventActionWrapper implements IPushWebEventActionWrapper
{
    private final PushEventAction action;

    PushWebEventActionWrapper(EventAction eventAction)
    {
        super(eventAction);
        action = (PushEventAction)eventAction.getAction();
    }

    @Override
    public boolean isFormatHTML()
    {
        return action.isHtml();
    }

    @Override
    public String getPushPresentationType()
    {
        switch (action.getPushPresentationType())
        {
            case OnlyInTheInterface:
                return messages.getMessage("PushPresentationType.onlyInTheInterface");
            case OnlyExternalPush:
                return messages.getMessage("PushPresentationType.onlyExternalPush");
            case AlwaysInTheInterface:
                return messages.getMessage("PushPresentationType.alwaysInTheInterface");
            case InterfaceAndBrowserNotices:
                return messages.getMessage("PushPresentationType.interfaceAndBrowserNotices");
            default:
                return "";
        }
    }

    @Override
    public String getCodeTemplate()
    {
        return StringUtilities.isEmpty(action.getMessageTemplate()) ? "" : action.getMessageTemplate();
    }
}