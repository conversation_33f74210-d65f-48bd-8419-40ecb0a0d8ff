package ru.naumen.core.server.naming.spi;

/**
 * D<PERSON><PERSON> для {@link PeriodicalSequence}
 *
 * <AUTHOR>
 *
 */
public interface SequenceDao
{
    /**
     * Создает последовательность с указанным идентификатором
     *
     * @param id идентификатор запрашиваемой последовательности. 
     * @return запрашиваемая последовательность.
     */
    PeriodicalSequence createSequence(String id);

    /**
     * Удаляет последовательность с заданным идентификатором
     *
     * @param id
     */
    void delete(String id);

    /**
     * Получение последовательности по её идентификатору.
     *
     * @param id идентификатор получаемой последовательности.
     * @return последовательность с заданным идентификатором.
     */
    PeriodicalSequence get(String id);

    /**
     * Устанавливает значение последовательности в указанное значение. В отличие от
     * {@link #updateSequence(String, long, int)}
     * создает последовательность, если ее нет.
     *
     * @param id
     * @param period
     * @param value
     */
    void set(String id, long period, int value);

    /**
     * Устанавливает значение последовательности в указанное значение. В отличие от {@link #set(String, long, int)}
     * не создает последовательность, если ее нет. Т.е. в случае отсутствия последовательности будет ошибка.
     *
     * @param id
     * @param period
     * @param value
     */
    void updateSequence(String id, long period, int value);
}
