package ru.naumen.core.server.script.storage.modification.usage;

import java.util.HashMap;
import java.util.Map;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.script.ScriptService.Constants;
import ru.naumen.core.shared.script.places.AttributeCategories;
import ru.naumen.metainfo.server.spi.elements.AbstractAttributeInfo;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Контракт поведения при изменении скрипта в атрибутах
 * Переопределяет специфичные действия при редактировании.
 * Основная логика редактирования содержится в {@link ScriptModifyProcessBase}
 * <AUTHOR>
 * @since Nov 10, 2015
 */
@Component
public class AttributeScriptModifyProcess extends AbstractAttributeScriptModifyProcess<AbstractAttributeInfo>
{
    @Override
    protected void dataUpdateSelfUsage(Script script, AbstractAttributeInfo newHolder, ScriptModifyContext context)
    {
        switch ((AttributeCategories)context.getCategory())
        {
            case COMPUTABLE_ON_FORM:
                updateComputableOnForm(script, newHolder);
                break;
            case FILTRATION:
                updateFiltration(script, newHolder);
                break;
            case DATE_TIME_RESTRICTION:
                updateDateTimeRestriction(script, newHolder);
                break;
            default:
                break;
        }
    }

    @Override
    protected void dataUpdateSelfUsageAfterRemove(AbstractAttributeInfo holder, ScriptModifyContext context)
    {
        switch ((AttributeCategories)context.getCategory())
        {
            case COMPUTABLE_ON_FORM:
                updateComputableOnFormAR(holder);
                break;
            case FILTRATION:
                updateFiltrationAR(holder);
                break;
            case DATE_TIME_RESTRICTION:
                updateDateTimeRestrictionAR(holder);
                break;
            default:
                break;
        }
    }

    @Override
    protected String getOldScriptCode(AbstractAttributeInfo oldHolder, ScriptModifyContext context)
    {
        return switch ((AttributeCategories)context.getCategory())
        {
            case COMPUTABLE -> oldHolder.getScript();
            case COMPUTABLE_ON_FORM -> oldHolder.getComputableOnFormScript();
            case DEFAULT_VALUE -> oldHolder.getScriptForDefault();
            case FILTRATION -> oldHolder.getScriptForFiltration();
            case DATE_TIME_RESTRICTION -> oldHolder.getDateTimeRestrictionScript();
            default -> null;
        };
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, AbstractAttributeInfo holder,
            ScriptModifyContext context)
    {
        switch ((AttributeCategories)context.getCategory())
        {
            case COMPUTABLE:
                holder.setScript(newScriptCode);
                break;
            case COMPUTABLE_ON_FORM:
                holder.setScriptForEditValue(newScriptCode);
                break;
            case DEFAULT_VALUE:
                holder.setScriptForDefault(newScriptCode);
                break;
            case FILTRATION:
                holder.setScriptForFiltration(newScriptCode);
                break;
            case DATE_TIME_RESTRICTION:
                holder.setDateTimeRestrictionScript(newScriptCode);
                break;
            default:
                break;
        }
    }

    Map<String, Object> getBindingsForDateTimeRestriction()
    {
        Map<String, Object> bindings = HashMap.newHashMap(3);
        bindings.put(Constants.SUBJECT, null);
        return bindings;
    }
}
