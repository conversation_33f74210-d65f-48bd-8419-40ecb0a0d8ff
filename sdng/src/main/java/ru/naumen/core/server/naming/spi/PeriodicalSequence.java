package ru.naumen.core.server.naming.spi;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Table;

import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * Хранит значение периодической последовательности (последовательности, значение которой периодически 
 * начинается с 0, например, ежедневно)
 *
 * <AUTHOR>
 *
 */
@Entity
@Table(name = "tbl_sys_sequence")
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
public class PeriodicalSequence
{
    @Id
    @Column(name = "id", nullable = false)
    private String id;
    @Column(name = "period", nullable = false)
    private long period;
    @Column(name = "value", nullable = false)
    private int value;

    public PeriodicalSequence()
    {
    }

    public PeriodicalSequence(String id, long period, int val)
    {
        this.id = id;
        this.period = period;
        this.value = val;
    }

    public String getId()
    {
        return id;
    }

    public long getPeriod()
    {
        return period;
    }

    public int getValue()
    {
        return value;
    }

    public void setId(String id)
    {
        this.id = id;
    }

    public void setPeriod(long period)
    {
        this.period = period;
    }

    public void setValue(int val)
    {
        this.value = val;
    }
}
