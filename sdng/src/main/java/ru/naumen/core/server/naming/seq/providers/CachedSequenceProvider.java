package ru.naumen.core.server.naming.seq.providers;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.collect.Maps;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.naming.generators.strategies.NextValueGenerationStrategy;
import ru.naumen.core.server.naming.generators.strategies.SequenceSaver;
import ru.naumen.core.server.naming.spi.PeriodicalSequence;
import ru.naumen.core.server.naming.spi.SequenceDao;

/**
 * Предоставляет кэшированный доступ к последовательностям.
 *
 * <AUTHOR>
 * @since Dec 17, 2015
 *
 */
public class CachedSequenceProvider implements SequenceProvider, SequenceSaver
{
    private static final Logger LOG = LoggerFactory.getLogger(CachedSequenceProvider.class);
    private final ConcurrentMap<String, Lock> locks = Maps.newConcurrentMap();
    /**
     * Запоминает сгенерированные, но возвращенные значения (например из-за отката транзакции) для каждого генератора.
     */
    private final ConcurrentMap<Pair<String, Long>, Queue<Integer>> returnedCache = Maps.newConcurrentMap();
    /**
     * Генераторы для каждого периода
     * <period, sequence>
     */
    private final ConcurrentMap<Pair<String, Long>, AtomicInteger> sequencesCache = Maps.newConcurrentMap();
    private final SequenceDao sequenceDao;

    /**
     * Провайдер последовательностей генерации (идентификаторов, номеров и т.п.) со внутренним кэшированием.
     *
     * @param sequenceDao DAO для доступа к значениям последовательностей в базе.
     */
    @Inject
    public CachedSequenceProvider(SequenceDao sequenceDao)
    {
        this.sequenceDao = sequenceDao;
    }

    @Override
    public void deleteSequence(String sequenceId, Long period)
    {
        clearSequenceCache(new Pair<>(sequenceId, period));
        sequenceDao.delete(sequenceId);
    }

    @Override
    public int generateId(Pair<String, Long> key, NextValueGenerationStrategy nextValStrategy)
    {
        Lock lock = locks.get(key.getLeft());
        if (lock == null)
        {
            lock = locks.putIfAbsent(key.getLeft(), new ReentrantLock());
            if (lock == null)
            {
                lock = locks.get(key.getLeft());
            }
        }
        lock.lock();

        try
        {
            Integer value = getReturnedValues(key).poll();
            // генерируем следующее значение
            if (null == value)
            {
                AtomicInteger atomicValue = sequencesCache.get(key);
                if (atomicValue == null)
                {
                    final PeriodicalSequence sequence = sequenceDao.get(key.getLeft());
                    atomicValue = new AtomicInteger(
                            sequence.getPeriod() == key.getRight() ? sequence.getValue() : 0);
                    atomicValue = sequencesCache.putIfAbsent(key, atomicValue);
                }
                value = nextValStrategy.next(key, sequencesCache.get(key).get(), this);
            }
            else
            {
                LOG.debug("Using returned id {}. {}", value, this.getClass().getName());
            }

            return value;
        }
        finally
        {
            lock.unlock();
        }
    }

    @Override
    public PeriodicalSequence getSequence(String sequenceId)
    {
        return sequenceDao.get(sequenceId);
    }

    @Override
    public int getSequenceValue(String sequenceId)
    {
        return getSequence(sequenceId).getValue();
    }

    @Override
    public void restartSequence(String sequenceId, Long period, int value)
    {
        sequenceDao.updateSequence(sequenceId, period, value);
        clearCache();
    }

    @Override
    public void returnValue(Pair<String, Long> key, Integer value)
    {
        LOG.debug("Returning {} value for {} sequence, {} period.", value, key.left, key.right);
        final Queue<Integer> list = getReturnedValues(key);
        list.add(value);
        LOG.debug("{} value for {} sequence, {} period has been returned.", value, key.left, key.right);
    }

    @Override
    public void save(Pair<String, Long> key, int value)
    {
        updateSequenceCaches(key, value);
        sequenceDao.updateSequence(key.getLeft(), key.getRight(), value);
    }

    @Override
    public void setSequence(String sequenceId, Long period, int value)
    {
        sequenceDao.set(sequenceId, period, value);
        clearCache();
    }

    @Override
    public void updateReturned(Pair<String, Long> key, Integer value)
    {
        final Queue<Integer> list = getReturnedValues(key);
        LOG.debug("Updatating local returned ids {} by {}. {}", key, value, this.getClass().getName());
        if (!list.contains(value))
        {
            list.add(value);
        }
    }

    private void clearCache()
    {
        sequencesCache.clear();
        returnedCache.clear();
    }

    private void clearSequenceCache(Pair<String, Long> key)
    {
        sequencesCache.remove(key);
        returnedCache.remove(key);
    }

    /**
     * Извлекает из {@link CachedSequenceProvider#returnedCache} возвращённые значения для заданных последовательности 
     * и периода.
     * Учитывает корректное атомарное получение и обновление содержимого {@link CachedSequenceProvider#returnedCache}
     * в отсутствие каких-либо внутренних структур.
     *
     * @param key идентификатор последовательности, период для которых происходит получение возвращённых значений. 
     * @return список возвращённых значений.
     */
    private Queue<Integer> getReturnedValues(Pair<String, Long> key)
    {
        Queue<Integer> list = returnedCache.get(key);
        if (list == null)
        {
            list = returnedCache.putIfAbsent(key, new ConcurrentLinkedQueue<Integer>());
            if (null == list)
            {
                list = returnedCache.get(key);
            }
        }
        return list;
    }

    private void updateSequenceCaches(Pair<String, Long> key, int newValue)
    {
        final Queue<Integer> list = returnedCache.get(key);
        if (list != null && list.remove(newValue))
        {
            LOG.debug("{} has been removed from returned cache for {} sequence, {} period.", newValue, key.left,
                    key.right);
            return;
        }

        LOG.debug("Updating sequence {} for period {} with {} value.", key.left, key.right, newValue);
        AtomicInteger value = sequencesCache.get(key);
        if (value == null)
        {
            value = new AtomicInteger(newValue);
            final AtomicInteger result = sequencesCache.putIfAbsent(key, value);
            if (result != null)
            {
                result.set(newValue);
            }
        }
        else
        {
            sequencesCache.get(key).set(newValue);
        }
        LOG.debug("Sequence {} for period {} has been updated with {} value.", key.left, key.right, newValue);
    }
}