package ru.naumen.core.server.hquery;

import java.util.Arrays;
import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;

import com.google.common.collect.Iterables;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.hquery.criterion.BetweenCriterion;
import ru.naumen.core.server.hquery.criterion.ClobContainsCriterion;
import ru.naumen.core.server.hquery.criterion.CollectionEmptyCriterion;
import ru.naumen.core.server.hquery.criterion.CollectionIsNotEmptyCriterion;
import ru.naumen.core.server.hquery.criterion.CollectionSizeCriterion;
import ru.naumen.core.server.hquery.criterion.ContainsCriterion;
import ru.naumen.core.server.hquery.criterion.ExistsCriterion;
import ru.naumen.core.server.hquery.criterion.FTSCriterion;
import ru.naumen.core.server.hquery.criterion.FalseCriterion;
import ru.naumen.core.server.hquery.criterion.InCriterion;
import ru.naumen.core.server.hquery.criterion.InCriterionBig;
import ru.naumen.core.server.hquery.criterion.InCriterionWithStorage;
import ru.naumen.core.server.hquery.criterion.InElementsCriterion;
import ru.naumen.core.server.hquery.criterion.InFunctionCriterion;
import ru.naumen.core.server.hquery.criterion.InFunctionValueCriterion;
import ru.naumen.core.server.hquery.criterion.InIndicesCriterion;
import ru.naumen.core.server.hquery.criterion.InSubquery;
import ru.naumen.core.server.hquery.criterion.IsNotNullCriterion;
import ru.naumen.core.server.hquery.criterion.IsNullCriterion;
import ru.naumen.core.server.hquery.criterion.IsNullSubCriterion;
import ru.naumen.core.server.hquery.criterion.LikeCriterion.LikeCriterionBuilder;
import ru.naumen.core.server.hquery.criterion.LogicCriterion;
import ru.naumen.core.server.hquery.criterion.NotCriterion;
import ru.naumen.core.server.hquery.criterion.NotInSubquery;
import ru.naumen.core.server.hquery.criterion.PropertyContainsCriterion;
import ru.naumen.core.server.hquery.criterion.RangeCriterion;
import ru.naumen.core.server.hquery.criterion.SimpleCriterion;
import ru.naumen.core.server.hquery.criterion.SimplePropertyCriterion;
import ru.naumen.core.server.hquery.criterion.SubqueryOperationCriterion;
import ru.naumen.core.server.hquery.criterion.TrueCriterion;
import ru.naumen.core.server.hquery.escape.EscapeFunctionsProvider;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.CastToType;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Набор factory method для создания {@link HCriterion} для использования в {@link HCriteria}
 *
 * <AUTHOR>
 * @since 01.02.2006 15:53:08
 */
public final class HRestrictions
{
    public static volatile boolean BIG_IN_CONSTRUCTED_SQL_ENABLE = false;
    private static final String AND = " AND "; //$NON-NLS-1$
    private static final String OR = " OR "; //$NON-NLS-1$
    private static final String EQ = "="; //$NON-NLS-1$
    private static final String NE = "<>"; //$NON-NLS-1$
    private static final String GT = ">"; //$NON-NLS-1$
    private static final String LT = "<"; //$NON-NLS-1$
    private static final String GE = ">="; //$NON-NLS-1$
    private static final String LE = "<="; //$NON-NLS-1$

    /**
     * @return возвращает {@link HCriterion} который всегда ложный
     */
    public static HCriterion alwaysFalse()
    {
        return FalseCriterion.getInstance();
    }

    /**
     * @return возвращает {@link HCriterion} который всегда истинный
     */
    public static HCriterion alwaysTrue()
    {
        return TrueCriterion.getInstance();
    }

    /**
     * @param criterions
     * @return возвращает {@link HCriterion} который является объединением нескольких {@link HCriterion} условием И
     */
    public static HCriterion and(HCriterion... criterions)
    {
        return and(Arrays.asList(criterions));
    }

    /**
     * @param criterions
     * @return возвращает {@link HCriterion} который является объединением нескольких {@link HCriterion} условием И
     */
    public static HCriterion and(@Nullable Iterable<HCriterion> criterions)
    {
        return null == criterions || Iterables.isEmpty(criterions) ? new TrueCriterion()
                : new LogicCriterion(AND, criterions);

    }

    /**
     * @param property
     * @param left
     * @param right
     * @return возвращает {@link HCriterion} проверяющий что значенеи property находится между значениями left и right
     */
    public static HCriterion between(HColumn property, Object left, Object right)
    {
        return new BetweenCriterion(property, left, right);
    }

    /**
     * Критерий для проверки нахождения значения в clob поле
     *
     * @param property
     * @param value
     * @return
     */
    public static HCriterion clobEq(HColumn property, Object value)
    {
        return new ClobContainsCriterion(property, value);
    }

    /**
     * Фильтрует объекты по One-To-Many и Many-To-Many.
     *
     * @param property
     * @return
     */
    public static HCriterion collectionEmpty(HColumn property)
    {
        return new CollectionEmptyCriterion(property);
    }

    /**
     * Возвращает критерий проверки свойства-коллекции на непустоту
     *
     * @param property свойство-коллекция
     */
    public static HCriterion collectionIsNotEmpty(HColumn property)
    {
        return new CollectionIsNotEmptyCriterion(property);
    }

    /**
     * Возвращает критерий проверки свойства-коллекции на соответствие определённому размеру
     *
     * @param property свойство-коллекция
     */
    public static HCriterion collectionSize(HColumn property, Object value)
    {
        return new CollectionSizeCriterion(property, value);
    }

    /**
     * Возвращает критерий: свойство-коллекция property содержит значение value
     *
     * @param property свойство-коллекция
     * @param value    проверяемое одиночное значение
     * @return условие проверки на вхождение
     */
    public static HCriterion contains(HColumn property, Object value)
    {
        return new ContainsCriterion(property, value);
    }

    /**
     * Возвращает критерий "содержит" для поиска в тексте с учетом морфологического значения
     * Поддержка реализована только для БД postgresql
     *
     * @param property проверяемое свойство
     * @param value    значение для поискка
     * @return критерий для поиска в тексте с учетом морфологического значения
     */
    public static HCriterion containsWithSemantic(HColumn property, String value)
    {
        return new FTSCriterion(property, value);
    }

    /**
     * Возвращает критерий проверки на равенство свойства property и значения value
     *
     * @param property проверяемое свойство
     * @param value    значение для сравнения
     * @return условие проверки на равенство
     */
    public static HCriterion eq(HColumn property, @Nullable Object value)
    {
        return new SimpleCriterion(property, EQ, value);
    }

    /**
     * Возвращает критерий проверки на равенство свойства property и значения value
     *
     * @param property   проверяемое свойство
     * @param value      значение для сравнения
     * @param ignoreCase признак "без учёта регистра букв"
     * @return условие проверки на равенство
     */
    public static HCriterion eq(HColumn property, @Nullable Object value, boolean ignoreCase)
    {
        SimpleCriterion result = new SimpleCriterion(property, EQ, value);
        result.setIgnoreCase(ignoreCase);
        return result;
    }

    /**
     * Критерий эквивалентности; в случае, если значение null, то используется {@link #isNull(HColumn)}
     *
     * @param property свойство
     * @param value    значение
     * @return критерий эквивалентности
     */
    public static HCriterion eqNullSafe(HColumn property, @Nullable Object value)
    {
        if (value != null)
        {
            return new SimpleCriterion(property, EQ, value);
        }
        return isNull(property);
    }

    /**
     * Возвращает критерий равенства значений двух свойств
     *
     * @param property1 "левое" сравниваемое свойство
     * @param property2 "правое" сравниваемое свойство
     * @return критерий равенства значений свойств
     */
    public static HCriterion eqProperty(HColumn property1, HColumn property2)
    {
        return new SimplePropertyCriterion(property1, EQ, property2);
    }

    /**
     * Критерий равенства свойства и значения подзапроса
     *
     * @param property сравниваемое свойство
     * @param subquery подзапрос
     */
    public static HCriterion eqSubquery(HColumn property, HCriteria subquery)
    {
        return new SubqueryOperationCriterion(property, EQ, subquery);
    }

    /**
     * Возвращает критерий "существуют значения, удовлетворяющие критерии subCriteria"
     *
     * @param subCriteria вложенная критерия, которая проверяется на существование
     *                    удовлетворяющих ей значений
     */
    public static HCriterion exists(HCriteria subCriteria)
    {
        return new ExistsCriterion(subCriteria);
    }

    /**
     * Возвращает критерий "property >= value"
     *
     * @param property проверяемое свойство
     * @param value    значение для сравнения
     * @return критерий "больше или равно"
     */
    public static HCriterion ge(HColumn property, Object value)
    {
        return new SimpleCriterion(property, GE, value);
    }

    /**
     * Критерий "больше или равно" для свойства и значения подзапроса
     *
     * @param property сравниваемое свойство
     * @param subquery подзапрос
     */
    public static HCriterion geSubquery(HColumn property, HCriteria subquery)
    {
        return new SubqueryOperationCriterion(property, GE, subquery);
    }

    /**
     * Возвращает критерий "property > value"
     *
     * @param property проверяемое свойство
     * @param value    значение для сравнения
     * @return критерий "больше"
     */
    public static HCriterion gt(HColumn property, Object value)
    {
        return new SimpleCriterion(property, GT, value);
    }

    /**
     * Критерий "больше" для свойства и значения подзапроса
     *
     * @param property сравниваемое свойство
     * @param subquery подзапрос
     */
    public static HCriterion gtSubquery(HColumn property, HCriteria subquery)
    {
        return new SubqueryOperationCriterion(property, GT, subquery);
    }

    /**
     * Возвращает критерий IN (вхождение значения свойства property в набор значений values)
     *
     * @param property свойство
     * @param values   набор значений
     * @return критерий IN
     */
    public static HCriterion in(HColumn property, Collection<?> values)
    {
        return in(property, values.toArray());
    }

    /**
     * Возвращает критерий IN (вхождение значения свойства property в набор значений values)
     *
     * @param property свойство
     * @param values   набор значений
     * @return критерий IN
     */
    public static HCriterion in(HColumn property, Object[] values)
    {
        if (BIG_IN_CONSTRUCTED_SQL_ENABLE && !checkEnum(values))
        {
            return new InCriterionBig(property, values);
        }
        return new InCriterion(property, values);
    }

    // для enum нужно использовать обычную реализацию InCriterion
    private static boolean checkEnum(@Nullable Object[] values)
    {
        return values != null && Arrays.stream(values).anyMatch(Enum.class::isInstance);
    }

    /**
     * Возвращает критерий, который проверяет, что значение value находится в коллекции property
     */
    public static HCriterion inElements(HColumn property, Object value)
    {
        return new InElementsCriterion(property, value);
    }

    /**
     * Создает и возвращает {@link HCriterion} для проверки наличия значения свойства среди результатов вызова
     * указанной функции
     *
     * @param property         исходное свойство
     * @param functionPattern  шаблон вызова
     * @param functionProperty свойство (столбец), подставляемый в шаблон вызова
     * @return созданное ограничение
     */
    public static HCriterion inFunction(HColumn property, String functionPattern, HProperty functionProperty)
    {
        return new InFunctionCriterion(property, functionPattern, functionProperty);
    }

    /**
     * Создает и возвращает {@link HCriterion} для проверки наличия указанного значения среди результатов вызова
     * определенной функции
     *
     * @param functionPattern  шаблон вызова функции
     * @param functionProperty свойство (столбец), подставляемый в шаблон вызова
     * @param value            проверяемое значение
     * @return созданное ограничение
     */
    public static HCriterion inFunction(String functionPattern, HProperty functionProperty, Object value)
    {
        return new InFunctionValueCriterion(functionPattern, functionProperty, value);
    }

    /**
     * Возвращает критерий, который проверяет, что значение value находится в индексе коллекции property
     */
    public static HCriterion inIndices(HColumn property, Object value)
    {
        return new InIndicesCriterion(property, value);
    }

    /**
     * Возвращает критерий, проверяющий вхождение значения свойства property в результаты подзапроса subCriteria
     *
     * @param subCriteria подзапрос
     * @param property    проверяемое свойство
     */
    public static HCriterion inSubquery(HCriteria subCriteria, HColumn property)
    {
        return new InSubquery(subCriteria, property);
    }

    public static HCriterion inWithIdStorage(HColumn property, String groupId, String tableName)
    {
        return new InCriterionWithStorage(property, groupId, tableName);
    }

    /**
     * Возвращает критерий, проверяющий, что свойство property задано
     *
     * @param property проверяемое свойство
     * @return критерий "not null"
     */
    public static HCriterion isNotNull(HColumn property)
    {
        return new IsNotNullCriterion(property);
    }

    /**
     * Возвращает критерий, проверяющий, что свойство property не задано
     *
     * @param property проверяемое свойство
     * @return критерий "is null"
     */
    public static HCriterion isNull(HColumn property)
    {
        return new IsNullCriterion(property);
    }

    /**
     * Возвращает критерий, проверяющий, что подзапрос subCriteria возвращает значение null
     *
     * @param subCriteria проверяемый подзапрос
     * @return критерий "is null"
     */
    public static HCriterion isNullSubquery(HCriteria subCriteria)
    {
        return new IsNullSubCriterion(subCriteria);
    }

    /**
     * Возвращает критерий "property <= value"
     *
     * @param property проверяемое свойство
     * @param value    значение для сравнения
     * @return критерий "меньше или равно"
     */
    public static HCriterion le(HColumn property, Object value)
    {
        return new SimpleCriterion(property, LE, value);
    }

    /**
     * Критерий "меньше или равно" для свойства и значения подзапроса
     *
     * @param property сравниваемое свойство
     * @param subquery подзапрос
     */
    public static HCriterion leSubquery(HColumn property, HCriteria subquery)
    {
        return new SubqueryOperationCriterion(property, LE, subquery);
    }

    /**
     * Создаёт фильтр like для запроса в базу данных. Фильтр like проверяет наличие определённой подстроки в значении
     * атрибута
     *
     * @param property имя колонки в таблице, по значениям которой происходит фильтрация (определяется атрибутом
     *                 метакласса)
     * @param value подстрока или значение, по которому будет производиться фильтрация
     * @return созданный фильтр like
     */
    public static HCriterion like(HColumn property, String value)
    {
        return LikeCriterionBuilder.create(property, value).build();
    }

    /**
     * Создаёт фильтр like для запроса в базу данных. Фильтр like проверяет наличие определённой подстроки в значении
     * атрибута
     *
     * @param property имя колонки в таблице, по значениям которой происходит фильтрация (определяется атрибутом
     *                 метакласса)
     * @param value подстрока или значение, по которому будет производиться фильтрация
     * @param matchMode определяет местоположение <b>value</b> в значении атрибута (начало, конец и т.д.)
     * @return созданный фильтр like
     */
    public static HCriterion like(HColumn property, String value, int matchMode)
    {
        return LikeCriterionBuilder.create(property, value)
                .matchMode(matchMode)
                .build();
    }

    /**
     * Создаёт фильтр like для запроса в базу данных. Фильтр like проверяет наличие определённой подстроки в значении
     * атрибута
     *
     * @param property имя колонки в таблице, по значениям которой происходит фильтрация (определяется атрибутом
     *                 метакласса)
     * @param value подстрока или значение, по которому будет производиться фильтрация
     * @param matchMode определяет местоположение <b>value</b> в значении атрибута (начало, конец и т.д.)
     * @param ignoreCase нужно ли учитывать регистр символов в значении атрибута
     * @return созданный фильтр like
     */
    public static HCriterion like(HColumn property, String value, int matchMode, boolean ignoreCase)
    {
        return LikeCriterionBuilder.create(property, value)
                .matchMode(matchMode)
                .ignoreCase(ignoreCase)
                .build();
    }

    /**
     * Создаёт фильтр like для запроса в базу данных. Фильтр like проверяет наличие определённой подстроки в значении
     * атрибута
     *
     * @param property имя колонки в таблице, по значениям которой происходит фильтрация (определяется атрибутом
     *                 метакласса)
     * @param value подстрока или значение, по которому будет производиться фильтрация
     * @param matchMode определяет местоположение <b>value</b> в значении атрибута (начало, конец и т.д.)
     * @param ignoreCase нужно ли учитывать регистр символов в значении атрибута
     * @param revert использовать ли обратную запись при описании запроса
     *               <pre>(_property _op :paramName) -> (:paramName _op _property)</pre>
     * @return созданный фильтр like
     */
    public static HCriterion like(HColumn property, String value, int matchMode, boolean ignoreCase, boolean revert)
    {
        return LikeCriterionBuilder.create(property, value)
                .matchMode(matchMode)
                .ignoreCase(ignoreCase)
                .revert(revert)
                .build();
    }

    /**
     * Создаёт фильтр like для запроса в базу данных. Фильтр like проверяет наличие определённой подстроки в значении
     * атрибута
     *
     * @param property имя колонки в таблице, по значениям которой происходит фильтрация (определяется атрибутом
     *                 метакласса)
     * @param value подстрока или значение, по которому будет производиться фильтрация
     * @param matchMode определяет местоположение <b>value</b> в значении атрибута (начало, конец и т.д.)
     * @param ignoreCase нужно ли учитывать регистр символов в значении атрибута
     * @param revert использовать ли обратную запись при описании запроса
     *               <pre>(_property _op :paramName) -> (:paramName _op _property)</pre>
     * @param escapeProvider обработчик, позволяющий экранировать специальные символы внутри строк, передаваемых в
     *                       запрос
     * @return созданный фильтр like
     */
    public static HCriterion like(HColumn property, String value, int matchMode, boolean ignoreCase, boolean revert,
            @Nullable EscapeFunctionsProvider escapeProvider)
    {
        return LikeCriterionBuilder.create(property, value)
                .matchMode(matchMode)
                .ignoreCase(ignoreCase)
                .revert(revert)
                .escapeProvider(escapeProvider)
                .build();
    }

    /**
     * Создаёт фильтр like для запроса в базу данных. Фильтр like проверяет наличие определённой подстроки в значении
     * атрибута
     *
     * @param property имя колонки в таблице, по значениям которой происходит фильтрация (определяется атрибутом
     *                 метакласса)
     * @param value подстрока или значение, по которому будет производиться фильтрация
     * @param matchMode определяет местоположение <b>value</b> в значении атрибута (начало, конец и т.д.)
     * @param ignoreCase нужно ли учитывать регистр символов в значении атрибута
     * @param revert использовать ли обратную запись при описании запроса
     *               <pre>(_property _op :paramName) -> (:paramName _op _property)</pre>
     * @param escapeProvider обработчик, позволяющий экранировать специальные символы внутри строк, передаваемых в
     *                       запрос
     * @param castTo тип, к которому нужно выполнить преобразование значения атрибута. Если null - то преобразование
     *               выполняться не будет. <b>ВАЖНО</b>: использование этого параметра может иметь side effect (см.
     *               AbstractRestrictionStrategy#resolveValueRaw(Attribute, Object, String, CastToType)).
     * @return созданный фильтр like
     */
    public static HCriterion like(HColumn property, String value, int matchMode, boolean ignoreCase, boolean revert,
            @Nullable EscapeFunctionsProvider escapeProvider, CastToType castTo)
    {
        return LikeCriterionBuilder.create(property, value)
                .matchMode(matchMode)
                .ignoreCase(ignoreCase)
                .revert(revert)
                .escapeProvider(escapeProvider)
                .castTo(castTo)
                .build();
    }

    /**
     * Возвращает критерий "property < value"
     *
     * @param property проверяемое свойство
     * @param value    значение для сравнения
     * @return критерий "меньше"
     */
    public static HCriterion lt(HColumn property, Object value)
    {
        return new SimpleCriterion(property, LT, value);
    }

    /**
     * Возвращает критерий "property1 < property2"
     *
     * @param property1 "левое" свойство
     * @param property2 "правое" свойство
     * @return критерий "первое свойство меньше второго"
     */
    public static HCriterion ltProperty(HColumn property1, HColumn property2)
    {
        return new SimplePropertyCriterion(property1, LT, property2);
    }

    /**
     * Критерий "меньше" для свойства и значения подзапроса
     *
     * @param property сравниваемое свойство
     * @param subquery подзапрос
     */
    public static HCriterion ltSubquery(HColumn property, HCriteria subquery)
    {
        return new SubqueryOperationCriterion(property, LT, subquery);
    }

    /**
     * Возвращает критерий "не равно"
     *
     * @param property проверяемое свойство
     * @param value    значение для сравнения
     * @return критерий "не равно"
     */
    public static HCriterion ne(HColumn property, Object value)
    {
        return new SimpleCriterion(property, NE, value);
    }

    /**
     * Возвращает критерий "property1 не равно property2"
     *
     * @param property1 "левое" свойство
     * @param property2 "правое" свойство
     * @return критерий "не равно"
     */
    public static HCriterion neProperty(HColumn property1, HColumn property2)
    {
        return new SimplePropertyCriterion(property1, NE, property2);
    }

    /**
     * Критерий неравенства свойства и значения подзапроса
     *
     * @param property сравниваемое свойство
     * @param subquery подзапрос
     */
    public static HCriterion neSubquery(HColumn property, HCriteria subquery)
    {
        return new SubqueryOperationCriterion(property, NE, subquery);
    }

    /**
     * Возвращает критерий "НЕ"
     *
     * @param criterion вложенный критерий, результаты которого мы инвертируем
     * @return отрицательный критерий
     */
    public static HCriterion not(HCriterion criterion)
    {
        return new NotCriterion(criterion);
    }

    /**
     * Возвращает критерий "значение property не входит в перечень значений values"
     * <p>
     * Использование этой критерии может привести к ошибке, если количество объектов в values будет слишком велико (>
     * 2 тыс).
     *
     * @param property проверяемое свойство
     * @param values   значение для сравнения
     * @return критерий "NOT IN"
     */
    public static HCriterion notIn(HColumn property, Collection<?> values)
    {
        return notIn(property, values.toArray());
    }

    /**
     * Возвращает критерий "значение property не входит в перечень значений values"
     * <p>
     * Использование этой критерии может привести к ошибке, если количество объектов в values будет слишком велико (>
     * 2 тыс).
     *
     * @param property проверяемое свойство
     * @param values   значение для сравнения
     * @return критерий "NOT IN"
     */
    public static HCriterion notIn(HColumn property, Object[] values)
    {
        return not(in(property, values));
    }

    /**
     * Возвращает критерий, проверяющий отсутствие значения свойства property в результатах подзапроса subCriteria
     *
     * @param subCriteria подзапрос
     * @param property    проверяемое свойство
     * @return критерий "NOT IN"
     */
    public static HCriterion notInSubquery(HCriteria subCriteria, HColumn property)
    {
        return new NotInSubquery(subCriteria, property);
    }

    /**
     * Возвращает критерий, являющийся объединением вложенных критериев по "ИЛИ"
     *
     * @param criterions вложенные критерии
     * @return логический критерий "ИЛИ"
     */
    public static HCriterion or(HCriterion... criterions)
    {
        return new LogicCriterion(OR, criterions);
    }

    /**
     * Возвращает критерий, являющийся объединением вложенных критериев по "ИЛИ"
     *
     * @param left  вложенный критерий
     * @param right вложенный критерий
     * @return логический критерий "ИЛИ"
     */
    public static HCriterion or(HCriterion left, HCriterion right)
    {
        return new LogicCriterion(OR, left, right);
    }

    /**
     * Возвращает критерий, являющийся объединением вложенных критериев по "ИЛИ"
     *
     * @param criterions вложенные критерии
     * @return логический критерий "ИЛИ"
     */
    public static HCriterion or(List<HCriterion> criterions)
    {
        HCriterion[] arr = new HCriterion[criterions.size()];
        criterions.toArray(arr);
        return or(arr);

    }

    /**
     * Вовращает ограничение для сriteria на исполнителя,
     * метод criteria.getProperty("id") должен возвращать проверяемые id исполнителей,
     * а именно id объектов класса performerFqn
     */
    public static HCriterion performer(HCriteria criteria, ClassFqn performerFqn)
    {
        return getPerformer(criteria, performerFqn, null);
    }

    /**
     * Вовращает ограничение для сriteria на исполнителя,
     * метод criteria.getProperty("id") должен возвращать проверяемые id исполнителей,
     * а именно id объектов класса performerFqn
     */
    public static HCriterion performer(HCriteria criteria, ClassFqn performerFqn,
            Collection<String> licenceEmployeesForClass)
    {
        return getPerformer(criteria, performerFqn, licenceEmployeesForClass);
    }

    /**
     * Возвращает критерий "collectionProperty содержит значение property"
     *
     * @param collectionProperty свойство-коллекция
     * @param property           свойство с единичным значением
     * @return критерий "свойство содержит"
     */
    public static HCriterion propertyContains(HColumn collectionProperty, HColumn property)
    {
        return new PropertyContainsCriterion(property, collectionProperty);
    }

    public static HCriterion range(HColumn property, Object minValue, Object maxValue, boolean strictMode)
    {
        return new RangeCriterion(property, minValue, maxValue, strictMode);
    }

    /**
     * Возвращает простой критерий сравнения двух свойств
     *
     * @param property1 "левое" свойство
     * @param op        операция сравнения
     * @param property2 "правое" свойство
     * @return простой критерий сравнения двух свойств
     */
    public static HCriterion simple(HColumn property1, String op, HColumn property2)
    {
        return new SimplePropertyCriterion(property1, op, property2);
    }

    private static HCriterion getPerformer(HCriteria criteria, ClassFqn performerFqn,
            @Nullable Collection<String> licenceEmployeesForClass)
    {
        if (Employee.FQN.isSameClass(performerFqn))
        {
            HCriteria employees = HHelper.createOverCriteria(criteria).addSource(Constants.Employee.FQN.toString());
            HCriteria licenses = employees.addInnerJoin(Constants.Employee.LICENSE);
            employees.add(HRestrictions.eqProperty(criteria.getProperty("id"), employees.getProperty("id")));
            if (null == licenceEmployeesForClass)
            {
                employees.add(HRestrictions.not(HRestrictions.inElements(licenses,
                        Constants.Employee.NOT_LICENSED_USER)));
            }
            else
            {
                employees.add(HRestrictions.in(licenses, licenceEmployeesForClass));
            }
            employees.add(HRestrictions.eq(employees.getProperty(Constants.AbstractBO.REMOVED), false));

            return HRestrictions.exists(employees);
        }
        else if (Team.FQN.isSameClass(performerFqn))
        {
            HCriteria employees = HHelper.createOverCriteria(criteria).addSource(Constants.Employee.FQN.toString());
            HCriteria licenses = employees.addInnerJoin(Constants.Employee.LICENSE);

            HCriteria teams = employees.addInnerJoin(Constants.Employee.TEAMS);
            employees.add(HRestrictions.eqProperty(teams.getProperty("id"), criteria.getProperty("id")));
            employees.add(HRestrictions.eq(employees.getProperty(Constants.AbstractBO.REMOVED), false));

            if (null == licenceEmployeesForClass)
            {
                employees.add(HRestrictions.not(HRestrictions.inElements(licenses,
                        Constants.Employee.NOT_LICENSED_USER)));
            }
            else
            {
                employees.add(HRestrictions.in(licenses, licenceEmployeesForClass));
            }

            return HRestrictions.exists(employees);
        }

        throw new FxException("Incorrect has performer class - " + performerFqn);
    }

    private HRestrictions()
    {
    }
}