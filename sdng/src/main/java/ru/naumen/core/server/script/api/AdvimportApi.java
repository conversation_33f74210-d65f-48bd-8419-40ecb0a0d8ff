package ru.naumen.core.server.script.api;

import static ru.naumen.advimport.server.AdvimportParametersConstants.ERROR;
import static ru.naumen.advimport.server.AdvimportParametersConstants.QUEUED;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.base.Joiner;

import ru.naumen.advimport.server.AdvImportApiUtils;
import ru.naumen.advimport.server.AdvImportQueueSender;
import ru.naumen.advimport.server.AdvImportUtils;
import ru.naumen.advimport.server.AdvimportInfoService;
import ru.naumen.advimport.server.AdvimportTasksState;
import ru.naumen.advimport.server.Constants;
import ru.naumen.advimport.server.RunImportMessage;
import ru.naumen.advimport.server.RunImportMessageConfiguration;
import ru.naumen.advimport.server.dispatch.HandlerUtils;
import ru.naumen.advimport.server.engine.AdvImportException;
import ru.naumen.advimport.shared.ImportConfigContainer;
import ru.naumen.advimport.shared.config.GuiParameter;
import ru.naumen.advimport.shared.config.Parameter;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileStorageHelper;
import ru.naumen.core.server.filestorage.spi.FileContentStorageBean;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * API для работы с AdvImport
 *
 * <AUTHOR>
 * @since 4.2.15
 * @see IAdvimportApi
 */
@Component("advimport")
public class AdvimportApi implements IAdvimportApi
{
    private static final Logger LOG = LoggerFactory.getLogger(AdvimportApi.class);
    private static final Pattern ADVIMPORT_CONFIG_PATTERN = Pattern.compile("advImportConfig\\:?");

    private final AdvimportTasksState tasksState;
    private final AdvImportApiUtils advImportApiUtils;
    private final AdvImportQueueSender sender;
    private final MessageFacade messages;
    private final MetaStorageService metaStorageService;
    private final AdvImportUtils advImportHelper;
    private final HandlerUtils handlerUtils;
    private final FileContentStorageBean fileContentStorageBean;
    private final CommonUtils commonUtils;
    private final MetainfoServiceBean metainfoService;
    private final ScriptDtOHelper scriptDtOHelper;
    private final AdvimportInfoService advimportService;

    @Inject
    public AdvimportApi(final AdvimportTasksState tasksState,
            final AdvImportApiUtils advImportApiUtils,
            final AdvImportQueueSender sender,
            final MessageFacade messages,
            final MetaStorageService metaStorageService,
            final AdvImportUtils advImportHelper,
            final HandlerUtils handlerUtils,
            final FileContentStorageBean fileContentStorageBean,
            final CommonUtils commonUtils,
            final MetainfoServiceBean metainfoService,
            final ScriptDtOHelper scriptDtOHelper,
            final AdvimportInfoService advimportService)
    {
        this.tasksState = tasksState;
        this.advImportApiUtils = advImportApiUtils;
        this.sender = sender;
        this.messages = messages;
        this.metaStorageService = metaStorageService;
        this.advImportHelper = advImportHelper;
        this.handlerUtils = handlerUtils;
        this.fileContentStorageBean = fileContentStorageBean;
        this.commonUtils = commonUtils;
        this.metainfoService = metainfoService;
        this.scriptDtOHelper = scriptDtOHelper;
        this.advimportService = advimportService;
    }

    @Override
    public void showStatusBar(boolean statusbar)
    {
        advImportApiUtils.showStatusBar(statusbar);
    }

    @Override
    public void start(String advimportConfigCode, Map<String, Object> parameters)
    {
        advimportConfigCode = ADVIMPORT_CONFIG_PATTERN.matcher(advimportConfigCode).replaceAll("");

        ImportConfigContainer cnt = metaStorageService.get(Constants.ADVIMPORT_METASTORAGE_TYPE, advimportConfigCode);
        checkAllGuiParameters(cnt.getConfigContainer().getConfig(), parameters);
        ArrayList<Parameter> params = advImportApiUtils.prepareParameters(parameters);

        RunImportMessage msg = new RunImportMessage(advimportConfigCode, params);
        sender.send(msg);
    }

    @Override
    public String stop(String code)
    {
        return tasksState.stopAdvimport(code);
    }

    @Override
    public List<String> stopAll()
    {
        Set<String> imports = advimportService.getTaskStates().keySet();
        List<String> messages = new ArrayList<>();
        imports.forEach(singleImport -> messages.add(stop(singleImport)));
        return messages;
    }

    @Override
    public List<String> getActiveImports()
    {
        return advimportService.getTaskStates().entrySet().stream()
                .filter(Entry::getValue)
                .map(Entry::getKey)
                .collect(Collectors.toList());
    }

    @Override
    public Map<String, Object> getImportInfo(String uuid)
    {
        return advImportHelper.prepareAdvimportParameters(uuid);
    }

    @Override
    public void importByXmlConfiguration(final String uuidFileXmlConfiguration,
            final IUUIDIdentifiable objectForLogAttachUuid, @Nullable final String attribute,
            final Map<String, Object> parameters)
    {
        importByXmlConfiguration(uuidFileXmlConfiguration, objectForLogAttachUuid, attribute, parameters, null);
    }

    @Override
    public void importByXmlConfiguration(final String uuidFileXmlConfiguration,
            final IUUIDIdentifiable objectForLogAttachUuid, final Map<String, Object> parameters)
    {
        importByXmlConfiguration(uuidFileXmlConfiguration, objectForLogAttachUuid, null, parameters);
    }

    @Override
    public void importByXmlConfiguration(final IUUIDIdentifiable fileXmlConfiguration,
            final IUUIDIdentifiable objectForLogAttachUuid, final Map<String, Object> parameters)
    {
        importByXmlConfiguration(fileXmlConfiguration.getUUID(), objectForLogAttachUuid, parameters);
    }

    @Override
    public void importByXmlConfiguration(final IUUIDIdentifiable fileXmlConfiguration,
            final IUUIDIdentifiable objectForLogAttachUuid, final String attribute,
            final Map<String, Object> parameters)
    {
        importByXmlConfiguration(fileXmlConfiguration.getUUID(), objectForLogAttachUuid, attribute, parameters);
    }

    @Override
    public void importByXmlConfiguration(final String uuidFileXmlConfiguration,
            final IUUIDIdentifiable objectForLogAttachUuid, final Map<String, Object> parameters,
            final String customUuid)
    {
        importByXmlConfiguration(uuidFileXmlConfiguration, objectForLogAttachUuid, null, parameters, customUuid);
    }

    @Override
    public void importByXmlConfiguration(final IUUIDIdentifiable fileXmlConfiguration,
            final IUUIDIdentifiable objectForLogAttachUuid, final Map<String, Object> parameters,
            final String customUuid)
    {
        importByXmlConfiguration(fileXmlConfiguration.getUUID(), objectForLogAttachUuid, parameters, customUuid);
    }

    @Override
    public void importByXmlConfiguration(final String uuidFileXmlConfiguration,
            final IUUIDIdentifiable objectForLogAttachUuid, @Nullable final String attribute,
            final Map<String, Object> parameters, @Nullable final String customUuid)
    {
        final File file = commonUtils.getByUUID(uuidFileXmlConfiguration);
        try
        {
            // проверяем конфигурацию импорта
            final String xmlConfiguration = IOUtils.toString(fileContentStorageBean.getContent(file),
                    StandardCharsets.UTF_8);
            handlerUtils.validateConfiguration(xmlConfiguration);

            // проверяем параметры
            checkAllGuiParameters(xmlConfiguration, parameters);
            ArrayList<Parameter> params = advImportApiUtils.prepareParameters(parameters);

            RunImportMessage msg = new RunImportMessage();
            msg.setUuidFileXmlConfigurationUuid(uuidFileXmlConfiguration);
            msg.setCustomUuid(customUuid);
            msg.setParameters(params);
            if (attribute != null)
            {
                MetaClass mc = metainfoService.getMetaClass(scriptDtOHelper.<Object> unwrap(objectForLogAttachUuid));
                String relation = FileStorageHelper.getRelation(mc.getAttribute(attribute).getDeclaredMetaClass(),
                        attribute);
                msg.setRelation(relation);
            }
            msg.setObjectForLogAttachUuid(objectForLogAttachUuid.getUUID());

            sender.send(msg);
            advImportHelper.putAdvimportStatus(customUuid == null ? uuidFileXmlConfiguration : customUuid, QUEUED);
        }
        catch (IOException e)
        {
            advImportHelper.putAdvimportStatus(customUuid == null ? uuidFileXmlConfiguration : customUuid, ERROR);
            LOG.error(e.getMessage(), e);
        }
    }

    @Override
    public void importByXmlConfiguration(final IUUIDIdentifiable fileXmlConfiguration,
            final IUUIDIdentifiable objectForLogAttachUuid, final String attribute,
            final Map<String, Object> parameters, final String customUuid)
    {
        importByXmlConfiguration(fileXmlConfiguration.getUUID(), objectForLogAttachUuid, attribute, parameters,
                customUuid);
    }

    @Override
    public IRunImportMessageConfiguration createConfig()
    {
        IRunImportMessageConfiguration config = new RunImportMessageConfiguration();
        SpringContext.getInstance().autowireBean(config);
        return config;
    }

    /**
     * Метод проверки наличия необходимых GUI параметров
     * @param xmlConfiguration xml конфигурация импорта
     * @param parameters карта из кодом параметров и их значений
     */
    private void checkAllGuiParameters(String xmlConfiguration, Map<String, Object> parameters)
    {
        List<GuiParameter> guiParams = advImportHelper.parseConfig(xmlConfiguration).getGuiParameters();

        ArrayList<String> errorMsgs = new ArrayList<>();
        for (GuiParameter guiParam : guiParams)
        {
            if (!parameters.containsKey(guiParam.getName()))
            {
                errorMsgs.add(guiParam.getName());
            }
        }

        if (!errorMsgs.isEmpty())
        {
            throw new AdvImportException(
                    messages.getMessage("advimport.AdvimportApi.noParameter", Joiner.on(", ").join(errorMsgs)));
        }
    }

}
