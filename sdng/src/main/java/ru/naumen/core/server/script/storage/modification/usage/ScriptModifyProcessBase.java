package ru.naumen.core.server.script.storage.modification.usage;

import static ru.naumen.commons.shared.utils.CollectionUtils.isEqualSmallCollections;
import static ru.naumen.core.shared.Constants.ScriptsComponentTree.EMPTY;
import static ru.naumen.core.shared.Constants.ScriptsComponentTree.EXISTING_SCRIPT;
import static ru.naumen.core.shared.Constants.ScriptsComponentTree.NEW_SCRIPT;
import static ru.naumen.core.shared.permission.AdminProfileAccessMarker.SCRIPTS;
import static ru.naumen.core.shared.permission.PermissionType.CREATE;
import static ru.naumen.core.shared.permission.PermissionType.EDIT;

import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.Objects;

import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Lazy;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.admin.server.permission.AdminPermissionCheckService;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.spi.ScriptCacheInvalidationHelper;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.script.storage.modification.edit.ScriptBodyUpdateStrategy;
import ru.naumen.core.server.script.storage.modification.edit.ScriptBodyUpdateStrategyFactory;
import ru.naumen.core.server.sets.usage.BeforeEditMetaInfoElementSettingsSetEvent;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.common.I18nUtil;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.sec.server.admin.log.ScriptLogService;

/**
 * Контракт поведения при изменении скрипта в соответствующей скриптовой настройке Т
 * Заданы основные шаги редактирования, и каждая скриптовая настройка Т переопределяет
 * необходимые действия.
 * Базовый абстрактный класс содержащий общие методы, не добавлять специфичной логики!
 * Все специфичные действия переопределяются в наследниках.
 * <AUTHOR>
 * @since Oct 23, 2015
 */
public abstract class ScriptModifyProcessBase<T> implements ScriptModifyProcess<T>
{
    @Lazy
    @Inject
    protected ScriptStorageService scriptStorageService;
    @Lazy
    @Inject
    private MetainfoServicePersister persister;
    @Lazy
    @Inject
    private I18nUtil i18nUtil;
    @Lazy
    @Inject
    private ScriptBodyUpdateStrategyFactory bodyUpdateStrategyFactory;
    @Lazy
    @Inject
    private ScriptLogService scriptLogService;
    @Lazy
    @Inject
    private MessageFacade messages;
    @Lazy
    @Inject
    private ScriptService scriptService;

    @Inject
    private ScriptCacheInvalidationHelper invalidationHelper;

    @Inject
    private ApplicationEventPublisher eventPublisher;
    @Inject
    private AdminPermissionCheckService adminPermissionCheckService;

    @Override
    public void copyHolder(T newHolder, ScriptModifyContext context)
    {
        String scriptCode = getOldScriptCode(newHolder, context);

        if (StringUtilities.isEmpty(scriptCode))
        {
            return;
        }

        Script script = scriptStorageService.getScript(scriptCode);
        context.addScriptLogInfo(createChangedScriptLog(script));
        context.setScriptBDPropertiesChanged(false);

        Preconditions.checkArgument(!CollectionUtils.isEmpty(script.getUsagePoints()),
                "Script " + script.getCode() + " must have usage points when copy from old holder");

        ScriptUsagePoint usage = getUsagePoint(newHolder, context);
        script.getUsagePoints().add(usage);

        saveScript(script, context);
    }

    @Override
    public void deleteHolder(T holder, ScriptModifyContext context)
    {
        String oldScriptCode = getOldScriptCode(holder, context);
        setNewScriptCode(null, holder, context);

        if (StringUtilities.isEmptyTrim(oldScriptCode))
        {
            return;
        }

        context.setImmediateLogSnapshot(true);
        removeUsagePoint(oldScriptCode, holder, context);
    }

    @Override
    public void save(T oldHolder, T newHolder, ScriptDto newScript, ScriptModifyContext context)
    {
        String oldScriptCode = getOldScriptCode(oldHolder, context);
        String newScriptCode = newScript.getCode();
        String selectStrategy = newScript.getSelectStrategy();

        if (EMPTY.equals(selectStrategy))
        {
            removeScriptUsage(oldScriptCode, newHolder, context);
            return;
        }

        Preconditions.checkNotNull(newScriptCode, "New script code can't be null!");

        if (NEW_SCRIPT.equals(selectStrategy))
        {
            adminPermissionCheckService.checkPermission(SCRIPTS, CREATE);

            newScript.setSelectStrategy(EXISTING_SCRIPT);
            addNewScript(oldScriptCode, newScript, newHolder, context);
            return;
        }

        if (ObjectUtils.equals(oldScriptCode, newScriptCode))
        {
            editScriptProperties(newHolder, newScript, context, false);
            return;
        }

        changeScriptWithEdit(oldScriptCode, newScript, newHolder, context);
    }

    protected abstract void dataUpdateSelfUsage(Script script, T newHolder, ScriptModifyContext context);

    protected abstract void dataUpdateSelfUsageAfterRemove(T holder, ScriptModifyContext context);

    protected abstract String getLocation(T holder, ScriptModifyContext context);

    @Nullable
    protected abstract String getOldScriptCode(T oldHolder, ScriptModifyContext context);

    protected abstract Collection<ClassFqn> getRelatedMetaClasses(T holder, ScriptModifyContext context);

    /**
     * Флаг возможности изменения параметров места использования при редактировании объектов
     * Если место не изменяется - часть действий на генерацию места использования пропускается
     */
    protected boolean isUsageChangeable()
    {
        return true;
    }

    protected boolean isUsagePointMatch(ScriptUsagePoint usage, String location,
            Collection<ClassFqn> relatedMetaClasses, ScriptModifyContext context)
    {
        return Objects.equals(usage.getLocation(), location)
               && Objects.equals(usage.getCategory(), context.getCategory())
               && Objects.equals(usage.getHolderType(), context.getHolderType());
    }

    protected abstract void setNewScriptCode(@Nullable String newScriptCode, T holder, ScriptModifyContext context);

    private void addNewScript(@Nullable String oldScriptCode, ScriptDto newScript, T newHolder,
            ScriptModifyContext context)
    {
        String newScriptCode = newScript.getCode();
        Script existingScript = scriptStorageService.getScript(newScriptCode);

        if (existingScript != null)
        {
            throw new FxException(messages.getMessage("scriptcatalog-scriptCodeExistsError", newScriptCode));
        }

        if (!StringUtilities.isEmptyTrim(oldScriptCode))
        {
            removeUsagePoint(oldScriptCode, newHolder, context);
        }

        Script script = generateNewScript(newScript, newHolder, context);
        context.addScriptLogInfo(createNewScriptLog(script));
        setNewScriptCode(script.getCode(), newHolder, context);
        dataUpdateSelfUsage(script, newHolder, context);
        eventPublisher.publishEvent(
                new BeforeEditMetaInfoElementSettingsSetEvent(script, null, newScript.getSettingsSet()));
    }

    private void changeScriptWithEdit(@Nullable String oldScriptCode, ScriptDto newScript, T newHolder,
            ScriptModifyContext context)
    {
        if (!StringUtilities.isEmptyTrim(oldScriptCode))
        {
            removeUsagePoint(oldScriptCode, newHolder, context);
        }

        setNewScriptCode(newScript.getCode(), newHolder, context);
        editScriptProperties(newHolder, newScript, context, true);
    }

    private static boolean compareScriptUsagePoints(ScriptUsagePoint usage1, ScriptUsagePoint usage2)
    {
        return Objects.equals(usage1.getLocation(), usage2.getLocation())
               && Objects.equals(usage1.getCategory(), usage2.getCategory())
               && Objects.equals(usage1.getHolderType(), usage2.getHolderType())
               && isEqualSmallCollections(usage1.getRelatedMetaClassFqns(), usage2.getRelatedMetaClassFqns());
    }

    private boolean compareUsages(Script oldScript, T holder, ScriptModifyContext context)
    {
        ScriptUsagePoint oldUsagePoint = findUsagePoint(oldScript, holder, context);
        ScriptUsagePoint newUsagePoint = getUsagePoint(holder, context);
        return compareScriptUsagePoints(oldUsagePoint, newUsagePoint);
    }

    private ScriptAdminLogInfo createChangedScriptLog(Script script)
    {
        ScriptAdminLogInfo scriptLogInfo = new ScriptAdminLogInfo();
        scriptLogInfo.setOldScriptProperties(scriptLogService.getScriptLogInfo(script));
        scriptLogInfo.setNewScript(script);
        scriptLogInfo.setSave(true);
        return scriptLogInfo;
    }

    private ScriptAdminLogInfo createChangedScriptLogBeforeChanges(Script script)
    {
        ScriptAdminLogInfo scriptLogInfo = new ScriptAdminLogInfo();
        scriptLogInfo.setOldScriptProperties(scriptLogService.getScriptLogInfo(script));
        return scriptLogInfo;
    }

    private static ScriptAdminLogInfo createNewScriptLog(Script script)
    {
        ScriptAdminLogInfo scriptLogInfo = new ScriptAdminLogInfo();
        scriptLogInfo.setNewScript(script);
        scriptLogInfo.setSave(true);
        return scriptLogInfo;
    }

    private void dataUpdateOtherUsage(Script script, T currentHolder, ScriptModifyContext context)
    {
        String location = getLocation(currentHolder, context);
        Collection<ClassFqn> relatedMetaClasses = getRelatedMetaClasses(currentHolder, context);
        for (ScriptUsagePoint usage : script.getUsagePoints())
        {
            if (isUsagePointMatch(usage, location, relatedMetaClasses, context))
            {
                continue;
            }

            ScriptBodyUpdateStrategy updateStrategy = bodyUpdateStrategyFactory.getStrategy(usage.getCategory());
            updateStrategy.updateScriptUsagePoint(usage);
        }
    }

    private void editScriptProperties(T newHolder, ScriptDto newScript, ScriptModifyContext context, boolean newUsage)
    {
        Script oldScript = scriptStorageService.getScript(newScript.getCode());
        boolean bdPropertiesEquals = isScriptBDPropertiesEquals(oldScript, newScript);
        context.setScriptBDPropertiesChanged(!bdPropertiesEquals);

        if (Boolean.FALSE.equals(isScriptChanged(oldScript, newHolder, context, newUsage)))
        {
            return;
        }
        if (Boolean.FALSE.equals(newUsage))
        {
            adminPermissionCheckService.checkPermission(SCRIPTS, EDIT);
        }
        adminPermissionCheckService.checkPermission(oldScript, EDIT);

        Script script = oldScript.clone();
        context.addScriptLogInfo(createChangedScriptLog(script));
        final boolean scriptBodyChanged = newScript.isEditable() && isScriptBodyChanged(script, newScript);
        if (scriptBodyChanged)
        {
            invalidationHelper.invalidateScript(oldScript);
        }
        updateScriptBDProperties(script, newScript, context);
        updateUsagePoints(script, newHolder, context, newUsage);
        saveScript(script, context);
        if (scriptBodyChanged)
        {
            dataUpdateOtherUsage(script, newHolder, context);
        }
        if (scriptBodyChanged || newUsage)
        {
            dataUpdateSelfUsage(script, newHolder, context);
        }
        eventPublisher.publishEvent(new BeforeEditMetaInfoElementSettingsSetEvent(script, oldScript.getSettingsSet(),
                script.getSettingsSet()));
    }

    /**
     * Используется только в тех случаях когда мы знаем, что уже скрипт использовался - иначе можно решить
     * проблему без пробега массива путем проталкивания флага newUsage
     */
    private ScriptUsagePoint findUsagePoint(Script script, T holder, ScriptModifyContext context)
    {
        if (CollectionUtils.isEmpty(script.getUsagePoints()))
        {
            throw new FxException("Usage point for " + holder + " in script " + script.getCode() + " not found");
        }

        String location = getLocation(holder, context);
        Collection<ClassFqn> relatedMetaClasses = getRelatedMetaClasses(holder, context);

        ScriptUsagePoint currentUsage = null;
        for (ScriptUsagePoint usage : script.getUsagePoints())
        {
            if (isUsagePointMatch(usage, location, relatedMetaClasses, context))
            {
                currentUsage = usage;
                break;
            }
        }

        Preconditions.checkNotNull(currentUsage,
                "Usage point for " + holder + " in script " + script.getCode() + " not found");
        return currentUsage;
    }

    private Script generateNewScript(ScriptDto newScript, T newHolder, ScriptModifyContext context)
    {
        Script script = new Script();
        context.setScriptBDPropertiesChanged(true);
        script.setCode(newScript.getCode());
        updateScriptBDProperties(script, newScript, context);

        ScriptUsagePoint usage = getUsagePoint(newHolder, context);
        script.setUsagePoints(Lists.newArrayList(usage));
        saveScript(script, context);

        return script;
    }

    private ScriptUsagePoint getUsagePoint(T holder, ScriptModifyContext context)
    {
        String location = getLocation(holder, context);
        Collection<ClassFqn> relatedMetaClasses = getRelatedMetaClasses(holder, context);
        ScriptUsagePoint usage = new ScriptUsagePoint();
        usage.setLocation(location);
        usage.setCategory(context.getCategory());
        usage.setHolderType(context.getHolderType());
        usage.setRelatedMetaClassFqns(relatedMetaClasses);

        return usage;
    }

    /**
     * Проверка на изменение свойств, которые сохраняются в БД
     */
    private boolean isScriptBDPropertiesEquals(Script oldScript, ScriptDto newScript)
    {
        if (!newScript.isLoaded())
        {
            return true;
        }
        return ObjectUtils.equals(i18nUtil.getLocalizedTitle(oldScript), newScript.getTitle())
               && ObjectUtils.equals(oldScript.getBody(), newScript.getBody())
               && ObjectUtils.equals(oldScript.getSettingsSet(), newScript.getSettingsSet());
    }

    private static boolean isScriptBodyChanged(Script oldScript, ScriptDto newScript)
    {
        return newScript.isLoaded() && !ObjectUtils.equals(oldScript.getBody(), newScript.getBody());
    }

    /**
     * Совокупность проверок на изменение скрипта, включая изменение его мест использования
     */
    private boolean isScriptChanged(Script oldScript, T holder, ScriptModifyContext context, boolean newUsage)
    {
        if (newUsage)
        {
            return true;
        }

        boolean equals = !context.isScriptBDPropertiesChanged();
        if (isUsageChangeable())
        {
            equals = equals && compareUsages(oldScript, holder, context);
        }

        return !equals;
    }

    private void processChangedScriptLogAfterChanges(Script script, ScriptAdminLogInfo scriptLogInfo)
    {
        scriptLogInfo.setNewScriptProperties(scriptLogService.getScriptLogInfo(script));
        scriptLogInfo.setSave(true);
    }

    private void removeScriptUsage(@Nullable String oldScriptCode, T holder, ScriptModifyContext context)
    {
        setNewScriptCode(null, holder, context);

        if (StringUtilities.isEmptyTrim(oldScriptCode))
        {
            return;
        }

        removeUsagePoint(oldScriptCode, holder, context);
        dataUpdateSelfUsageAfterRemove(holder, context);
    }

    private void removeUsagePoint(String oldScriptCode, T holder, ScriptModifyContext context)
    {
        Script oldScript = scriptStorageService.getScript(oldScriptCode);
        if (oldScript == null)
        {
            return;
        }
        Script script = oldScript.clone();
        if (CollectionUtils.isEmpty(script.getUsagePoints()))
        {
            throw new FxException(messages.getMessage("scriptcatalog-usagePointNotFoundError", script.getCode()));
        }

        ScriptAdminLogInfo logInfo = null;
        if (!context.isImmediateLogSnapshot())
        {
            context.addScriptLogInfo(createChangedScriptLog(script));
        }
        else
        {
            logInfo = createChangedScriptLogBeforeChanges(script);
        }

        String location = getLocation(holder, context);
        Collection<ClassFqn> relatedMetaClasses = getRelatedMetaClasses(holder, context);

        Iterator<ScriptUsagePoint> iterator = script.getUsagePoints().iterator();
        while (iterator.hasNext())
        {
            ScriptUsagePoint usage = iterator.next();
            if (isUsagePointMatch(usage, location, relatedMetaClasses, context))
            {
                iterator.remove();
                scriptStorageService.saveScript(script);

                if (context.isImmediateLogSnapshot())
                {
                    processChangedScriptLogAfterChanges(script, logInfo);
                    context.addScriptLogInfo(logInfo);
                }
                return;
            }
        }

        throw new FxException(messages.getMessage("scriptcatalog-usagePointNotFoundError", script.getCode()));
    }

    private void saveScript(Script script, ScriptModifyContext context)
    {
        if (context.isScriptBDPropertiesChanged())
        {
            scriptService.updateSubjectDependencies(script, context.getCompilationCustomizers());
        }
        scriptStorageService.saveScript(script);

        if (!context.isScriptBDPropertiesChanged())
        {
            return;
        }

        if (script.isEditable())
        {
            if (context.isDefferedPersist())
            {
                context.putScriptForDefferedPersist(script);
            }
            else
            {
                persister.persist(script);
            }
        }
    }

    private void updateScriptBDProperties(Script script, ScriptDto newScript, ScriptModifyContext context)
    {
        if (!context.isScriptBDPropertiesChanged())
        {
            return;
        }
        i18nUtil.updateI18nObjectTitle(script, newScript.getTitle());
        script.setSettingsSet(newScript.getSettingsSet());
        if (script.isEditable())
        {
            script.setBody(newScript.getBody());
        }
    }

    private void updateUsagePoints(Script script, T newHolder, ScriptModifyContext context, boolean newUsage)
    {
        if (newUsage)
        {
            ScriptUsagePoint usage = getUsagePoint(newHolder, context);
            if (script.getUsagePoints() == null)
            {
                script.setUsagePoints(new ArrayList<>());
            }
            script.getUsagePoints().add(usage);
            return;
        }

        if (!isUsageChangeable())
        {
            return;
        }

        ScriptUsagePoint usage = findUsagePoint(script, newHolder, context);
        Collection<ClassFqn> actualFqns = getRelatedMetaClasses(newHolder, context);

        usage.setRelatedMetaClassFqns(actualFqns);
    }
}
