package ru.naumen.core.server.script.storage;

import java.util.Collection;

import jakarta.annotation.Nullable;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Сервис "Хранилище скриптов"
 * Сервис для работы со скриптами и модулями скриптов
 * <AUTHOR>
 * @since Jul 17, 2015
 */
public interface ScriptStorageService
{
    /**
     * Удаление скрипта из хранилища скриптов по коду.
     * @param code код скрипта для удаления.
     */
    void deleteScript(String code);

    /**
     * Удаление коллекции криптов из хранилища скриптов
     * @param scriptCodes коды, удаляемых скриптов
     */
    void deleteScripts(Collection<String> scriptCodes);

    /**
     * Получение скрипта по коду
     * @param code код искомого скрипта
     * @return скрипт
     */
    @Nullable
    Script getScript(@Nullable String code);

    /**
     * Получение тела скрипта по коду
     * @param code код искомого скрипта
     * @return тело скрипта, null - если скрипт не существует
     */
    @Nullable
    String getScriptBody(@Nullable String code);

    /**
     * Получить все скрипты зарегистрированные в системе (в том числе и в библиотеках)
     */
    Collection<Script> getScripts();

    /**
     * Сохраняет скрипт в хранилище скриптов.
     */
    void saveScript(Script script);

    /**
     * Сохраняет коллекцию скриптов в хранилище скриптов.
     */
    void saveScripts(Collection<Script> scripts);
}
