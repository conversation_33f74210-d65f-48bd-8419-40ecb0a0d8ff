package ru.naumen.core.server.upload.strategies;

import static ru.naumen.core.server.filestorage.spi.RestartableContentFactory.createRestartableContent;

import java.io.IOException;
import java.util.Objects;

import org.apache.commons.fileupload2.core.FileItem;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.filestorage.spi.FileStorageSettingsService;
import ru.naumen.core.server.filestorage.spi.RestartableContent;
import ru.naumen.core.server.filestorage.spi.storages.FileHash;
import ru.naumen.core.server.filestorage.spi.storages.FileHashDao;
import ru.naumen.core.server.upload.spi.DBFileItem;
import ru.naumen.core.server.upload.spi.DBFileItemDao;
import ru.naumen.core.server.upload.spi.UploadFileItemContext;
import ru.naumen.core.server.upload.spi.UploadFileItemContextBuilder;

/**
 * Базовый класс стратегии загрузки файла
 *
 * <AUTHOR>
 * @since Aug 31, 2020
 */
public abstract class AbstractUploadStrategy implements UploadStrategy
{
    private final FileHashDao fileHashDao;
    protected final DBFileItemDao dbFileItemDao;
    private final FileStorageSettingsService fileStorageSettingsService;

    protected AbstractUploadStrategy(
            final FileHashDao fileHashDao,
            final DBFileItemDao dbFileItemDao,
            final FileStorageSettingsService fileStorageSettingsService)
    {
        this.fileHashDao = fileHashDao;
        this.dbFileItemDao = dbFileItemDao;
        this.fileStorageSettingsService = fileStorageSettingsService;
    }

    @Override
    public void add(final UploadFileItemContext uploadFileItemContext)
    {
        final var fileItem = uploadFileItemContext.getFileItem();
        try (RestartableContent restartableContent = createRestartableContent(fileItem))
        {
            final var hash = Objects.requireNonNull(restartableContent).getHash(uploadFileItemContext.isSystem());
            if (fileStorageSettingsService.isDeduplication())
            {
                final var existingFileHash = fileHashDao.get(hash);
                if (existingFileHash != null)
                {
                    addFileItemWithExistingFileHash(uploadFileItemContext, existingFileHash);
                    return;
                }
            }

            final var fileHash = new FileHash(hash);
            fileHashDao.save(fileHash);

            final var setContentContext = new SetContentContext(uploadFileItemContext.getStorageCode(),
                    uploadFileItemContext.getUuid(),
                    fileHash.getId(),
                    fileItem.getInputStream(),
                    fileItem.getSize(),
                    uploadFileItemContext.isCompress(),
                    uploadFileItemContext.isSystem()
            );

            setContent(setContentContext);
            dbFileItemDao.save(uploadFileItemContext, fileHash);
        }
        catch (IOException e)
        {
            throw new FxException(e);
        }
    }

    private void addFileItemWithExistingFileHash(final UploadFileItemContext uploadFileItemContext,
            final FileHash existingFileHash) // NOPMD
    {
        final var existingFile = fileHashDao.getFirstFile(existingFileHash);
        if (existingFile != null)
        {
            // Если до этого уже был загружен файл с таким хэш, но в другое фх
            // После загрузки файла сменили активное фх
            final var existingFileContext = new UploadFileItemContextBuilder().setStorageCode(existingFile.getStorage())
                    .setUuid(uploadFileItemContext.getUuid())
                    .setFileItem(uploadFileItemContext.getFileItem())
                    .setCompress(uploadFileItemContext.isCompress())
                    .setSystem(uploadFileItemContext.isSystem())
                    .setFileStorageDirectUpload(uploadFileItemContext.isFileStorageDirectUpload())
                    .createUploadFileItemContext();
            dbFileItemDao.save(existingFileContext, existingFileHash);
            return;
        }

        final var existingDBFileItem = fileHashDao.getFirstDBFileItem(existingFileHash);
        if (existingDBFileItem != null)
        {
            // Если до этого уже был загружен файл в tbl_sys_uploaded с таким хэшом, но в другое фх
            // После загрузки файла на форме добавления сменили активное фх
            final var existingDBFileItemContext = new UploadFileItemContextBuilder().setStorageCode(
                            existingDBFileItem.getStorageId())
                    .setUuid(uploadFileItemContext.getUuid())
                    .setFileItem(uploadFileItemContext.getFileItem())
                    .setCompress(uploadFileItemContext.isCompress())
                    .setSystem(uploadFileItemContext.isSystem())
                    .setFileStorageDirectUpload(uploadFileItemContext.isFileStorageDirectUpload())
                    .createUploadFileItemContext();
            dbFileItemDao.save(existingDBFileItemContext, existingFileHash);
            return;
        }

        // По идее сюда уже не дойдем т.к. с таким хэшом уже есть какой-то прикрепленный файл или другой
        // файл, который находится сейчас на форме добавления (tbl_sys_uploaded)
        dbFileItemDao.save(uploadFileItemContext, existingFileHash);
    }

    @Override
    public void delete(final String uuid, final boolean reuse)
    {
        final var dbFileItem = (DBFileItem)dbFileItemDao.get(uuid);

        if (!fileHashDao.existsAnotherFileHash(dbFileItem) && dbFileItem.getContent() != null)
        {
            deleteContent(dbFileItem);
        }

        dbFileItemDao.delete(uuid, reuse);
    }

    @Override
    public FileItem get(final String uuid)
    {
        return dbFileItemDao.get(uuid);
    }

    @Override
    public long getFileSize(final String uuid)
    {
        return dbFileItemDao.get(uuid).getSize();
    }

    protected abstract void setContent(final SetContentContext setContentContext);

    protected abstract void deleteContent(final DBFileItem dbFileItem);
}
