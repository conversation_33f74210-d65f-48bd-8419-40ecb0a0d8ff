package ru.naumen.core.server.script.js;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.bo.root.RootDao;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.spi.FileToXmlTransformer;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.script.js.CustomJSElement;

/**
 * Компонент переноса файлов кастомизации в/из загружаемой метаинформации.
 * <AUTHOR>
 * @since Dec 05, 2017
 */
@Component
public class CustomJSMetainfoTransfer
{
    @Inject
    private IPrefixObjectLoaderService objectLoader;
    @Inject
    private CommonUtils commonUtils;
    @Inject
    private CustomJavaScriptService customJSService;
    @Inject
    private RootDao rootDao;
    @Inject
    private FileToXmlTransformer fileXmlTransformer;
    @Inject
    private CustomJavaScriptValidator validator;

    public void prepareExport(CustomJSElement element)
    {
        File file = objectLoader.getSafe(element.getFileUuid());
        element.setFileUuid(null);
        if (null == file)
        {
            return;
        }
        element.setFile(fileXmlTransformer.apply(file));
    }

    public void prepareImport(CustomJSElement element)
    {
        element.setFileUuid(null);
        byte[] content = null == element.getFile() ? new byte[0] : element.getFile().getContent();
        validator.validate(content);
        CustomJSElement oldJS = customJSService.get(element.getCode());
        if (null != oldJS && null != oldJS.getFileUuid()
            && Constants.File.CLASS_ID.equals(UuidHelper.toPrefix(oldJS.getFileUuid())))
        {
            File oldFile = objectLoader.getSafe(oldJS.getFileUuid());
            if (null != oldFile)
            {
                commonUtils.delete(oldFile);
            }
        }
        File file = commonUtils.attachFile(rootDao.getCoreRoot(), Constants.CustomJSElement.ROOT_RELATION,
                element.getFile().getFileName(), Constants.CustomJSElement.MIME_TYPE, StringUtilities.EMPTY, content);
        element.setFileUuid(file.getUUID());
        element.setFile(null);
    }
}
