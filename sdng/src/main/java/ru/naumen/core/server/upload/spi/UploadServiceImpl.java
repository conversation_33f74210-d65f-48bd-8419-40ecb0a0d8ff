package ru.naumen.core.server.upload.spi;

import org.apache.commons.fileupload2.core.FileItem;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.transaction.Transactional;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.common.FormattersSrv;
import ru.naumen.core.server.filestorage.conf.Storage;
import ru.naumen.core.server.filestorage.conf.Storage.StorageType;
import ru.naumen.core.server.filestorage.spi.FileStorageSettingsService;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.upload.UploadServiceException;
import ru.naumen.core.server.upload.strategies.DatabaseUploadStrategy;
import ru.naumen.core.server.upload.strategies.DiskUploadStrategy;
import ru.naumen.core.server.upload.strategies.GroovyUploadStrategy;
import ru.naumen.core.server.upload.strategies.S3UploadStrategy;
import ru.naumen.core.server.upload.strategies.UploadStrategy;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.utils.UUIDGenerator;

/**
 * Реализация {@link UploadService}.
 *
 * <AUTHOR>
 */
@Component
public class UploadServiceImpl implements UploadService
{
    private static final Logger LOG = LoggerFactory.getLogger(UploadServiceImpl.class);

    private final MessageFacade message;
    private final ConfigurationProperties configurationProperties;
    private final FormattersSrv formattersSrv;
    private final FileStorageSettingsService fileStorageSettingsService;
    private final UploadStrategy databaseUploadStrategy;
    private final UploadStrategy diskUploadStrategy;
    private final UploadStrategy s3UploadStrategy;
    private final UploadStrategy groovyUploadStrategy;
    private final FileUploadDirectConfiguration fileUploadDirectConfiguration;

    @Inject
    public UploadServiceImpl(
            final MessageFacade message,
            final ConfigurationProperties configurationProperties,
            final FormattersSrv formattersSrv,
            final DatabaseUploadStrategy databaseUploadStrategy,
            final DiskUploadStrategy diskUploadStrategy,
            final S3UploadStrategy s3UploadStrategy,
            final GroovyUploadStrategy groovyUploadStrategy,
            final FileStorageSettingsService fileStorageSettingsService,
            final FileUploadDirectConfiguration fileUploadDirectConfiguration)
    {
        this.message = message;
        this.configurationProperties = configurationProperties;
        this.formattersSrv = formattersSrv;
        this.databaseUploadStrategy = databaseUploadStrategy;
        this.diskUploadStrategy = diskUploadStrategy;
        this.s3UploadStrategy = s3UploadStrategy;
        this.groovyUploadStrategy = groovyUploadStrategy;
        this.fileStorageSettingsService = fileStorageSettingsService;
        this.fileUploadDirectConfiguration = fileUploadDirectConfiguration;
    }

    @Override
    @Transactional
    public String add(final FileItem fileItem, boolean system)
    {
        return add(fileItem, system, false);
    }

    @Override
    @Transactional
    public String add(final FileItem fileItem, boolean system, boolean moveFromDisk)
    {
        final boolean fileStorageDirectUpload = fileUploadDirectConfiguration.isFileStorageDirectUpload();
        final String uuid = !moveFromDisk && (!fileStorageDirectUpload || system)
                ? uploadToDatabase(fileItem, system)
                : uploadDirectly(fileItem, fileStorageSettingsService.getActiveStorage(), !moveFromDisk);
        LOG.debug("Added resource with uuid={}", uuid);
        return uuid;
    }

    @Override
    public String addDirectly(final FileItem item, final Storage storage)
    {
        return uploadDirectly(item, storage, true);
    }

    private String uploadDirectly(final FileItem item, @Nullable final Storage storage, boolean enableCompress)
    {
        final String storageCode = storage == null ? null : storage.getCode();
        final boolean compress = enableCompress && (storage == null || storage.isCompress());
        final StorageType storageType = fileStorageSettingsService.getStorageType(storage);
        final UploadFileItemContext uploadFileItemContext = new UploadFileItemContextBuilder()
                .setStorageCode(storageCode)
                .setUuid(UUIDGenerator.get().nextUUID())
                .setFileItem(item)
                .setCompress(compress)
                // storageType будет null только, если работа напрямую с файловым хранилищем включена, но
                // хранилище не подключено и де-факто работает с базой
                .setFileStorageDirectUpload(storageType != null)
                .createUploadFileItemContext();

        getStrategy(storageType).add(uploadFileItemContext);

        return uploadFileItemContext.getUuid();
    }

    private String uploadToDatabase(FileItem item, boolean isSystem)
    {
        final UploadFileItemContext uploadFileItemContext = new UploadFileItemContextBuilder()
                .setUuid(UUIDGenerator.get().nextUUID())
                .setFileItem(item)
                .setSystem(isSystem)
                .createUploadFileItemContext();

        databaseUploadStrategy.add(uploadFileItemContext);

        return uploadFileItemContext.getUuid();
    }

    @Override
    public void delete(final String uuid)
    {
        UploadStrategy strategy = fileUploadDirectConfiguration.isFileStorageDirectUpload()
                ? getStrategy(getStorageType(uuid))
                : databaseUploadStrategy;
        strategy.delete(uuid, false);
    }

    private StorageType getStorageType(String fileItemUuid)
    {
        DBFileItem fileItem = (DBFileItem)get(fileItemUuid);
        String storageId = fileItem.getStorageId();
        return fileStorageSettingsService.getStorageType(storageId);
    }

    @Override
    public FileItem get(final String uuid)
    {
        if (uuid.isBlank())
        {
            throw new UploadServiceException(message.getMessage("UploadServiceImpl.errorNoUUIDTransferred",
                    formattersSrv.bytesToMbStringWithPrecision(configurationProperties.getUploadFilesGroupMaxSize(),
                            2)));
        }

        final FileItem fileItem = databaseUploadStrategy.get(uuid);

        if (fileItem == null)
        {
            throw new UploadServiceException(message.getMessage("UploadServiceImpl.errorFileItemNotFound", uuid));
        }
        return fileItem;
    }

    @Override
    public long getFileSize(final String uuid)
    {
        return uuid.startsWith("file$") ? getUploadedFileSize() : getUploadedFileSize(uuid);
    }

    private UploadStrategy getStrategy(@Nullable final StorageType storageType) // NOPMD
    {
        if (StorageType.DISK == storageType)
        {
            return diskUploadStrategy;
        }
        else if (StorageType.S_3 == storageType)
        {
            return s3UploadStrategy;
        }
        else if (StorageType.GROOVY == storageType)
        {
            return groovyUploadStrategy;
        }
        else
        {
            return databaseUploadStrategy;
        }
    }

    /**
     * Получение размера файла, который загружен ранее и хранится в БД (хранится в tbl_file)
     */
    private static long getUploadedFileSize()
    {
        /* До реализации нормальной проверки на размер всех файлов в атрибуте файлы,
         * загруженные ранее, не подлежат проверке. Поэтому для них возвращаем 0 */
        return 0L;
    }

    /**
     * Получения размера файла, который мы загружаем в данный момент (хранится в tbl_sys_uploaded)
     */
    private long getUploadedFileSize(final String uuid)
    {
        return databaseUploadStrategy.getFileSize(uuid);
    }
}