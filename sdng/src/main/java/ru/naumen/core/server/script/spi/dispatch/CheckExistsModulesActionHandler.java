package ru.naumen.core.server.script.spi.dispatch;

import java.util.HashSet;
import java.util.Set;

import org.apache.commons.fileupload2.core.FileItem;
import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.admin.server.permission.AdminPermissionCheckService; //NOPMD admin-operator
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.script.modules.storage.ScriptModulesStorageService;
import ru.naumen.core.server.script.modules.storage.ScriptStorageContainer;
import ru.naumen.core.server.upload.UploadService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.HasCode;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.core.shared.permission.AdminProfileAccessMarker;
import ru.naumen.core.shared.permission.PermissionType;
import ru.naumen.metainfo.shared.dispatch2.CheckExistsModulesAction;

/**
 * Обработчик {@link CheckExistsModulesAction}
 *
 * <AUTHOR>
 * @since Jun 2, 2015
 */
@Component
public class CheckExistsModulesActionHandler extends
        TransactionalReadActionHandler<CheckExistsModulesAction, SimpleResult<String>>
{
    private final ScriptModulesStorageService scriptModulesStorageService;
    private final UploadService uploadService;
    private final XmlUtils xmlUtils;
    private final MessageFacade messages;
    private final ConfigurationProperties configurationProperties;
    private final AdminPermissionCheckService adminPermissionCheckService;

    @Inject
    public CheckExistsModulesActionHandler(UploadService uploadService,
            XmlUtils xmlUtils,
            MessageFacade messages,
            ConfigurationProperties configurationProperties,
            AdminPermissionCheckService adminPermissionCheckService,
            ScriptModulesStorageService scriptModulesStorageService)
    {
        this.uploadService = uploadService;
        this.xmlUtils = xmlUtils;
        this.messages = messages;
        this.configurationProperties = configurationProperties;
        this.adminPermissionCheckService = adminPermissionCheckService;
        this.scriptModulesStorageService = scriptModulesStorageService;
    }

    @Override
    public SimpleResult<String> executeInTransaction(CheckExistsModulesAction action, ExecutionContext context)
            throws DispatchException
    {
        adminPermissionCheckService.checkPermission(AdminProfileAccessMarker.SCRIPTS, PermissionType.VIEW);
        ScriptStorageContainer newConf = deserializeFile(action.getFileUuid());

        Set<ScriptModule> existedTwins = new HashSet<>(scriptModulesStorageService.getUserModules());
        existedTwins.retainAll(newConf.getModules());

        String warning = StringUtilities.joinDoubleQuoted(
                existedTwins.stream()
                        .map(HasCode.CODE_EXTRACTOR)
                        .toList());

        return new SimpleResult<>(warning);
    }

    private ScriptStorageContainer deserializeFile(String fileUuid)
    {
        FileItem<?> fileItem = uploadService.get(fileUuid);
        if (fileItem.getSize() == 0)
        {
            throw new FxException(messages.getMessage("FillFileAttrOperation.errorFileEmpty", fileItem.getName()));
        }
        try
        {
            return xmlUtils.parseXml(fileItem.getString(), ScriptStorageContainer.class,
                    configurationProperties.isProcessingExternalEntityInXML());
        }
        catch (Exception e)
        {
            throw new FxException(messages.getMessage("importModules.error",
                    messages.getMessage("ImportModulesActionHandler.wrongFileFormat")), e);
        }
    }
}
