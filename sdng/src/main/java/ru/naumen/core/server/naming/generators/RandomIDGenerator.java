package ru.naumen.core.server.naming.generators;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import com.google.common.collect.Maps;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.naming.extractors.PeriodExtractor;
import ru.naumen.core.server.naming.extractors.SequenceExtractor;
import ru.naumen.core.server.naming.generators.strategies.NextValueGenerationStrategy;
import ru.naumen.core.server.naming.generators.strategies.SequenceSaver;

/**
 * Генератор возвращающий случайный номер из последовательности
 *
 * <AUTHOR>
 */
public class RandomIDGenerator extends PeriodicalIDGenerator
{
    private final class NextValueStrategy implements NextValueGenerationStrategy
    {
        @Override
        public int next(Pair<String, Long> key, Integer value, SequenceSaver saver)
        {
            //Проверяем не через putIfAbsent, чтобы не создавать лишний раз ArrayList
            List<Integer> gen = values.get(key);
            if (null == gen)
            {
                gen = new ArrayList<>(NUM_WIN);
                List<Integer> prev = values.putIfAbsent(key, gen);
                //Если в другом потоке успели добавить, то надо использовать значение из другого потока            
                if (prev != null)
                {
                    gen = prev;
                }
            }

            Lock newLock = new ReentrantLock();
            Lock lock = locks.putIfAbsent(key, newLock);
            if (lock == null)
            {
                lock = newLock;
            }
            lock.lock();
            try
            {
                if (gen.isEmpty())
                {
                    int from = value;
                    int to = value + NUM_WIN;
                    saver.save(key, to);

                    for (; from < to; ++from)
                    {
                        gen.add(from);
                    }
                }
                return gen.remove(RANDOM.nextInt(gen.size()));
            }
            finally
            {
                lock.unlock();
            }
        }
    }

    private static final Random RANDOM = new Random();
    private static final int NUM_WIN = 1000;
    private final ConcurrentMap<Pair<String, Long>, List<Integer>> values = Maps.newConcurrentMap();

    private final ConcurrentMap<Pair<String, Long>, Lock> locks = Maps.newConcurrentMap();

    /**
     * Генератор возвращающий случайный номер из последовательности
     *
     * @param sequenceIdExtractor
     * @param periodExtractor
     */
    public RandomIDGenerator(SequenceExtractor sequenceIdExtractor,
            @SuppressWarnings("rawtypes") PeriodExtractor periodExtractor)
    {
        super(sequenceIdExtractor, periodExtractor);
    }

    @Override
    protected NextValueGenerationStrategy getNextValueStrategy()
    {
        return new NextValueStrategy();
    }
}
