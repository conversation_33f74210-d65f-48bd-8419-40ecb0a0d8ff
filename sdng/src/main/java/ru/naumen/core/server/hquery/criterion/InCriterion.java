package ru.naumen.core.server.hquery.criterion;

import java.util.Arrays;
import java.util.Collection;

import org.hibernate.query.Query;

import com.google.common.base.Joiner;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * Использование этой критерии может привести к ошибке, если количество объектов в values будет слишком велико (> 2
 * тыс).
 * Пример из Oracle: "Prepared or callable statement has more than 2000 parameter markers."
 * <p>
 * Copyright Naumen Ltd
 * User: Vlad
 * Date: 06.02.2006
 * Time: 12:36:17
 */
public class InCriterion extends AbstractHCriterion
{
    protected static final String IN = " IN";
    protected static final char DELIMITER_FOR_VALUES = ',';
    protected static final char PREFIX_FOR_NAME_PARAM = ':';
    protected static final char OPEN_BRACE = '(';
    protected static final char CLOSE_BRACE = ')';
    protected Object[] values;
    protected String[] valuesNames;

    public InCriterion(HColumn property, Collection<?> values)
    {
        super(property);
        this.values = new Object[values.size()];
        values.toArray(this.values);
    }

    public InCriterion(HColumn property, Object[] values)
    {
        super(property);
        this.values = values;
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        generateValueNamesArray(parameterCounter);

        if (0 == values.length)
        {
            FalseCriterion.getInstance().append(sb, builder, parameterCounter);
            return;
        }

        sb.append(property.getHQL(builder))
                .append(IN)
                .append(OPEN_BRACE);
        for (int index = 0; index < values.length; index++)
        {
            if (index > 0)
            {
                sb.append(DELIMITER_FOR_VALUES);
            }
            sb.append(PREFIX_FOR_NAME_PARAM);
            sb.append(valuesNames[index]);
        }
        sb.append(CLOSE_BRACE);
    }

    @Override
    public void setParameters(Query q)
    {
        super.setParameters(q);
        for (int i = 0; i < values.length; i++)
        {
            q.setParameter(valuesNames[i], values[i]);
        }
    }

    @Override
    public String toString()
    {
        return property + IN + OPEN_BRACE + Joiner.on(DELIMITER_FOR_VALUES).join(values) + CLOSE_BRACE;
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new InCriterion(property, Arrays.asList(values));
    }

    protected void generateValueNamesArray(NameGenerator parameterCounter)
    {
        valuesNames = new String[values.length];
        for (int i = 0; i < values.length; i++)
        {
            valuesNames[i] = parameterCounter.next();
        }
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        InCriterion that = (InCriterion)o;
        return Arrays.equals(values, that.values);
    }

    @Override
    public int hashCode()
    {
        int result = super.hashCode();
        result = 31 * result + Arrays.hashCode(values);
        return result;
    }
}
