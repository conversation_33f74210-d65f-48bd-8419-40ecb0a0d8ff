package ru.naumen.core.server.hquery.criterion;

import java.util.Objects;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;

import org.hibernate.query.Query;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.HCriteriaDelegate;
import ru.naumen.core.server.hquery.impl.NameGenerator;
import ru.naumen.core.shared.Constants.CastToType;

/**
 * Фильтр, позволяющий применить к запросу фильтрацию с некоторой операцией. Фильтр поддерживает следующие операции:
 * <ol>
 * <li>Игнорирование регистра значения атрибута, по которому производится фильтрация</li>
 * <li>Преобразование типа значения атрибута, по которому производится фильтрация</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 03.02.2006
 */
public class SimpleCriterion extends AbstractHCriterion
{
    protected final String op;
    @CheckForNull
    private Object value;
    protected String paramName;
    protected boolean ignoreCase = false;
    protected CastToType castTo = CastToType.NONE;

    public SimpleCriterion(HColumn property, String op, @Nullable Object value)
    {
        super(property);
        this.op = op;
        this.value = value;
    }

    public SimpleCriterion(HColumn property, String op, @Nullable Object value, CastToType castTo)
    {
        this(property, op, value);
        this.castTo = castTo;
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder, NameGenerator parameterCounter)
    {
        this.paramName = parameterCounter.next();

        appendAttributeWithIgnoreCase(sb, builder);
        sb.append(op);
        sb.append(':');
        sb.append(paramName);
    }

    /**
     * Добавляет получение значения атрибута в формирующийся запрос с учётом регистра. Если в фильтре было настроено
     * игнорирование регистра значения, то это применится к итоговому запросу
     *
     * @param sb {@link StringBuilder}, который формирует hql запрос
     * @param builder построитель HQL запроса из {@link HCriteriaDelegate специальных делегатов}
     */
    protected void appendAttributeWithIgnoreCase(StringBuilder sb, HBuilder builder)
    {
        if (ignoreCase)
        {
            sb.append("lower("); //$NON-NLS-1$
            appendAttribute(sb, builder);
            sb.append(") "); //$NON-NLS-1$
        }
        else
        {
            appendAttribute(sb, builder);
        }
    }

    private void appendAttribute(StringBuilder sb, HBuilder builder)
    {
        if (castTo != null && !CastToType.NONE.equals(castTo))
        {
            sb.append(castTo.getCastFunction());
            sb.append('(');
            sb.append(property.getHQL(builder));
            sb.append(')');
        }
        else
        {
            sb.append(property.getHQL(builder));
        }
    }

    /**
     * Устанавливает признак необходимости игнорирования регистра значения у фильтруемого атрибута
     */
    public void setIgnoreCase(boolean ignoreCase)
    {
        this.ignoreCase = ignoreCase;
    }

    @Override
    public void setParameters(Query q)
    {
        super.setParameters(q);
        process();
        q.setParameter(paramName, value);
    }

    @Override
    public String toString()
    {
        return "SimpleCriterion{" +
               "property=" + property +
               ", op='" + op + '\'' +
               ", paramName='" + paramName + '\'' +
               ", value=" + value +
               ", ignoreCase=" + ignoreCase +
               ", castTo='" + castTo + '\'' +
               '}';
    }

    /**
     * Создаёт копию данного фильтра
     */
    @Override
    protected HCriterion createCopyInstance()
    {
        return new SimpleCriterion(property, op, value);
    }

    /**
     * Возвращает значение, по которому производится фильтрация
     */
    @CheckForNull
    protected final Object getValue()
    {
        return value;
    }

    /**
     * Обрабатывает устанавливаемые параметры
     */
    protected void process()
    {
        if (ignoreCase && value instanceof String && StringUtilities.isNotEmpty((String)value))
        {
            value = value.toString().toLowerCase();
        }
    }

    /**
     * Устанавливает значение, по которому производится фильтрация
     */
    protected final void setValue(@Nullable Object value)
    {
        this.value = value;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        SimpleCriterion that = (SimpleCriterion)o;
        return ignoreCase == that.ignoreCase &&
               op.equals(that.op) &&
               Objects.equals(value, that.value);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), op, value, ignoreCase);
    }
}