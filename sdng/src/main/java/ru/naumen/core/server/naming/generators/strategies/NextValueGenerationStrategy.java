package ru.naumen.core.server.naming.generators.strategies;

import ru.naumen.commons.shared.utils.Pair;

/**
 * Стратегия генерации следующего значения последовательности.
 *
 * <AUTHOR>
 * @since Dec 23, 2015
 *
 */
public interface NextValueGenerationStrategy
{
    /**
     * Сгенерировать следующее значение последовательности.
     *
     * @param key идентификатор последовательности, период, для которых необходимо 
     * сгенерировать следующее значение.
     * @param value текущее значение последовательности.
     * @param saver средство сохранения значения последовательности внутри {@link NextValueGenerationStrategy}.
     * @return новое сгенерированное значение.
     */
    int next(Pair<String, Long> key, Integer value, SequenceSaver saver);
}
