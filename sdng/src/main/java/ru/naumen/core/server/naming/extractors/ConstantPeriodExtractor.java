package ru.naumen.core.server.naming.extractors;

/**
 * Реализация {@link PeriodExtractor} для последовательностей с единственным периодом,
 * например, для последовательностей генерирующих "сквозную" нумерацию для запросов 
 *
 * <AUTHOR>
 *
 */
public class ConstantPeriodExtractor<T> implements PeriodExtractor<T>
{
    private final long period;

    public ConstantPeriodExtractor(long period)
    {
        this.period = period;
    }

    @Override
    public long currentPeriod()
    {
        return period;
    }

    @Override
    public long extractPeriod(T obj)
    {
        return period;
    }
}
