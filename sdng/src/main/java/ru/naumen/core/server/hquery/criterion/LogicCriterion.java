package ru.naumen.core.server.hquery.criterion;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.Objects;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

import org.hibernate.query.Query;
import org.hibernate.Session;

import com.google.common.base.Joiner;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;

/**
 * Copyright Naumen Ltd
 * User: Vlad
 * Date: 03.02.2006
 * Time: 16:02:54
 */
public class LogicCriterion implements HCriterion
{
    protected final Iterable<HCriterion> criterions;
    private final String op;

    public LogicCriterion(String op, HCriterion... criterions)
    {
        this(op, Arrays.asList(criterions));
    }

    public LogicCriterion(String op, Iterable<HCriterion> criterions)
    {
        this.criterions = criterions;
        this.op = op;
    }

    @Override
    public void afterQuery(Session session)
    {
        AfterQueryHelper.afterQuery(session, criterions).throwIfHasExceptions();
    }

    @Override
    public HCriterion createCopy()
    {
        return createCopyInstance();
    }

    /**
     * @return the criterions
     */
    public Iterable<HCriterion> getSubCriterions()
    {
        return criterions;
    }

    @Override
    public Iterable<HColumn> properties()
    {
        return Collections.emptyList();
    }

    @Override
    public void setParameters(Query q)
    {
        for (HCriterion cri : criterions)
        {
            cri.setParameters(q);
        }
    }

    @Override
    public String toString()
    {
        return "(" + Joiner.on(op).join(criterions) + ")";
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        HBuilder whereCollector = new HBuilder(builder);
        for (HCriterion criterion : criterions)
        {
            criterion.visit(whereCollector);
        }
        StringBuilder sb = new StringBuilder();
        buildOverallCondition(sb, whereCollector.getWhereStrings());
        return sb.toString();
    }

    @Override
    public void visit(HBuilder builder)
    {
        builder.where(getHQL(builder));
    }

    protected void buildOverallCondition(StringBuilder sb, Collection<String> conditions)
    {
        sb.append(conditions.size() > 1 ? '(' : "");
        for (Iterator<String> it = conditions.iterator(); it.hasNext(); )
        {
            sb.append(it.next());
            if (it.hasNext())
            {
                sb.append(op);
            }
        }
        sb.append(conditions.size() > 1 ? ')' : "");
    }

    protected HCriterion createCopyInstance()
    {
        return new LogicCriterion(op, StreamSupport.stream(criterions.spliterator(), false).map(HCriterion::createCopy)
                .collect(Collectors.toList()));
    }

    @Override
    public HCriterion fillEmptyBaseProperty(HColumn other)
    {
        getSubCriterions().forEach(sc -> sc.fillEmptyBaseProperty(other));
        return this;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        LogicCriterion that = (LogicCriterion)o;
        return criterions.equals(that.criterions) &&
               op.equals(that.op);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(criterions, op);
    }
}
