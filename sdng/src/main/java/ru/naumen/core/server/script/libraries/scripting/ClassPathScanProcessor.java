package ru.naumen.core.server.script.libraries.scripting;

import io.github.classgraph.ScanResult;
import jakarta.annotation.Nullable;
import ru.naumen.core.server.script.libraries.ScanProcessResult;
import ru.naumen.core.shared.HasCode;

/**
 * Обработчик результата сканирования аннотаций в библиотеках.
 * @param <V> тип объектов, возвращаемых в соответствие с их строковыми идентификаторами.
 */
public interface ClassPathScanProcessor<V extends HasCode>
{
    /**
     * Запустить обработку результата сканирования аннотаций в библиотеках
     *
     * @param scanResult              результат сканирования библиотек;
     * @param classLoader             класcлоадер в который были загружены библиотеки для сканирования;
     * @param embeddedApplicationCode код встроенного приложения, к которому относится обрабатываемый результат
     *                                сканирования;
     * @return результат сканирования {@link ScanProcessResult}.
     */
    ScanProcessResult<V> process(
            ScanResult scanResult,
            ClassLoader classLoader,
            @Nullable String embeddedApplicationCode);
}
