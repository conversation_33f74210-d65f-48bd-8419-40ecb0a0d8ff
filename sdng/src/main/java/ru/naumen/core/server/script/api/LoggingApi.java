package ru.naumen.core.server.script.api;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import org.apache.logging.log4j.core.appender.RollingFileAppender;
import org.apache.logging.log4j.core.appender.rolling.CompositeTriggeringPolicy;
import org.apache.logging.log4j.core.appender.rolling.DefaultRolloverStrategy;
import org.apache.logging.log4j.core.appender.rolling.DirectWriteRolloverStrategy;
import org.apache.logging.log4j.core.appender.rolling.RolloverStrategy;
import org.apache.logging.log4j.core.appender.rolling.SizeBasedTriggeringPolicy;
import org.apache.logging.log4j.core.appender.rolling.TimeBasedTriggeringPolicy;
import org.apache.logging.log4j.core.appender.rolling.TriggeringPolicy;
import org.apache.logging.log4j.core.appender.rolling.action.Action;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.Maps;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.util.log.LogUtils;
import ru.naumen.core.server.util.log.ReadyStateLoggingSettings;

/**
 *
 * <AUTHOR>
 * @since 04.03.2014
 *
 */
@Component("logging")
public class LoggingApi implements ILoggingApi
{
    private static final Logger LOG = LoggerFactory.getLogger(LoggingApi.class);
    private static final String MAX_BACKUP_INDEX = "MaxBackupIndex";
    private static final String MINUTELY_PERIOD = "MinutelyPeriod";
    private static final String MAX_FILE_SIZE = "MaxFileSize";

    /**
     * Поля политики ротирования по времени, необходимые при переконфигурации (создание новой политики), получить к ним
     * доступ, кроме как через рефлексию невозможно
     */
    private Field maxRandomDelayMillisField;
    private Field modulateField;

    @Inject
    private LogUtils logUtils;

    @Inject
    private ReadyStateLoggingSettings loggingSettings;

    public LoggingApi()
    {
        try
        {
            maxRandomDelayMillisField = TimeBasedTriggeringPolicy.class.getDeclaredField("maxRandomDelayMillis");
            maxRandomDelayMillisField.setAccessible(true);
            modulateField = TimeBasedTriggeringPolicy.class.getDeclaredField("modulate");
            modulateField.setAccessible(true);
        }
        catch (NoSuchFieldException e)
        {
            throw new FxException(e);
        }
    }

    @Override
    public Map<String, String> getMinutelyAppenderParams()
    {
        Map<String, String> params = Maps.newHashMapWithExpectedSize(2);
        List<RollingFileAppender> minutelyAppenders = logUtils.getMinutelyAppenders();
        if (!minutelyAppenders.isEmpty())
        {
            if (minutelyAppenders.size() > 1)
            {
                LOG.warn("There are several minutely appenders. The first is returned.");
            }
            RollingFileAppender appender = minutelyAppenders.get(0);
            TimeBasedTriggeringPolicy timePolicy = LogUtils.getTimeBasedTriggeringPolicy(appender);
            SizeBasedTriggeringPolicy sizePolicy = LogUtils.getSizeBasedTriggeringPolicy(appender);
            RolloverStrategy rolloverStrategy = appender.getManager().getRolloverStrategy();
            if (rolloverStrategy instanceof DefaultRolloverStrategy)
            {
                params.put(MAX_BACKUP_INDEX, String.valueOf(((DefaultRolloverStrategy)rolloverStrategy).getMaxIndex()));
            }
            else if (rolloverStrategy instanceof DirectWriteRolloverStrategy)
            {
                params.put(MAX_BACKUP_INDEX,
                        String.valueOf(((DirectWriteRolloverStrategy)rolloverStrategy).getMaxFiles()));
            }
            else
            {
                params.put(MAX_BACKUP_INDEX, String.valueOf(-1));
            }
            params.put(MINUTELY_PERIOD, String.valueOf(timePolicy != null ? timePolicy.getInterval() : -1));
            params.put(MAX_FILE_SIZE, String.valueOf(sizePolicy != null ? sizePolicy.getMaxFileSize() : -1));
        }
        return params;
    }

    @Override
    public void setGwtLogDepth(int depth)
    {
        loggingSettings.setGwtLogDepth(depth);
    }

    @Override
    public void setLoggingEnable(boolean loggingEnable)
    {
        loggingSettings.setLogEnable(loggingEnable);
    }

    @Override
    public void setMinutelyAppenderParam(String param, int value)
    {
        List<RollingFileAppender> minutelyAppenders = logUtils.getMinutelyAppenders();
        if (!minutelyAppenders.isEmpty())
        {
            if (minutelyAppenders.size() > 1)
            {
                LOG.warn("There are several minutely appenders. The first is edited.");
            }
            RollingFileAppender appender = minutelyAppenders.get(0);
            if (param.equalsIgnoreCase(MAX_BACKUP_INDEX))
            {
                RolloverStrategy strategy = appender.getManager().getRolloverStrategy();
                if (strategy instanceof DefaultRolloverStrategy)
                {
                    DefaultRolloverStrategy currentStrategy = (DefaultRolloverStrategy)strategy;
                    var newStrategyBuilder = DefaultRolloverStrategy.newBuilder()
                            .withCompressionLevelStr(String.valueOf(currentStrategy.getCompressionLevel()))
                            .withMin(String.valueOf(currentStrategy.getMinIndex()))
                            .withMax(String.valueOf(value))
                            .withCustomActions(currentStrategy.getCustomActions().toArray(new Action[0]));
                    if (currentStrategy.getTempCompressedFilePattern() != null)
                    {
                        newStrategyBuilder.withTempCompressedFilePattern(
                                currentStrategy.getTempCompressedFilePattern().getPattern());
                    }
                    appender.getManager().setRolloverStrategy(newStrategyBuilder.build());
                }
                else if (strategy instanceof DirectWriteRolloverStrategy)
                {
                    DirectWriteRolloverStrategy currentStrategy = (DirectWriteRolloverStrategy)strategy;
                    var newStrategy = DirectWriteRolloverStrategy.newBuilder()
                            .withCompressionLevelStr(String.valueOf(currentStrategy.getCompressionLevel()))
                            .withCustomActions(currentStrategy.getCustomActions().toArray(new Action[0]))
                            .withTempCompressedFilePattern(currentStrategy.getTempCompressedFilePattern().getPattern())
                            .withMaxFiles(String.valueOf(currentStrategy.getMaxFiles()))
                            .build();
                    appender.getManager().setRolloverStrategy(newStrategy);
                }
            }
            else if (param.equalsIgnoreCase(MINUTELY_PERIOD))
            {
                if (value < 1 || value > 60)
                {
                    return;
                }
                TimeBasedTriggeringPolicy currentPolicy = LogUtils.getTimeBasedTriggeringPolicy(appender);
                TimeBasedTriggeringPolicy newPolicy = null;
                try
                {
                    newPolicy = currentPolicy == null ?
                            TimeBasedTriggeringPolicy.newBuilder().withInterval(value).build() :
                            TimeBasedTriggeringPolicy.newBuilder()
                                    .withInterval(value)
                                    .withModulate(modulateField.getBoolean(currentPolicy))
                                    .withMaxRandomDelay(
                                            Math.toIntExact(maxRandomDelayMillisField.getLong(currentPolicy)))
                                    .build();
                }
                catch (IllegalAccessException e)
                {
                    LOG.error("Error get field in TimeBasedTriggeringPolicy", e);
                }

                List<TriggeringPolicy> resultPolicy = new ArrayList<>();
                TriggeringPolicy triggeringPolicy = appender.getTriggeringPolicy();
                if (triggeringPolicy instanceof CompositeTriggeringPolicy)
                {
                    for (var policy : ((CompositeTriggeringPolicy)triggeringPolicy).getTriggeringPolicies())
                    {
                        if (!(policy instanceof TimeBasedTriggeringPolicy))
                        {
                            resultPolicy.add(policy);
                        }
                    }
                }
                else if (!(triggeringPolicy instanceof TimeBasedTriggeringPolicy))
                {
                    resultPolicy.add(triggeringPolicy);
                }
                resultPolicy.add(newPolicy);
                appender.getManager().setTriggeringPolicy(
                        CompositeTriggeringPolicy.createPolicy(resultPolicy.toArray(new TriggeringPolicy[0])));
            }
        }
    }

    @Override
    public void setMinutelyAppenderParam(String param, String value)
    {
        List<RollingFileAppender> minutelyAppenders = logUtils.getMinutelyAppenders();
        if (!minutelyAppenders.isEmpty())
        {
            if (minutelyAppenders.size() > 1)
            {
                LOG.warn("There are several minutely appenders. The first is edited.");
            }
            RollingFileAppender appender = minutelyAppenders.get(0);
            if (param.equalsIgnoreCase(MAX_FILE_SIZE))
            {
                var newPolicy = SizeBasedTriggeringPolicy.createPolicy(value);
                List<TriggeringPolicy> resultPolicy = new ArrayList<>();
                TriggeringPolicy triggeringPolicy = appender.getTriggeringPolicy();
                if (triggeringPolicy instanceof CompositeTriggeringPolicy)
                {
                    for (var policy : ((CompositeTriggeringPolicy)triggeringPolicy).getTriggeringPolicies())
                    {
                        if (!(policy instanceof SizeBasedTriggeringPolicy))
                        {
                            resultPolicy.add(policy);
                        }
                    }
                }
                else if (!(triggeringPolicy instanceof SizeBasedTriggeringPolicy))
                {
                    resultPolicy.add(triggeringPolicy);
                }
                resultPolicy.add(newPolicy);
                appender.getManager().setTriggeringPolicy(CompositeTriggeringPolicy.createPolicy(
                        resultPolicy.toArray(new TriggeringPolicy[0])));
            }
        }
    }

    @Override
    public void setLogLevel(String loggerName, String level)
    {
        LogUtils.setLogLevel(loggerName, level);
    }

    @Override
    public String getLogLevel(String loggerName)
    {
        return LogUtils.getLogLevel(loggerName).name();
    }
}