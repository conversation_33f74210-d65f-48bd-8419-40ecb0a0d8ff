package ru.naumen.core.server.script.ast.persistence_context;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.codehaus.groovy.transform.GroovyASTTransformationClass;

/**
 * Аннотация которой помечаются скрипты в которые необходимо добавить {@link PersistenceContextASTTransformation}
 *
 * <AUTHOR>
 * @since Dec 3, 2019
 */
@Documented
@Retention(RetentionPolicy.SOURCE)
@Target({ ElementType.PACKAGE, ElementType.METHOD, ElementType.FIELD, ElementType.TYPE, ElementType.LOCAL_VARIABLE })
@GroovyASTTransformationClass({
        "ru.naumen.core.server.script.ast.persistence_context.PersistenceContextASTTransformation" })
public @interface PersistenceContextInterrupt
{
}