package ru.naumen.core.server.hquery.impl;

import jakarta.annotation.Nullable;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HCriteria;

/**
 * Колонка в виде подзапроса
 *
 * <AUTHOR>
 * @since 15.07.2021
 */
public class HSubqueryColumn extends AbstractHColumn
{
    private final HCriteria criteria;

    public HSubqueryColumn(HCriteria criteria, @Nullable String alias)
    {
        super(alias);
        this.criteria = criteria;
    }

    @Override
    public void setParameters(Query query)
    {
        criteria.setParameters(query);
    }

    @Override
    public String getHQL(HBuilder parent)
    {
        HBuilder subBuilder = new HBuilder(parent, criteria.getDelegate());
        return '(' + subBuilder.build() + ')';
    }

    @Override
    public String toString()
    {
        return "HSubqueryColumn{" +
               "criteria=" + criteria +
               ", alias=" + getAlias() +
               '}';
    }
}
