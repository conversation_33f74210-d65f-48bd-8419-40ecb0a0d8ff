package ru.naumen.core.server.script.spi;

import org.codehaus.groovy.control.CompilerConfiguration;

/**
 * Расширенная конфигурация компилятора скриптов.
 * Содержит контекст для обеспечения возможности обмена дополнительной информацией с обработчиками синтаксического
 * дерева.
 * <AUTHOR>
 * @since Mar 28, 2016
 */
public class NauCompilerConfiguration extends CompilerConfiguration
{
    private final CompilationContext compilationContext = new CompilationContext();

    public NauCompilerConfiguration()
    {
        //установим уровень байт кода по умолчанию для версий java 17+ и groovy 3+
        super.setTargetBytecode(CompilerConfiguration.JDK17);
        /* 
         Отключаем стратегию поиска классов "ASM decompilation", т.к. она неприменима в наших реалиях
         (для скриптов, выполняемых в динамических средах, где указанные классы могут быть
         доступны только в загрузчике классов, а не на диске), используем только загрузку классов через classLoader.
         Подробнее можно посмотреть тут: org.codehaus.groovy.control.ClassNodeResolver#tryAsLoaderClassOrScript
         */
        getOptimizationOptions().put("asmResolving", Boolean.FALSE);
    }

    public CompilationContext getCompilationContext()
    {
        return compilationContext;
    }
}
