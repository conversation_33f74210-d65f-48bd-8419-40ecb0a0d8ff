package ru.naumen.core.server.script.api;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import ru.naumen.core.server.script.IListLinkDefinition;
import ru.naumen.core.server.script.IListLinkDefinition.IFilter;

/**
 * Реализация {@link IFilter}
 *
 * <AUTHOR>
 * @since 15.03.2019c
 */
public class ListFilter implements IFilter
{
    /** Объект конструктора ссылки на список объектов, для которого настроена данная(this) фильтрация */
    private final IListLinkDefinition linkDefinition;

    /**
     * Сложная видимая фильтрация списка на языке фильтрации, которая будет доступна для настройки на странице списка
     * (Карточка объекта: опциональный / Отдельная страница: опциональный)
     */
    private List<IFilterAnd> complexFilters = new ArrayList<>();

    /** Ограничивающая не видимая фильтрация, которая будет не доступна для настройки на странице списка объектов */
    private final List<IFilterAnd> restrictionFilters = new ArrayList<>();

    /**
     * Быстрая фильтрация
     * (Карточка объекта: опциональный / Отдельная страница: не используется)
     */
    private final List<IFilterOr> fastFilterElements = new ArrayList<>();

    /**
     * Быстрая фильтрация по подстроке
     * (Карточка объекта: опциональный / Отдельная страница: не используется)
     */
    private final List<IFilterOr> fastSubstringFilters = new ArrayList<>();

    /** Список настроек ограничения фильтрации из списочного дескриптора */
    private final List<IFilterOr> restrictionFilterSettings = new ArrayList<>();

    public ListFilter(IListLinkDefinition linkDefinition)
    {
        this.linkDefinition = linkDefinition;
    }

    public ListFilter(ListLinkDefinition linkDefinition, List<IFilterAnd> restrictionFilters)
    {
        this(linkDefinition);
        this.restrictionFilters.addAll(restrictionFilters);
    }

    @Override
    public IFilterOr OR(String attrCode, String conditionCode, Object value)
    {
        return new ListFilterOr(attrCode, conditionCode, value);
    }

    @Override
    public IFilter AND(IFilterOr... ors)
    {
        complexFilters.add(new ListFilterAnd(ors));
        return this;
    }

    @Override
    public List<IFilterAnd> listAndElements()
    {
        return complexFilters;
    }

    @Override
    public void setListAndElements(List<IFilterAnd> filterAnds)
    {
        complexFilters = filterAnds;
    }

    @Override
    public IListLinkDefinition apply()
    {
        return linkDefinition;
    }

    @Override
    public List<IFilterOr> getFastFilterElements()
    {
        return fastFilterElements;
    }

    @Override
    public IFilter fastFilter(IFilterOr... ors)
    {
        fastFilterElements.addAll(Arrays.asList(ors));
        return this;
    }

    @Override
    public IFilter fastSubstringFilter(IFilterOr... ors)
    {
        fastSubstringFilters.addAll(Arrays.asList(ors));
        return this;
    }

    @Override
    public IFilter restrictionFilter(IFilterOr... ors)
    {
        restrictionFilterSettings.addAll(Arrays.asList(ors));
        return this;
    }

    /**
     * @return быстрая фильтрация по подстроке
     */
    public List<IFilterOr> getFastSubstringFilters()
    {
        return fastSubstringFilters;
    }

    /**
     * @return ограничивающая (не видимая) фильтрация
     */
    public List<IFilterAnd> getRestrictionFilters()
    {
        return this.restrictionFilters;
    }

    /**
     * @return Список настроек ограничения фильтрации из списочного дескриптора
     */
    public List<IFilterOr> getRestrictionFilterSettings()
    {
        return restrictionFilterSettings;
    }
}