package ru.naumen.core.server.naming.seq.providers;

import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import jakarta.inject.Inject;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.naming.generators.strategies.NextValueGenerationStrategy;
import ru.naumen.core.server.naming.spi.IsolatedSequenceDao;
import ru.naumen.core.server.naming.spi.PeriodicalSequence;

/**
 * Некэшированный провайдер последовательностей.
 *
 * <AUTHOR>
 * @since Dec 30, 2015
 *
 */
public class DbSequenceProvider implements SequenceProvider
{
    private final IsolatedSequenceDao sequenceDao;
    private final ConcurrentMap<String, Lock> locks = new ConcurrentHashMap<>();

    /**
     * Некэшированный провайдер последовательностей.
     * @param sequenceDao средство доступа к хранилищу последовательностей.
     */
    @Inject
    public DbSequenceProvider(IsolatedSequenceDao sequenceDao)
    {
        this.sequenceDao = sequenceDao;
    }

    @Override
    public void deleteSequence(String sequenceId, Long period)
    {
        sequenceDao.deleteSequence(sequenceId, period);
    }

    @Override
    public int generateId(Pair<String, Long> key, NextValueGenerationStrategy nextValStrategy)
    {
        Lock lock = locks.get(key.getLeft());
        if (lock == null)
        {
            lock = locks.putIfAbsent(key.getLeft(), new ReentrantLock());
            if (lock == null)
            {
                lock = locks.get(key.getLeft());
            }
        }
        lock.lock();

        try
        {
            return sequenceDao.generateId(key, nextValStrategy);
        }
        finally
        {
            lock.unlock();
        }
    }

    @Override
    public PeriodicalSequence getSequence(String sequenceId)
    {
        return sequenceDao.getSequence(sequenceId);
    }

    @Override
    public int getSequenceValue(String sequenceId)
    {
        return sequenceDao.getSequence(sequenceId).getValue();
    }

    @Override
    public void restartSequence(String sequenceId, Long period, int value)
    {
        sequenceDao.restartSequence(sequenceId, period, value);

    }

    @Override
    public void returnValue(Pair<String, Long> key, Integer value)
    {
        sequenceDao.returnValue(key.left, key.right, value);
    }

    @Override
    public void setSequence(String sequenceId, Long period, int value)
    {
        sequenceDao.setSequence(sequenceId, period, value);
    }

    @Override
    public void updateReturned(Pair<String, Long> key, Integer value)
    {
        sequenceDao.updateSequence(key.left, key.right, value);
    }
}
