package ru.naumen.core.server.timing.period;

import ru.naumen.commons.server.utils.DateUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Период времени внутри дня.
 * Имеет начало и конец.
 * <ul>
 * <li> start - количество миллисекунд от начала дня до начала периода
 * <li> end - количество миллисекунд от начала дня до конца периода
 * </ul>
 *
 * <AUTHOR>
 * @since 18.06.2007
 */
public class DayPeriodImpl implements DayPeriod
{
    private long start;
    private long end;

    public DayPeriodImpl()
    {
        super();
    }

    /**
     * @param start количество миллисекунд от начала дня до начала периода
     * @param end количество миллисекунд от начала дня до конца периода
     */
    public DayPeriodImpl(long start, long end)
    {
        super();
        setStart(start);
        setEnd(end);
    }

    @Override
    public int compareTo(DayPeriod o)
    {
        return Long.compare(getStart(), o.getStart());
    }

    @Override
    public boolean equals(Object obj)
    {
        if (!(obj instanceof DayPeriodImpl))
        {
            return false;
        }
        DayPeriodImpl otherPeriod = (DayPeriodImpl)obj;

        return ObjectUtils.equals(otherPeriod.getStart(), getStart())
               && ObjectUtils.equals(otherPeriod.getEnd(), this.getEnd());
    }

    @Override
    public long getEnd()
    {
        return end;
    }

    @Override
    public long getStart()
    {
        return start;
    }

    @Override
    public String getTitle() throws FxException
    {
        return DateUtils.time2Str(start) + " - " + DateUtils.time2Str(end);
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(getStart(), getEnd());
    }

    @Override
    public void setEnd(long end)
    {
        this.end = end;
    }

    @Override
    public void setStart(long start)
    {
        this.start = start;
    }
}
