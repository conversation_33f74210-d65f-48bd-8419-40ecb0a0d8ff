package ru.naumen.core.server.hquery.criterion;

import static ru.naumen.commons.server.utils.StringUtilities.EMPTY;
import static ru.naumen.core.shared.Constants.TEMP_UUID;

import java.util.List;

import java.util.ArrayList;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * Вставка аргументов в запрос напрямую, минуя параметры
 * <AUTHOR>
 * @since 02.10.2018
 */
public class InCriterionBig extends InCriterion
{
    private static final int SEPARATE_PARAMETERS_COUNT = 999;
    public static final String OR = ") OR ";

    public InCriterionBig(HColumn property, Object[] values)
    {
        super(property, values);
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        generateValueNamesArray(parameterCounter);

        List<String> params = checkValuesAndGetValuesNames();
        if (params.isEmpty())
        {
            FalseCriterion.getInstance().append(sb, builder, parameterCounter);
            return;
        }

        boolean isSeparated = params.size() >= SEPARATE_PARAMETERS_COUNT;
        sb.append(isSeparated ? OPEN_BRACE : EMPTY)
                .append(property.getHQL(builder))
                .append(IN)
                .append(OPEN_BRACE);
        sb.append(PREFIX_FOR_NAME_PARAM).append(params.get(0));
        for (int idx = 1; idx < params.size(); idx++)
        {
            if (idx % SEPARATE_PARAMETERS_COUNT == 0)
            {
                sb.append(OR)
                        .append(property.getHQL(builder))
                        .append(IN)
                        .append(OPEN_BRACE);
            }
            else
            {
                sb.append(DELIMITER_FOR_VALUES);
            }
            sb.append(PREFIX_FOR_NAME_PARAM).append(params.get(idx));
        }
        sb.append(isSeparated ? "))" : CLOSE_BRACE);
    }

    private List<String> checkValuesAndGetValuesNames()
    {
        List<String> params = new ArrayList<>();
        List<Object> newValues = new ArrayList<>();
        for (int idx = 0; idx < values.length; idx++)
        {
            if (!(values[idx] instanceof String value && value.startsWith(TEMP_UUID)))
            {
                newValues.add(values[idx]);
                params.add(valuesNames[idx]);
            }
        }
        values = newValues.toArray();
        return params;
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new InCriterionBig(property, values);
    }
}
