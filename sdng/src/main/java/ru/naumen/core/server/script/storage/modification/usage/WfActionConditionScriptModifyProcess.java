package ru.naumen.core.server.script.storage.modification.usage;

import java.util.Collection;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;

import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.dispatch2.wf.WfConstants;
import ru.naumen.metainfo.shared.elements.wf.WfActionCondition;
import ru.naumen.metainfo.shared.script.WorkflowLocationUtils;

/**
 * Контракт поведения при изменении скрипта в Действиях\Условиях при входе\выходе в статус
 * жизненного цикла объектов.
 * Переопределяет специфичные действия при редактировании.
 * Основная логика редактирования содержится в {@link ScriptModifyProcessBase}
 * <AUTHOR>
 * @since Nov 19, 2015
 */
@Component
public class WfActionConditionScriptModifyProcess extends ScriptModifyProcessSupport<WfActionCondition>
{
    public static final String META_CLASS_FQN = "metaClassFqn";
    public static final String STATE_CODE = "stateCode";

    @Override
    protected String getLocation(WfActionCondition holder, ScriptModifyContext context)
    {
        return WorkflowLocationUtils.createLocation(context.getProperty(META_CLASS_FQN),
                context.getProperty(STATE_CODE), holder.getCode());
    }

    @Override
    protected String getOldScriptCode(@Nullable WfActionCondition oldHolder, ScriptModifyContext context)
    {
        if (oldHolder == null)
        {
            return null;
        }
        String oldCode = oldHolder.getProperties().getProperty(WfConstants.SCRIPT_PROPERTY);
        Preconditions.checkNotNull(oldCode, "Old script code required must be not null!");
        return oldCode;
    }

    @Override
    protected Collection<ClassFqn> getRelatedMetaClasses(WfActionCondition holder, ScriptModifyContext context)
    {
        return Lists.newArrayList(ClassFqn.parse(context.getProperty(META_CLASS_FQN)));
    }

    @Override
    protected boolean isUsageChangeable()
    {
        return false;
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, WfActionCondition holder,
            ScriptModifyContext context)
    {
        MapProperties properties = new MapProperties(holder.getProperties());
        properties.setProperty(WfConstants.SCRIPT_PROPERTY, newScriptCode);
        holder.setProperties(properties);
    }
}
