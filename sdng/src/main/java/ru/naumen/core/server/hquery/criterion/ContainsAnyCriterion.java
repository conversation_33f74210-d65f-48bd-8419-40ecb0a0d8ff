package ru.naumen.core.server.hquery.criterion;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * <AUTHOR>
 * @since 04.10.2012
 */
public class ContainsAnyCriterion extends AbstractHCriterion
{
    private final List<?> values;
    private final List<String> valueNames = new ArrayList<>();

    public ContainsAnyCriterion(HColumn property, List<?> values)
    {
        super(property);
        this.values = values;
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        valueNames.clear();
        sb.append('(');
        for (int i = 0; i < values.size(); i++)
        {
            if (i != 0)
            {
                sb.append(" or ");
            }
            String valueName = parameterCounter.next();
            valueNames.add(valueName);
            sb.append(':');
            sb.append(valueName);
            sb.append(" IN elements(");
            sb.append(property.getHQL(builder));
            sb.append(')');
        }
        sb.append(')');
    }

    @Override
    public void setParameters(Query q)
    {
        super.setParameters(q);
        for (int i = 0; i < valueNames.size(); i++)
        {
            q.setParameter(valueNames.get(i), values.get(i).toString());
        }
    }

    @Override
    public String toString()
    {
        return property + " contains any of (" + values + ')';
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new ContainsAnyCriterion(property, new ArrayList<>(values));
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        ContainsAnyCriterion that = (ContainsAnyCriterion)o;
        return Objects.equals(values, that.values);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), values);
    }
}