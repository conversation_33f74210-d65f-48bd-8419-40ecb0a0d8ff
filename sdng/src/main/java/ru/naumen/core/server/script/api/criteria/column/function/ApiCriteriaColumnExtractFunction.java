package ru.naumen.core.server.script.api.criteria.column.function;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HHelper;

/**
 * Извлекает значение из даты или значения интервала.
 * <br>
 * Функция является СУБД-зависимой (в некоторых СУБД может отсутствовать).
 *
 * <AUTHOR>
 * @since 13.04.20
 */
public class ApiCriteriaColumnExtractFunction implements IApiCriteriaColumnFunction
{
    /**
     * Извлекаемое поле. Набор возможных полей зависит от СУБД.
     */
    private final String function;

    public ApiCriteriaColumnExtractFunction(String extractedField)
    {
        this.function = "extract(" + extractedField + " from %s)";
    }

    @Override
    public HColumn format(HColumn innerExpression, @Nullable String alias)
    {
        return HHelper.getFunctionColumn(function, innerExpression, alias);
    }
}
