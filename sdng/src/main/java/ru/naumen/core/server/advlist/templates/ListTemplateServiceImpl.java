package ru.naumen.core.server.advlist.templates;

import java.io.ByteArrayOutputStream;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.LinkedList;
import java.util.List;
import java.util.Objects;
import java.util.Queue;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.xml.bind.JAXBContext;
import jakarta.xml.bind.JAXBElement;
import jakarta.xml.bind.JAXBException;
import jakarta.xml.bind.Marshaller;
import ru.naumen.commons.shared.FxException;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.templates.list.ListTemplate;
import ru.naumen.metainfo.shared.templates.list.usage.ListTemplateUsagePoint;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfoadmin.shared.UsagePointsUtils;
import ru.naumen.objectlist.server.advlist.AdvlistUtils;

/**
 * Реализация сервиса для работы с шаблонами списков
 * <AUTHOR>
 * @since 06.04.2018
 */
@Component
public class ListTemplateServiceImpl implements ListTemplateService
{
    @Inject
    private ListTemplateStorageService storageService;
    @Inject
    private MetainfoServicePersister persister;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private AdvlistUtils advlistUtils;

    @Override
    public void addTemplate(ListTemplate template)
    {
        Objects.requireNonNull(template);
        ListTemplate cloned = template.clone();
        cloned.setCreationDate(new Date());
        cloned.setLastModifiedDate(cloned.getCreationDate());
        advlistUtils.swapDtObjectToUUID(cloned.getTemplate());
        storageService.addTemplate(cloned);
        persister.persistListTemplate(cloned);
    }

    @Override
    public void addUsagePointInListTemplate(ListTemplate template, ListTemplateUsagePoint newUsagePoint)
    {
        String usagePointUuid = UsagePointsUtils.createUUID(newUsagePoint);
        List<ListTemplateUsagePoint> oldUsagePoints = template.getUsagePoints();
        List<ListTemplateUsagePoint> usagePoints = oldUsagePoints.stream().filter(u -> usagePointUuid.equals(
                UsagePointsUtils.createUUID(u))).toList();
        if (usagePoints.isEmpty())
        {
            oldUsagePoints.add(newUsagePoint);
        }
        else
        {
            int index = oldUsagePoints.indexOf(usagePoints.getFirst());
            oldUsagePoints.get(index).getSettingsParts().addAll(newUsagePoint.getSettingsParts());
        }
        saveTemplate(template);
    }

    @Override
    public void deleteTemplates(String... codes)
    {
        storageService.deleteTemplates(codes);
        Arrays.stream(codes).forEach(persister::deleteListTemplate);
    }

    @Override
    public List<ListTemplate> findByFqns(ClassFqn classFqn, List<ClassFqn> casesFqns, String attrGroupCode)
    {
        List<ListTemplate> result = new ArrayList<>();
        List<ListTemplate> templates = getAll();
        for (ListTemplate template : templates)
        {
            if (isSuitableTemplate(template, classFqn, casesFqns, attrGroupCode))
            {
                result.add(template);
            }
        }
        return result;
    }

    @Override
    public List<ListTemplate> getAll()
    {
        return storageService.getAll().stream()
                .map(ListTemplate::clone)
                .peek(template -> advlistUtils.swapUuidToDtObject(template.getTemplate()))
                .collect(Collectors.toList());
    }

    @Override
    @Nullable
    public ListTemplate getTemplate(String code)
    {
        ListTemplate template = storageService.getTemplate(code);
        if (null == template)
        {
            return null;
        }
        ListTemplate cloned = template.clone();
        advlistUtils.swapUuidToDtObject(cloned.getTemplate());
        return cloned;
    }

    @Override
    public boolean isSuitableTemplate(ListTemplate template, ClassFqn classFqn, List<ClassFqn> casesFqns,
            @Nullable String attrGroupCode)
    {
        return ((template.getTemplate().getClazz() != null
                 && template.getTemplate().getClazz().equals(classFqn))
                || (!casesFqns.isEmpty()
                    && ((template.getTemplate().getClazz() != null
                         && getAllDescendants(template.getTemplate().getClazz()).containsAll(casesFqns))
                        || (!template.getTemplate().getCase().isEmpty()
                            && getAllDescendants(template.getTemplate().getCase()).containsAll(casesFqns)))))
               && (attrGroupCode == null || attrGroupCode.equals(template.getTemplate().getAttributeGroup()));
    }

    @Override
    public void removeAllUsagePointInListTemplates(ClassFqn fqn, String formCode, Content deleteContent)
    {
        Queue<Content> queue = new LinkedList<>();
        queue.add(deleteContent);

        while (!queue.isEmpty())
        {
            Content cont = queue.poll();
            if (cont instanceof ObjectListBase)
            {
                removeAllUsagePointInListTemplatesForList(fqn, formCode, cont);
            }
            queue.addAll(cont.getChilds());
        }
    }

    @Override
    public void removeUsagePointInListTemplates(ListTemplate template, ListTemplateUsagePoint usagePoint)
    {
        String usagePointUuid = UsagePointsUtils.createUUID(usagePoint);
        if (template.getUsagePoints().removeIf(u -> UsagePointsUtils.createUUID(u).equals(usagePointUuid)))
        {
            saveTemplate(template);
        }
    }

    @Override
    public void saveTemplate(ListTemplate template)
    {
        Objects.requireNonNull(template);
        ListTemplate old = storageService.getTemplate(template.getCode());
        if (null == old)
        {
            addTemplate(template);
            return;
        }

        ListTemplate cloned = template.clone();
        advlistUtils.swapDtObjectToUUID(cloned.getTemplate());
        if (!serializeListTemplate(old.getTemplate()).equals(serializeListTemplate(cloned.getTemplate())))
        {
            cloned.setLastModifiedDate(new Date());
        }
        storageService.saveTemplate(cloned);
        persister.persistListTemplate(cloned);
    }

    @Override
    public String serializeListTemplate(ObjectListBase listTemplate)
    {
        ObjectListBase clonedList = (ObjectListBase)listTemplate.clone();
        try
        {
            JAXBContext jc = JAXBContext.newInstance(ObjectListBase.class.getPackage().getName());
            ByteArrayOutputStream os = new ByteArrayOutputStream();
            Marshaller m = jc.createMarshaller();
            m.setProperty(Marshaller.JAXB_FORMATTED_OUTPUT, Boolean.TRUE);
            m.setProperty(Marshaller.JAXB_FRAGMENT, true);
            m.marshal(new JAXBElement<>(new javax.xml.namespace.QName("uri", "local"),
                    ObjectListBase.class, clonedList), os);
            return os.toString(StandardCharsets.UTF_8);
        }
        catch (JAXBException e)
        {
            throw new FxException(e);
        }
    }

    private List<ClassFqn> getAllDescendants(ClassFqn clazz)
    {
        return new ArrayList<>(metainfoService.getMetaClassDescendants(clazz, true));
    }

    private List<ClassFqn> getAllDescendants(List<ClassFqn> cases)
    {
        List<ClassFqn> result = new ArrayList<>();
        for (ClassFqn classFqn : cases)
        {
            if (classFqn.isClass())
            {
                continue;
            }
            result.addAll(metainfoService.getMetaClassDescendants(classFqn, true));
        }
        return result;
    }

    private void removeAllUsagePointInListTemplatesForList(ClassFqn fqn, String formCode, Content listContent)
    {
        String usagePointUuid = UsagePointsUtils.createUUID(fqn, formCode, listContent.getUuid());
        getAll().forEach(template ->
        {
            if (template.getUsagePoints()
                    .removeIf(usagePoint -> usagePointUuid.equals(UsagePointsUtils.createUUID(usagePoint))))
            {
                saveTemplate(template);
            }
        });
    }
}
