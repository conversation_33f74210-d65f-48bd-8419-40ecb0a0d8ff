package ru.naumen.core.server.script.storage.modification.usage;

import java.util.Collection;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.storage.ScriptUsageUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;

/**
 * Контракт поведения при изменении скрипта в планировщике задач
 * Переопределяет специфичные действия при редактировании.
 * Основная логика редактирования содержится в {@link ScriptModifyProcessBase}
 * <AUTHOR>
 * @since Dec 3, 2015
 */
@Component
public class ExecuteScriptTaskScriptModifyProcess extends ScriptModifyProcessSupport<ExecuteScriptTask>
{

    @Override
    protected String getLocation(ExecuteScriptTask holder, ScriptModifyContext context)
    {
        return ScriptUsageUtils.getExecuteScriptTaskLocation(holder);
    }

    @Override
    protected String getOldScriptCode(@Nullable ExecuteScriptTask oldHolder, ScriptModifyContext context)
    {
        return oldHolder == null ? null : oldHolder.getScript();
    }

    @Override
    protected Collection<ClassFqn> getRelatedMetaClasses(ExecuteScriptTask holder, ScriptModifyContext context)
    {
        return ScriptUsageUtils.getExecuteScriptTaskFqns();
    }

    @Override
    protected boolean isUsageChangeable()
    {
        return false;
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, ExecuteScriptTask holder,
            ScriptModifyContext context)
    {
        holder.setScript(newScriptCode);
    }

}
