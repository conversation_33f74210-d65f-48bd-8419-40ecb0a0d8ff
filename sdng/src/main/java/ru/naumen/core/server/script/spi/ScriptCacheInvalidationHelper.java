package ru.naumen.core.server.script.spi;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.support.TransactionSynchronization;
import org.springframework.transaction.support.TransactionSynchronizationManager;

import groovy.text.Template;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.templates.TemplateService;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Компонент помощник, помогает инвалидировать значение в кеше шаблнов по коммиту транзакции
 * Инвалидировать нужно старые значения шаблонов и скриптов при их изме<PERSON><PERSON><PERSON>и<PERSON> или удалении
 *
 * <AUTHOR>
 * @since 04.10.16
 * @see ScriptServiceImpl
 */
@Component
public class ScriptCacheInvalidationHelper
{
    private static final Logger LOG = LoggerFactory.getLogger(ScriptCacheInvalidationHelper.class);

    private final ScriptService scriptService;
    private final TemplateService templateService;

    @Inject
    public ScriptCacheInvalidationHelper(ScriptService scriptService, TemplateService templateService)
    {
        this.scriptService = scriptService;
        this.templateService = templateService;
    }

    /**
     * Инвалидировать скрипт
     * @see Script
     * @param script инвалидируемый скрипт
     */
    public void invalidateScript(Script script)
    {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization()
        {
            @Override
            public void afterCommit()
            {
                if (LOG.isDebugEnabled())
                {
                    LOG.debug("Removing script with code {} from caches", script.getCode());
                }
                scriptService.removeScriptFromCache(script);
            }
        });
    }

    /**
     * Инвалидировать шаблон
     * @see Template
     * @param template инвалидируемый шаблон
     */
    public void invalidateTemplate(String template)
    {
        TransactionSynchronizationManager.registerSynchronization(new TransactionSynchronization()
        {
            @Override
            public void afterCommit()
            {
                if (LOG.isDebugEnabled())
                {
                    LOG.debug("Removing template {} from caches", template);
                }
                templateService.removeTemplateFromCache(template);
            }
        });
    }

}
