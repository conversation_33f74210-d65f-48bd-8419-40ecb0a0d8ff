package ru.naumen.core.server.script.libraries.scripting;

import java.util.Collection;
import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import io.github.classgraph.ClassInfo;
import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.script.libraries.registration.modules.InternalAutomationScriptModule;
import ru.naumen.core.server.script.libraries.registration.scripts.InternalAutomationScript;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Сервис для валидации аннотаций скриптовых параметров
 *
 * @see InternalAutomationScript
 * @see InternalAutomationScriptModule
 * <AUTHOR>
 * @since 01.07.2021
 */
@Component
public class ScriptLibraryValidator
{
    private final MessageFacade messages;
    private final MetainfoUtils metainfoUtils;

    @Inject
    public ScriptLibraryValidator(MessageFacade messages, MetainfoUtils metainfoUtils)
    {
        this.messages = messages;
        this.metainfoUtils = metainfoUtils;
    }

    /**
     * Проверить названия на корректность
     * @param titles локализованные названия
     * @param scriptCode код скрипта
     */
    public void validateScriptTitle(Collection<LocalizedString> titles, String scriptCode)
    {
        boolean hasTitleWithEmptyName = titles.stream().anyMatch(a -> StringUtilities.isEmptyTrim(a.getValue()));
        if (titles.isEmpty() || hasTitleWithEmptyName)
        {
            throw new FxException(messages.getMessage("metainfoValidation.scriptsTitleCantBeEmpty", scriptCode));
        }
        boolean hasTitleLengthWithNameMore1000Symbols = titles.stream()
                .anyMatch(title -> title.getValue().length() > 1000);
        if (hasTitleLengthWithNameMore1000Symbols)
        {
            throw new FxException(messages.getMessage("metainfoValidation.scriptsTitleExceedsMaxLength",
                    scriptCode, 1000));
        }
    }

    /**
     * Проверить скриптовый код на корректность
     * @param scriptCode код скрипта
     * @param visitedScriptCodes посещенные ранее коды скриптов (используется для проверки на дубли в рамках одной
     *                           библиотеки)
     * @param titles локализованные названия
     */
    public void validateScriptCode(String scriptCode, Set<String> visitedScriptCodes,
            Collection<LocalizedString> titles)
    {
        if (!visitedScriptCodes.add(scriptCode))
        {
            throw new FxException(messages.getMessage("scriptcatalog-scriptCodeExistsError", scriptCode));
        }
        if (!MetainfoUtils.isValidDefaultCode(scriptCode, true))
        {
            throw new FxException(messages.getMessage("metainfoValidation.scriptsCodeIncorrect",
                    metainfoUtils.getLocalizedValue(titles), scriptCode));
        }
    }

    /**
     * Проверяет наследуется ли переданный класс от {@link groovy.lang.Script}
     * @param classInfo метаданные о классе
     */
    public static void validateSuperclass(ClassInfo classInfo)
    {
        if (!classInfo.extendsSuperclass(groovy.lang.Script.class.getName()))
        {
            throw new FxException("[{}] class must extend groovy.lang.Script.", classInfo.getName());
        }
    }
}