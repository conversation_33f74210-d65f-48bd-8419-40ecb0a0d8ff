package ru.naumen.core.server.naming.generators;

import java.util.concurrent.ConcurrentMap;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.transaction.Status;
import jakarta.transaction.Synchronization;
import jakarta.transaction.SystemException;
import jakarta.transaction.Transaction;
import jakarta.transaction.TransactionManager;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.base.Preconditions;
import com.google.common.collect.Maps;

import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.naming.extractors.PeriodExtractor;
import ru.naumen.core.server.naming.extractors.SequenceExtractor;
import ru.naumen.core.server.naming.generators.strategies.NextValueGenerationStrategy;
import ru.naumen.core.server.naming.generators.strategies.SequenceSaver;
import ru.naumen.core.server.naming.seq.providers.SequenceProvider;

/**
 * Реализация {@link IIdGenerator} для генерации периодических последовательностей.
 * <p>
 * Например, для последовательности ежедневно начинающейся с 1.
 *
 * Используется как для генерации Id так и для правил именования (NamingService)
 *
 * <AUTHOR>
 */
public class PeriodicalIDGenerator implements IIdGenerator
{
    static class NextValueStrategy implements NextValueGenerationStrategy
    {
        @Override
        public int next(Pair<String, Long> key, Integer value, SequenceSaver saver)
        {
            Preconditions.checkNotNull(value, "Current value should not be null");
            Preconditions.checkNotNull(key, "Sequence-period key should not be null.");
            Preconditions.checkNotNull(saver, "Sequence saver should not be null.");

            Integer old = null;
            String className = null;
            if (LOG.isDebugEnabled())
            {
                old = value;
                className = this.getClass().getName();
            }

            int result = value + 1;
            LOG.debug("Increment sequence from {} to {}. {}", old, result, className);

            saver.save(key, result);
            return result;
        }
    }

    private static class IdentityKey
    {
        private final Object delegate;

        public IdentityKey(Object delegate)
        {
            this.delegate = delegate;
        }

        @Override
        public boolean equals(Object obj)
        {
            if (null == obj)
            {
                return false;
            }
            if (!(obj instanceof IdentityKey))
            {
                return false;
            }
            // сравниваем не по equals т.к. должны работать именно с одним объектом
            return delegate == ((IdentityKey)obj).delegate;
        }

        @Override
        public int hashCode()
        {
            return System.identityHashCode(delegate);
        }
    }

    /**
     * Синхронизация транзакции для возвращения сгенерированных значений в пул (требуется для отсутствия дырок в
     * нумерации).
     * Не гарантируется отсутствие дырок после перезапуска системы. 
     */
    private class TransactionSynchronization implements Synchronization
    {
        private final Object obj;
        private final Pair<String, Long> key;
        private final Integer value;

        public TransactionSynchronization(Object obj, Pair<String, Long> key, Integer value)
        {
            this.key = key;
            this.value = value;
            this.obj = obj;
        }

        @Override
        public void afterCompletion(int status)
        {
            switch (status)
            {
                case Status.STATUS_COMMITTED:
                    break;
                case Status.STATUS_ROLLEDBACK:
                    // возвращаем сгенерированное значение
                    returnValue(key, value);
                    break;
                default://do nothing
                    break;
            }

            generatedCache.remove(obj);
        }

        @Override
        public void beforeCompletion()
        {
        }
    }

    private final static Logger LOG = LoggerFactory.getLogger(PeriodicalIDGenerator.class);

    //@formatter:on
    @Inject
    protected MetaStorageService metaStorage;
    @Inject
    protected SequenceProvider sequenceProvider;
    @Inject
    protected TransactionManager transactionManager;
    /**
     * <Объект для которого сгенерировано значение, сгенерированное значение>
     */
    private final ConcurrentMap<Object, Integer> generatedCache = Maps.newConcurrentMap();

    private final SequenceExtractor sequenceExtractor;

    @SuppressWarnings("rawtypes")
    private final PeriodExtractor periodExtractor;

    /**
     * Реализация {@link IIdGenerator} для генерации периодических последовательностей.
     *
     * @param sequenceExtractor определятор последовательности по объекту.
     * @param periodExtractor определятор периода по объекту.
     */
    public PeriodicalIDGenerator(SequenceExtractor sequenceExtractor,
            @SuppressWarnings("rawtypes") PeriodExtractor periodExtractor)
    {
        this.sequenceExtractor = sequenceExtractor;
        this.periodExtractor = periodExtractor;
    }

    /**
     * @return название генератора.
     */
    public String getGeneratorName()
    {
        return "periodicalIdGenerator" + periodExtractor.currentPeriod();
    }

    @Override
    public int getId(Object obj)
    {
        // генерация номера может происходить до того момента как объект сохранен в БД. Следовательно
        // у объекта еще не сгенерирован id и у всех объектов будет одинаковый hashCode (переопределенный)
        // Используем identityHashCode чтобы отличать не сохраненные объекты в БД
        IdentityKey identityHashCode = new IdentityKey(obj);

        // гарантируем что для одного и того же объекта будет возвращено одно и тоже значение в пределах одной
        // транзакции
        Integer value = generatedCache.get(identityHashCode);
        if (value == null)
        {
            Pair<String, Long> key = getSequenceAndPeriodKey(obj);
            value = sequenceProvider.generateId(key, getNextValueStrategy());
            generatedCache.put(identityHashCode, value);
            registerReturnValueSynchronization(identityHashCode, key, value);
        }
        return value;
    }

    @SuppressWarnings("rawtypes")
    @Override
    public PeriodExtractor getPeriodExtractor()
    {
        return periodExtractor;
    }

    @Override
    public SequenceExtractor getSequenceExtractor()
    {
        return sequenceExtractor;
    }

    @PostConstruct
    public void init()
    {
        updateSequenceTable();
    }

    /**
     * Обновить список возвращенных номеров
     * @param key идентификатор последовательности, период, для которых обновляются возвращённые значения.
     * @param value новое возвращённое значение.
     */
    public void updateReturned(Pair<String, Long> key, Integer value)
    {
        sequenceProvider.updateReturned(key, value);
    }

    protected NextValueGenerationStrategy getNextValueStrategy()
    {
        return new NextValueStrategy();
    }

    protected Pair<String, Long> getSequenceAndPeriodKey(String sequenceId)
    {
        return Pair.create(sequenceId, periodExtractor.currentPeriod());
    }

    protected void restart(int id, Object obj)
    {
        Pair<String, Long> pair = getSequenceAndPeriodKey(obj);
        sequenceProvider.restartSequence(pair.getLeft(), pair.getRight(), id);
    }

    /**
     * Возвращение номера.
     */
    protected void returnValue(Pair<String, Long> key, Integer value)
    {
        sequenceProvider.returnValue(key, value);
    }

    protected void updateSequenceTable()
    {
    }

    Transaction getTransaction() throws SystemException
    {
        return transactionManager.getTransaction();
    }

    private Pair<String, Long> getSequenceAndPeriodKey(Object obj)
    {
        String sequenceId = sequenceExtractor.getSequenceId(obj);
        @SuppressWarnings("unchecked")
        Long keyPeriod = periodExtractor.extractPeriod(obj);
        return Pair.create(sequenceId, keyPeriod);
    }

    private void registerReturnValueSynchronization(IdentityKey identityHashCode, Pair<String, Long> key, Integer value)
    {
        try
        {
            TransactionSynchronization sync = new TransactionSynchronization(identityHashCode, key, value);
            getTransaction().registerSynchronization(sync);
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }
}