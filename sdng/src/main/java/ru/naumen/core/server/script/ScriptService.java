package ru.naumen.core.server.script;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.regex.Pattern;

import org.codehaus.groovy.control.customizers.CompilationCustomizer;
import org.springframework.beans.factory.BeanFactory;

import jakarta.annotation.Nullable;
import net.customware.gwt.dispatch.server.Dispatch;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.Version;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.script.api.SearchParams;
import ru.naumen.core.server.script.modules.compile.ModulesClassLoaderManager.ModuleClassLoaderTemplate;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.script.spi.ScriptConditionsApi;
import ru.naumen.core.server.script.spi.limits.ScriptDependencies;
import ru.naumen.core.server.script.spi.limits.ScriptImportTemplate;
import ru.naumen.core.server.script.spi.limits.ScriptMethodCallTemplate;
import ru.naumen.core.shared.changetracking.MessageConstants;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Сервис для выполнения скриптов.
 *
 * <AUTHOR>
 *
 */
public interface ScriptService extends Cloneable
{
    class Constants
    {
        /**
         * Текущий объект над которым производится действие (например, смены состояния)
         */
        public static final String SUBJECT = "subject";
        public static final String FORM = "form";

        /**
         * Объект с формы блока комментария
         */
        public static final String COMMENT_FORM = "commentForm";

        public static final String CURRENT_SUBJECT = "currentSubject";
        public static final String CURRENT_FILE = "currentFile";
        public static final String CURRENT_COMMENT = "currentComment";
        public static final String SOURCE_OBJECT = "sourceObject";
        /**
         * Объект, представляющий {@link #SUBJECT} до выполнения над ним действий
         */
        public static final String OLD_SUBJECT = "oldSubject";
        public static final String ACTION_USER = "user";
        /**
         * Паттерн для поиска использования переменной user в скрипте
         */
        public static final Pattern ACTION_USER_PATTERN = Pattern.compile("[\\W|^]" + ACTION_USER + "[\\W|$]");
        /**
         * Uuid текущей ветки режима планирования
         */
        public static final String CURRENT_BRANCH = "currentBranch";
        /**
         * Переменные доступные в скриптах в вычислимых ролях
         */
        public static final List<String> ROLE_BINDINGS = List.of(Constants.SOURCE_OBJECT, Constants.SUBJECT,
                Constants.ACTION_USER, Constants.OLD_SUBJECT);
        /**
         * Псевдоатрибут для прокидывания user-а в следующий скрипт цепочки операций при вызове utils.edit/create
         */
        public static final String CUSTOM_USER = "@user";

        /**
         * Псевдоатрибут для прокидывания кода файлового хранилища при вызове utils.attachFile/create
         */
        public static final String STORAGE_CODE = "@storage";
        /**
         * Псевдоатрибут для прокидывания призанака системности файла при вызове utils.attachFile/create
         */
        public static final String SYSTEM_CODE = "@system";

        public static final String PROCESS = "process";

        public static final String COMMENT = "comment";

        /**
         * Контекстная переменная commentObject, представляющая собой объект комментария
         */
        public static final String COMMENT_OBJECT = "commentObject";

        public static final String COMMENT_OBJECT_UUID = "commentObjectUuid";

        public static final String COMMENT_IS_PRIVATE = "isCommentPrivate";
        /**
         * Возможные для перехода статусы текущего объекта. Используется в скриптах фильтраци статусов.
         */
        public static final String POSSIBLE_STATES = "possibleStates";

        /**
         * IP-адрес компьютера, с которого выполняется действие в приложении
         */
        public static final String IP_ADDRESS = "ip";

        /**
         * Код контента, доступен в скриптах встроенных приложений.
         */
        public static final String CONTENT_CODE = "contentCode";

        public static final String CHANGED_ATTRIBUTES = "changedAttributes";

        /**
         * Уровень эскалации
         */
        public static final String ESCALATION_LEVEL = "escalationLevel";

        /**
         * Название переменой доступной в контексте выполнения скрипта в которой содержится системное API приложения.
         */
        public static final String API_PARAM = "api";

        /**
         * Название переменой доступной в контексте выполнения скрипта в которой содержится модули приложения
         * (т.е. набор скриптов-библиотек)
         * <p>
         * Для каждого {@link Script скрипта} и {@link ScriptModule модуля} становится доступна
         * переменная {@value #MODULES_PARAM}.код_модуля.
         *
         * @see
         * <a href="https://projects.naumen.ru/ServiceDesk/Releases/4.0/Scripts#.D0.9C.D0.BE.D0.B4.D1.83.D0.BB.D0.B8">Wiki scripts</a>
         */
        public static final String MODULES_PARAM = "modules";

        /**
         * Название переменой доступной в контексте выполнения скрипта в которой содержится {@link BeanFactory}
         * приложения
         */
        public static final String BEAN_FACTORY_PARAM = "beanFactory";

        /**executeFunction
         * Название переменой доступной в контексте выполнения скрипта в которой содержится {@link Dispatch}
         * приложения.
         * Должен использоваться для выполнения комманд на сервере
         */
        public static final String DISPATCH_PARAM = "dispatch";

        /**
         * Название переменой доступной в контексте выполнения скрипта в которой содержится {@link DaoFactory}
         * приложения.
         */
        public static final String DAOFACTORY_PARAM = "daoFactory";

        /**
         * Название переменой доступной в контексте выполнения скрипта в которой содержится {@link MetainfoUtils}.
         */
        public static final String METAINFO_UTILS_PARAM = "metainfoUtils";

        /**
         * Название переменой доступной в контексте выполнения скрипта в которой содержится
         * {@link Version#getVersion() версия приложения}
         */
        public static final String APP_VERSION_PARAM = "appVersion";

        /**
         * Название переменой доступной в контексте выполнения скрипта в которой содержится {@link CommonUtils
         * утилитарные методы}
         */
        public static final String UTILS_PARAM = "utils";

        /**
         * Название переменной доступной в контексте выполнения скрипта, в которой содержатся
         * {@link ScriptConditionsApi методы}
         */
        public static final String CONDITIONS_PARAM = "op";

        /**
         * Название переменной, доступной в контексте выполнения скрипта, в которой содержатся {@link SearchParams
         * методы}
         */
        public static final String SEARCH_PARAMS = "sp";

        /**
         * Название переменной доступной в контексте скрипта кастомизации, в которой содержится объект оповещения.
         * Скрипт кастомизации может изменять этот объект, чтобы изменять параметры последующего оповещения.
         */
        public static final String NOTIFICATION_PARAM = "notification";

        /**
         * Название локали доступной в контексте скрипта кастомизации оповещения
         */
        public static final String NOTIFICATION_LANG = "lang";

        /**
         * Название переменной доступной в контексте скрипта кастомизации, в которой содержится объект уведомления.
         * Скрипт кастомизации может изменять этот объект, чтобы изменять параметры последующего уведомления.
         */
        public static final String PUSH_PARAM = "push";

        /**
         * Название переменной доступной в контексте скрипта кастомизации, в которой содержится объект уведомления на
         * портале.
         * Скрипт кастомизации может изменять этот объект, чтобы изменять параметры последующего уведомления.
         */
        public static final String PUSH_PORTAL_PARAM = "pushPortal";

        /**
         * Название переменной, доступной в контексте скрипта кастомизации. В переменной содержится объект сообщения,
         * передаваемого по WebSocket-каналу. Скрипт кастомизации может изменять этот объект.
         */
        public static final String WS_MESSAGE_PARAM = "wsMessage";

        /**
         * Название переменной доступной в контексте скрипта кастомизации, в которой содержится объект уведомления.
         * Скрипт кастомизации может изменять этот объект, чтобы изменять параметры последующего уведомления.
         */
        public static final String PUSH_MOBILE_PARAM = "pushMobile";

        /**
         * Название переменной, содержащей тело оповещения. Переменная доступна в шаблонах стилей.
         */
        public static final String CONTENT_PARAM = "content";

        public static final String MESSAGE_PARAM = "message";

        public static final String PARAM_RESULT = "result";

        public static final String INBOUND_MESSAGE_SERVER = "incomingServer";

        /**
         * Контекст упоминания объекта в тексте RTF
         */
        public static final String MENTION_PARAM = "mention";

        /**
         * Оригинальный скрипт, результат которого используется в дальнейшем
         */
        public static final String WRAPPED_SCRIPT_PARAM = "wrapped";

        /**
         * Информация о текущем таске {@link ru.naumen.metainfo.shared.scheduler.SchedulerTask}
         */
        public static final String CURRENT_TASK_INFO = "currentTaskInfo";

        /**
         * Начальные значения доступные в вычислимых ролях, в скриптах на вход встатус зарегистрирован и пр.
         */
        public static final String INITIAL_VALUES = "initialValues";

        /**
         * Переменные, доступные в рамках выполнения скрипта фильтрации параметра пользовательского события
         */
        public static final String SUBJECTS = "subjects";
        public static final String LIST = "list";
        public static final String CARD_OBJECT = "cardObject";
        public static final String SOURCE = "source";
        public static final String MENTIONS = "mentions";

        /**
         * Информация о местоположении
         * Переменная необходима для передачи координат из мобильного приложения
         * имеет два свойства: широта (longitude) и долгота (latitude) типа double
         */
        public static final String GEO = "geo";

        /**
         *
         */
        public static final String VOICE_ATTACHMENTS = "voiceAttachments";

        /**
         *
         */
        public static final String VOICE_TRANSCRIPTION = "voiceTranscription";

        /**
         *
         */
        public static final String AVAILABLE_CASES = "availableCases";

        /**
         * Код текущего действия по событию
         */
        public static final String EVENT_ACTION_CODE = "eventActionCode";

        public static final String CHANGED_ATTRIBUTE_TITLES = MessageConstants.CHANGED_ATTRIBUTE_TITLES_VAR;

        public static final String EDITABLE_ATTRIBUTE_TITLES = MessageConstants.EDITABLE_ATTRIBUTE_TITLES_VAR;

        public static final String AUTHOR_NAME = MessageConstants.AUTHOR_NAME_VAR;
    }

    /**
     * Удаляет скомпилированный модуль - code
     */
    void deleteCompiledModule(String code);

    /**
     * Удаляет скомпилированные модули - code
     */
    void deleteCompiledModules(Collection<String> moduleCodes);

    /**
     * Выполняет заданный скрипт
     *
     * @param <T> тип результата выполнения скрипта
     * @param script объект скрипта
     */
    <T> T execute(Script script);

    /**
     * Выполняет заданный скрипт с указанными начальными значениями контекста.
     * <p>
     * @param <T> тип возвращаемого значения
     * @param script выполняемый скрипт
     * @param initialBindings начальные значения контекста выполнения скрипта
     */
    <T> T execute(Script script, Map<String, Object> initialBindings);

    /**
     * Выполняет заданный скрипт с указанными начальными значениями контекста.
     * @param <T> тип возвращаемого значения
     * @param script выполняемый скрипт
     * @param initialBindings начальные значения контекста выполнения скрипта
     * @param ctx контекст выполнения скрипта
     */
    <T> T execute(Script script, Map<String, Object> initialBindings, ScriptExecutionContext ctx);

    /**
     * Выполняет заданный скрипт с указанными начальными значениями контекста.
     * @param <T> тип возвращаемого значения
     * @param script выполняемый скрипт
     * @param initialBindings начальные значения контекста выполнения скрипта
     * @param ctx контекст выполнения скрипта
     * @param embeddedApplication код встроенного приложения, если вызов идет из встроенного приложения, иначе null
     */
    <T> T execute(Script script, Map<String, Object> initialBindings, ScriptExecutionContext ctx,
            @Nullable String embeddedApplication);

    <T> T executeFunction(Script script, String name, Object... args);

    <T> T executeFunction(Script script, String name, @Nullable Map<String, Object> initialBindings, Object... args);

    <T> T executeFunction(Script script, String name, @Nullable Map<String, Object> initialBindings,
            ScriptExecutionContext ctx, Object... args);

    /**
     * Выполняет метод в скриптовом модуле
     * @param <T> тип возвращаемого значения
     * @param module модуль
     * @param name выполняемый метод
     * @param args аргументы, передаваемые методу
     * @return результат выполнения метода модуля
     */
    <T> T executeModuleFunction(String module, String name, Object... args);

    /**
     * Выполняет метод в скриптовом модуле
     * @param <T> тип возвращаемого значения
     * @param module модуль
     * @param name выполняемый метод
     * @param initialBindings начальные значения контекста выполнения скрипта
     * @param args аргументы, передаваемые методу
     * @return результат выполнения метода модуля
     */
    <T> T executeModuleFunction(String module, String name, @Nullable Map<String, Object> initialBindings,
            Object... args);

    <T> T executeModuleFunction(String module, String name, @Nullable Map<String, Object> initialBindings,
            ScriptExecutionContext ctx, Object... args);

    <T> T executeModuleFunction(String module, @Nullable String embeddedApplication, String name,
            @Nullable Map<String, Object> initialBindings, ScriptExecutionContext ctx, Object... args);

    /**
     * Извлекает информацию о скрипте (импорты, вызовы...) из скрипта.
     *
     * @param script скрипт из которого информация должна быть извлечена.
     * @return извлечённая информация. null, если скрипт был null, либо пуст.
     */
    ScriptDependencies extractScriptDependencies(Script script);

    /**
     * Выполнить принудительную перекомпиляцию всех модулей.<br>
     * Очищает кеш скомпилированных скриптов, загружает все модули
     * и вызывает их перекомпиляцию <b>без использования оптимизаций</b>.
     * @deprecated Нигде, кроме API метода api.metainfo.recompileAllModules() использоваться не должен.
     */
    @Deprecated
    void forceRecompileAllModules();

    /**
     * Получает ошибку компиляции, обернутю в {# ScriptServiceException}, либо null, если ошибок не было
     * @param script
     * @param embeddedApplication код встроенного приложения, если вызов идет из встроенного приложения, иначе null
     * @param customizers
     * @return
     */
    ScriptServiceException getCompilationError(Script script,
            @Nullable String embeddedApplication, CompilationCustomizer... customizers);

    /**
     * @return контекстная переменная modules, готовая к использованию в скриптах
     */
    Map<String, groovy.lang.Script> getModulesReadyForUsingInScript(@Nullable String embeddedApplicationCode);

    /**
     * Метод проверяет наличие модуля
     */
    boolean isModuleExists(String moduleCode);

    /**
     * Проверить разрешено ли выполнение модуля через REST
     * @param moduleCode - код модуля
     * @return true - если выполнение модуля разрешено, иначе false
     */
    boolean isModuleRestAllowed(String moduleCode);

    /**
     * Метод проверяет наличие метода с определенной сигнатурой в модуле
     *
     * @param moduleCode код модуля
     * @param name сигнатура метода
     * @param args аргументы метода
     *
     * @return true, если модуль с кодом moduleCode существует и метод с
     * сигнатурой name присутствует в модуле
     */
    boolean isModuleHasMethod(String moduleCode, String name, Object... args);

    boolean isScriptHasMethod(Script script, String name, Object... args);

    /**
     * Компиляция скрипта и проверка используемых в
     * нем конструкций(вызовов методов, импортов)
     */
    boolean isScriptValid(@Nullable Script script);

    /**
     * Создать новый класс лоадер и выполнить в нём указанное действие.
     * <p>После выполнения действия новый класс лоадер применится автоматически</p>
     * <p>В случае ошибки - класс лоадер будет закрыт</p>
     */
    void executeInNewClassLoader(Consumer<ModuleClassLoaderTemplate> action);

    /**
     * Компилирует и перегружает модуль code
     */
    void reloadModule(String code);

    void reloadModules(Collection<String> codes);

    void reloadModules();

    /**
     * Удалить скрипт из кеша
     */
    void removeScriptFromCache(Script script);

    /**
     * Сгенерировать текст по шаблону для скрипта/модуля, включая стандартные скриптовые контекстные
     * переменные (api, utils, modules и т. д.)
     *
     * @param template код шаблона
     * @param bindings биндинги для скрипта/модуля
     */
    String runScriptTemplate(String template, Map<String, Object> bindings);

    /**
     * Указать список доступных для выполнения методов
     * @param acmc
     */
    void setAllowedClassMethods(Collection<ScriptMethodCallTemplate> acmc);

    /**
     * Указать список возможных импортов
     * @param imports
     */
    void setImportsWhiteList(Collection<ScriptImportTemplate> imports);

    /**
     * Обновляет информацию о кодах атрибутов контекстной переменной subject, от которых зависит указанный скрипт.
     * @param script скрипт, информация о котором должна быть обновлена
     * @param customizers пользовательские конфигураторы процесса компиляции
     */
    void updateSubjectDependencies(Script script, CompilationCustomizer... customizers);
}
