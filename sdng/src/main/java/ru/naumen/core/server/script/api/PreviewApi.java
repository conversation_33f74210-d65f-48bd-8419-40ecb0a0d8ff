package ru.naumen.core.server.script.api;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.filestorage.ImagePreviewService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.mailreader.server.queue.IInboundMailMessage;

/**
 *
 * <AUTHOR>
 * @since 12 апр. 2016 г.
 */
@Component("preview")
public class PreviewApi implements IPreviewApi
{
    @Inject
    ImagePreviewService previewService;
    @Inject
    IPrefixObjectLoaderService loaderService;
    @Inject
    ConfigurationProperties configurationProperties;

    @Override
    public void shrinkImages(IInboundMailMessage mail)
    {
        String html = previewService.adjustImgSizes(
                mail.getHtmlBody(configurationProperties.isCutAnswerTagEmailBody()));
        mail.setHtmlBody(html);
    }

    @Override
    public void shrinkImages(IInboundMailMessage message, boolean skipWithSizes)
    {
        String html = previewService.adjustImgSizes(
                message.getHtmlBody(configurationProperties.isCutAnswerTagEmailBody()), skipWithSizes);
        message.setHtmlBody(html);
    }

    @Override
    public void shrinkImages(IUUIDIdentifiable bo, String attrCode)
    {
        previewService.adjustImgSizesInAttr(loaderService.load(bo.getUUID()), attrCode, true, true);
    }

    @Override
    public void shrinkImages(IUUIDIdentifiable bo, String attrCode, boolean skipWithSizes)
    {
        previewService.adjustImgSizesInAttr(loaderService.load(bo.getUUID()), attrCode, skipWithSizes, true);
    }
}