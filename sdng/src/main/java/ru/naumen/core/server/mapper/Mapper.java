package ru.naumen.core.server.mapper;

import jakarta.annotation.Nullable;

import ru.naumen.core.shared.criteria.DtoProperties;

/**
 *  Интерфейс преобразователя значении различных типов.
 *
 *  Каждый преобразователь умеет преобразовыввать значения одного типа в значение другого
 *
 * <AUTHOR>
 *
 * @param <F> тип преобразовываемого объекта
 * @param <T> тип объекта в который будет преобразовано значение
 */
public interface Mapper<F, T>
{
    /**
     * Возвращает класс объектов для которых {@link Mapper} умеет преобразовывать значения
     *
     * @return исходный класс объектов
     */
    Class<F> getFrom();

    /**
     * Возвращает класс объектов в которые {@link Mapper} умеет преобразовывать значения
     *
     * @return класс объектов назначения
     */
    Class<T> getTo();

    /**
     * Производит инициализацию {@link Mapper}
     * <p>
     * Инициализация выполняется сервисом единожды при регистрации {@link Mapper}-а
     *
     * @param service Сервис преобразования объектов
     */
    void init(MappingService service);

    /**
     * Производит непосредственное преобразование
     *
     * @param from преобразуемое значение
     * @param to объект в котрый необходимо преобразовать
     * @param properties список атрибутов объекта необходимых для преобразования. Является не обязательным (может быть 
     *                   null). Каждая конкретная реализация mapper-а может решать будет ли она преобразовывать объект
     *                   полностью, либо только указанные атрибуты.
     */
    void transform(F from, T to, @Nullable DtoProperties properties);

    /**
     * Производит непосредственное преобразование
     *
     * @param from преобразуемое значение
     * @param to объект в котрый необходимо преобразовать
     * @param mappingContext контекст преобразования
     */
    void transform(F from, T to, MappingContext mappingContext);
}
