package ru.naumen.core.server.catalog;

import jakarta.persistence.Cacheable;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.ImpactCatalog;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Элемент справочника "Уровни влияния"
 *
 * <AUTHOR>
 * @since 24.12.2010
 */
//@formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Table(name = "tbl_impact", uniqueConstraints = { 
        @UniqueConstraint(name = "tbl_impact_code_key", columnNames = {"code"})},
        indexes={@jakarta.persistence.Index(name = "idx_impact_parent", columnList="parent")})
@Cacheable
@DiscriminatorValue("-")
@DiscriminatorFormula(FlexHelper.NULL_DISCRIMINATOR_FORMULA)
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(ImpactCatalogItem.CLASS_ID)
@Metaclass(id = ImpactCatalogItem.CLASS_ID, 
           title = { @LStr(value = "Элемент справочника 'Уровни влияния'"),
                   @LStr(lang = "en", value = "'Impact levels' catalog item"),
                   @LStr(lang = "de", value = "Katalogartikel 'Einflussebenen'") },
           withCase = false)
@Catalog(code = ImpactCatalog.CODE, 
        title = { @LStr(value = "Уровни влияния"),
                @LStr(lang = "en", value = "Impact levels"),
                @LStr(lang = "de", value = "Einflussebenen")},
        description = { @LStr(value = "Содержит уровни влияния инцидентов на бизнес-процессы."), 
                        @LStr(lang = "en", value = "Contains levels of incidents impact on business processes."),
                        @LStr(lang = "de", value = "Enthält Eindlussebenen von Incidenten auf Geschäftsprozesse.")})
//@formatter:on
public class ImpactCatalogItem extends CatalogItem<ImpactCatalogItem>
{
    public static final String CLASS_ID = ImpactCatalog.ITEM_CLASS_ID;

    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    @Override
    public ClassFqn getMetaClass()
    {
        return ImpactCatalog.ITEM_FQN;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }
}
