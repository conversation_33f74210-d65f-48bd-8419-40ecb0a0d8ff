package ru.naumen.core.server.script.api;

import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.Iterables;

import java.util.ArrayList;
import java.util.HashMap;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.spi.ScriptServiceScriptCollector;
import ru.naumen.core.server.script.spi.ScriptServiceStatistic.Sync;
import ru.naumen.core.server.script.spi.ScriptServiceStatisticImpl.ScriptStatus;

/**
 * API для автоматизированного тестирования
 *
 * <AUTHOR>
 * @since 4.3.13
 * @see IAtApi
 */
@Component("at")
public class AtApi implements IAtApi
{
    public static final String NO_SUCH_SCRIPT_FOUND = "No such script found";

    private static final Logger LOG = LoggerFactory.getLogger(AtApi.class);

    @Inject
    ScriptServiceScriptCollector scriptServiceScriptCollector;

    @Override
    public void clearScripts()
    {
        scriptServiceScriptCollector.clearScripts();
    }

    @Override
    public int countScriptRuns(String script)
    {
        return scriptServiceScriptCollector.getScriptObjs(script).size();
    }

    @Override
    public void enableCollectScripts(boolean collectScripts)
    {
        LOG.info("Change error collector to: " + collectScripts);
        scriptServiceScriptCollector.setCollectScripts(collectScripts);
    }

    @Override
    public List<String> getErrors()
    {
        return getScriptsExceptionMessages(scriptServiceScriptCollector.getExecutedScripts());
    }

    @Override
    public List<String> getErrors(String script)
    {
        return getScriptsExceptionMessages(scriptServiceScriptCollector.getScriptObjs(script));
    }

    @Override
    public String getLastError(String script)
    {
        List<Sync> scriptObjs = scriptServiceScriptCollector.getScriptObjs(script);
        return scriptObjs.isEmpty() ? NO_SUCH_SCRIPT_FOUND : Iterables.getLast(scriptObjs).getExceptionMessage();
    }

    @Override
    public String getLastState(String script)
    {
        List<Sync> scriptObjs = scriptServiceScriptCollector.getScriptObjs(script);
        return scriptObjs.isEmpty() ? NO_SUCH_SCRIPT_FOUND : Iterables.getLast(scriptObjs).getExecutionStatus();
    }

    @Override
    public Map<String, String> getScriptsAndErrors()
    {
        return getScriptsAndExceptionMessages(scriptServiceScriptCollector.getExecutedScripts());
    }

    @Override
    public boolean isCollectScripts()
    {
        return scriptServiceScriptCollector.isCollectScripts();
    }

    private Map<String, String> getScriptsAndExceptionMessages(List<Sync> executedScripts)
    {
        Map<String, String> errors = new HashMap<>();
        for (Sync executedScript : executedScripts)
        {
            String exceptionScript = executedScript.getScript().getBody();
            if (executedScript.getExecutionStatus().equals(ScriptStatus.FAIL.getText())
                && !StringUtilities.isEmptyTrim(exceptionScript))
            {
                errors.put(exceptionScript, executedScript.getExceptionMessage());
            }
        }
        return errors;
    }

    private List<String> getScriptsExceptionMessages(List<Sync> executedScripts)
    {
        List<String> errors = new ArrayList<>();
        for (Sync executedScript : executedScripts)
        {
            String exceptionMessage = executedScript.getExceptionMessage();
            if (executedScript.getExecutionStatus().equals(ScriptStatus.FAIL.getText())
                && !StringUtilities.isEmptyTrim(exceptionMessage))
            {
                errors.add(exceptionMessage);
            }
        }
        return errors;
    }
}
