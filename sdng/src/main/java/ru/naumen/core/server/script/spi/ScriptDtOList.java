package ru.naumen.core.server.script.spi;

import java.util.Collection;
import java.util.List;
import java.util.ListIterator;

/**
 * Реализация {@link List} для использования в скриптах
 * <AUTHOR>
 *
 * @param <T> тип элементов списка
 */
public class ScriptDtOList<T> extends ScriptDtOCollection<T, List<Object>> implements List<T>
{
    /**
     * @param delegate
     * @param helper
     */
    public ScriptDtOList(List<Object> delegate, ScriptDtOHelper helper)
    {
        super(delegate, helper);
    }

    @Override
    public void add(int index, T element)
    {
        helper.unmodify();
    }

    @Override
    public boolean addAll(int index, Collection<? extends T> c)
    {
        helper.unmodify();
        return false;
    }

    @Override
    public T get(int index)
    {
        return helper.wrap(delegate.get(index));
    }

    @Override
    public int indexOf(Object o)
    {
        return delegate.indexOf(helper.unwrap(o));
    }

    @Override
    public int lastIndexOf(Object o)
    {
        return delegate.lastIndexOf(helper.unwrap(o));
    }

    @Override
    public ListIterator<T> listIterator()
    {
        return helper.wrapListIterator(delegate.listIterator());
    }

    @Override
    public ListIterator<T> listIterator(int index)
    {
        return helper.wrapListIterator(delegate.listIterator(index));
    }

    @Override
    public T remove(int index)
    {
        return helper.<T> unmodify();
    }

    @Override
    public T set(int index, T element)
    {
        return helper.<T> unmodify();
    }

    @Override
    public List<T> subList(int fromIndex, int toIndex)
    {
        return helper.wrapList(delegate.subList(fromIndex, toIndex));
    }

}
