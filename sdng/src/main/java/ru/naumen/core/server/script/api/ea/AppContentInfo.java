package ru.naumen.core.server.script.api.ea;

import jakarta.annotation.Nullable;

/**
 * Информация о контенте встроенного приложения
 *
 * <AUTHOR>
 * @since 24.09.2019
 * @see IAppContentInfo
 */
public class AppContentInfo implements IAppContentInfo
{
    /**
     * fqn класса/типа, в котором присутствует ВП
     */
    private final String subjectFqn;
    /**
     * тип формы: карточка объекта, форма добавления, форма редактирования
     */
    private final String formType;
    /**
     * UUID контента
     */
    private final String contentUuid;
    /**
     * название контента
     */
    private final String contentTitle;
    /**
     * код вкладки
     */
    private final String tabUuid;

    AppContentInfo(String subjectFqn, String formType, String contentUuid, String contentTitle,
            @Nullable String tabUuid)
    {
        this.subjectFqn = subjectFqn;
        this.formType = formType;
        this.contentUuid = contentUuid;
        this.contentTitle = contentTitle;
        this.tabUuid = tabUuid;
    }

    @Override
    public String getSubjectFqn()
    {
        return subjectFqn;
    }

    @Override
    public String getFormType()
    {
        return formType;
    }

    @Override
    public String getContentUuid()
    {
        return contentUuid;
    }

    @Override
    public String getContentTitle()
    {
        return contentTitle;
    }

    @Override
    public String getTabUuid()
    {
        return tabUuid;
    }

    @Override
    public String toString()
    {
        return "AppContentInfo [subjectFqn=" + subjectFqn + ", formType=" + formType + ", contentUuid="
               + contentUuid + ", contentTitle=" + contentTitle + ", tabUuid=" + tabUuid + "]";
    }
}
