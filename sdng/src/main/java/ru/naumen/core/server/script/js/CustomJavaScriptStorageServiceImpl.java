package ru.naumen.core.server.script.js;

import static ru.naumen.metainfo.server.Constants.CUSTOM_JS;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map.Entry;
import java.util.Objects;

import org.infinispan.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.support.TransactionTemplate;

import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.AppContext;
import ru.naumen.core.server.cache.HasISCache;
import ru.naumen.core.server.cache.infinispan.ISCacheProvider;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.spi.IServiceInitializer;
import ru.naumen.metainfo.shared.script.js.CustomJSElement;

/**
 * Реализация хранилища JS-файлов кастомизации.
 * Хранит описания файлов в кэше.
 * <AUTHOR>
 * @since Oct 12, 2017
 */
@Component
public class CustomJavaScriptStorageServiceImpl
        implements CustomJavaScriptStorageService, HasISCache
{
    private static final int INIT_TIMEOUT = 600;

    private static final String ITEMS_FQN = "items";
    private static final String FILES_FQN = "files";

    private static final Logger LOG = LoggerFactory.getLogger(CustomJavaScriptStorageServiceImpl.class);

    private final ISCacheProvider cacheProvider;
    private final PlatformTransactionManager txManager;
    private final List<IServiceInitializer<CustomJavaScriptStorageService>> initializers;
    private final MessageFacade messages;

    @Inject
    public CustomJavaScriptStorageServiceImpl(
            ISCacheProvider cacheProvider,
            PlatformTransactionManager txManager,
            @Named(CustomJavaScriptStorageConfiguration.JS_ELEMENT_STORAGE_INITIALIZERS_NAME)
            List<IServiceInitializer<CustomJavaScriptStorageService>> initializers,
            MessageFacade messages)
    {
        this.cacheProvider = cacheProvider;
        this.txManager = txManager;
        this.initializers = initializers;
        this.messages = messages;
    }

    @Override
    public void add(CustomJSElement jsElement)
    {
        Objects.requireNonNull(jsElement);
        if (get(jsElement.getCode()) != null)
        {
            throw new FxException(messages.getMessage("customjs-codeNonUnique", jsElement.getCode()));
        }
        save(jsElement);
    }

    @Override
    public void clearFileContent(@Nullable String code)
    {
        if (code == null)
        {
            return;
        }
        cacheProvider.remove(CUSTOM_JS, getCacheKey(FILES_FQN, code));
    }

    @Override
    public CustomJSElement get(@Nullable String code)
    {
        if (code == null)
        {
            return null;
        }
        return cacheProvider.get(CUSTOM_JS, getCacheKey(ITEMS_FQN, code));
    }

    @Override
    public <K, V> Cache<K, V> getCache()
    {
        return cacheProvider.getCache(CUSTOM_JS);
    }

    @Override
    public byte[] getFileContent(@Nullable String code)
    {
        if (code == null)
        {
            return null;
        }
        return cacheProvider.get(CUSTOM_JS, getCacheKey(FILES_FQN, code));
    }

    @Override
    public List<CustomJSElement> list()
    {
        final Collection<CustomJSElement> scripts =
                cacheProvider.<String, CustomJSElement> getCache(CUSTOM_JS)
                        .entrySet()
                        .stream()
                        .filter(entry -> entry.getKey().startsWith(ITEMS_FQN))
                        .map(Entry::getValue)
                        .toList();
        return new ArrayList<>(scripts);
    }

    @Override
    public void reloadStorage()
    {
        TransactionTemplate tt = new TransactionTemplate(txManager);
        tt.setTimeout(INIT_TIMEOUT);
        tt.execute(status ->
        {
            cacheProvider.initCache(CUSTOM_JS);
            if (!AppContext.isReadOnly())
            {
                processInitializers();
            }
            return null;
        });
    }

    @PostConstruct
    public void init()
    {
        reloadStorage();
    }

    @Override
    public void remove(String... codes)
    {
        for (String code : codes)
        {
            cacheProvider.remove(CUSTOM_JS, getCacheKey(ITEMS_FQN, code));
        }
    }

    @Override
    public void save(@Nullable CustomJSElement jsElement)
    {
        Objects.requireNonNull(jsElement);
        cacheProvider.put(CUSTOM_JS, getCacheKey(jsElement), jsElement);
    }

    @Override
    public void setFileContent(@Nullable String code, byte[] content)
    {
        if (code == null)
        {
            return;
        }
        cacheProvider.put(CUSTOM_JS, getCacheKey(FILES_FQN, code), content);
    }

    private static String getCacheKey(CustomJSElement jsElement)
    {
        return getCacheKey(ITEMS_FQN, jsElement.getCode());
    }

    private static String getCacheKey(String region, String code)
    {
        return region + ":" + code.toLowerCase();
    }

    private void processInitializers()
    {
        for (IServiceInitializer<CustomJavaScriptStorageService> initializer : initializers)
        {
            LOG.debug("Process initializing {}", initializer.getClass().getSimpleName());
            initializer.initialize(this);
            LOG.debug("Finished initializing {}", initializer.getClass().getSimpleName());
        }
    }

    @Override
    public String getMetaRegion()
    {
        return CUSTOM_JS;
    }
}