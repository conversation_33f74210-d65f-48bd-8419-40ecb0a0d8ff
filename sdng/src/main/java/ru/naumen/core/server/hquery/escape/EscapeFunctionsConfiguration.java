package ru.naumen.core.server.hquery.escape;

import static org.springframework.context.annotation.Bean.Bootstrap.BACKGROUND;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import ru.naumen.core.server.hibernate.DataBaseInfo;

/**
 * Конфигурация для автоматической подстановки нужного провайдера функций экранирования строк в запросах
 * в зависимости от используемой СУБД.
 * <AUTHOR>
 * @since Jul 6, 2015
 */
@Configuration
public class EscapeFunctionsConfiguration
{
    @Bean(name = "escapeFunctionsProvider", bootstrap = BACKGROUND)
    public EscapeFunctionsProvider getEscapeFunctionsProvider(DataBaseInfo dataBaseInfo)
    {
        return switch (dataBaseInfo.getDbType())
        {
            case ORACLE -> new OracleEscapeFunctionsProvider();
            case MSSQL -> new SqlServerEscapeFunctionsProvider();
            case POSTGRES -> new PostgresEscapeFunctionsProvider();
        };
    }
}
