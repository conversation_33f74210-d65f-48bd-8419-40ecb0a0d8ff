package ru.naumen.core.server.mapper;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.core.server.bo.DefaultMetaObjectToDtObjectMapper;
import ru.naumen.core.server.mapper.strategy.MappingContextStrategy;
import ru.naumen.core.shared.criteria.DtoProperties;
import ru.naumen.core.shared.dto.SimpleTreeDtObject;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Контекст, в котором выпоняется преобразование объекта (DTO-преобразование) при помощи сервиса {@link MappingService}.
 * При каких-то условиях преобразование одних объектов должно идти по одним правилам, а при других условиях -
 * совершенно иначе. Все эти условия
 * должны складываться в контекст до выполнения преобразования, а потом конкретный {@link Mapper} использует данные
 * условия.
 * </p><p>
 * Пример:<br/>
 * При загрузке списка объектов на advlist не надо проверять права на каждый атрибут каждого объекта. Проверка прав
 * на атрибуты
 * является частью DTO-преобразования {@link DefaultMetaObjectToDtObjectMapper}, и это преобразование выпоняется для
 * большого количества действий, не только
 * для получения списка объектов. Да и получение списка объектов может выполняться не только для загрузки advlist'а
 * .<br/>
 * Таким образом, по умолчанию контекст содержит информацию о том, что проверку прав на атрибуты выполнять нужно. И
 * только в одном случае - при загрузке
 * списка объектов для adlist'а - в контекст попадает информация, что эту проверку надо проигнорировать.
 *
 * <AUTHOR>
 *
 */
public class MappingContext
{
    /** Ключ, указывающий, что не нужно учитывать (получать) иерархию родителей при маппинге. */
    public static final String NO_HIERARCHIES = "NO_HIERARCHIES";

    /** Ключ, указывающий, что вычислимые атрибуты не нужно вычислять */
    public static final String NOT_COMPUTE_ATTRS = "NOT_COMPUTE_ATTRS";
    /** Ключ, указывающий, что ссылочные атрибуты не нужно поднимать */
    public static final String NOT_LINK_ATTRS = "NOT_LINK_ATTRS";

    /** Ключ, указывающий, что не нужно учитывать (формировать) заголовок вкладки для объекта при маппинге. */
    public static final String NO_NAME_MAPPING = "NO_NAME_MAPPING";
    /**
     * Ключ, указывающий на то, что подготавливаются объекты для отображения в списке.
     */
    public static final String OBJECT_LIST_MAPPING = "OBJECT_LIST_MAPPING";

    /** Ключ, указывающий, что в качестве коллекций, указанных в {@link DtoProperties#getEmptyCollections()},
     * следует передавать пустые коллекции.*/
    public static final String EMPTY_COLLECTIONS = "EMPTY_COLLECTIONS";
    /** Ключ для хранения кодов атрибутов, для которых нужно получить значение */
    public static final String REQUIRED_ATTRIBUTES = "REQUIRED_ATTRIBUTES";
    /** Ключ, который сигнализирует о необходимости получения значения только тех атрибутов, коды которых переданы в
     * ключе REQUIRED_ATTRIBUTES. Это позволяет получить значение точно переданных атрибутов без "мусора"
     * обязательных и прочего
     */
    public static final String ONLY_REQUIRED_ATTRIBUTES = "ONLY_REQUIRED_ATTRIBUTES";
    /**
     * Игнорировать невидимые атрибуты в статусах
     */
    public static final String IGNORE_INVISIBLE_IN_STATE = "IGNORE_INVISIBLE_IN_STATE";
    /**
     * Ключ свойства группа атрибутов
     */
    public static final String ATTRIBUTE_GROUP = "ATTRIBUTE_GROUP";
    /**
     * Ключ признака, что необходимо пропускать вычислимые атрибуты при формировании DTO
     */
    public static final String SKIP_COMPUTABLE_ATTRS = "SKIP_COMPUTABLE_ATTRS";
    /**
     * Ключ признака, что необходимо пропускать атрибуты типа "Обратная ссылка" при формировании DTO не видимые на форме
     */
    public static final String SKIP_BACK_LINK_ATTRS = "SKIP_BACK_LINK_ATTRS";
    /**
     * Ключ признака, списка видимых атрибутов
     */
    public static final String VISIBLE_ATTRS = "VISIBLE_ATTRS";
    /**
     * Ключ признака, что необходимо пропускать все атрибуты типа "Обратная ссылка" при формировании DTO в не
     * зависимости от видимости
     */
    public static final String SKIP_ALL_BACK_LINK_ATTRS = "SKIP_ALL_BACK_LINK_ATTRS";
    /** Список атрибутов объекта необходимых для преобразования. Является не обязательным (может быть null).
     * Каждая конкретная реализация mapper-а может решать будет ли она преобразовывать объект полностью, либо только
     * указанные атрибуты. */
    @CheckForNull
    private DtoProperties properties = null;
    /** Необходимо проверять права на каждый атрибут */
    private boolean checkAttrPermissions = true;

    /** Необходимо проверять права на запись */
    private boolean checkWritePermissions = true;
    private boolean checkWritePermissionForProcessLic = false;
    private final IProperties checkAttrPermissionsProperties;
    /** Необходимо ли проверять права на изменение объекта в ИА. */
    private boolean checkAdminPermissions = false;
    /** Дополнительные стратегии обработки данных */
    private final List<MappingContextStrategy> mappingContextStrategies = new ArrayList<>();
    /**
     * Кэшируем список атрибутов для типов, чтобы не формировать списки повторно
     */
    private HashMap<ClassFqn, Collection<Attribute>> attributesCache = new HashMap<>();

    /**
     * Кэш преобразованных значений, можно использовать при повторяющихся значениях атрибутов в списках
     */
    private HashMap<String, Object> transformCache = new HashMap<>();

    /**
     * Набор значений для отложенного batch преобразования
     */
    private HashMap<String, DeferredMappingContext<?, ?>> deferredMappings = new HashMap<>();

    /**
     * Предки объекта, который маппим. В этой коллекции сохраняются и передаются уже загруженные объекты,
     * чтоб не приходилось проходить по всей цепочке наследования повторно.
     */
    private Map<String, SimpleTreeDtObject> parentsCache = new HashMap<>();

    /**
     * Набор "флагов" для переопрделения поведения Mapper'ов
     */
    private final IProperties overrides = new MapProperties();

    @Nullable
    private String sourceOverride;

    public MappingContext(boolean checkWritePermissions)
    {
        this(null, checkWritePermissions);
    }

    public MappingContext(@Nullable DtoProperties properties)
    {
        this(properties, true);
    }

    public MappingContext(@Nullable DtoProperties properties, boolean checkWritePermissions)
    {
        this(properties, true, checkWritePermissions, IProperties.EMPTY, false);
    }

    public MappingContext(@Nullable DtoProperties properties, boolean checkWritePermissions,
            boolean checkAdminPermissions)
    {
        this(properties, true, checkWritePermissions, IProperties.EMPTY, checkAdminPermissions);
    }

    public MappingContext(@Nullable DtoProperties properties, boolean checkAttrPermissions,
            boolean checkWritePermissions, IProperties checkAttrPermissionsProperties, boolean checkAdminPermissions)
    {
        this.properties = properties;
        this.checkAttrPermissions = checkAttrPermissions;
        this.checkWritePermissions = checkWritePermissions;
        this.checkAttrPermissionsProperties = checkAttrPermissionsProperties;
        this.checkAdminPermissions = checkAdminPermissions;
    }

    public MappingContext(@Nullable DtoProperties properties, boolean checkAttrPermissions,
            boolean checkWritePermissions, IProperties checkAttrPermissionsProperties)
    {
        this(properties, checkAttrPermissions, checkWritePermissions, checkAttrPermissionsProperties,
                false);
    }

    public MappingContext(@Nullable DtoProperties properties, IProperties checkAttrPermissionsProperties)
    {
        this(properties, false, false, checkAttrPermissionsProperties, false);
    }

    public MappingContext(@Nullable DtoProperties properties, boolean checkAttrPermissions,
            IProperties checkAttrPermissionsProperties)
    {
        this(properties, checkAttrPermissions, true, checkAttrPermissionsProperties, false);
    }

    public <T extends MappingContextStrategy> void addMappingContextStrategy(Collection<T> mappingContextStrategy)
    {
        this.mappingContextStrategies.addAll(mappingContextStrategy);
    }

    public void addMappingContextStrategy(MappingContextStrategy mappingContextStrategy)
    {
        this.mappingContextStrategies.add(mappingContextStrategy);
    }

    public Collection<Attribute> getAttributes(ClassFqn fqn)
    {
        return attributesCache.get(fqn);
    }

    public HashMap<ClassFqn, Collection<Attribute>> getAttributesCache()
    {
        return attributesCache;
    }

    public IProperties getCheckAttrPermissionsProperties()
    {
        return checkAttrPermissionsProperties;
    }

    public HashMap<String, DeferredMappingContext<?, ?>> getDeferredMappings()
    {
        return deferredMappings;
    }

    public List<MappingContextStrategy> getMappingContextStrategies()
    {
        return mappingContextStrategies;
    }

    public Object getOverrideProperty(String key)
    {
        return overrides.getProperty(key);
    }

    @CheckForNull
    public DtoProperties getProperties()
    {
        return properties;
    }

    public HashMap<String, Object> getTransformCache()
    {
        return transformCache;
    }

    public boolean isCheckAttrPermissions()
    {
        return checkAttrPermissions;
    }

    public boolean isCheckWritePermissions()
    {
        return checkWritePermissions;
    }

    public boolean isCheckWritePermissionForProcessLic()
    {
        return checkWritePermissionForProcessLic;
    }

    public void setAttributes(ClassFqn fqn, Collection<Attribute> attributes)
    {
        attributesCache.put(fqn, attributes);
    }

    public void setAttributesCache(HashMap<ClassFqn, Collection<Attribute>> attributesCache)
    {
        this.attributesCache = attributesCache;
    }

    public void setCheckAttrPermissions(boolean checkAttrPermissions)
    {
        this.checkAttrPermissions = checkAttrPermissions;
    }

    public void setCheckWritePermissions(boolean checkWritePermissions)
    {
        this.checkWritePermissions = checkWritePermissions;
    }

    public void setDeferredMappings(HashMap<String, DeferredMappingContext<?, ?>> deferredMappings)
    {
        this.deferredMappings = deferredMappings;
    }

    /**
     * Указать особые параметры для более оптимального процесса маппинга.
     */
    public void setOverrideProperty(String key, @Nullable Object value)
    {
        overrides.setProperty(key, value);
    }

    public void setProperties(DtoProperties properties)
    {
        this.properties = properties;
    }

    public void setTransformCache(HashMap<String, Object> transformCache)
    {
        this.transformCache = transformCache;
    }

    @Override
    public String toString()
    {
        return "MappingContext [properties=" + properties + ", checkAttrPermissions=" + checkAttrPermissions
               + ", checkWritePermissions=" + checkWritePermissions + ", checkAttrPermissionsProperties="
               + checkAttrPermissionsProperties + ", mappingContextStrategies=" + mappingContextStrategies
               + ", overrides=" + overrides + "]";
    }

    @Nullable
    public String getSourceOverride()
    {
        return sourceOverride;
    }

    public void setSourceOverride(String sourceOverride)
    {
        this.sourceOverride = sourceOverride;
    }

    public Map<String, SimpleTreeDtObject> getParentsCache()
    {
        return parentsCache;
    }

    public void setParentsCache(Map<String, SimpleTreeDtObject> parentsCache)
    {
        this.parentsCache = parentsCache;
    }

    public void setCheckWritePermissionForProcessLic(boolean checkWritePermissionForProcessLic)
    {
        this.checkWritePermissionForProcessLic = checkWritePermissionForProcessLic;
    }

    public boolean isCheckAdminPermissions()
    {
        return checkAdminPermissions;
    }

    public void setCheckAdminPermissions(boolean checkAdminPermissions)
    {
        this.checkAdminPermissions = checkAdminPermissions;
    }
}
