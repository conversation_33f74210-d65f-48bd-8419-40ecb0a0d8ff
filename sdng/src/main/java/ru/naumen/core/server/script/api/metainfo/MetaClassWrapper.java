package ru.naumen.core.server.script.api.metainfo;

import java.util.Collection;
import java.util.List;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.MetaClassLite.Status;

public class MetaClassWrapper implements IMetaClassWrapper
{
    private static final Function<MetaClass, List<ITagWrapper>> TAGS_EXTRACTOR = new Function<MetaClass,
            List<ITagWrapper>>()
    {
        private ApiUtils apiUtils;

        @Override
        public List<ITagWrapper> apply(MetaClass input)
        {
            ensureInitialized();
            return apiUtils.getElementTags(input);
        }

        private void ensureInitialized()
        {
            if (null == apiUtils)
            {
                apiUtils = SpringContext.getInstance().getBean(ApiUtils.class);
            }
        }
    };

    public static final Function<MetaClass, MetaClassWrapper> WRAPPER =
            input -> null == input ? null : new MetaClassWrapper(input);

    static final Function<ClassFqn, IMetaClassWrapper> FQN_WRAPPER = new Function<ClassFqn, IMetaClassWrapper>()
    {
        private MetainfoService metainfoService;

        @Override
        public IMetaClassWrapper apply(ClassFqn input)
        {
            ensureInitialized();
            return null == input ? null : MetaClassWrapper.WRAPPER.apply(metainfoService.getMetaClass(input));
        }

        private void ensureInitialized()
        {
            if (null == metainfoService)
            {
                metainfoService = SpringContext.getInstance().getBean(MetainfoService.class);
            }
        }
    };

    private final MetaClass metaClass;

    public MetaClassWrapper(MetaClass metaClass)
    {
        this.metaClass = metaClass;
    }

    @Override
    public IAttributeWrapper getAttribute(String code)
    {
        return AttributeWrapper.WRAPPER.apply(metaClass.getAttribute(code));
    }

    @Override
    public Collection<String> getAttributeCodes()
    {
        return metaClass.getAttributeCodes();
    }

    @Override
    public IAttributeGroupWrapper getAttributeGroup(String code)
    {
        return AttributeGroupWrapper.WRAPPER.apply(metaClass.getAttributeGroup(code));
    }

    @Override
    public Collection<String> getAttributeGroupCodes()
    {
        return metaClass.getAttributeGroupCodes();
    }

    @Override
    public Collection<IAttributeGroupWrapper> getAttributeGroups()
    {
        return Collections2.transform(metaClass.getAttributeGroups(), AttributeGroupWrapper.WRAPPER);
    }

    @Override
    public Collection<IAttributeWrapper> getAttributes()
    {
        return Collections2.transform(metaClass.getAttributes(), AttributeWrapper.WRAPPER);
    }

    @Override
    public Collection<IMetaClassWrapper> getChildren()
    {
        return Collections2.transform(metaClass.getChildren(), FQN_WRAPPER);
    }

    @Override
    public String getCode()
    {
        return metaClass.getCode();
    }

    @Override
    public String getDescription()
    {
        return metaClass.getDescription();
    }

    @Override
    public ClassFqn getFqn()
    {
        return metaClass.getFqn();
    }

    @Override
    public String getFqnCase()
    {
        return metaClass.getFqn().getCase();
    }

    @Override
    public Collection<IRelationWrapper> getIncomingRelations()
    {
        return Collections2.transform(metaClass.getIncomingRelations(), RelationWrapper.WRAPPER);
    }

    @Override
    public Collection<IRelationWrapper> getOutgoingRelation()
    {
        return Collections2.transform(metaClass.getOutgoingRelation(), RelationWrapper.WRAPPER);
    }

    @Override
    public IMetaClassWrapper getParent()
    {
        return FQN_WRAPPER.apply(metaClass.getParentVisible());
    }

    @Override
    public Object getProperty(String code)
    {
        if (metaClass.getProperties() != null)
        {
            return metaClass.getProperties().getProperty(code);
        }
        else
        {
            return null;
        }
    }

    @Override
    public List<ITagWrapper> getTags()
    {
        return TAGS_EXTRACTOR.apply(metaClass);
    }

    @Override
    public String getTitle()
    {
        return metaClass.getTitle();
    }

    @Override
    public WorkflowWrapper getWorkflow()
    {
        return WorkflowWrapper.WRAPPER.apply(metaClass.getWorkflow());
    }

    @Override
    public boolean hasAttribute(String code)
    {
        return metaClass.hasAttribute(code);
    }

    @Override
    public boolean isAbstract()
    {
        return metaClass.isAbstract();
    }

    @Override
    public boolean isHardcoded()
    {
        return metaClass.isHardcoded();
    }

    @Override
    public boolean isHasCases()
    {
        return metaClass.isHasCases();
    }

    @Override
    public boolean isHasResponsible()
    {
        return metaClass.isHasResponsible();
    }

    @Override
    public boolean isHasWorkflow()
    {
        return metaClass.isHasWorkflow();
    }

    @Override
    public boolean isSingleton()
    {
        return metaClass.isSingleton();
    }

    @Override
    public boolean isRemoved()
    {
        return metaClass.getStatus().equals(Status.REMOVED);
    }

    @Override
    public String toString()
    {
        return this.getFqn().toString();
    }
}
