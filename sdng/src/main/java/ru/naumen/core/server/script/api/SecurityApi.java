package ru.naumen.core.server.script.api;

import static ru.naumen.metainfo.shared.Constants.Group.DASHBOARD_MASTER_GROUP_CODE;
import static ru.naumen.metainfo.shared.Constants.Group.GATEWAY_INTEGRATION_GROUP_CODE;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.bo.GroupMembersDao;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.employee.EmployeeDao;
import ru.naumen.core.server.comment.Comment;
import ru.naumen.core.server.features.FeatureConfiguration;
import ru.naumen.core.server.license.conf.PermissionSetUnlicUsers;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.api.utils.ChangesInPermissionSetUnlicUsers;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.wf.HasState;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.SecurityService;
import ru.naumen.metainfo.server.spi.ui.AddBOPermissionChecker;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.IClassFqn;
import ru.naumen.metainfo.shared.elements.sec.Group;
import ru.naumen.metainfo.shared.elements.sec.ISGroup;
import ru.naumen.metainfo.shared.elements.wf.TransitionLite;
import ru.naumen.password.server.PasswordGenerator;
import ru.naumen.password.server.PasswordValidationService;
import ru.naumen.sec.server.autorize.AuthorizationContext;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.sec.server.autorize.SimpleAuthorizationContext;
import ru.naumen.sec.server.security.AuthStatisticStorage;
import ru.naumen.sec.server.security.SecurityPolicyService;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.superuser.SuperUserDetailsService;

/**
 * Реализация {@link ISecurityApi}
 *
 * <AUTHOR>
 * @since *******
 */
@Component("security")
public class SecurityApi implements ISecurityApi
{
    private static final Logger LOG = LoggerFactory.getLogger(SecurityApi.class);

    private final AuthorizationService authorizationService;
    private final AuthorizationRunnerService authorizeRunner;
    private final IPrefixObjectLoaderService loaderService;
    private final MetainfoService metainfoService;
    private final SecurityService securityService;
    private final GroupMembersDao grpMembersDao;
    private final SecurityPolicyService securityPolicyService;
    private final PasswordGenerator passwordGenerator;
    private final ChangesInPermissionSetUnlicUsers changesInPermissionSetUnlicUsers;
    private final MessageFacade messages;
    private final EmployeeDao<Employee> employeeDao;
    private final AddBOPermissionChecker permissionChecker;
    private final ApiUtils apiUtils;
    private final FeatureConfiguration systemGroupsConfiguration;
    private final PasswordValidationService passwordValidationService;
    private final SuperUserDetailsService superUserDetailsService;
    private final AuthStatisticStorage statisticStorage;
    private final ScriptDtOHelper wrapper;

    @Inject
    public SecurityApi(
            final IPrefixObjectLoaderService loaderService,
            final AuthorizationService authorizationService,
            final AuthorizationRunnerService authorizeRunner,
            final MetainfoService metainfoService,
            final SecurityService securityService,
            final EmployeeDao<Employee> employeeDao,
            final GroupMembersDao grpMembersDao,
            final SecurityPolicyService securityPolicyService,
            final PasswordGenerator passwordGenerator,
            final ChangesInPermissionSetUnlicUsers changesInPermissionSetUnlicUsers,
            final MessageFacade messages,
            final AddBOPermissionChecker permissionChecker,
            final ApiUtils apiUtils,
            final FeatureConfiguration systemGroupsConfiguration,
            final PasswordValidationService passwordValidationService,
            final SuperUserDetailsService superUserDetailsService,
            final AuthStatisticStorage statisticStorage,
            final ScriptDtOHelper wrapper)
    {
        this.loaderService = loaderService;
        this.authorizationService = authorizationService;
        this.authorizeRunner = authorizeRunner;
        this.metainfoService = metainfoService;
        this.securityService = securityService;
        this.employeeDao = employeeDao;
        this.grpMembersDao = grpMembersDao;
        this.securityPolicyService = securityPolicyService;
        this.passwordGenerator = passwordGenerator;
        this.changesInPermissionSetUnlicUsers = changesInPermissionSetUnlicUsers;
        this.messages = messages;
        this.permissionChecker = permissionChecker;
        this.apiUtils = apiUtils;
        this.systemGroupsConfiguration = systemGroupsConfiguration;
        this.passwordValidationService = passwordValidationService;
        this.superUserDetailsService = superUserDetailsService;
        this.statisticStorage = statisticStorage;
        this.wrapper = wrapper;
    }

    @Override
    public void addEmployeeToGroup(String grpCode, @Nullable IUUIDIdentifiable employee)
    {
        if (!isValidGroupCode(grpCode))
        {
            return;
        }
        if (employee == null)
        {
            LOG.error("Illegal employee object, must be not null.");
            return;
        }
        if (!Constants.Employee.FQN.isClassOf(metainfoService.getClassFqn(employee)))
        {
            LOG.error("Illegal employee object. Passed UUID: '{}'.", employee.getUUID());
            return;
        }

        grpMembersDao.saveMembersForGroup(Set.of(grpCode), Set.of(employee.getUUID()));
    }

    @Override
    public void addMemberToGroup(String grpCode, String memberUuid)
    {
        if (isValidGroupCode(grpCode) && isValidMemberUuid(memberUuid))
        {
            grpMembersDao.saveMembersForGroup(Set.of(grpCode), Sets.newHashSet(memberUuid));
        }
    }

    @Override
    public void addMembersToGroup(String grpCode, Collection<String> membersUuids)
    {
        if (isValidGroupCode(grpCode) && isValidMemberUuids(membersUuids))
        {
            grpMembersDao.saveMembersForGroup(Set.of(grpCode), membersUuids);
        }
    }

    @Override
    public void removeMemberFromGroup(String grpCode, String memberUuid)
    {
        if (isValidGroupCode(grpCode) && isValidMemberUuid(memberUuid))
        {
            grpMembersDao.removeMembersFromGroups(Set.of(grpCode), Sets.newHashSet(memberUuid));
        }
    }

    @Override
    public void removeMembersFromGroup(String grpCode, Collection<String> membersUuids)
    {
        if (isValidGroupCode(grpCode) && isValidMemberUuids(membersUuids))
        {
            grpMembersDao.removeMembersFromGroups(Sets.newHashSet(grpCode), membersUuids);
        }
    }

    private static boolean isValidGroupCode(String groupCode)
    {
        if (StringUtilities.isEmptyTrim(groupCode))
        {
            LOG.error("Illegal group code, must be not empty string.");
            return false;
        }
        return true;
    }

    private static boolean isValidMemberUuid(String memberUuid)
    {
        if (StringUtilities.isEmptyTrim(memberUuid))
        {
            LOG.error("Illegal group member, must be not null.");
            return false;
        }
        if (isNotMemberUuid(memberUuid))
        {
            LOG.error("Illegal group member UUID: '{}'.", memberUuid);
            return false;
        }
        return true;
    }

    private static boolean isValidMemberUuids(Collection<String> membersUuids)
    {
        if (CollectionUtils.isEmpty(membersUuids))
        {
            LOG.error("Illegal group member collection, must be not empty.");
            return false;
        }
        List<String> illegalUuids = membersUuids.stream().filter(SecurityApi::isNotMemberUuid).toList();
        if (!illegalUuids.isEmpty())
        {
            String uuidStr = illegalUuids.stream().collect(Collectors.joining("','", "'", "'"));
            LOG.error("Illegal group members UUIDs: {}.", uuidStr);
            return false;
        }
        return true;
    }

    private static boolean isNotMemberUuid(String uuid)
    {
        if (!UuidHelper.isValidSafe(uuid))
        {
            return true;
        }

        String uuidPrefix = UuidHelper.toPrefix(uuid);
        return !Employee.CLASS_ID.equals(uuidPrefix) && !OU.CLASS_ID.equals(uuidPrefix)
               && !Team.CLASS_ID.equals(uuidPrefix);
    }

    @Override
    public void forceGlobalPasswordChange()
    {
        securityPolicyService.forceGlobalPasswordChange();
    }

    @Override
    public void forcePasswordChange(IUUIDIdentifiable employee)
    {
        forcePasswordChange(employee.getUUID());
    }

    @Override
    public void forcePasswordChange(String uuid)
    {
        IUUIDIdentifiable employee = loaderService.get(uuid);
        if (!(employee instanceof Employee))
        {
            throw new IllegalArgumentException("Wrong argument type - " + employee.getClass().getName());
        }
        securityPolicyService.forcePasswordChange((Employee)employee);
    }

    @Override
    public String generatePassword()
    {
        return passwordGenerator.generate();
    }

    @Override
    public Set<String> getAllEmployees(Collection<ISGroup> groups)
    {
        return new HashSet<>(employeeDao.getAllEmployeesByGroups(groups.stream().map(ISGroup::getCode).toList()));
    }

    @Override
    public Set<String> getAllEmployees(ISGroup group)
    {
        return new HashSet<>(employeeDao.getAllEmployeesByGroup(group.getCode()));
    }

    @Override
    public Group getGroup(String code)
    {
        return securityService.getGroup(code);
    }

    @Override
    public Set<Group> getGroups()
    {
        final Set<Group> employeeGroups = new HashSet<>(securityService.getGroups());
        if (!systemGroupsConfiguration.isGatewayIntegrationEnabled())
        {
            employeeGroups.remove(getGroup(GATEWAY_INTEGRATION_GROUP_CODE));
        }
        if (!systemGroupsConfiguration.isDashboardMasterGroupEnabled())
        {
            employeeGroups.remove(getGroup(DASHBOARD_MASTER_GROUP_CODE));
        }
        return employeeGroups;
    }

    @Override
    public Set<String> getGroupEmployees(String groupCode)
    {
        return new HashSet<>(grpMembersDao.getGroupEmployees(groupCode));
    }

    @Override
    public Collection<IClassFqn> hasAddServiceCallPermission(Collection<Object> fqns, Object client, Object employee)
    {
        Preconditions.checkNotNull(client, "Client must be not null");
        Collection<ClassFqn> cases = ApiUtils.extractFqns(fqns);
        IProperties defaultProperties = AddBOPermissionChecker.getDefaultProperties(ApiUtils.getUuid(client));

        return apiUtils.callAsEmployee(employee, () ->
        {
            Set<IClassFqn> casesWithRights = new HashSet<>();
            for (ClassFqn caseFqn : cases)
            {
                if (permissionChecker.checkPermission(caseFqn, defaultProperties))
                {
                    casesWithRights.add(caseFqn);
                }
            }

            return casesWithRights;
        });
    }

    @Override
    public boolean hasEditPermission(IUUIDIdentifiable obj, String attributeCode)
    {
        if (!(obj instanceof IHasMetaInfo))
        {
            throw new FxException(String.format("Object %s has no metainfo", obj));
        }
        final IUUIDIdentifiable object = loaderService.get(obj.getUUID());
        return authorizeRunner.suspendCall(
                () -> authorizationService.hasAttrPermission((IHasMetaInfo)object, attributeCode, true)
                      || authorizationService.hasEditAttrPermissionForProcessingLicense(
                        (IHasMetaInfo)object, attributeCode));
    }

    @Override
    public boolean hasViewAttrPermission(Object object, String attributeCode)
    {
        return hasAttrPermission(object, attributeCode, false, false);
    }

    @Override
    public boolean hasEditAttrPermission(Object object, String attributeCode)
    {
        return hasAttrPermission(object, attributeCode, true, false);
    }

    @Override
    public boolean hasViewCommentAttrPermission(Object object, String attributeCode)
    {
        return hasAttrPermission(object, attributeCode, false, true);
    }

    @Override
    public boolean hasEditCommentAttrPermission(Object object, String attributeCode)
    {
        return hasAttrPermission(object, attributeCode, true, true);
    }

    private boolean hasAttrPermission(Object object, String attributeCode, boolean edit, boolean fromComment)
    {
        Preconditions.checkNotNull(object, "Object must be not null");
        Preconditions.checkNotNull(attributeCode, "Attribute code must be not null");

        AuthorizationContext authContext = getAuthorizationContext(object, true);
        return authorizeRunner.suspendCall(() ->
                fromComment || authContext.getObject() instanceof Comment
                        ? authorizationService.hasCommentAttrPermission(authContext, attributeCode, edit)
                        : authorizationService.hasAttrPermission(authContext, attributeCode, edit));

    }

    @Override
    public boolean hasPermission(Object object, String permission)
    {
        AuthorizationContext authContext = getAuthorizationContext(object, false);

        Preconditions.checkNotNull(permission, "Permission code must be not null");
        return authorizeRunner.suspendCall(() -> authorizationService.hasPermission(authContext, permission));
    }

    private AuthorizationContext getAuthorizationContext(Object object, boolean isAttrCheck)
    {
        return switch (object)
        {
            case String asString -> UuidHelper.isValidSafe(asString)
                    ? getAuthorizationContext(loaderService.get(asString), isAttrCheck)
                    : new SimpleAuthorizationContext(null, ClassFqn.parse(asString));
            case IUUIDIdentifiable uuidIdentifiable ->
                    getAuthorizationContext(wrapper.unwrap(uuidIdentifiable), isAttrCheck);
            case IClassFqn classFqn -> new SimpleAuthorizationContext(null, classFqn);
            default -> throw new IllegalArgumentException("Illegal first argument. "
                                                          + "Must be a IUUIDIdentifiable or IClassFqn or String with "
                                                          + "object UUID or String with class FQN");
        };
    }

    private AuthorizationContext getAuthorizationContext(IUUIDIdentifiable object, boolean isAttrCheck)
    {
        if (isAttrCheck && object instanceof Comment comment)
        {
            final IUUIDIdentifiable sourceObject = loaderService.get(comment.getSource());
            final ClassFqn sourceFqn = metainfoService.getClassFqn(sourceObject);
            return new SimpleAuthorizationContext(object, sourceFqn);
        }
        final ClassFqn objectFqn = metainfoService.getClassFqn(object);
        return new SimpleAuthorizationContext(object, objectFqn);
    }

    @Override
    public boolean hasChangeStatePermission(@Nullable Object object, @Nullable Object transitionObject)
    {
        Preconditions.checkNotNull(object, "Object must be not null");
        Preconditions.checkNotNull(transitionObject, "Transition must be not null");

        final String objectUuid = ApiUtils.getUuid(object);
        final IUUIDIdentifiable loadedObject = loaderService.get(objectUuid);
        Preconditions.checkArgument(loadedObject instanceof HasState, "Metaclass of object must contain a workflow");

        ClassFqn fqn = metainfoService.getClassFqn(loadedObject);
        AuthorizationContext authContext = new SimpleAuthorizationContext(loadedObject, fqn);
        final TransitionLite transition = ApiUtils.getTransition(transitionObject, (HasState)loadedObject);

        return authorizeRunner.suspendCall(
                () -> authorizationService.hasChangeStatePermission(authContext, transition));
    }

    @Override
    public boolean hasProfile(String fqn, final String profile)
    {
        return hasProfile(ClassFqn.parse(fqn), profile);
    }

    @Override
    public boolean hasProfile(final IClassFqn fqn, final String profile)
    {
        return authorizeRunner.suspendCall(() -> authorizationService.hasProfile(fqn, profile));
    }

    @Override
    public boolean hasProfile(IUUIDIdentifiable obj, final String profile)
    {
        final IUUIDIdentifiable object = loaderService.get(obj.getUUID());
        return authorizeRunner.suspendCall(() -> authorizationService.hasProfile(object, profile));
    }

    @Override
    public boolean hasRole(IUUIDIdentifiable obj, final String role)
    {
        final IUUIDIdentifiable object = loaderService.get(obj.getUUID());
        return authorizeRunner.suspendCall(() -> authorizationService.hasRole(object, role));
    }

    @Override
    public String showNewPermissionsForUnlicensedUser(String... permissionSets)
    {
        List<String> notAllowedValues = Arrays.stream(permissionSets)
                .filter(value -> !PermissionSetUnlicUsers.isAllowedValue(value))
                .toList();
        if (!notAllowedValues.isEmpty())
        {
            return messages.getMessage("security.errorWrongShowNewPermissionsParameters")
                   + ' ' + notAllowedValues.stream().collect(Collectors.joining("', '", "'", "'"));
        }
        return switch (permissionSets.length)
        {
            case 0 -> changesInPermissionSetUnlicUsers.asHTML(
                    PermissionSetUnlicUsers.SYSTEM, PermissionSetUnlicUsers.FULL);
            case 1 -> changesInPermissionSetUnlicUsers.asHTML(
                    null, PermissionSetUnlicUsers.fromParameterValue(permissionSets[0]));
            default -> changesInPermissionSetUnlicUsers.asHTML(
                    PermissionSetUnlicUsers.fromParameterValue(permissionSets[0]),
                    PermissionSetUnlicUsers.fromParameterValue(permissionSets[1]));
        };
    }

    @Override
    public boolean passwordShouldBeChanged(IUUIDIdentifiable employee)
    {
        return passwordShouldBeChanged(employee.getUUID());
    }

    @Override
    public boolean passwordShouldBeChanged(String uuid)
    {
        if (!UuidHelper.isValid(uuid, true))
        {
            throw new IllegalArgumentException("Wrong UUID - " + uuid);
        }

        String prefix = UuidHelper.toPrefix(uuid);
        if (!Employee.CLASS_ID.equals(prefix))
        {
            throw new IllegalArgumentException("Wrong argument type, expected - Employee");
        }

        return passwordValidationService.passwordShouldBeChanged(loaderService.get(uuid));
    }

    /**
     * Проверяет наличие роли у пользователя.
     *
     * @param role роль
     * @return true, если пользователь обладает указанной ролью или false в противном случае
     * @deprecated оставлен для обратной совместимости, использоваться не должен
     * @since *******
     */
    @Deprecated
    // метод API оставлен для обратной совместимости
    @SuppressWarnings({ "java:S1133", "java:S6355" })
    boolean hasRole(final String role)
    {
        return authorizeRunner.suspendCall(() -> authorizationService.hasRole(role));
    }

    @Override
    @SuppressWarnings("java:S5804") // ошибка не приведёт к уязвимости, потому что не является readable
    public void unlockSuperUser(@Nullable String login)
    {
        if (StringUtilities.isEmpty(login))
        {
            throw new FxException("SuperUser login is empty!", true);
        }
        String superUserLogin = CurrentEmployeeContext.getCurrentSuperUserLogin();
        if (StringUtilities.isEmpty(superUserLogin)
            || superUserLogin.equals(login)
            || !superUserDetailsService.getSuperUsersLogins().contains(superUserLogin))
        {
            throw new FxException("Current user is not superUser!", true);
        }
        if (!superUserDetailsService.unlockSuperUser(login))
        {
            throw new FxException("Locked super user '%s' is not found!".formatted(login));
        }
        statisticStorage.dropStatistic(superUserDetailsService.loadUserByUsername(login).getUUID());
    }

    @Override
    public void changeSuperUserPassword(String loginOrUuid, String newPassword)
    {
        superUserDetailsService.changeSuperUserPassword(loginOrUuid, newPassword);
    }

    @Override
    public void changeSuperUserCredentials(String loginOrUuid, String newLogin, String newPassword)
    {
        superUserDetailsService.changeSuperUserCredentials(loginOrUuid, newLogin, newPassword);
    }

    @Override
    public boolean hasAdminProfile(String profileCode)
    {
        return CurrentEmployeeContext.getSuperUserProfiles().contains(profileCode);
    }
}
