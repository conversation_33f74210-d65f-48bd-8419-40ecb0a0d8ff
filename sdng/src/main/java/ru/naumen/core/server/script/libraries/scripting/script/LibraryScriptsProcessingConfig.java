package ru.naumen.core.server.script.libraries.scripting.script;

import java.util.Set;

import org.infinispan.Cache;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.inject.Named;
import ru.naumen.core.server.InfinispanCacheManager;
import ru.naumen.core.server.script.libraries.scripting.StorageService;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Конфигурация бинов для работы с библиотечными скриптами.
 */
@Configuration
public class LibraryScriptsProcessingConfig
{
    private static final String SCRIPT_CODE_TO_SCRIPT_CACHE_NAME = "scriptCodeToScriptCache";
    private static final String LIBRARY_NAME_TO_SCRIPT_CODE_CACHE_NAME = "libraryNameToScriptCodeCache";

    /**
     * @return кэш для хранения соответствия кода скрипта и его содержимого;
     */
    @Bean(SCRIPT_CODE_TO_SCRIPT_CACHE_NAME)
    public Cache<String, Script> scriptCodeToScriptCache(InfinispanCacheManager cacheManager)
    {
        return cacheManager.getManager().getCache(SCRIPT_CODE_TO_SCRIPT_CACHE_NAME);
    }

    /**
     * @return кэш для хранения соответствия названия библиотеки и кодов скриптов, содержащихся в ней;
     */
    @Bean(LIBRARY_NAME_TO_SCRIPT_CODE_CACHE_NAME)
    public Cache<String, Set<String>> libraryNameToScriptCodeCache(InfinispanCacheManager cacheManager)
    {
        return cacheManager.getManager().getCache(LIBRARY_NAME_TO_SCRIPT_CODE_CACHE_NAME);
    }

    /**
     * @param codeToScriptCache кэш для хранения соответствия кода скрипта и его содержимого;
     * @param libraryToScriptCodeCache кэш для хранения соответствия названия библиотеки и кодов скриптов,
     *                                 содержащихся в ней;
     * @param classpathScanProcessor обработчик результата сканирования аннотаций скриптов в библиотеках;
     * @param eventPublisher публикатор событий используется для событий {@link ScriptsSetRefreshedEvent}
     * @return сервис хранения скриптов из библиотек;
     */
    @Bean("libraryScriptStorageService")
    StorageService<Script, Script> libraryScriptStorageService(
            @Named(SCRIPT_CODE_TO_SCRIPT_CACHE_NAME) Cache<String, Script> codeToScriptCache,
            @Named(LIBRARY_NAME_TO_SCRIPT_CODE_CACHE_NAME) Cache<String, Set<String>> libraryToScriptCodeCache,
            ScriptClassPathScanProcessor classpathScanProcessor,
            ApplicationEventPublisher eventPublisher)
    {
        return new StorageService<>(
                codeToScriptCache,
                libraryToScriptCodeCache,
                classpathScanProcessor,
                libraryPerScriptsCodes -> eventPublisher.publishEvent(new ScriptsSetRefreshedEvent(this,
                        libraryPerScriptsCodes)),
                script -> script);
    }
}
