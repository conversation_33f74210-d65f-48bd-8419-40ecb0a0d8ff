package ru.naumen.core.server.script.api;

import static ru.naumen.core.server.jta.TransactionRunner.TransactionType.NEW;
import static ru.naumen.core.shared.Constants.FULL_EXPORT_MODE;
import static ru.naumen.metainfo.server.Constants.METACLASS;
import static ru.naumen.metainfo.server.Constants.SYSTEM_NAME_MAX_SIZE;
import static ru.naumen.metainfo.shared.Constants.MetaClassProperties.DEFAULT_CLIENT_AGREEMENT;
import static ru.naumen.metainfo.shared.Constants.MetaClassProperties.DEFAULT_CLIENT_SERVICE;
import static ru.naumen.metainfo.shared.Constants.MetaClassProperties.DEFAULT_SC_TYPE;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.codehaus.groovy.control.CompilationFailedException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.server.utils.localization.LocalizationService;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.bo.userentity.AbstractUserEntity;
import ru.naumen.core.server.cache.CoreClusterCacheService;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.eventaction.EventActionServiceBean;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.api.metainfo.IAttributeTypeWrapper;
import ru.naumen.core.server.script.api.metainfo.IMetaClassWrapper;
import ru.naumen.core.server.script.api.metainfo.IServiceCallParameters;
import ru.naumen.core.server.script.api.metainfo.MetaClassWrapper;
import ru.naumen.core.server.script.api.metainfo.ServiceCallParameters;
import ru.naumen.core.server.script.crawler.ScriptDependenciesVisitor;
import ru.naumen.core.server.script.crawler.ScriptsCrawler;
import ru.naumen.core.server.script.crawler.ValidationScriptVisitor;
import ru.naumen.core.server.script.modules.compile.TestModuleCompilationService;
import ru.naumen.core.server.script.spi.IScriptDtObject;
import ru.naumen.core.server.script.spi.IScriptUtils;
import ru.naumen.core.server.settings.SettingsStorage;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.Comment;
import ru.naumen.core.shared.Constants.File;
import ru.naumen.core.shared.Constants.HasState;
import ru.naumen.core.shared.EntityReference;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.utils.ILocaleInfo;
import ru.naumen.fts.server.external.search.metainfo.MetainfoSearchService;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.ExtMetainfoDataProvider;
import ru.naumen.metainfo.server.spi.MetaClassHierarchy;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.SystemNameService;
import ru.naumen.metainfo.server.spi.store.SearchSetting;
import ru.naumen.metainfo.server.vcs.VersionControlledMetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.MetaClassProperties;
import ru.naumen.metainfo.shared.IClassFqn;
import ru.naumen.metainfo.shared.dispatch2.AddAttributeGroupAction;
import ru.naumen.metainfo.shared.dispatch2.AddMetaClassAction;
import ru.naumen.metainfo.shared.dispatch2.EditMetaClassAction;
import ru.naumen.metainfo.shared.dispatch2.wf.EditStateSettingsAction;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.wf.FakeStateSetting;
import ru.naumen.metainfo.shared.elements.wf.StateSetting;
import ru.naumen.metainfo.shared.elements.wf.Workflow;
import ru.naumen.metainfo.shared.ui.LocalizedString;
import ru.naumen.sec.server.users.CurrentEmployeeContext;

/**
 * API для доступа к метаинформации
 *
 * <AUTHOR>
 *
 * @since ******* (03.08.2012)
 * @since 03.08.2012
 * @see IMetainfoApi
 */
@Component("metainfo")
public class MetainfoApi implements IMetainfoApi
{
    private static final Logger LOG = LoggerFactory.getLogger(MetainfoApi.class);
    private static final String ACTION_EXECUTION_ERROR = "Error while execute AddMetaClassAction from script ";
    private static final String METAINFO_UPDATED = "Metainfo updated";

    private final MetainfoService metainfoService;
    private final ExtMetainfoDataProvider extMetainfoDataProvider;
    private final IScriptUtils utils;
    private final ApiUtils apiUtils;
    private final IPrefixObjectLoaderService prefixObjectLoaderService;
    private final MetaStorageService metaStorage;
    private final MessageFacade messages;
    private final MetainfoServicePersister persister;
    private final MetainfoServiceBean metainfoServiceBean;
    private final MetainfoSearchService metainfoSearchService;
    private final ModulesService modulesService;
    private final EventActionServiceBean eventActionService;
    private final SecurityServiceBean securityService;
    private final Provider<ValidationScriptVisitor> validationScriptVisitorProvider;
    private final Provider<ScriptDependenciesVisitor> scriptInfosVisitorProvider;
    private final ScriptsCrawler scriptCrawler;
    private final Dispatch dispatch;
    private final LocaleUtils localeUtils;
    private final SettingsStorage settingsStorage;
    private final VersionControlledMetainfoService versionControlledMetainfoService;
    private final TestModuleCompilationService testCompilationService;
    private final TagService tagService;
    private final SystemNameService systemNameService;
    private final List<CoreClusterCacheService> clusterCacheServices;
    private final LocalizationService localizationService;
    private final ScriptService scriptService;

    @Inject
    public MetainfoApi(
            final IScriptUtils utils,
            final MetainfoService metainfoService,
            final ExtMetainfoDataProvider extMetainfoDataProvider,
            final ApiUtils apiUtils,
            final IPrefixObjectLoaderService prefixObjectLoaderService,
            final VersionControlledMetainfoService versionControlledMetainfoService,
            final MetaStorageService metaStorage,
            final MessageFacade messages,
            final Dispatch dispatch,
            final SecurityServiceBean securityService,
            final MetainfoServicePersister persister,
            final SettingsStorage settingsStorage,
            final EventActionServiceBean eventActionService,
            final Provider<ScriptDependenciesVisitor> scriptInfosVisitorProvider,
            final LocaleUtils localeUtils, MetainfoServiceBean metainfoServiceBean,
            final ScriptsCrawler scriptCrawler,
            final ModulesService modulesService,
            final Provider<ValidationScriptVisitor> validationScriptVisitorProvider,
            final TestModuleCompilationService testCompilationService,
            final TagService tagService,
            final SystemNameService systemNameService,
            final @Lazy List<CoreClusterCacheService> clusterCacheServices,
            final LocalizationService localizationService,
            final ScriptService scriptService,
            final MetainfoSearchService metainfoSearchService)
    {
        this.utils = utils;
        this.metainfoService = metainfoService;
        this.extMetainfoDataProvider = extMetainfoDataProvider;
        this.apiUtils = apiUtils;
        this.prefixObjectLoaderService = prefixObjectLoaderService;
        this.versionControlledMetainfoService = versionControlledMetainfoService;
        this.metaStorage = metaStorage;
        this.messages = messages;
        this.dispatch = dispatch;
        this.securityService = securityService;
        this.persister = persister;
        this.settingsStorage = settingsStorage;
        this.eventActionService = eventActionService;
        this.scriptInfosVisitorProvider = scriptInfosVisitorProvider;
        this.localeUtils = localeUtils;
        this.metainfoServiceBean = metainfoServiceBean;
        this.scriptCrawler = scriptCrawler;
        this.modulesService = modulesService;
        this.validationScriptVisitorProvider = validationScriptVisitorProvider;
        this.testCompilationService = testCompilationService;
        this.tagService = tagService;
        this.systemNameService = systemNameService;
        this.clusterCacheServices = clusterCacheServices;
        this.localizationService = localizationService;
        this.scriptService = scriptService;
        this.metainfoSearchService = metainfoSearchService;
    }

    @Override
    public void addAttrGroup(String fqn, String code, String title, List<String> attrCodes)
    {
        //Проверить, что все атрибуты существуют
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        for (String attrCode : attrCodes)
        {
            metaClass.getAttribute(attrCode);
        }
        try
        {
            dispatch.execute(new AddAttributeGroupAction(ClassFqn.parse(fqn), code, title, null, attrCodes));
        }
        catch (DispatchException e)
        {
            LOG.error("Error while execute AddAttributeGroupAction from script ", e);
        }
    }

    @Override
    public void addCase(String code, String parentFqn, String title, String desc)
    {
        try
        {
            ClassFqn parent = ClassFqn.parse(parentFqn);
            ClassFqn fqn = ClassFqn.parse(parent.getId(), code);
            dispatch.execute(new AddMetaClassAction(fqn, parent, title, desc));
        }
        catch (DispatchException e)
        {
            LOG.error(ACTION_EXECUTION_ERROR, e);
        }
    }

    @Override
    public void addCase(String code, String parentFqn, String title, String desc, Map<String, String> metaClassProps)
    {
        try
        {
            Map<String, Object> properties = new HashMap<>();
            for (Entry<String, String> prop : metaClassProps.entrySet())
            {
                String key = prop.getKey();
                if (MetaClassProperties.DEFAULT_CLIENT_AGREEMENT.equals(key)
                    || MetaClassProperties.DEFAULT_CLIENT_SERVICE.equals(key))
                {
                    properties.put(key, apiUtils.getObject(prop.getValue()));
                }
                if (MetaClassProperties.DEFAULT_SC_TYPE.equals(key))
                {
                    properties.put(key, ClassFqn.parse(prop.getValue()));
                }
            }
            ClassFqn parent = ClassFqn.parse(parentFqn);
            ClassFqn fqn = ClassFqn.parse(parent.getId(), code);
            AddMetaClassAction action = new AddMetaClassAction(
                    fqn, parent, null, title, desc, Collections.emptyMap(), new MapProperties(properties));
            dispatch.execute(action);
        }
        catch (DispatchException e)
        {
            LOG.error(ACTION_EXECUTION_ERROR, e);
        }
    }

    @Override
    public void addClass(String code, String title, String desc)
    {
        addClass(code, title, desc, null, false, false);
    }

    @Override
    public void addClass(String code, String title, String desc, @Nullable String parentRelFqn, boolean hasLifecycle,
            boolean hasResponsibility)
    {
        try
        {
            ClassFqn parent = ClassFqn.parse(AbstractUserEntity.CLASS_ID);
            ClassFqn parentRel = parentRelFqn == null ? null : ClassFqn.parse(parentRelFqn);
            AddMetaClassAction action = new AddMetaClassAction(ClassFqn.parse(code), parent, parentRel, title, desc,
                    Collections.emptyMap(), hasLifecycle, hasResponsibility, IProperties.EMPTY);
            dispatch.execute(action);
        }
        catch (DispatchException e)
        {
            LOG.error(ACTION_EXECUTION_ERROR, e);
        }
    }

    @Override
    public String analyzeScripts()
    {
        LOG.debug("Scripts info extraction has been started.");
        ScriptDependenciesVisitor visitor = scriptInfosVisitorProvider.get();
        scriptCrawler.visit(visitor);
        LOG.debug("Scripts info extraction has come to its end.");
        return visitor.getInfos().toString();
    }

    @Override
    public boolean areScriptsValid()
    {
        //Advimport
        //Атрибуты
        //Вычислимые роли
        //Действия по событиям
        //Планировщик задач
        //Условия запуска таймера
        //Скрипт обработки почты
        //Отчеты

        ValidationScriptVisitor visitor = validationScriptVisitorProvider.get();
        scriptCrawler.visit(visitor);

        return visitor.isValid();
    }

    @Override
    public String checkAttributeExisting(Object object, String attributeCode)
    {
        ClassFqn fqn = ApiUtils.extractFqn(object);
        MetaClass metaClass = getMetaClassInt(fqn);
        return apiUtils.checkAttrExisting(apiUtils.requireMetaclassExists(metaClass, fqn), attributeCode);
    }

    @Override
    public String checkAttrsExisting(Object object, List<String> attributeCodes)
    {
        ClassFqn fqn = ApiUtils.extractFqn(object);
        MetaClass metaClass = getMetaClassInt(fqn);
        String result = "";
        for (String attributeCode : attributeCodes)
        {
            String error = apiUtils.checkAttrExisting(apiUtils.requireMetaclassExists(metaClass, fqn), attributeCode);
            result = StringUtilities.join(List.of(result, error));
        }
        return result;
    }

    @Override
    public String checkAttributeType(Object object, String attributeCode, List<String> possibleTypes)
    {
        ClassFqn fqn = ApiUtils.extractFqn(object);
        MetaClass metaClass = getMetaClassInt(fqn);
        return apiUtils.checkAttributeType(apiUtils.requireMetaclassExists(metaClass, fqn), attributeCode,
                possibleTypes);
    }

    @Override
    public String checkAttrsType(Object object, Map<String, List<String>> possibleTypes)
    {
        ClassFqn fqn = ApiUtils.extractFqn(object);
        MetaClass metaClass = getMetaClassInt(fqn);
        String result = "";
        for (Entry<String, List<String>> types : possibleTypes.entrySet())
        {
            String error = apiUtils.checkAttributeType(
                    apiUtils.requireMetaclassExists(metaClass, fqn), types.getKey(), types.getValue());
            result = StringUtilities.join(Lists.newArrayList(result, error));
        }
        return result;
    }

    @Override
    public boolean checkTagEnabled(String tagCode)
    {
        return tagService.isAnyTagEnabled(Lists.newArrayList(tagCode));
    }

    @Deprecated
    @SuppressWarnings({ "java:S1123", "java:S1133", "java:S6355" }) // метод API оставлен для обратной совместимости
    public boolean checkRestrictions(IScriptDtObject object, boolean skipRequired, boolean skipUnique)
    {
        return utils.checkRestrictions(object, skipRequired, skipUnique);
    }

    /**
     * Метод сравнивает типы объектов, определённых для заданных типов атрибутов.
     * Имеет смысл для типов "элемент справочника", "набор элементов справочника",
     * "ссылка на БО", "набор ссылок на БО", "обратная ссылка на БО".
     *
     * @param attributeType1 тип(AttributeType) одного атрибута.
     * @param attributeType2 тип(AttributeType) второго атрибута.
     *
     * @return true, если типы объектов, определённых для заданных типов атрибутов совпадают или
     * false в остальных случаях.
     *
     * @since *******
     */
    @Override
    public boolean compareTargetClassForObjectAttrTypes(IAttributeTypeWrapper attributeType1,
            IAttributeTypeWrapper attributeType2)
    {
        IClassFqn fqn1 = attributeType1.getRelatedMetaClass();
        return fqn1 != null && fqn1.isSameClass(attributeType2.getRelatedMetaClass());
    }

    @Override
    public boolean compileAll() throws CompilationFailedException
    {
        return testCompilationService.testCompileAllModulesWithCompilationModeALL();
    }

    @Override
    public String clearCache(String cacheRegion)
    {
        CoreClusterCacheService service = clusterCacheServices.stream()
                .filter(cacheService -> cacheService.getMetaRegion() != null
                                        && cacheService.getMetaRegion().equalsIgnoreCase(cacheRegion))
                .findFirst().orElse(null);
        // по условиям задачи NSDPRD-27848 пока разрешено инвалидировать только один регион кэша
        if (service == null || !service.getMetaRegion().equals(ru.naumen.metainfo.server.Constants.JMS_QUEUE))
        {
            throw new FxException(messages.getMessage("clusterCacheRegion.type.error"), true);
        }
        try
        {
            service.reloadCache();
        }
        catch (Exception ex)
        {
            throw new FxException(messages.getMessage("clusterCacheRegion.error"), true, ex);
        }
        return messages.getMessage("clusterCacheRegion.success");
    }

    @Override
    @SuppressWarnings("deprecation") // на уровне API метод не считается устаревшим
    public void recompileAllModules()
    {
        if (!CurrentEmployeeContext.isCurrentUserAdmin())
        {
            throw new FxException(messages.getMessage("AuthorizationService.accessDenied"), true);
        }
        scriptService.forceRecompileAllModules();
    }

    @Override
    @SuppressWarnings("java:S1192") // нет смысла в строковых литералах для ошибок
    public void copyToRepository(@Nullable String uri, @Nullable String username, @Nullable String password,
            @Nullable String branch, @Nullable String commitHash)
    {
        Preconditions.checkArgument(StringUtils.isNotEmpty(uri), "URI must be specified");
        Preconditions.checkArgument(StringUtils.isNotEmpty(username), "Username must be specified");
        Preconditions.checkArgument(StringUtils.isNotEmpty(password), "Password must be specified");
        Preconditions.checkArgument(StringUtils.isNotEmpty(branch), "Branch must be specified");
        Preconditions.checkArgument(StringUtils.isNotEmpty(commitHash), "Commit hash must be specified");
        versionControlledMetainfoService.copyToRepository(uri, username, password, branch, commitHash);
    }

    @Override
    public void editStateSetting(String fqn, String stateCode, String attrCode, boolean canView, boolean canEdit,
            int preFill, int postFill)
    {
        MetaClass metaClass = metainfoService.getMetaClass(fqn);
        boolean isComment = Constants.Workflow.ACTION_ADD_COMMENT.equals(attrCode);

        String title = isComment
                ? messages.getMessage("ou.window.Comments")
                : Objects.requireNonNull(metaClass.getAttribute(attrCode)).getTitle();

        //Проверка, что статус есть
        Workflow workflow = metaClass.getWorkflow();
        ClassFqn classFqn = metaClass.getFqn();
        if (workflow.getState(stateCode) == null)
        {
            throw new FxException(
                    "State '" + stateCode + "' is not exists in metaclass '" + classFqn.asString() + "'.");
        }

        FakeStateSetting fs = new FakeStateSetting(attrCode, title, classFqn, stateCode)
                .setInherited(workflow.isInherit())
                .setEditable(isComment || Objects.requireNonNull(metaClass.getAttribute(attrCode)).isEditable());
        fs.setCanView(canView);

        if (fs.isEditable())
        {
            fs.setCanEdit(canEdit);
            fs.setPreFill(preFill);
            fs.setPostFill(postFill);
        }

        Map<String, Collection<StateSetting>> map = new HashMap<>(1);
        map.put(stateCode, Lists.newArrayList(fs));

        try
        {
            dispatch.execute(new EditStateSettingsAction(classFqn, map));
        }
        catch (DispatchException e)
        {
            throw new FxException("Error while EditStateSettingsAction from script ", e);
        }
    }

    @Override
    public void exportToRemote(@CheckForNull String branch, @Nullable String commitMessage, @Nullable String exportMode)
    {
        Preconditions.checkArgument(StringUtils.isNotEmpty(branch), "Branch must be specified");
        if (StringUtils.isEmpty(commitMessage))
        {
            commitMessage = METAINFO_UPDATED;
        }
        if (StringUtils.isEmpty(exportMode))
        {
            exportMode = FULL_EXPORT_MODE;
        }
        versionControlledMetainfoService.exportToVCS(branch, commitMessage, exportMode);
    }

    @Override
    public void exportToRemote(@CheckForNull String branch, @Nullable String commitMessage)
    {
        Preconditions.checkArgument(StringUtils.isNotEmpty(branch), "Branch must be specified");
        if (StringUtils.isEmpty(commitMessage))
        {
            exportToRemote(branch);
        }
        else
        {
            versionControlledMetainfoService.exportToVCS(branch, commitMessage, FULL_EXPORT_MODE);
        }
    }

    @Override
    public void exportToRemote(@CheckForNull String branch)
    {
        Preconditions.checkArgument(StringUtils.isNotEmpty(branch), "Branch must be specified");
        versionControlledMetainfoService.exportToVCS(branch, METAINFO_UPDATED, FULL_EXPORT_MODE);
    }

    @Override
    @SuppressWarnings("java:S3776")
    public String fixEmptyFieldsInSearchSettings()
    {
        TransactionRunner.run(NEW, () ->
        {
            boolean isEmptyField = false;
            Collection<ru.naumen.metainfo.server.spi.store.MetaClass> metaClasses = Collections2.transform(
                    metaStorage.get(METACLASS),
                    MetaClassHierarchy.META_CLASS_EXTRACTOR);

            for (ru.naumen.metainfo.server.spi.store.MetaClass metaClass : metaClasses)
            {
                for (SearchSetting searchSetting : metaClass.getSearchSetting())
                {
                    for (LocalizedString title : searchSetting.getTitle())
                    {
                        if (StringUtilities.isEmptyTrim(title.getLang()))
                        {
                            isEmptyField = true;
                        }
                    }
                    if (isEmptyField)
                    {
                        String code = searchSetting.getCode();
                        String messageCode = searchSetting.getAttrCode();
                        if (File.SEARCH_SETTINGS_CODES_TO_TITLES.get(code) != null)
                        {
                            messageCode = File.SEARCH_SETTINGS_CODES_TO_TITLES.get(code);
                        }
                        else if (Comment.SEARCH_SETTINGS_CODES_TO_TITLES.get(code) != null)
                        {
                            messageCode = Comment.SEARCH_SETTINGS_CODES_TO_TITLES.get(code);
                        }
                        if (!searchSetting.getTitle().isEmpty())
                        {
                            searchSetting.getTitle().clear();
                        }
                        String ruTitle = messages.getMessage(messageCode, Locale.of(ILocaleInfo.DEFAULT_LANG));
                        String enTitle = messages.getMessage(messageCode, Locale.of(ILocaleInfo.ENGLISH));
                        searchSetting.getTitle().add(new LocalizedString(ILocaleInfo.DEFAULT_LANG, ruTitle));
                        searchSetting.getTitle().add(new LocalizedString(ILocaleInfo.ENGLISH, enTitle));
                        isEmptyField = false;
                    }
                }
                persister.persist(metaClass.getFqn(), metaClass);
            }
        });
        securityService.clearCache();
        metainfoServiceBean.clearCache();
        metainfoSearchService.clearCache();
        metainfoServiceBean.init();
        modulesService.init();
        eventActionService.init();
        return messages.getMessage("metainfo.FixEmptyFieldsInSearchSettingsResult");
    }

    @Override
    public IServiceCallParameters getServiceCallParameters()
    {
        SCParameters scParameters = settingsStorage.getSettings().getScParameters();

        return new ServiceCallParameters(scParameters);
    }

    @Override
    public IClassFqn getDefaultServiceCallCase(@Nullable String uuid)
    {
        if (!StringUtilities.isEmpty(uuid))
        {
            IUUIDIdentifiable parentObject = prefixObjectLoaderService.get(uuid);
            MetaClass parentMetaClass = (parentObject != null) ? metainfoService.getMetaClass(parentObject) : null;

            if (parentMetaClass != null && parentMetaClass.getProperties() != null)
            {
                Object defaultScType = parentMetaClass.getProperties().getProperty(MetaClassProperties.DEFAULT_SC_TYPE);
                MetaClass defaultScWrapper = getMetaClassInt(ApiUtils.extractFqn(defaultScType));
                return (defaultScWrapper != null) ? defaultScWrapper.getFqn() : null;
            }
        }
        return null;
    }

    @Override
    public MetaClassWrapper getMetaClass(Object fqn)
    {
        MetaClass metaClass = getMetaClassInt(ApiUtils.extractFqn(fqn));
        return metaClass == null ? null : new MetaClassWrapper(metaClass);
    }

    private MetaClass getMetaClassInt(@Nullable ClassFqn classFqn)
    {
        if (classFqn == null || !metainfoService.isMetaclassExists(classFqn))
        {
            return null;
        }

        MetaClass metaClass = metainfoService.getMetaClass(classFqn);
        return metaClass.isHidden() ? null : metaClass;
    }

    @Nullable
    @Override
    @SuppressWarnings("DataFlowIssue") // NPE не возможно, проверка на null выполняется
    public String getMetaClassTitle(Object fqn)
    {
        ClassFqn classFqn = CommonUtils.requireNonNull(ApiUtils.extractFqn(fqn), "fqn");
        Locale locale = localeUtils.getCurrentUserLocale();
        return apiUtils.getMetaClassTitle(classFqn, locale);
    }

    @Nullable
    @Override
    @SuppressWarnings("DataFlowIssue") // NPE не возможно, проверка на null выполняется
    public String getMetaClassTitle(Object fqn, String locale)
    {
        ClassFqn classFqn = CommonUtils.requireNonNull(ApiUtils.extractFqn(fqn), "fqn");
        if (!ILocaleInfo.AVAILABLE_LOCALES.contains(locale))
        {
            return null;
        }
        return apiUtils.getMetaClassTitle(classFqn, Locale.of(locale));
    }

    @Nullable
    @Override
    public IClassFqn getParentFqn(Object fqn)
    {
        ClassFqn classFqn = ApiUtils.extractFqn(fqn);
        return classFqn != null ? metainfoService.getParentClassFqn(classFqn) : null;
    }

    @Nullable
    @Override
    public String getStateTitle(IScriptDtObject obj)
    {
        String lang = LocaleContextHolder.getLocale().getLanguage();
        if (!ILocaleInfo.AVAILABLE_LOCALES.contains(lang))
        {
            return null;
        }

        String stateCode = Objects.requireNonNull(obj.getProperty(HasState.STATE));
        return getStateTitle(obj, stateCode, lang);
    }

    @Nullable
    @Override
    public String getStateTitle(IScriptDtObject obj, String lang)
    {
        if (!ILocaleInfo.AVAILABLE_LOCALES.contains(lang))
        {
            return null;
        }

        String stateCode = Objects.requireNonNull(obj.getProperty(HasState.STATE));
        return getStateTitle(obj, stateCode, lang);
    }

    @Nullable
    @Override
    public String getStateTitle(Object fqn, String stateCode)
    {
        return getStateTitle(fqn, stateCode, LocaleContextHolder.getLocale().getLanguage());
    }

    @Nullable
    @Override
    @SuppressWarnings("DataFlowIssue") // NPE не возможно, проверка на null выполняется
    public String getStateTitle(Object fqn, String stateCode, String lang)
    {
        ClassFqn classFqn = CommonUtils.requireNonNull(ApiUtils.extractFqn(fqn), "fqn");
        if (!ILocaleInfo.AVAILABLE_LOCALES.contains(lang))
        {
            return null;
        }

        CommonUtils.requireNonNull(stateCode, "stateCode");
        return apiUtils.getStateTitle(classFqn, stateCode, lang);
    }

    @Override
    public String getSystemName()
    {
        return systemNameService.getSystemName();
    }

    @Override
    @SuppressWarnings({ "java:S6204", "DataFlowIssue" }) // NPE не возможно, проверка на null выполняется
    public Collection<IMetaClassWrapper> getTypes(IClassFqn fqn)
    {
        ClassFqn classFqn = CommonUtils.requireNonNull(ApiUtils.extractFqn(fqn), "fqn");
        return metainfoService.getTypes(classFqn).stream()
                .map(MetaClassWrapper::new)
                .collect(Collectors.toList());
    }

    @Override
    @SuppressWarnings({ "java:S6204", "DataFlowIssue" }) // NPE не возможно, проверка на null выполняется
    public Collection<MetaClassWrapper> getTypes(String fqn)
    {
        ClassFqn classFqn = CommonUtils.requireNonNull(ApiUtils.extractFqn(fqn), "fqn");
        return metainfoService.getTypes(classFqn).stream()
                .map(MetaClassWrapper::new)
                .collect(Collectors.toList());
    }

    @Override
    public void importFromRemote(@CheckForNull String branch)
    {
        importFromRemote(branch, false);
    }

    @Override
    public void importFromRemote(@CheckForNull String branch, boolean isFullReload)
    {
        Preconditions.checkArgument(StringUtils.isNotEmpty(branch), "Branch must be specified");
        versionControlledMetainfoService.importFromVCS(branch, isFullReload);
    }

    @Override
    public MetaClassWrapper metaClass(Object fqn)
    {
        return getMetaClass(fqn);
    }

    @Override
    public boolean metaClassExists(Object fqn)
    {
        ClassFqn realFqn = ApiUtils.extractFqn(fqn);
        return realFqn != null && metainfoService.isMetaclassExists(realFqn)
               && !metainfoService.getMetaClass(realFqn).isHidden();
    }

    @Override
    public void resetExternalTitleTables()
    {
        extMetainfoDataProvider.recreateStatesAndTitles(true);
    }

    @Override
    public void resetFullExternalTitleTables()
    {
        extMetainfoDataProvider.recreateStatesAndTitles(false);
    }

    @Override
    public void setSystemName(String systemName)
    {
        if (StringUtils.isBlank(systemName))
        {
            systemName = messages.getMessage("DefaultMetaObjectToDtObjectMapper.superuser");
        }
        if (systemName.length() > SYSTEM_NAME_MAX_SIZE)
        {
            systemName = StringUtils.truncate(systemName, SYSTEM_NAME_MAX_SIZE);
        }
        systemNameService.setSystemName(systemName);
    }

    @Override
    public void useNotificationLocalized(boolean value)
    {
        localizationService.useNotificationLocalized(value);
    }

    @Override
    public void setDefaultServiceCallParams(Object targetFqn, Object scFqn, Object agreement, Object service)
    {
        MetaClass metaClass = metainfoService.getMetaClass(targetFqn);

        MapProperties properties = new MapProperties();
        IUUIDIdentifiable agreementObject = apiUtils.getObject(agreement);
        EntityReference agreementProperty = agreementObject != null ? new EntityReference(agreementObject) : null;
        IUUIDIdentifiable serviceObject = apiUtils.getObject(service);
        EntityReference serviceProperty = serviceObject != null ? new EntityReference(serviceObject) : null;

        properties.put(DEFAULT_CLIENT_AGREEMENT, agreementProperty);
        properties.put(DEFAULT_CLIENT_SERVICE, serviceProperty);
        properties.put(DEFAULT_SC_TYPE, ApiUtils.extractFqn(scFqn));

        EditMetaClassAction editAction = new EditMetaClassAction(
                metaClass.getFqn(), metaClass.getTitle(), metaClass.getDescription(), Map.of(), properties);

        try
        {
            dispatch.execute(editAction);
        }
        catch (DispatchException ex)
        {
            throw new FxException("An error occurred when setting serviceCall default params: " + ex.getMessage(), ex);
        }
    }
}
