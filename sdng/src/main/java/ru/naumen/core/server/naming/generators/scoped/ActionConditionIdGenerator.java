package ru.naumen.core.server.naming.generators.scoped;

import java.util.List;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.eventaction.EventActionConfig;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.eventaction.EventAction;

/**
 * Генератор идентификаторов условий выполнения действий по событиям.
 * Идентификатор уникален в пределах действия по событию. 
 * <AUTHOR>
 * @since Feb 20, 2017
 */
@Component(ActionConditionIdGenerator.BEAN_NAME)
public class ActionConditionIdGenerator implements ScopedIdGenerator<EventAction>
{
    public static final String BEAN_NAME = "eventActionConditionIdGenerator";

    @Override
    public long generateId(EventAction scope)
    {
        List<ActionCondition> conditions = scope.getConditions();
        return conditions.stream().filter(c -> null != c.getCode()).mapToLong(c -> UuidHelper.toId(c.getCode())).max()
                       .orElse(0L) + 1L;
    }

    @Override
    public String generatePrefixedId(EventAction scope)
    {
        return UuidHelper.toUuid(generateId(scope), EventActionConfig.ACTION_CONDITION);
    }
}
