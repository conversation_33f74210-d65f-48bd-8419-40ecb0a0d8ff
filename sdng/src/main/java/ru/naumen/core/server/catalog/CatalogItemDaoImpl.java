package ru.naumen.core.server.catalog;

import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_CODE;
import static ru.naumen.core.shared.Constants.CatalogItem.ITEM_TITLE;

import java.util.Collection;
import java.util.List;

import org.hibernate.NonUniqueResultException;
import org.hibernate.query.Query;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Propagation;
import org.springframework.transaction.annotation.Transactional;

import jakarta.inject.Inject;
import ru.naumen.core.server.bo.AbstractDao;
import ru.naumen.core.server.bo.DefaultDao;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.criteria.Order;
import ru.naumen.core.shared.filters.IObjectFilter;

/**
 *
 * <AUTHOR>
 * @since 17.01.2011
 */
@Component
@Scope(value = BeanDefinition.SCOPE_PROTOTYPE)
@Transactional(propagation = Propagation.REQUIRED)
public class CatalogItemDaoImpl<T extends CatalogItem<T>> extends DefaultDao<T> implements CatalogItemDao<T>
{
    public static final String CACHE_REGION_CODE = "catalogItem.code";
    public static final String CACHE_REGION_FILTER_ID = "catalogItem.filterId";

    @Inject
    private TitleTransformer titleTransformer;

    public CatalogItemDaoImpl()
    {
        this(CatalogItem.CLASS_ID);
    }

    public CatalogItemDaoImpl(String classId)
    {
        super(classId);
    }

    @Override
    public boolean existItem(String code)
    {
        HCriteria criteria = createCriteria();
        return exist(criteria.add(HRestrictions.eq(criteria.getProperty(ITEM_CODE), code)));
    }

    @Override
    public T getItem(String code) throws NonUniqueResultException
    {
        HCriteria criteria = createCriteria();

        criteria.add(HRestrictions.eq(criteria.getProperty(ITEM_CODE), code));

        Query query = createQuery(criteria);
        query.setCacheable(true);
        query.setCacheRegion(CACHE_REGION_CODE);
        return (T)AbstractDao.unique(query.list());
    }

    @Override
    public List<T> listByCodes(Collection<String> codes)
    {
        HCriteria criteria = createCriteria();

        criteria.add(HRestrictions.in(criteria.getProperty(ITEM_CODE), codes));
        return createQuery(criteria).list();
    }

    @Override
    public List<String> listCodes()
    {
        HCriteria criteria = HHelper.create().addSource(getEntityName());
        criteria.addColumn(criteria.getProperty(ITEM_CODE));
        Query query = createQuery(criteria);
        query.setCacheable(true);
        query.setCacheRegion(CACHE_REGION_CODE);
        return query.list();
    }

    @Override
    public List<T> listItems(boolean removed)
    {
        HCriteria criteria = createCriteria();
        criteria.add(HRestrictions.eq(criteria.getProperty(Constants.AbstractBO.REMOVED), removed));
        Query query = createQuery(criteria);

        query.setCacheable(true);
        query.setCacheRegion(CACHE_REGION_CODE);
        return query.list();
    }

    @Override
    public List<T> listItems()
    {
        HCriteria criteria = createCriteria();
        Query<T> query = createQuery(criteria);

        query.setCacheable(true);
        query.setCacheRegion(CACHE_REGION_CODE);
        return query.list();
    }

    @Override
    public List<String> listTitles()
    {
        HCriteria criteria = HHelper.create().addSource(getEntityName());
        criteria.addColumn(criteria.getProperty(ITEM_TITLE));
        criteria.add(HRestrictions.eq(criteria.getProperty(Constants.AbstractBO.REMOVED), false));
        Query query = createQuery(criteria);
        query.setCacheable(true);
        query.setCacheRegion(CACHE_REGION_CODE);
        return query.list().stream().map(titleTransformer).toList();
    }

    @Override
    protected Query getListIdsQuery(Iterable<IObjectFilter> filters, Collection<Order> orders, int firstResult,
            Integer maxResults, Long lastId)
    {
        return super.getListIdsQuery(filters, orders, firstResult, maxResults, null)
                .setCacheable(true)
                .setCacheRegion(CACHE_REGION_FILTER_ID);
    }
}
