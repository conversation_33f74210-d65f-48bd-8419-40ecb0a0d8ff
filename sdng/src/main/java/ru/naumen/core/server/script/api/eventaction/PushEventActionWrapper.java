package ru.naumen.core.server.script.api.eventaction;

import java.util.List;
import java.util.stream.Collectors;

import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.Constants.OU;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.utils.ClassFqnHelper;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventActionWithRecipients;

/**
 * Обертка {@link EventAction} уведомление для использования в скриптах
 * <AUTHOR>
 * @since 12.03.2021
 */
public abstract class PushEventActionWrapper extends EventActionWrapper implements IPushEvenActionWrapper
{
    protected final EventActionWithRecipients action;
    private final ScriptStorageService scriptStorageService;

    PushEventActionWrapper(EventAction eventAction)
    {
        super(eventAction);
        action = (EventActionWithRecipients)eventAction.getAction();
        scriptStorageService = SpringContext.getInstance().getBean(ScriptStorageService.class);
    }

    @Override
    public List<String> getRoles()
    {
        return action.getRoles();
    }

    @Override
    public List<String> getEmployeeUuids()
    {
        return action.getRecipients().stream()
                .filter(uuid -> Employee.CLASS_ID.equals(ClassFqnHelper.toClassId(uuid).getId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getOuUuids()
    {
        return action.getRecipients().stream()
                .filter(uuid -> OU.CLASS_ID.equals(ClassFqnHelper.toClassId(uuid).getId()))
                .collect(Collectors.toList());
    }

    @Override
    public List<String> getTeamUuids()
    {
        return action.getRecipients().stream()
                .filter(uuid -> Team.CLASS_ID.equals(ClassFqnHelper.toClassId(uuid).getId()))
                .collect(Collectors.toList());
    }

    @Override
    public String getMessage()
    {
        return metainfoUtils.getLocalizedValue(action.getMessage());
    }

    @Override
    public String getScriptName()
    {
        return StringUtilities.isEmpty(action.getScript()) ? "" :
                metainfoUtils.getLocalizedValue(scriptStorageService.getScript(action.getScript()).getTitle());
    }

    @Override
    public String getScriptText()
    {
        return StringUtilities.isEmpty(action.getScript()) ? "" :
                scriptStorageService.getScript(action.getScript()).getBody();
    }
}