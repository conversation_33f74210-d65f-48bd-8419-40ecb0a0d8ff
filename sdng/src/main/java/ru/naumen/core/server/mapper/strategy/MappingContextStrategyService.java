package ru.naumen.core.server.mapper.strategy;

import java.util.Map;

import jakarta.inject.Inject;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import ru.naumen.core.shared.Constants;

import java.util.HashMap;

/**
 * Сервис стратегий для преобразования значений атрибутов перед отправкой на сторону клиента
 * {@link MappingContextStrategy}
 *
 * На стороне клиента для списков также можно воспользоваться ExtendedColumnValueFormatter,
 * к примеру, для доводки верстки в ячейках для определенных атрибутов, или в завис<PERSON><PERSON><PERSON><PERSON>ти от браузера.
 *
 * <AUTHOR>
 */
@Configuration
public class MappingContextStrategyService
{
    public static final String MAPPING_STRATEGIES = "mappingContextStrategiesInitializers";

    @Inject
    private MappingContextCutTextStrategy mappingContextCutTextStrategy;
    @Inject
    private MappingContextEmulateReferencesStrategy mappingContextEmulateReferencesStrategy;
    @Inject
    private MappingContextCutImagesStrategy mappingContextCutImagesStrategy;

    @Bean(name = MAPPING_STRATEGIES)
    public Map<String, MappingContextStrategy> getStrategies()
    {
        Map<String, MappingContextStrategy> map = new HashMap<>();
        map.put(Constants.MappingContextStrategy.CUT_TEXT_STRATEGY, mappingContextCutTextStrategy);
        map.put(Constants.MappingContextStrategy.EMULATE_REFERENCES_STRATEGY, mappingContextEmulateReferencesStrategy);
        map.put(Constants.MappingContextStrategy.CUT_IMAGES_STRATEGY, mappingContextCutImagesStrategy);
        return map;
    }
}
