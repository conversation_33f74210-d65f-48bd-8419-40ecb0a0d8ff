package ru.naumen.core.server.advlist.filtersettings;

import static com.google.common.base.Predicates.and;
import static com.google.common.base.Predicates.not;
import static com.google.common.base.Predicates.or;
import static ru.naumen.metainfo.shared.Constants.FILTER_SETTINGS_TYPES;
import static ru.naumen.metainfo.shared.filters.AttributeFilters.computable;
import static ru.naumen.metainfo.shared.filters.AttributeFilters.determinable;
import static ru.naumen.metainfo.shared.filters.AttributeFilters.inTypeCodes;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Objects;
import java.util.Queue;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import ru.naumen.sec.server.admin.log.ListTemplatesLogService;
import ru.naumen.sec.server.admin.log.MetaClassLogService;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.sec.server.admin.log.ScriptLogService;
import ru.naumen.core.server.advlist.templates.ListTemplateService;
import ru.naumen.core.server.script.storage.modification.usage.FilterRestrictionScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.server.utils.MetainfoUtilities;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;
import ru.naumen.metainfo.shared.templates.list.ListTemplate;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.HasFilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.ObjectListBase;
import ru.naumen.metainfo.shared.ui.ObjectListBase.PresentationType;

/**
 * Сервис работы с настройками ограничения фильтрации для атрибутов.
 *
 * <AUTHOR>
 * @since 10.07.2017
 */
@Component
public class FilterRestrictionSettingsServiceImpl implements FilterRestrictionSettingsService
{
    private final MetainfoServiceBean metainfoService;
    private final MetainfoUtilities metainfoUtilities;
    private final ScriptModifyRegistry scriptModifyRegistry;
    private final ScriptLogService scriptLogService;
    private final MetaClassLogService log;
    private final ListTemplatesLogService logService;
    private final ListTemplateService templateService;

    public static final String INDEX_OF_DELIMITER = "@#DelimiterForFilterRestrictionScriptUsagePoint#@";
    public static final String INDEX_OF_DELIMITER_FOR_TEMPLATE =
            "@#DelimiterForFilterRestrictionForTemplateScriptUsagePoint#@";

    @Inject
    public FilterRestrictionSettingsServiceImpl(MetainfoServiceBean metainfoService,
            MetainfoUtilities metainfoUtilities,
            ScriptModifyRegistry scriptModifyRegistry, ScriptLogService scriptLogService,
            MetaClassLogService log, ListTemplatesLogService logService,
            ListTemplateService templateService)
    {
        this.metainfoService = metainfoService;
        this.metainfoUtilities = metainfoUtilities;
        this.scriptModifyRegistry = scriptModifyRegistry;
        this.scriptLogService = scriptLogService;
        this.log = log;
        this.logService = logService;
        this.templateService = templateService;
    }

    @Override
    public void removeAllScriptUsagePoints(HasFilterRestrictionStrategy contentOwner, ClassFqn fqn,
            @Nullable String templateCode, @Nullable String formCode)
    {
        checkAllScriptUsagePoints(contentOwner, fqn, templateCode, true, formCode);
    }

    private void checkAllScriptUsagePoints(HasFilterRestrictionStrategy contentOwner, ClassFqn fqn,
            @Nullable String templateCode, boolean isRemove, @Nullable String formCode)
    {
        List<ScriptAdminLogInfo> scriptsLog = new ArrayList<>();

        for (Entry<AttributeFqn, FilterRestrictionStrategy> entry : contentOwner.getFilterRestrictionSettings()
                .entrySet())
        {
            if (FilterRestrictionStrategy.Strategy.SCRIPT.name().equals(entry.getValue().getStrategy()) && isRemove)
            {
                scriptsLog.addAll(
                        removeUsagePoint(entry.getKey(), entry.getValue(), (Content)contentOwner, fqn, templateCode,
                                formCode));
            }
            else if (FilterRestrictionStrategy.Strategy.SCRIPT.name().equals(entry.getValue().getStrategy())
                     && !isRemove)
            {
                scriptsLog.addAll(
                        addUsagePoint(entry.getKey(), contentOwner, fqn, templateCode, formCode));
            }
        }
        scriptLogService.makeLogs(scriptsLog);
    }

    @Override
    public void removeAllScriptUsagePointsInHierarchical(Content content, ClassFqn fqn, String formCode)
    {
        checkAllScriptUsagePointsInHierarchical(content, fqn, null, true, formCode);
    }

    @Override
    public void addAllScriptUsagePointsInHierarchical(Content content, ClassFqn fqn, @Nullable String templateCode,
            @Nullable String formCode)
    {
        checkAllScriptUsagePointsInHierarchical(content, fqn, templateCode, false, formCode);
    }

    private void checkAllScriptUsagePointsInHierarchical(Content content, ClassFqn fqn, @Nullable String templateCode,
            boolean isRemove, @Nullable String formCode)
    {
        Queue<Content> queue = Lists.newLinkedList();
        queue.add(content);

        while (!queue.isEmpty())
        {
            Content cont = queue.poll();
            if (cont instanceof HasFilterRestrictionStrategy)
            {
                checkAllScriptUsagePoints((HasFilterRestrictionStrategy)cont, fqn, templateCode, isRemove, formCode);
            }
            queue.addAll(cont.getChilds());
        }
    }

    @Override
    public List<ScriptAdminLogInfo> removeUsagePoint(AttributeFqn attributeFqn,
            FilterRestrictionStrategy oldFilterRestrictionStrategy, Content contentOwner, ClassFqn fqn,
            @Nullable String templateCode, @Nullable String formCode)
    {
        List<ScriptAdminLogInfo> logInfo = new ArrayList<>();
        ScriptModifyProcess<FilterRestrictionStrategy> process = scriptModifyRegistry
                .getProcess(oldFilterRestrictionStrategy);

        ScriptModifyContext scriptModifyContext = createScriptModifyContext(contentOwner, fqn, attributeFqn,
                templateCode, formCode);

        process.save(oldFilterRestrictionStrategy, oldFilterRestrictionStrategy, ScriptDtoFactory.createWithout(),
                scriptModifyContext);
        logInfo.addAll(scriptModifyContext.getScriptsLogInfo());
        return logInfo;
    }

    private List<ScriptAdminLogInfo> addUsagePoint(AttributeFqn attributeFqn, HasFilterRestrictionStrategy contentOwner,
            ClassFqn fqn, @Nullable String templateCode, @Nullable String formCode)
    {
        List<ScriptAdminLogInfo> logInfo = new ArrayList<>();
        FilterRestrictionStrategy newFilterRestrictionStrategy = contentOwner.getFilterRestrictionSettings()
                .get(attributeFqn);
        ScriptModifyContext scriptModifyContext = createScriptModifyContext((Content)contentOwner, fqn, attributeFqn,
                templateCode, formCode);
        ScriptModifyProcess<FilterRestrictionStrategy> process = scriptModifyRegistry
                .getProcess(newFilterRestrictionStrategy);

        ScriptDto scriptDto = new ScriptDto(newFilterRestrictionStrategy.getScript());
        scriptDto.setSelectStrategy(Constants.ScriptsComponentTree.EXISTING_SCRIPT);
        process.save(null, newFilterRestrictionStrategy, scriptDto, scriptModifyContext);
        logInfo.addAll(scriptModifyContext.getScriptsLogInfo());
        return logInfo;
    }

    @Override
    public List<Attribute> getAttributesForFilterSettings(ObjectListBase content)
    {
        Map<String, Attribute> map = metainfoUtilities.getAttributesForList(content, new ArrayList<>());
        return map.values().stream()
                .filter(and(inTypeCodes(FILTER_SETTINGS_TYPES), not(or(computable(), determinable()))))
                .filter(attr -> !attr.getType().isAttributeOfRelatedObject())
                .sorted(ITitled.COMPARATOR).collect(Collectors.toList());
    }

    @Override
    public void checkFilterRestrictionSettings(ClassFqn fqn, ObjectListBase oldContent, ObjectListBase savedContent,
            @Nullable String templateCode, @Nullable String formCode)
    {
        if (savedContent.getPresentation().equals(PresentationType.DEFAULT.getCode())
            || !Objects.equals(savedContent.getFqnOfClass(), oldContent.getFqnOfClass()))
        {
            removeAllScriptUsagePoints((HasFilterRestrictionStrategy)oldContent, fqn, templateCode, formCode);
            ((HasFilterRestrictionStrategy)savedContent).getFilterRestrictionSettings().clear();
        }
        else if (!savedContent.getAttributeGroup().equals(oldContent.getAttributeGroup())
                 || !savedContent.getFqns().equals(oldContent.getFqns()))
        {
            List<Attribute> newAttrs = getAttributesForFilterSettings(savedContent);
            List<Attribute> oldAttrs = getAttributesForFilterSettings(oldContent);

            oldAttrs.stream()
                    .map(Attribute::getFqn)
                    .filter(attrFqn -> newAttrs.stream().noneMatch(newAttr -> newAttr.getFqn().equals(attrFqn)))
                    .filter(attrFqn -> ((HasFilterRestrictionStrategy)savedContent).getFilterRestrictionSettings()
                            .containsKey(attrFqn))
                    .forEach(attrFqn ->
                    {
                        FilterRestrictionStrategy oldFilterRestrictionStrategy =
                                ((HasFilterRestrictionStrategy)oldContent)
                                        .getFilterRestrictionSettings().get(attrFqn);
                        if (oldFilterRestrictionStrategy.getStrategy()
                                .equals(FilterRestrictionStrategy.Strategy.SCRIPT.name()))
                        {
                            removeUsagePoint(attrFqn, oldFilterRestrictionStrategy, savedContent, fqn,
                                    templateCode, formCode);
                        }
                        ((HasFilterRestrictionStrategy)savedContent).getFilterRestrictionSettings().remove(attrFqn);
                    });
        }
    }

    @Override
    public void cleanAttributeUsageInContent(ClassFqn fqn, Collection<String> removedAttrs, String attrGroup)
    {
        if (removedAttrs.isEmpty())
        {
            return;
        }

        for (String attr : removedAttrs)
        {
            AttributeFqn forDelAttrFqn = Objects.requireNonNull(metainfoService.getMetaClass(fqn).getAttribute(attr))
                    .getFqn();
            Collection<ContentInfo> uis = metainfoService.getUiForms();
            uis.forEach(contentInfo -> cleanFilterSettingsForForm(contentInfo, fqn, forDelAttrFqn, attrGroup));
            List<ListTemplate> templates = templateService.getAll();
            templates.forEach(template -> cleanFilterSettingsForForm(template, fqn, forDelAttrFqn, attrGroup));
        }
    }

    private void cleanFilterSettingsForForm(Object container, ClassFqn fqn, AttributeFqn forDelAttrFqn,
            String attrGroup)
    {
        Content content = null;
        String templateCode = null;
        ClassFqn declaredMetaclass = null;
        ListTemplate originalTemplate = null;
        ListTemplate template = null;
        String formCode = null;
        if (container instanceof ContentInfo)
        {
            content = ((ContentInfo)container).getContent();
            declaredMetaclass = ((ContentInfo)container).getDeclaredMetaclass();
            formCode = ((ContentInfo)container).getFormId();
        }
        else if (container instanceof ListTemplate)
        {
            originalTemplate = ((ListTemplate)container);
            template = originalTemplate.clone();
            content = template.getTemplate();
            templateCode = template.getCode();
            declaredMetaclass = ((ObjectListBase)content).getFqnOfClass();
        }

        Queue<Content> queue = Lists.newLinkedList();
        queue.add(content);
        boolean needToUpdateWindow = false;

        while (!queue.isEmpty())
        {
            Content cont = queue.poll();
            if (needCleanFilterSettings(cont, fqn, attrGroup))
            {
                FilterRestrictionStrategy filterRestrictionStrategy = ((HasFilterRestrictionStrategy)cont)
                        .getFilterRestrictionSettings().get(forDelAttrFqn);
                if (filterRestrictionStrategy != null)
                {
                    ((HasFilterRestrictionStrategy)cont).getFilterRestrictionSettings().remove(forDelAttrFqn);
                    if (filterRestrictionStrategy.getStrategy()
                            .equals(FilterRestrictionStrategy.Strategy.SCRIPT.name()))
                    {
                        removeUsagePoint(forDelAttrFqn, filterRestrictionStrategy, cont,
                                Objects.requireNonNull(declaredMetaclass),
                                templateCode, formCode);
                        ((HasFilterRestrictionStrategy)cont).getFilterRestrictionSettings().remove(forDelAttrFqn);
                    }
                    needToUpdateWindow = true;
                }
            }
            queue.addAll(cont.getChilds());
        }
        if (container instanceof ContentInfo && needToUpdateWindow
            && metainfoService.setUIForm(declaredMetaclass, formCode, content, true, false))
        {
            log.saveUI(metainfoService.getMetaClass(declaredMetaclass), formCode);
        }
        else if (container instanceof ListTemplate && needToUpdateWindow)
        {
            template.setTemplate((ObjectListBase)content);
            templateService.saveTemplate(template);
            logService.templateChanged(template, originalTemplate);
        }
    }

    private static boolean needCleanFilterSettings(Content content, ClassFqn fqn, @Nullable String attrGroup)
    {
        return content instanceof HasFilterRestrictionStrategy && content instanceof ObjectListBase
               && Objects.equals(((ObjectListBase)content).getFqnOfClass(), fqn.fqnOfClass())
               && (attrGroup == null || attrGroup.equals(((ObjectListBase)content).getAttributeGroup()));
    }

    @Override
    public ScriptModifyContext createScriptModifyContext(Content content, ClassFqn fqn, AttributeFqn attrFqn,
            @Nullable String templateCode, @Nullable String formCode)
    {
        ScriptModifyContext context = new ScriptModifyContext(OtherCategories.FILTER_RESTRICTION,
                ScriptHolders.FILTER_RESTRICTION);
        context.put(FilterRestrictionScriptModifyProcess.METACLASS_HOLDER, fqn);
        context.put(FilterRestrictionScriptModifyProcess.KEY, getKey(content, attrFqn, templateCode, formCode));
        return context;
    }

    public static String getKey(Content content, AttributeFqn attrFqn, @Nullable String templateCode,
            @Nullable String formCode)
    {
        if (templateCode != null)
        {
            return attrFqn + INDEX_OF_DELIMITER_FOR_TEMPLATE + templateCode;
        }
        String key = attrFqn + INDEX_OF_DELIMITER + content.getUuid();
        if (formCode != null && !formCode.equals(UI.WINDOW_KEY))
        {
            key += INDEX_OF_DELIMITER + formCode;
        }
        return key;
    }
}
