package ru.naumen.core.server.hquery.criterion;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

public class CollectionEmptyCriterion extends AbstractHCriterion
{
    public CollectionEmptyCriterion(HColumn property)
    {
        super(property);
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        sb.append(property.getHQL(builder)).append(" is empty");
    }

    @Override
    public String toString()
    {
        return "CollectionEmptyCriterion{" +
               "property=" + property +
               '}';
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new CollectionEmptyCriterion(property);
    }
}
