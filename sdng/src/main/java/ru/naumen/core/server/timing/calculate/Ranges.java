package ru.naumen.core.server.timing.calculate;

import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.Iterator;
import java.util.NavigableSet;
import java.util.TreeSet;

import com.google.common.collect.Iterators;
import com.google.common.collect.Sets;

import ru.naumen.common.server.range.LongRange;
import ru.naumen.common.server.range.Range.RELATION;

/**
 * Вспомогательный класс описывающий множество значений {@link Long}, представленных
 * объектами диапазонов {@link LongRange} и предоставляющий методы расчётов,
 * связанные с такой абстракцией:<ul>
 * <li>Расчёт меры множества (суммарной длины диапазонов) {@link #getRangesLength()}</li>
 * <li>Поиск значения принадлежащего множеству, ближайшего к данному {@link #ceiling(Long)}</li>
 * <li>Получение длины диапазонов от начала множества, до заданного значения {@link #getRangesLengthTill(Long)}</li>
 * <li>Получение длины диапазонов от заданного значения до конца множества {@link #getRangesLengthFrom(Long)}</li>
 * <li>Вычисление значения принадлежащего множеству, находящемуся на заданном расстоянии от
 * начала множества {@link #getRangeValueFromBegin(long)}</li>
 * <li>Вычисление значения принадлежащего множеству, находящемуся на заданном расстоянии от
 * заданного значения {@link #getRangeValueFromDistance(long, long)}</li>
 * </ul>
 * <p>Имеет метдоды добавления и удаления диапазонов значений, при этом
 * диапазоны трактуются не как java-объекты, а как множества значений.
 * </p>
 * <AUTHOR>
 */
public class Ranges implements Iterable<LongRange>, Cloneable
{
    private TreeSet<LongRange> ranges = new TreeSet<LongRange>();

    /**
     * Суммарная длина диапазонов, включенных в набор
     */
    private long rangesLength = 0L;

    public Ranges()
    {
    }

    public Ranges(LongRange... ranges)
    {
        this.ranges.addAll(Arrays.asList(ranges));
    }

    /**
     * Метод предназначен для добавления указанного диапазона в набор.
     * Диапазоны с нулевой длиной {@link LongRange#length()} не добавляются.
     * Если указанный диапазон пересекается с уже существующим(ми) в наборе,
     * либо примыкает к уже существующему, то такие диапазоны объединяются в один
     * объект {@link LongRange}
     *
     * @param range новый диапазон значений
     */
    public void add(LongRange range)
    {
        if (range.length() == 0L)
        {
            return;
        }
        LongRange newRange = new LongRange(range);
        TreeSet<LongRange> newRanges = new TreeSet<LongRange>();
        long length = 0L;
        for (LongRange r : this.ranges)
        {
            // если диапазоны пересекаются или примыкают - объединяем
            if (newRange.intersects(r) || newRange.getEnd().equals(r.getStart())
                || newRange.getStart().equals(r.getEnd()))
            {
                newRange.include(r);
            }
            else
            {
                newRanges.add(r);
                length += r.length();
            }
        }
        newRanges.add(newRange);
        length += newRange.length();
        this.ranges = newRanges;
        this.rangesLength = length;
    }

    /**
     * Поиск значения принадлежащего множеству, ближайшего большего к указанному.
     *
     * @param value значение аргумента
     * @return Ближайшее большее значение принадлежащее множеству,
     *         либо само значение аргумента, если оно принадлежит множеству,
     *         либо <code>null</code> если такое значение не существует
     */
    public Long ceiling(Long value)
    {
        LongRange sr = new LongRange(value, value);
        LongRange r = ranges.floor(sr);
        if (r != null && r.contains(value))
        {
            return value;
        }
        r = ranges.ceiling(sr);
        return r != null ? r.getStart() : null;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Ranges clone()
    {
        try
        {
            Ranges clon = (Ranges)super.clone();
            TreeSet<LongRange> rangesClone = new TreeSet<LongRange>();
            for (LongRange lr : clon.ranges)
            {
                rangesClone.add(lr.clone());
            }
            clon.ranges = rangesClone;
            return clon;
        }
        catch (CloneNotSupportedException e)
        {
            throw new RuntimeException(e);
        }
    }

    /**
     * Получение аддитивной меры множества (суммарная длина всех диапазонов,
     * определяющих множество)
     * @return
     */
    public long getRangesLength()
    {
        return this.rangesLength;
    }

    /**
     * Получение суммарной длины диапазонов от заданного значения до конца множества,
     * или меры подмножества, ограниченного слева на оси значений указанным значением
     * @param value
     * @return
     */
    public long getRangesLengthFrom(Long value)
    {
        return this.rangesLength - getRangesLengthTill(value);
    }

    /**
     * Получение длины диапазонов от начала множества, до указанного значения,
     * или меры подмножества, ограниченного справа на оси значений указанным значением
     * @param value
     * @return
     */
    public long getRangesLengthTill(Long value)
    {
        long res = 0L;
        for (LongRange r : ranges.headSet(new LongRange(value, value)))
        {
            res += r.contains(value) ? value - r.getStart() : r.length();
        }
        return res;
    }

    /**
     * Вычисление значения принадлежащего множеству, находящемуся на заданном
     * расстоянии от начала множества. В значение расстояния укладываются только
     * диапазоны составляющие множество.
     * @param distance расстояние
     * @return значение, принадлежащее множеству,
     *         либо <code>null</code> еси такого значения не существует
     */
    public Long getRangeValueFromBegin(long distance)
    {
        for (LongRange lr : ranges)
        {
            if (distance > lr.length())
            {
                distance -= lr.length();
            }
            else
            {
                return lr.getStart() + distance;
            }
        }
        return null;
    }

    /**
     * Вычисление значения принадлежащего множеству, находящемуся на указанном
     * расстоянии от указанного значения. В значение расстояния укладываются только
     * диапазоны составляющие множество.
     * @param from значение отсчёта
     * @param distance расстояние
     * @return значение, принадлежащее множеству,
     *         либо <code>null</code> еси такого значения не существует
     */
    public Long getRangeValueFromDistance(long from, long distance)
    {
        LongRange sr = new LongRange(from, from);
        LongRange r = ranges.floor(sr);
        if (r != null && r.contains(from))
        {
            if (from + distance <= r.getEnd())
            {
                return from + distance;
            }
            distance -= r.getEnd() - from;
            sr = r;
        }
        for (LongRange lr : ranges.tailSet(sr, false))
        {
            if (distance > lr.length())
            {
                distance -= lr.length();
            }
            else
            {
                return lr.getStart() + distance;
            }
        }
        return null;
    }

    /**
     * Вычисление значения принадлежащего множеству, находящемуся слева на указанном
     * расстоянии от указанного значения. В значение расстояния укладываются только
     * диапазоны составляющие множество.
     * @param from значение отсчёта
     * @param distance расстояние
     * @return значение, принадлежащее множеству,
     *         либо <code>null</code> если такого значения не существует
     */
    public Long getRangeValueLeftFromDistance(long from, long distance)
    {
        LongRange startRange = new LongRange(from, from);
        LongRange range = this.ranges.floor(startRange);
        if (range != null && range.contains(from))
        {
            if (from - distance >= range.getStart())
            {
                return from - distance;
            }
            distance -= from - range.getStart();
            startRange = range;
        }
        return getRangeValueLeftFromEndInt(distance, createInverseRanges(this.ranges.headSet(startRange, false)));
    }

    /**
     * Вычисление значения принадлежащего множеству, находящемуся на заданном
     * расстоянии слева от конца множества. В значение расстояния укладываются только
     * диапазоны составляющие множество.
     * @param distance расстояние
     * @return значение, принадлежащее множеству,
     *         либо <code>null</code> еси такого значения не существует
     */
    public Long getRangeValueLeftFromEnd(long distance)
    {
        TreeSet<LongRange> ranges = createInverseRanges(this.ranges);
        return getRangeValueLeftFromEndInt(distance, ranges);
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Iterator<LongRange> iterator()
    {
        return Iterators.unmodifiableIterator(ranges.iterator());
    }

    /**
     * Метод предназначен для исключения из множества значений указанного диапазона.
     * Если указанный диапазон пересекается с уже существующим(ми) в наборе,
     * то такие диапазоны усекаются до границ указанного диапазона. Если лежит внутри
     * существующего диапазона, последний разбивается на два.
     *
     * @param val удаляемый диапазон значений
     */
    public void remove(LongRange val)
    {
        TreeSet<LongRange> newRanges = Sets.newTreeSet();
        long length = 0L;
        for (LongRange range : this.ranges)
        {
            RELATION rel = val.getRelation(range);
            if (rel == RELATION.EQUALS || rel == RELATION.OUTSIDE)
            {
                continue;
            }
            if (rel == RELATION.INSIDE)
            {
                length += addRange(newRanges, new LongRange(range.getStart(), val.getStart()));
                length += addRange(newRanges, new LongRange(val.getEnd(), range.getEnd()));
                continue;
            }

            LongRange copy = range.clone();
            if (rel == RELATION.INTERSECT)
            {
                //@formatter:off
                copy = copy.before(val) 
                        ? (LongRange)copy.setEnd(val.getStart()) 
                        : (LongRange)copy.setStart(val.getEnd());
                //@formatter:on
            }
            length += addRange(newRanges, copy);
        }
        this.ranges = newRanges;
        this.rangesLength = length;
    }

    @Override
    public String toString()
    {
        return "Ranges [ranges=" + ranges + ", rangesLength=" + rangesLength + "]";
    }

    private long addRange(Collection<LongRange> ranges, LongRange range)
    {
        if (range.length() > 0)
        {
            ranges.add(range);
        }
        return range.length();
    }

    private TreeSet<LongRange> createInverseRanges(NavigableSet<LongRange> ranges)
    {
        TreeSet<LongRange> inverseRanges = new TreeSet<>(Collections.reverseOrder());
        inverseRanges.addAll(ranges);
        return inverseRanges;
    }

    private Long getRangeValueLeftFromEndInt(long distance, TreeSet<LongRange> ranges)
    {
        for (LongRange range : ranges)
        {
            if (distance > range.length())
            {
                distance -= range.length();
            }
            else
            {
                return range.getEnd() - distance;
            }
        }
        return null;
    }
}
