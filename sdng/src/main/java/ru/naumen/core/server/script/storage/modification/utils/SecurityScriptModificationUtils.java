package ru.naumen.core.server.script.storage.modification.utils;

import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_ACCESS_DTO_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_ACCESS_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_FAST_LINK_RIGHTS_DTO_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_FAST_LINK_RIGHTS_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_LIST_FILTER_DTO_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_LIST_FILTER_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_OWNERS_DTO_KEY;
import static ru.naumen.metainfo.shared.Constants.Role.SCRIPT_OWNERS_KEY;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;

import jakarta.inject.Inject;

import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.sec.server.admin.log.ScriptLogService;
import ru.naumen.common.shared.utils.IProperties;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.script.storage.modification.usage.AccessMatrixScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.shared.script.places.OtherCategories;
import ru.naumen.core.shared.script.places.RoleCategories;
import ru.naumen.core.shared.script.places.ScriptHolders;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.server.spi.SecurityServiceBean;
import ru.naumen.metainfo.server.spi.elements.MetaClassImpl;
import ru.naumen.metainfo.server.spi.elements.sec.AccessMatrixImpl;
import ru.naumen.metainfo.server.spi.elements.sec.ProfileImpl;
import ru.naumen.metainfo.server.spi.elements.sec.RoleImpl;
import ru.naumen.metainfo.server.spi.elements.sec.SecDomainImpl;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix.Key;
import ru.naumen.metainfo.shared.elements.sec.Marker;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.elements.sec.SecDomain;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;

/**
 * Утилитарные методы редактирования скриптов прав доступа, ролей и всего что связанно с безопасностью
 * содержат логику создания, редактирования, удаления скриптов
 * <AUTHOR>
 * @since 12.09.2015
 */
@Component
public class SecurityScriptModificationUtils
{
    private final SecurityServiceBean securityService;
    private final ScriptModifyRegistry scriptModifyRegistry;
    private final ScriptDtoFactory scriptDtoFactory;
    private final MetainfoServicePersister persister;
    private final ScriptStorageService scriptStorageService;
    private final ScriptLogService scriptLogService;

    @Inject
    public SecurityScriptModificationUtils(SecurityServiceBean securityService,
            ScriptModifyRegistry scriptModifyRegistry,
            ScriptDtoFactory scriptDtoFactory, @Lazy MetainfoServicePersister persister,
            @Lazy ScriptStorageService scriptStorageService, @Lazy ScriptLogService scriptLogService)
    {
        this.securityService = securityService;
        this.scriptModifyRegistry = scriptModifyRegistry;
        this.scriptDtoFactory = scriptDtoFactory;
        this.persister = persister;
        this.scriptStorageService = scriptStorageService;
        this.scriptLogService = scriptLogService;
    }

    public static HashMap<Key, ScriptDto> convertToMapScriptDtos(Map<Key, String> scripts)
    {
        ScriptDto withoutScript = ScriptDtoFactory.createWithout();
        HashMap<Key, ScriptDto> scriptDtos = new HashMap<>();

        for (Entry<Key, String> entry : scripts.entrySet())
        {
            Key key = entry.getKey();
            String scriptCode = entry.getValue();

            if (StringUtilities.isEmpty(scriptCode))
            {
                scriptDtos.put(key, withoutScript);
            }
            else
            {
                scriptDtos.put(key, ScriptDtoFactory.createNeedLoad(scriptCode));
            }
        }
        return scriptDtos;
    }

    public void copyPermissionsFromMarker(SecDomainImpl domain, String copyFromMarker, String copyToMarker)
    {
        Preconditions.checkNotNull(copyFromMarker, "Marker code can't be null");
        Preconditions.checkNotNull(copyToMarker, "Marker code can't be null");

        Map<Key, Boolean> data = new HashMap<>();
        Map<Key, String> scriptCopies = new HashMap<>();

        boolean isUnlicensedAllowed = domain.getMarker(copyToMarker).isUnlicensedAllowed();

        AccessMatrixImpl sourceDomainMatrix = domain.getAccessMatrix();

        for (ProfileImpl profile : sourceDomainMatrix.getProfiles())
        {
            if (profile.isForLicensedUsers() || isUnlicensedAllowed)
            {
                Key newKey = new Key(profile.getCode(), copyToMarker);
                Boolean currentValue = sourceDomainMatrix.get(profile.getCode(), copyFromMarker);
                if (currentValue == null)
                {
                    // Значение права не определено даже на уровне родителей
                    currentValue = false;
                }
                data.put(newKey, currentValue);
                scriptCopies.put(newKey, sourceDomainMatrix.getScript(profile.getCode(), copyFromMarker));
            }
        }

        processSaveAccessMatrixScripts(domain, sourceDomainMatrix, convertToMapScriptDtos(scriptCopies));
        sourceDomainMatrix.putPermissions(data);
    }

    public void copyPermissionsFromProfile(SecDomainImpl copyForDomain, String copyFromProfile, String copyToProfile)
    {
        Preconditions.checkNotNull(copyFromProfile, "Profile code can't be null");
        Preconditions.checkNotNull(copyToProfile, "Profile code can't be null");

        Map<Key, Boolean> data = new HashMap<>();
        Map<Key, String> scriptCopies = new HashMap<>();

        ProfileImpl profile = securityService.getProfile(copyToProfile);
        boolean isForLicensedUsers = profile.isForLicensedUsers();

        ProfileImpl profileFrom = securityService.getProfile(copyFromProfile);
        AccessMatrixImpl sourceDomainMatrix = profileFrom.isVersioning() ? copyForDomain.getVersAccessMatrix() :
                copyForDomain.getAccessMatrix();
        AccessMatrixImpl targetDomainMatrix = profile.isVersioning() ? copyForDomain.getVersAccessMatrix() :
                copyForDomain.getAccessMatrix();

        for (Marker marker : sourceDomainMatrix.getMarkers())
        {
            if (isForLicensedUsers || marker.isUnlicensedAllowed())
            {
                Key newKey = new Key(copyToProfile, marker.getCode());
                Boolean currentValue = sourceDomainMatrix.get(copyFromProfile, marker.getCode());
                if (Boolean.TRUE.equals(currentValue))
                {
                    data.put(newKey, true);
                    scriptCopies.put(newKey,
                            sourceDomainMatrix.getScript(copyFromProfile, marker.getCode()));
                }
            }
        }

        processSaveAccessMatrixScripts(copyForDomain, targetDomainMatrix, convertToMapScriptDtos(scriptCopies));
        targetDomainMatrix.putPermissions(data);
    }

    public void processAfterCopyAccessMatrixScripts(SecDomain domain, Set<Key> createdKeys)
    {
        AccessMatrix matrix = domain.getAccessMatrix();
        ScriptModifyProcess<AccessMatrix> process = scriptModifyRegistry.getProcess(matrix);
        ScriptModifyContext context = new ScriptModifyContext(OtherCategories.PERMISSIONS, ScriptHolders.PERMISSIONS);
        context.setProperty(AccessMatrixScriptModifyProcess.SEC_DOMAIN_CODE, domain.getCode());

        for (Key key : createdKeys)
        {
            context.setProperty(AccessMatrixScriptModifyProcess.KEY, key);
            process.copyHolder(matrix, context);
        }

        scriptLogService.makeLogs(context.getScriptsLogInfo());
    }

    public void processDeleteAccessMatrixScripts(SecDomainImpl domain,
            AccessMatrixImpl matrix, Set<Key> deletedKeys, boolean doLog)
    {
        ScriptModifyProcess<AccessMatrixImpl> process = scriptModifyRegistry.getProcess(matrix);
        ScriptModifyContext context = new ScriptModifyContext(OtherCategories.PERMISSIONS, ScriptHolders.PERMISSIONS);
        context.setProperty(AccessMatrixScriptModifyProcess.SEC_DOMAIN_CODE, domain.getCode());

        for (Key key : deletedKeys)
        {
            context.setProperty(AccessMatrixScriptModifyProcess.KEY, key);
            process.deleteHolder(matrix, context);
            matrix.removeDeclaredScript(key);
        }

        if (doLog)
        {
            scriptLogService.makeLogs(context.getScriptsLogInfo());
        }
    }

    public void processDeleteAccessMatrixScripts(SecDomainImpl domain, AccessMatrixImpl matrix, Set<Key> deletedKeys)
    {
        processDeleteAccessMatrixScripts(domain, matrix, deletedKeys, true);
    }

    public List<ScriptAdminLogInfo> processDeleteSecurityRoleScripts(RoleImpl role)
    {
        List<ScriptAdminLogInfo> scriptsLog = new ArrayList<>();
        scriptsLog.addAll(processDeleteRoleScript(role, RoleCategories.ACCESS));
        scriptsLog.addAll(processDeleteRoleScript(role, RoleCategories.OWNERS));
        scriptsLog.addAll(processDeleteRoleScript(role, RoleCategories.LIST_FILTER));
        scriptsLog.addAll(processDeleteRoleScript(role, RoleCategories.FAST_LINK_RIGHTS));
        return scriptsLog;
    }

    public void processRemovePermissionsForDeletedMetaClass(MetaClassImpl current)
    {
        SecDomainImpl domain = securityService.getDomain(current.getFqn());
        if (domain != null)
        {
            Map<Key, String> declaredScripts = domain.getAccessMatrix().getDeclaredScripts();
            processDeleteAccessMatrixScripts(domain, domain.getAccessMatrix(), declaredScripts.keySet());

            persister.persist(domain);
        }
    }

    public void processResetSecDomainScripts(SecDomainImpl domain, boolean doLog)
    {
        Map<Key, String> declaredScripts = domain.getAccessMatrix().getDeclaredScripts();
        processDeleteAccessMatrixScripts(domain, domain.getAccessMatrix(),
                declaredScripts.keySet(), doLog);
    }

    public void processResetSecDomainScripts(SecDomainImpl domain)
    {
        processResetSecDomainScripts(domain, true);
    }

    public void processRoleScriptsToDto(Role role)
    {
        processRoleScriptToDto(SCRIPT_ACCESS_KEY, SCRIPT_ACCESS_DTO_KEY, role.getProperties());
        processRoleScriptToDto(SCRIPT_OWNERS_KEY, SCRIPT_OWNERS_DTO_KEY, role.getProperties());
        processRoleScriptToDto(SCRIPT_LIST_FILTER_KEY, SCRIPT_LIST_FILTER_DTO_KEY, role.getProperties());
        processRoleScriptToDto(SCRIPT_FAST_LINK_RIGHTS_KEY, SCRIPT_FAST_LINK_RIGHTS_DTO_KEY, role.getProperties());
    }

    public void processSaveAccessMatrixScripts(SecDomain domain, AccessMatrix matrix, Map<Key, ScriptDto> newScripts)
    {
        ScriptModifyProcess<AccessMatrix> process = scriptModifyRegistry.getProcess(matrix);
        ScriptModifyContext context = new ScriptModifyContext(OtherCategories.PERMISSIONS, ScriptHolders.PERMISSIONS);
        context.setProperty(AccessMatrixScriptModifyProcess.SEC_DOMAIN_CODE, domain.getCode());

        for (Entry<Key, ScriptDto> entry : newScripts.entrySet())
        {
            context.setProperty(AccessMatrixScriptModifyProcess.KEY, entry.getKey());
            process.save(matrix, matrix, entry.getValue(), context);
        }

        scriptLogService.makeLogs(context.getScriptsLogInfo());
    }

    public List<ScriptAdminLogInfo> processSaveSecurityRoleScripts(RoleImpl role, IProperties properties)
    {
        List<ScriptAdminLogInfo> scriptsLog = new ArrayList<>();
        scriptsLog.addAll(processSaveRoleScript(role, properties.getProperty(SCRIPT_ACCESS_DTO_KEY),
                RoleCategories.ACCESS));
        scriptsLog.addAll(processSaveRoleScript(role, properties.getProperty(SCRIPT_OWNERS_DTO_KEY),
                RoleCategories.OWNERS));
        scriptsLog.addAll(processSaveRoleScript(role, properties.getProperty(SCRIPT_LIST_FILTER_DTO_KEY),
                RoleCategories.LIST_FILTER));
        scriptsLog.addAll(processSaveRoleScript(role, properties.getProperty(SCRIPT_FAST_LINK_RIGHTS_DTO_KEY),
                RoleCategories.FAST_LINK_RIGHTS));
        return scriptsLog;
    }

    private List<ScriptAdminLogInfo> processDeleteRoleScript(Role role, RoleCategories category)
    {
        ScriptModifyProcess<Role> process = scriptModifyRegistry.getProcess(role);
        ScriptModifyContext context = new ScriptModifyContext(category, ScriptHolders.ROLE);
        process.deleteHolder(role, context);
        return context.getScriptsLogInfo();
    }

    private void processRoleScriptToDto(String scriptCodeKey, String scriptDtoKey, IProperties roleProperties)
    {
        String scriptCode = roleProperties.getProperty(scriptCodeKey);
        if (StringUtilities.isEmpty(scriptCode))
        {
            return;
        }

        Script script = scriptStorageService.getScript(scriptCode);
        roleProperties.setProperty(scriptDtoKey, scriptDtoFactory.create(script));
    }

    private List<ScriptAdminLogInfo> processSaveRoleScript(Role role, ScriptDto newScript, RoleCategories category)
    {
        ScriptModifyProcess<Role> process = scriptModifyRegistry.getProcess(role);
        ScriptModifyContext context = new ScriptModifyContext(category, ScriptHolders.ROLE);
        process.save(role, role, newScript, context);
        return context.getScriptsLogInfo();
    }
}
