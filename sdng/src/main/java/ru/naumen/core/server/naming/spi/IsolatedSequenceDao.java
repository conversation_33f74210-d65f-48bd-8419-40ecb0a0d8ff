package ru.naumen.core.server.naming.spi;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.naming.generators.strategies.NextValueGenerationStrategy;

/**
 * D<PERSON><PERSON> для {@link PeriodicalSequence}
 *
 * <AUTHOR>
 *
 */
public interface IsolatedSequenceDao
{
    /**
     * Удаляет последовательность с заданным идентификатором
     *
     * @param id идентификатор удаляемой последовательности.
     * @param period период, для которого происходит удаление данных последовательности.
     */
    void deleteSequence(String id, Long period);

    /**
     * Генерация следующего идентификатора на основе стратегии генерации следующего значения.
     *
     * @param key идентификатор последовательности, период для которого производится генерация.
     * @param nextValStrategy стратегия генерации следующего значения.
     * @return следующее значение.
     */
    Integer generateId(Pair<String, Long> key, NextValueGenerationStrategy nextValStrategy);

    /**
     * Получение последовательности по её идентификатору.
     *
     * @param id идентификатор последовательности.
     * @return последовательность.
     */
    PeriodicalSequence getSequence(String id);

    /**
     * Запускает последовательность с заданного значения очищая все возвращенные значения 
     *
     * @param sequenceId идентификатор последовательности
     * @param period очищаемый период
     * @param value новое начальное значение последовательности 
     */
    void restartSequence(String sequenceId, Long period, int value);

    /**
     * Возвращает значение последовательности в очередь неиспользованных значений 
     *
     * @param sequenceId идентификатор последовательности
     * @param period очищаемый период
     * @param value значение которое следует поместить в очередь
     */
    void returnValue(String sequenceId, Long period, Integer value);

    /**
     * Устанавливает значение последовательности в указанное значение. В отличие от
     * {@link #updateSequence(String, Long, Integer)}
     * создает последовательность, если ее нет.
     *
     * @param sequenceId идентификатор последовательности
     * @param period очищаемый период
     * @param value новое начальное значение последовательности 
     */
    void setSequence(String sequenceId, Long period, Integer value);

    /**
     * Устанавливает значение последовательности в указанное значение.
     *
     * @param sequenceId идентификатор последовательности
     * @param period очищаемый период
     * @param value новое начальное значение последовательности 
     */
    void updateSequence(String sequenceId, Long period, Integer value);

}
