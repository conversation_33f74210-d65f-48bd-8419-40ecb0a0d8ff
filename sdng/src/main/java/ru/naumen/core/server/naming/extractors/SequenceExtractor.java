package ru.naumen.core.server.naming.extractors;

import ru.naumen.core.server.naming.generators.PeriodicalIDGenerator;

/**
 * Определяет идентификатор последовательности. Используется 
 * для {@link PeriodicalIDGenerator}
 * <p>
 * Например, может сформировать последовательность на основе кода класса, чтобы сгенерированный номер
 * последовательности относился к конкретному классу.
 *
 * <AUTHOR>
 * @since 02.05.2012
 */
public interface SequenceExtractor
{
    String getSequenceId(Object obj);
}
