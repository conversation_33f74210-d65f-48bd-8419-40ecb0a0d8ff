package ru.naumen.core.server.script.spi;

import java.io.Serial;
import java.util.Date;
import java.util.Locale;

import org.joda.time.DateTime;
import org.joda.time.format.DateTimeFormat;
import org.joda.time.format.DateTimeFormatter;

import ru.naumen.core.shared.utils.ILocaleInfo;

/**
 * Обёртка для объекта {@link Date}, запрещающая изменение значения
 * <AUTHOR>
 */
public class ScriptDate extends Date
{
    @Serial
    private static final long serialVersionUID = 8744356387363072073L;

    final Date delegate;
    private transient final ScriptDtOHelper helper;
    private final boolean isDateTime;

    public ScriptDate(Date date, ScriptDtOHelper helper)
    {
        this(date, helper, true);
    }

    public ScriptDate(Date date, ScriptDtOHelper helper, boolean isDateTime)
    {
        this.isDateTime = isDateTime;
        this.delegate = date;
        this.helper = helper;
        super.setTime(date.getTime());
    }

    @Override
    public boolean equals(Object obj)
    {
        if (obj == this)
        {
            return true;
        }
        if (obj != null && this.getClass().equals(obj.getClass()))
        {
            return this.delegate.equals(((ScriptDate)obj).delegate);
        }
        return false;
    }

    @Override
    public int hashCode()
    {
        return delegate.hashCode();
    }

    @Override
    public final void setDate(int date)
    {
        helper.unmodify();
    }

    @Override
    public final void setHours(int hours)
    {
        helper.unmodify();
    }

    @Override
    public final void setMinutes(int minutes)
    {
        helper.unmodify();
    }

    @Override
    public final void setMonth(int month)
    {
        helper.unmodify();
    }

    @Override
    public final void setSeconds(int seconds)
    {
        helper.unmodify();
    }

    @Override
    public final void setTime(long time)
    {
        helper.unmodify();
    }

    @Override
    public final void setYear(int year)
    {
        helper.unmodify();
    }

    /**
     * Форматирует дату/дату и время с учетом локали
     */
    @Override
    public String toString()
    {
        String dateTimePattern = DateTimeFormat.patternForStyle(isDateTime ? "MS" : "M-", getLocale());
        DateTimeFormatter dateTimeFormatter = DateTimeFormat.forPattern(dateTimePattern);
        return dateTimeFormatter.print(new DateTime(this.delegate));
    }

    private Locale getLocale()
    {
        if (helper.getLocaleInfo() == null || helper.getLocaleInfo().getCurrentLang().equals(ILocaleInfo.CLIENT_LANG))
        {
            return Locale.of(ILocaleInfo.DEFAULT_LANG);
        }
        return Locale.of(helper.getLocaleInfo().getCurrentLang());
    }
}
