package ru.naumen.core.server.script.spi;

import static ru.naumen.core.server.events.impl.EventServiceBean.DEFAULT_EVENT_LOCALE;
import static ru.naumen.metainfo.shared.Constants.HyperlinkAttributeType.TEXT;
import static ru.naumen.metainfo.shared.Constants.HyperlinkAttributeType.TEXT_SUFFIX;
import static ru.naumen.metainfo.shared.Constants.HyperlinkAttributeType.URL;
import static ru.naumen.metainfo.shared.Constants.HyperlinkAttributeType.URL_SUFFIX;

import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.hibernate.SessionFactory;
import org.hibernate.query.MutationQuery;
import org.hibernate.query.NativeQuery;
import org.hibernate.type.StandardBasicTypes;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;
import com.google.common.collect.Sets;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.bcp.server.operations.ChangeRecordFormatHelper;
import ru.naumen.bcp.server.operations.OperationHelper;
import ru.naumen.bcp.server.operations.context.HasObjectBOContext;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.Hyperlink;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverContext;
import ru.naumen.core.server.attrdescription.resolvers.ResolverUtils;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.events.Constants.Categories;
import ru.naumen.core.server.events.EventService;
import ru.naumen.core.server.flex.attr.AttrStrategyFactory;
import ru.naumen.core.server.flex.codegen.attributes.AttributePropertyAppender;
import ru.naumen.core.server.flex.codegen.attributes.object.back.BackBOLinkAttributeGenerator;
import ru.naumen.core.server.hibernate.DataBaseInfo;
import ru.naumen.core.server.jta.managed.local.TransactionalDataSource;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.util.log.ChangeRecord;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.attr.FormatterService;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.fts.server.external.event.AfterFastEditObjectEvent;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.CaseListAttributeType;
import ru.naumen.metainfo.shared.elements.DateTimeIntervalAttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.ObjectAttributeType;

@Component("systemUtils")
@Primary
public class SystemUtilsApi implements ISystemUtilsApi
{
    private static final Logger LOG = LoggerFactory.getLogger(SystemUtilsApi.class);
    //Коды атрибутов, которые нельзя редактировать напрямую в базе.
    private static final Set<String> NOT_EDITABLE_ATTRS = Set.of(
            ru.naumen.core.shared.Constants.AbstractBO.REMOVED,
            ru.naumen.core.shared.Constants.AbstractBO.REMOVAL_DATE,
            ru.naumen.core.shared.Constants.AbstractBO.CREATION_DATE,
            ru.naumen.core.shared.Constants.LAST_MODIFIED_DATE,
            ru.naumen.core.shared.Constants.ServiceCall.SERVICE_TIME,
            ru.naumen.core.shared.Constants.Association.CLIENT_OU,
            ru.naumen.core.shared.Constants.Association.CLIENT_EMPLOYEE,
            ru.naumen.core.shared.Constants.Association.CLIENT_TEAM,
            ru.naumen.core.shared.Constants.Association.AGREEMENT,
            ru.naumen.core.shared.Constants.Association.SERVICE,
            ru.naumen.core.shared.Constants.ServiceCall.TIME_ZONE,
            ru.naumen.core.shared.Constants.HasState.STATE_START_TIME,
            ru.naumen.core.shared.Constants.ServiceCall.REGISTRATION_DATE,
            ru.naumen.core.shared.Constants.ServiceCall.REQUEST_DATE,
            ru.naumen.core.shared.Constants.ServiceCall.WF_PROFILE_CODE,
            ru.naumen.core.shared.Constants.ServiceCall.MASS_PROBLEM,
            ru.naumen.core.shared.Constants.ServiceCall.MASSPROBLEM_SLAVES,
            ru.naumen.core.shared.Constants.ServiceCall.MASTER_MASS_PROBLEM,
            ru.naumen.core.shared.Constants.ServiceCall.HAS_MASTER_PROBLEM,
            ru.naumen.core.shared.Constants.ServiceCall.RESOLUTION_TIME,
            ru.naumen.core.shared.Constants.ServiceCall.DEADLINE_TIME,
            ru.naumen.core.shared.Constants.HasFolders.FOLDERS,
            ru.naumen.core.shared.Constants.AbstractBO.UUID,
            ru.naumen.core.shared.Constants.Association.CLIENT_LINK_NAME,
            ru.naumen.core.shared.Constants.PARENT_ATTR
    );
    private static final Set<String> AVAILABLE_ATTR_TYPES = Set.of(
            Constants.IntegerAttributeType.CODE,
            Constants.ObjectAttributeType.CODE, Constants.BOLinksAttributeType.CODE,
            Constants.BackLinkAttributeType.CODE, Constants.TextAttributeType.CODE,
            Constants.StringAttributeType.CODE, Constants.BooleanAttributeType.CODE,
            Constants.CatalogItemAttributeType.CODE, Constants.RichTextAttributeType.CODE,
            Constants.DoubleAttributeType.CODE, Constants.DateTimeIntervalAttributeType.CODE,
            Constants.HyperlinkAttributeType.CODE, Constants.DateAttributeType.CODE,
            Constants.DateTimeAttributeType.CODE, Constants.CatalogItemsAttributeType.CODE,
            Constants.CaseListAttributeType.CODE
    );

    private static final String HQL_IN_OPEN_BRACE = " = (:";

    private final ApiUtils apiUtils;
    private final transient AttrStrategyFactory attrStrategyFactory; //NOSONAR
    private final EventService eventService;
    private final FormatterService formatterService;
    private final OperationHelper operationHelper;
    private final MetainfoService metainfoService;
    private final AccessorHelper accessorHelper;
    private final SessionFactory sessionFactory;
    private final ResolverUtils resolverUtils;
    private final ApplicationEventPublisher eventPublisher;
    private final TransactionalDataSource dataSource;
    private final ChangeRecordFormatHelper changeRecordFormatHelper;
    private final DataBaseInfo dataBaseInfo;

    @Inject
    public SystemUtilsApi(ApiUtils apiUtils,
            AttrStrategyFactory attrStrategyFactory,
            EventService eventService,
            FormatterService formatterService,
            OperationHelper operationHelper,
            MetainfoService metainfoService,
            AccessorHelper accessorHelper,
            SessionFactory sessionFactory,
            ResolverUtils resolverUtils,
            ApplicationEventPublisher eventPublisher,
            TransactionalDataSource dataSource,
            ChangeRecordFormatHelper changeRecordFormatHelper,
            DataBaseInfo dataBaseInfo)
    {
        this.apiUtils = apiUtils;
        this.attrStrategyFactory = attrStrategyFactory;
        this.eventService = eventService;
        this.formatterService = formatterService;
        this.operationHelper = operationHelper;
        this.metainfoService = metainfoService;
        this.accessorHelper = accessorHelper;
        this.sessionFactory = sessionFactory;
        this.resolverUtils = resolverUtils;
        this.eventPublisher = eventPublisher;
        this.dataSource = dataSource;
        this.changeRecordFormatHelper = changeRecordFormatHelper;
        this.dataBaseInfo = dataBaseInfo;
    }

    @Override
    public void fillAttrsDirect(Object obj, Map<String, Object> attrValues)
    {
        String errorPrefix = "Method fillAttrsDirect. Many attrs.";
        for (String attrCode : attrValues.keySet())
        {
            if (NOT_EDITABLE_ATTRS.contains(attrCode))
            {
                LOG.error("{} Cannot change attribute with code '{}' because it is forbidden for editing by this API "
                          + "method", errorPrefix, attrCode);
                return;
            }
        }
        IUUIDIdentifiable object = apiUtils.getObject(obj);
        updateAttrValuesDirect(errorPrefix, object, attrValues);
    }

    @Override
    public void fillAttrsDirect(Object obj, String attrCode, Object attrValue)
    {
        String errorPrefix = "Method fillAttrsDirect. Single attr.";
        if (NOT_EDITABLE_ATTRS.contains(attrCode))
        {
            LOG.error("{} Cannot change attribute with code '{}' because it is forbidden for editing by this API "
                      + "method", errorPrefix, attrCode);
            return;
        }
        IUUIDIdentifiable object = apiUtils.getObject(obj);
        Map<String, Object> attrValues = new HashMap<>();
        attrValues.put(attrCode, attrValue);
        updateAttrValuesDirect(errorPrefix, object, attrValues);
    }

    @Override
    public void removeBOLinksDirect(Object obj, String attrCode, Object attrValue)
    {
        String errorPrefix = "Method removeBOLinksDirect";
        IUUIDIdentifiable object = apiUtils.getObject(obj);
        Long objId = UuidHelper.toId(object.getUUID());

        MetaClass metaClass = metainfoService.getMetaClass(object);
        Attribute attribute = metaClass.getAttribute(attrCode);
        Set<String> attrTypes = Sets.newHashSet(Constants.BOLinksAttributeType.CODE,
                Constants.BackLinkAttributeType.CODE, Constants.CatalogItemsAttributeType.CODE);
        if (!isAttrTypeCorrectAndAttrIsEditable(attrCode, errorPrefix, attribute, attrTypes))
        {
            return;
        }
        Object resolvedValue = resolverUtils.resolv(new ResolverContext(attribute, attrValue, object));
        List<String> removedUUIDs = addOrDelObjectsFromLinks(errorPrefix, objId, attribute, resolvedValue, false);
        if (!removedUUIDs.isEmpty())
        {
            Collection<IUUIDIdentifiable> removedObjects = filterResolvedValueByUUIDs(resolvedValue, removedUUIDs);
            eventService.event(Categories.OBJECT_FAST_EDIT, object, operationHelper.getObjectParent(object),
                    formatterService.format(null, Presentations.BO_REFERENCE, object),
                    getChangesAsStr(attribute, removedObjects, null));
            addToSearchIndexIfNeed(object, metaClass.getFqn(), attribute);
            updateLastModifiedDate(objId, metaClass);
        }
    }

    /**
     * Добавляет новые типы для атрибута "Набор типов класса"
     *
     * @param objId
     * @param casesToAdd
     * @param props
     */
    private void addNewCases(Long objId, Collection<ClassFqn> casesToAdd, Map<Object, Object> props)
    {
        for (ClassFqn fqn : casesToAdd)
        {
            String tableName = (String)props.get("table");
            String keyColumnName = (String)props.get("keyColumn");
            String relatedColumnName = (String)props.get("relatedColumn");
            StringBuilder sql = new StringBuilder("insert into ").append(tableName).append(" (").append(keyColumnName)
                    .append(',').append(relatedColumnName);
            if (dataBaseInfo.isOracle())
            {
                sql.append(") select :objId, :attrValue from dual WHERE  NOT EXISTS  ( SELECT  1 FROM ")
                        .append(tableName).append(" WHERE ").append(keyColumnName).append("= :objId and ")
                        .append(relatedColumnName).append("= :attrValue)").toString();
            }
            else
            {
                sql.append(") select :objId, :attrValue WHERE  NOT EXISTS  ( SELECT  1 FROM ").append(tableName)
                        .append(" WHERE ").append(keyColumnName).append("= :objId and ").append(relatedColumnName)
                        .append("= :attrValue)").toString();
            }
            NativeQuery<?> query = sessionFactory.getCurrentSession().createNativeQuery(sql.toString(), Integer.class);
            query.addSynchronizedQuerySpace(tableName);
            query.setParameter("attrValue", fqn.toString());
            query.setParameter("objId", objId);
            LOG.debug("Executing SQL: '{}' Params: 'attrValue'={}, 'objId'={}", sql, fqn, objId);
            query.executeUpdate();
        }
    }

    private boolean addObjectsToAttrValue(String errorPrefix, IUUIDIdentifiable object, Long objId, Attribute attribute,
            Object resolvedValue, String attrTypeCode, @Nullable List<IUUIDIdentifiable> oldValueForCatItem,
            List<String> attrChanges)
    {
        boolean linksOrCasesWasUpdated;
        List<String> addedUUIDs = addOrDelObjectsFromLinks(errorPrefix, objId, attribute, resolvedValue, true);
        if (!addedUUIDs.isEmpty())
        {
            Collection<IUUIDIdentifiable> addedObjects = filterResolvedValueByUUIDs(resolvedValue, addedUUIDs);
            if (Constants.CatalogItemsAttributeType.CODE.equals(attrTypeCode))
            {
                addedObjects.addAll(oldValueForCatItem);
                attrChanges.addAll(getChangesAsCollection(attribute, oldValueForCatItem, addedObjects));
            }
            else
            {
                eventService.event(Categories.OBJECT_FAST_EDIT, object, operationHelper.getObjectParent(object),
                        formatterService.format(null, Presentations.BO_REFERENCE, object),
                        getChangesAsStr(attribute, null, addedObjects));
            }
        }
        linksOrCasesWasUpdated = true;
        return linksOrCasesWasUpdated;
    }

    /**
     * Добавляет или удаляет объекты из набора ссылок
     *
     * @param errorPrefix   префикс сообщения об ошибке
     * @param objId         id объекта, у которого меняется набор ссылок на БО(или обратная ссылка)
     * @param attribute     атрибут, значение которого меняется
     * @param resolvedValue новое значение атрибута
     * @param addObjects    true, если объекта добавляются, false - в противном случае
     * @return список UUID-ов, которые были добавлены или удалены
     */
    private List<String> addOrDelObjectsFromLinks(String errorPrefix, Long objId, Attribute attribute,
            Object resolvedValue, boolean addObjects)
    {
        if (CollectionUtils.isEmpty((Collection<?>)resolvedValue))
        {
            LOG.warn(errorPrefix + " Attribute value is empty collection");
            return Collections.emptyList();
        }
        Collection<Long> ids = ((Collection<?>)resolvedValue).stream().
                map(UuidHelper.SAFE_UUID_EXTRACTOR)
                .map(UuidHelper.TO_ID)
                .collect(Collectors.toList());
        AttributePropertyAppender strategy = attrStrategyFactory.getStrategy(attribute);
        Map<Object, Object> props = strategy.getCommonProperties(attribute);
        changePropsForSystemAtrs(attribute, props);
        ObjectAttributeType type = attribute.getType().cast();
        String sql = !addObjects && isSingleBackBo(attribute, strategy) ? getSQLForDelLink(props)
                : getSQLForAddOrDelLinks(props, addObjects);
        return executeBatch(sql, objId, ids, type, addObjects);
    }

    /**
     * Добавляет объект в поисковые индексы, если по атрибуту настроен поиск
     *
     * @param object
     * @param attribute
     */
    private boolean addToSearchIndexIfNeed(IUUIDIdentifiable object, ClassFqn fqn, Attribute attribute) //NOPMD
    {
        if (object instanceof IHasMetaInfo iHasMetaInfo && (
                Boolean.TRUE.equals(attribute.isSimpleSearchableForLicensed())
                || Boolean.TRUE.equals(attribute.isSimpleSearchableForNotLicensed())
                || Boolean.TRUE.equals(attribute.isExtendedSearchableForLicensed())
                || Boolean.TRUE.equals(attribute.isExtendedSearchableForNotLicensed())))
        {
            eventPublisher.publishEvent(new AfterFastEditObjectEvent<>(new HasObjectBOContext<>(iHasMetaInfo, fqn)));
            return true;
        }
        return false;
    }

    /**
     * Для системных атрибутов название таблиц и колонок в этих таблицах для Набора ссылок и Набора элементов
     * справочника могут
     * быть зашиты в коде и сформированы не по правилу, используемому для пользовательсиких атрибутов.
     * Для таких атрибутов меняем нужные свойства в мапе свойств для промежуточных таблиц
     *
     * @param attribute
     * @param props
     */
    private void changePropsForSystemAtrs(Attribute attribute, Map<Object, Object> props)
    {
        String attrDeclaredMclsId = attribute.getDeclaredMetaClass().getId();
        String attrCode = attribute.getCode();
        if (ru.naumen.core.shared.Constants.Employee.CLASS_ID.equals(attrDeclaredMclsId)
            && ru.naumen.core.shared.Constants.Employee.RECIPIENT_AGREEMENTS.equals(attrCode))
        {
            props.put("table", "tbl_employee_ragreements");
        }
        else if (ru.naumen.core.shared.Constants.OU.CLASS_ID.equals(attrDeclaredMclsId)
                 && ru.naumen.core.shared.Constants.OU.RECIPIENT_AGREEMENTS.equals(attrCode))
        {
            props.put("table", "tbl_ou_recipientagreements");
        }
        else if (ru.naumen.core.shared.Constants.Team.CLASS_ID.equals(attrDeclaredMclsId)
                 && ru.naumen.core.shared.Constants.Team.RECIPIENT_AGREEMENTS.equals(attrCode))
        {
            props.put("table", "tbl_team_recipientagreements");
        }
        else if (ru.naumen.core.shared.Constants.SlmService.CLASS_ID.equals(attrDeclaredMclsId)
                 && ru.naumen.core.shared.Constants.SlmService.AGREEMENTS.equals(attrCode))
        {
            props.put("table", "tbl_slmservice_agreements");
            props.put("keyColumn", "service_id");
        }
        else if (ru.naumen.core.shared.Constants.ServiceCall.CLASS_ID.equals(attrDeclaredMclsId)
                 && ru.naumen.core.shared.Constants.ServiceCall.CATEGORIES.equals(attrCode))
        {
            props.put("table", "tbl_servicecall_categories");
        }
    }

    /**
     * Удаляет типы из значения атрибута "Набор типов класса"
     *
     * @param objId
     * @param casesToDelete
     * @param props
     */
    private void deleteRemovedCases(Long objId, Collection<ClassFqn> casesToDelete, Map<Object, Object> props)
    {
        if (!casesToDelete.isEmpty())
        {
            String tableName = (String)props.get("table");
            String sqlToDelete = "delete from " + tableName + " WHERE "
                                 + props.get("keyColumn") + "= :objId and " + props.get("relatedColumn")
                                 + " in :attrValue";
            NativeQuery<?> queryToDelete = sessionFactory.getCurrentSession()
                    .createNativeQuery(sqlToDelete, Integer.class);
            queryToDelete.addSynchronizedQuerySpace(tableName);
            Set<String> casesToDeleteAsStr = casesToDelete.stream().map(ClassFqn.TO_STRING_CONVERTER)
                    .collect(Collectors.toSet());
            queryToDelete.setParameterList("attrValue", casesToDeleteAsStr);
            queryToDelete.setParameter("objId", objId);
            if (LOG.isDebugEnabled())
            {
                LOG.debug("Executing SQL: '{}' Params: 'attrValue'={}, 'objId'={}", sqlToDelete,
                        StringUtilities.join(casesToDeleteAsStr), objId);
            }
            queryToDelete.executeUpdate();
        }
    }

    /**
     * Метод выполняет sql запросы на редактирование объекта в пакетном виде.
     *
     * @return список уникальных идентификаторов объектов, которые были добавлены или удалены.
     */
    private List<String> executeBatch(String sql, Long objId, Collection<Long> ids, ObjectAttributeType type,
            boolean addObjects)
    {
        List<String> addedOrDeletedUUIDs = new ArrayList<>();
        String objectMetaClassCode = type.getRelatedMetaClass().getId();
        try (Connection connection = dataSource.getConnection();
             PreparedStatement statement = connection.prepareStatement(sql))
        {
            int counter = 1;
            List<Long> processedIds = new ArrayList<>();
            for (Long id : ids)
            {
                statement.setLong(1, objId);
                statement.setLong(2, id);
                if (addObjects)
                {
                    statement.setLong(3, objId);
                    statement.setLong(4, id);
                }
                statement.addBatch();
                processedIds.add(id);
                if (counter % ru.naumen.core.shared.Constants.MAX_BATCH_SIZE == 0 || counter == ids.size())
                {
                    int[] result = statement.executeBatch();
                    if (result != null && result.length > 0)
                    {
                        addedOrDeletedUUIDs.addAll(processedIds.stream().map(processedId -> UuidHelper.toUuid(
                                processedId, objectMetaClassCode)).collect(Collectors.toList()));
                        processedIds.clear();
                    }
                }
                counter++;
            }
        }
        catch (SQLException e)
        {
            LOG.error(e.getMessage(), e);
            throw new FxException(e);
        }
        if (!CollectionUtils.isEmpty(addedOrDeletedUUIDs))
        {
            String entityName = metainfoService.getFullEntityName(type.getRelatedMetaClass().fqnOfClass());
            if (sessionFactory.getCache().containsEntity(entityName, objId))
            {
                sessionFactory.getCache().evictEntityData(entityName, objId);
            }
        }
        return addedOrDeletedUUIDs;
    }

    /**
     * Заполняет атрибут типа "Набор типов класса"
     *
     * @param object
     * @param linksOrCasesWasUpdated
     * @param objId
     * @param attributeCode
     * @param attribute
     * @param resolvedValue
     * @param attrChanges
     * @return
     */
    private boolean fillCaseListDirect(IUUIDIdentifiable object, boolean linksOrCasesWasUpdated, Long objId,
            String attributeCode, Attribute attribute, @Nullable Object resolvedValue, List<String> attrChanges)
    {
        Set<ClassFqn> oldValue = Sets.newHashSet(
                (Collection<ClassFqn>)accessorHelper.getAttributeValueWithoutPermission(object, attributeCode));
        Collection<ClassFqn> newValue = resolvedValue == null
                ? Collections.emptyList()
                : (Collection<ClassFqn>)resolvedValue;
        CaseListAttributeType attrType = attribute.getType().cast();
        ClassFqn relatedFqn = attrType.getRelatedFqn();
        newValue = newValue.stream().filter(classFqn -> classFqn.isCaseOf(relatedFqn)).collect(
                Collectors.toList());
        Collection<ClassFqn> casesToAdd = CollectionUtils.subtract(newValue, oldValue);
        Collection<ClassFqn> casesToDelete = CollectionUtils.subtract(oldValue, newValue);

        Map<Object, Object> props = attrStrategyFactory.getStrategy(attribute)
                .getCommonProperties(attribute);
        if (ru.naumen.core.shared.Constants.SlmService.CLASS_ID.equals(attribute.getDeclaredMetaClass().getId())
            && ru.naumen.core.shared.Constants.SlmService.CALL_CASES.equals(attributeCode))
        {
            props.put("table", "tbl_slmservice_callcases");
        }
        deleteRemovedCases(objId, casesToDelete, props);
        addNewCases(objId, casesToAdd, props);

        if (!casesToAdd.isEmpty() || !casesToDelete.isEmpty())
        {
            attrChanges.addAll(getChangesAsCollection(attribute, oldValue, newValue));
            linksOrCasesWasUpdated = true;
        }
        return linksOrCasesWasUpdated;
    }

    /**
     * Фильтрует новое значение атрибута ,пришедшее из скрипта, оставляя только те, объекты ,которые содержатся в
     * коллекции UUID-ов
     *
     * @param resolvedValue
     * @param uuids
     * @return
     */
    private static Collection<IUUIDIdentifiable> filterResolvedValueByUUIDs(Object resolvedValue, List<String> uuids)
    {
        return ((Collection<IUUIDIdentifiable>)resolvedValue).stream()
                .filter(input -> uuids.contains(input.getUUID()))
                .collect(Collectors.toSet());
    }

    /**
     * Возвращает коллекцию строк для записи в истории событий объекта об изменении значения атрибута
     *
     * @param attribute    измененный атрибут
     * @param oldAttrValue старое значение атрибута
     * @param newAttrValue новое значение атрибута
     * @return
     */
    private Collection<String> getChangesAsCollection(Attribute attribute, Object oldAttrValue, Object newAttrValue)
    {
        List<ChangeRecord> changeLogs = Lists.newArrayList(new ChangeRecord(null, attribute.getCode(), attribute,
                oldAttrValue, newAttrValue, false, getEventLocale()));
        return changeLogs.stream()
                .flatMap(changeRecord -> changeRecordFormatHelper.formatChanges(changeRecord).stream())
                .collect(Collectors.toList());
    }

    /**
     * Возвращает строку для записи в истории событий объекта об изменении значения атрибута
     *
     * @param attribute    измененный атрибут
     * @param oldAttrValue старое значение атрибута
     * @param newAttrValue новое значение атрибута
     * @return
     */
    private String getChangesAsStr(Attribute attribute, Object oldAttrValue, Object newAttrValue)
    {
        return StringUtilities.join(getChangesAsCollection(attribute, oldAttrValue, newAttrValue), ";\n\t") + ".";
    }

    /**
     * Возвращает локаль истории изменений
     *
     * @return
     */
    private Locale getEventLocale()
    {
        SpringContext ctx = SpringContext.getInstance();
        return ctx != null ? ctx.getBean("eventServiceBean", EventService.class).getEventLocale()
                : DEFAULT_EVENT_LOCALE;
    }

    private String getSQLForAddOrDelLinks(Map<Object, Object> props, boolean addObjects)
    {
        if (addObjects)
        {
            if (dataBaseInfo.isOracle())
            {
                return new StringBuilder("insert into ").append(props.get("table")).append(" (")
                        .append(props.get("keyColumn")).append(',').append(props.get("relatedColumn"))
                        .append(") select ?, ? from dual WHERE  NOT EXISTS  ( SELECT  1 FROM ")
                        .append(props.get("table")).append(" WHERE ").append(props.get("keyColumn"))
                        .append("= ? and ").append(props.get("relatedColumn")).append("= ?)").toString();
            }
            return new StringBuilder("insert into ").append(props.get("table")).append(" (")
                    .append(props.get("keyColumn")).append(',').append(props.get("relatedColumn"))
                    .append(") select ?, ? WHERE  NOT EXISTS  ( SELECT  1 FROM ")
                    .append(props.get("table")).append(" WHERE ").append(props.get("keyColumn")).append("= ? and ")
                    .append(props.get("relatedColumn")).append("= ?)").toString();
        }
        return new StringBuilder("delete from ").append(props.get("table")).append(" WHERE ")
                .append(props.get("keyColumn")).append("= ? and ").append(props.get("relatedColumn"))
                .append("= ?").toString();
    }

    private String getSQLForDelLink(Map<Object, Object> props)
    {
        return new StringBuilder("update ").append(props.get("table")).append(" set ").append(props.get("column"))
                .append("= null where ").append(props.get("column")).append("= ? ").append("and id = ?")
                .toString();
    }

    /**
     * Проверяет, что атрибут нужного типа и что он редактируемый
     *
     * @param attrCode
     * @param errorPrefix
     * @param attribute
     * @param attrTypes
     * @return
     */
    private boolean isAttrTypeCorrectAndAttrIsEditable(String attrCode, String errorPrefix, Attribute attribute,
            Set<String> attrTypes)
    {
        if (!isCorrectAttrType(attribute, attrTypes))
        {
            LOG.error(errorPrefix + " Attribute type is wrong. Attribute code:" + attrCode);
            return false;
        }
        if (Boolean.TRUE.equals(attribute.isComputable()) || Boolean.TRUE.equals(attribute.isComposite())
            || Boolean.TRUE.equals(attribute.isDeterminable())
            || Boolean.TRUE.equals(attribute.isUseGenerationRule()))
        {
            LOG.error(errorPrefix + " Attribute " + attrCode + " is not editable");
            return false;
        }
        return true;
    }

    /**
     * Проверяет, что тип атрибута соответствует одному из подходящих типов
     *
     * @param attribute
     * @param types
     * @return
     */
    private boolean isCorrectAttrType(Attribute attribute, Set<String> types)
    {
        String attrType = attribute.getType().getCode();
        return types.contains(attrType);
    }

    private boolean isSingleBackBo(Attribute attribute, AttributePropertyAppender strategy)
    {
        if (strategy instanceof BackBOLinkAttributeGenerator backGenerator)
        {
            return backGenerator.isSingleBackBo(attribute);
        }

        return false;
    }

    /**
     * Заполняет атрибуты напрямую в базе, в обход bcp, ДПС и т.п.
     * Метод работает для атрибутов типа:
     * Строка, текст, набор ссылок на БО, ссылка на БО,обратная ссылка, целое число, логическое значение,
     * элемент справочника, набор элементов справочника, гиперссылка, временной интервал, вещественное число,
     * набор типов класса, дата, дата/время, текст в формате rtf
     * Если атрибут неправильного типа или он вычислимый, составной, вычисляется по таблице соответствий или для
     * расчета его значения используется правило генерации, то в лог выводится ошибка, а метод возвращает null
     * Если попытаться изменить значение атрибута с кодом removed, то в логе также будет ошибка, метод вернет null
     * Для набора ссылок на БО и обратной ссылки метод добавляет указанные значения к уже существующим значениям
     * этого атрибута
     *
     * @param errorPrefix префикс сообщение об ошибке. Используется, если передан атрибут нередактируемого типа
     * @param object      объект, который необходимо отредактировать
     */
    private void updateAttrValuesDirect(String errorPrefix, IUUIDIdentifiable object, Map<String, Object> attrValues)
    {
        MetaClass metaClass = metainfoService.getMetaClass(object);
        Map<String, Attribute> attrs = new HashMap<>();
        for (Entry<String, Object> attrEntry : attrValues.entrySet())
        {
            String attributeCode = attrEntry.getKey();
            Attribute attribute = metaClass.getAttribute(attributeCode);
            attrs.put(attributeCode, attribute);

            if (!isAttrTypeCorrectAndAttrIsEditable(attributeCode, errorPrefix, attribute, AVAILABLE_ATTR_TYPES))
            {
                return;
            }
        }
        Map<String, Object> propsForQuery = new HashMap<>();
        List<String> queryPartsToUpdateSimpleAttrs = new ArrayList<>();
        boolean linksOrCasesWasUpdated = false;
        List<String> attrChanges = new ArrayList<>();
        Long objId = UuidHelper.toId(object.getUUID());
        for (Entry<String, Object> attrEntry : attrValues.entrySet())
        {
            String attributeCode = attrEntry.getKey();
            Object attributeValue = attrEntry.getValue();
            Attribute attribute = attrs.get(attributeCode);

            Object resolvedValue = resolverUtils.resolv(new ResolverContext(attribute, attributeValue));
            String attrTypeCode = attribute.getType().getCode();
            List<IUUIDIdentifiable> oldValueForCatItem = null;
            if (Constants.CatalogItemsAttributeType.CODE.equals(attrTypeCode))
            {
                oldValueForCatItem = Lists.newArrayList((Collection<IUUIDIdentifiable>)accessorHelper
                        .getAttributeValueWithoutPermission(object, attributeCode));
            }
            if (Constants.ENTITY_COLLECTION_TYPES.contains(attrTypeCode))
            {
                linksOrCasesWasUpdated = addObjectsToAttrValue(errorPrefix, object, objId, attribute, resolvedValue,
                        attrTypeCode, oldValueForCatItem, attrChanges);
            }
            else if (Constants.CaseListAttributeType.CODE.equals(attrTypeCode))
            {
                linksOrCasesWasUpdated = fillCaseListDirect(object, linksOrCasesWasUpdated, objId, attributeCode,
                        attribute, resolvedValue, attrChanges);
            }
            else if (Constants.DateTimeIntervalAttributeType.CODE.equals(attrTypeCode))
            {
                DateTimeIntervalAttributeType dateTimeIntervalAttributeType = attribute.getType()
                        .<DateTimeIntervalAttributeType> cast();
                if (dateTimeIntervalAttributeType.isNeedStoreUnits())
                {
                    linksOrCasesWasUpdated = updateAttrValuesForDateTimeIntervalDirect(object, attribute, metaClass,
                            objId, resolvedValue, attrChanges);
                }
            }
            else if (Constants.HyperlinkAttributeType.CODE.equals(attrTypeCode))
            {
                String columnName = (String)attrStrategyFactory
                        .getStrategy(attribute).getCommonProperties(attribute).get("column");

                Object oldValue = accessorHelper.getAttributeValueWithoutPermission(object, attributeCode);

                queryPartsToUpdateSimpleAttrs.add(
                        columnName + "." + TEXT + HQL_IN_OPEN_BRACE + columnName + TEXT_SUFFIX + "), " + columnName
                        + "." + URL + HQL_IN_OPEN_BRACE + columnName + URL_SUFFIX + ")");

                Hyperlink hyperlink = (Hyperlink)resolvedValue;
                propsForQuery.put(columnName + TEXT_SUFFIX, hyperlink.getText());
                propsForQuery.put(columnName + URL_SUFFIX, hyperlink.getURL());
                attrChanges.addAll(getChangesAsCollection(attribute, oldValue, resolvedValue));
            }
            else
            {
                Object oldValue = accessorHelper.getAttributeValueWithoutPermission(object, attributeCode);
                queryPartsToUpdateSimpleAttrs
                        .add(attributeCode + HQL_IN_OPEN_BRACE + attributeCode + ")");

                propsForQuery.put(attributeCode, resolvedValue);
                attrChanges.addAll(getChangesAsCollection(attribute, oldValue, resolvedValue));
            }
        }
        //Если в результате выполнения метода обновились только атрибуты типа "Набор ссылок на БО", 
        //"Набор элементов справочника", "Набор типов класса", то дату последнего изменения меняем отдельным
        // hql-запросом.
        //В противном случае добавляем обновление даты последнего изменения в hql-запрос, обновляющий простые
        // атрибуты объекта
        if (linksOrCasesWasUpdated && queryPartsToUpdateSimpleAttrs.isEmpty())
        {
            updateLastModifiedDate(objId, metaClass);
        }
        else if (!queryPartsToUpdateSimpleAttrs.isEmpty())
        {
            String hql = new StringBuilder("UPDATE ").append(metaClass.getFqn().getId())
                    .append(" set lastModifiedDate = :lastModifiedDate, ")
                    .append(StringUtilities.join(queryPartsToUpdateSimpleAttrs)).append("  WHERE id = :obj_Id")
                    .toString();
            List<String> hqlParamsAsStrForLog = new ArrayList<>();
            MutationQuery query = sessionFactory.getCurrentSession().createMutationQuery(hql);

            Date currentDate = new Date();
            query.setParameter("lastModifiedDate", currentDate);
            query.setParameter("obj_Id", objId);
            hqlParamsAsStrForLog.add("'lastModifiedDate' = " + currentDate);
            hqlParamsAsStrForLog.add("'obj_Id' = " + objId);

            for (Entry<String, Object> propsEntry : propsForQuery.entrySet())
            {
                Object paramValue = propsEntry.getValue();
                if (paramValue instanceof Long paramLong)
                {
                    query.setParameter(propsEntry.getKey(), paramLong, StandardBasicTypes.LONG);
                }
                else
                {
                    query.setParameter(propsEntry.getKey(), paramValue);
                }

                String paramValueAsString = "null";
                if (paramValue instanceof IUUIDIdentifiable paramUUID)
                {
                    paramValueAsString = paramUUID.getUUID();
                }
                else if (paramValue != null)
                {
                    paramValueAsString = paramValue.toString();
                }
                hqlParamsAsStrForLog.add("'propsEntry.getKey()' = " + paramValueAsString);

            }
            LOG.debug("Executing HQL: '" + hql + "' Params: " + StringUtilities.join(hqlParamsAsStrForLog));
            query.executeUpdate();
        }
        if (!attrChanges.isEmpty())
        {
            eventService.event(Categories.OBJECT_FAST_EDIT, object, operationHelper.getObjectParent(object),
                    formatterService.format(null, Presentations.BO_REFERENCE, object),
                    StringUtilities.join(attrChanges, ";\n\t") + ".");
        }
        for (Attribute attr : attrs.values())
        {
            if (addToSearchIndexIfNeed(object, metaClass.getFqn(), attr))
            {
                break;
            }

        }
    }

    private boolean updateAttrValuesForDateTimeIntervalDirect(IUUIDIdentifiable object, Attribute attribute,
            MetaClass metaClass, Long objId, Object resolvedValue, List<String> attrChanges)
    {
        String columnName = (String)(attrStrategyFactory.getStrategy(attribute))
                .getCommonProperties(attribute).get("column");
        String columnInterval = columnName + "_i";

        String tableName = "tbl_" + metaClass.getFqn().getId();
        String sql = "update " + tableName + " set "
                     + columnName + " = :ms, " + columnInterval + " = :intervalName "
                     + " where id = :objId";

        NativeQuery<?> query = sessionFactory.getCurrentSession().createNativeQuery(sql, Integer.class);
        query.addSynchronizedQuerySpace(tableName);
        query.setParameter("ms", ((DateTimeInterval)resolvedValue).getMs(), StandardBasicTypes.LONG);
        query.setParameter("intervalName", ((DateTimeInterval)resolvedValue).getIntervalName(),
                StandardBasicTypes.STRING);
        query.setParameter("objId", objId, StandardBasicTypes.LONG);

        LOG.atDebug()
                .setMessage("Executing SQL: '{}' Params: 'ms'={}, 'intervalName'={}, 'objId'={}")
                .addArgument(sql).addArgument(((DateTimeInterval)resolvedValue).getMs().toString())
                .addArgument(object).addArgument(object).log();

        Object oldValue = accessorHelper.getAttributeValueWithoutPermission(object, attribute.getCode());
        attrChanges.addAll(getChangesAsCollection(attribute, oldValue, resolvedValue));

        query.executeUpdate();
        return true;
    }

    /**
     * Изменяет атрибут "Дата последнего изменения"
     *
     * @param objId
     * @param metaClass
     */
    private void updateLastModifiedDate(Long objId, MetaClass metaClass)
    {
        String hql = new StringBuilder("UPDATE ").append(metaClass.getFqn().getId())
                .append(" set lastModifiedDate = :currentDate").append("  WHERE id = :objId").toString();

        MutationQuery query = sessionFactory.getCurrentSession().createMutationQuery(hql);
        Date currentDate = new Date();
        query.setParameter("currentDate", currentDate);
        query.setParameter("objId", objId);
        LOG.debug("Executing HQL for update lastModifiedDate: '" + hql + "' Params: 'objId'=" + objId
                  + ",'currentDate'=" + currentDate.toString());
        query.executeUpdate();
    }
}
