package ru.naumen.core.server.script.spi.resolvers;

import static ru.naumen.core.shared.Constants.PARENT_ATTR;

import java.util.ArrayDeque;
import java.util.Collection;
import java.util.Deque;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.server.attrdescription.resolvers.UUIDIdentifiableResolver;
import ru.naumen.core.server.attrdescription.resolvers.UUIDResover;
import ru.naumen.core.server.bo.DaoHelper;
import ru.naumen.core.server.treefilter.IsActivePredicate;
import ru.naumen.core.shared.Constants.Root;
import ru.naumen.core.shared.Constants.Team;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Обрабатывает результаты работы скрипта фильтрации атрибутов типа "Ссылка на БО", строит коллекцию уидов, без
 * поднятия объектов
 * <AUTHOR>
 * @since 01.01.2021
 **/
@Component
public class ScriptsUUIDsResolver extends ScriptTreeObjectsResolverBase
{
    private final DaoHelper daoHelper;
    private final UUIDResover uuidResolver;
    private final UUIDIdentifiableResolver boResolver;

    @Inject
    public ScriptsUUIDsResolver(DaoHelper daoHelper,
            UUIDResover uuidResolver,
            @Named("UUIDIdentifiableResolver") UUIDIdentifiableResolver boResolver)
    {
        this.daoHelper = daoHelper;
        this.uuidResolver = uuidResolver;
        this.boResolver = boResolver;
    }

    /**
     * Получим список уидов
     */
    public Set<String> resolve(StopWatch sw, Collection<Object> rawValue, ClassFqn classFqn, ClassFqn sourceFqn,
            String attributeCode, boolean withFolders, boolean allowRemoved)
    {
        ObjectResolveContext context = new ObjectResolveContext();
        context.classFqn = classFqn;
        context.sourceFqn = sourceFqn;
        context.withFolders = withFolders;
        context.attributeCode = attributeCode;
        context.allowRemoved = allowRemoved;

        return doResolveUUIDs(sw, rawValue, context);
    }

    protected Set<String> prepareObjects(Collection<Object> rawValue, ResolveContext context)
    {
        Set<String> uuidsToResolve = new HashSet<>(rawValue.size());

        Deque<Object> stack = new ArrayDeque<>(rawValue);

        while (!stack.isEmpty())
        {
            final Object obj = stack.pop();
            if (obj instanceof Collection<?>)
            {
                stack.addAll((Collection<?>)obj);
            }
            else if (isUuid(obj))
            {
                uuidsToResolve.add(uuidResolver.doResolve(obj));
            }
            else if (obj instanceof IUUIDIdentifiable)
            {
                IUUIDIdentifiable bo = boResolver.doResolve(obj);
                if (!IsActivePredicate.INSTANCE.test(bo) && !context.allowRemoved)
                {
                    //Игнорируем архивные объекты
                    continue;
                }
                uuidsToResolve.add(bo.getUUID());
            }
        }
        return uuidsToResolve;
    }

    protected Set<String> filter(Set<String> uuids, ResolveContext context)
    {
        return filterPerformers(context, filterByClass(uuids, context));
    }

    /**
     * @return uuidы, отфильтрованные по классу объектов атрибута
     */
    private Set<String> filterByClass(Set<String> uuids, ResolveContext context)
    {
        final ObjectResolveContext ctx = (ObjectResolveContext)context;
        return uuids.stream()
                .filter(UuidHelper.TO_PREFIX.andThen(s -> Objects.equals(s, ctx.classFqn.getId())
                                                          || ctx.attributeCode.equals(PARENT_ATTR) && Objects.equals(s,
                        Root.FQN.getId()))::apply)
                .collect(Collectors.toSet());
    }

    /**
     * @return uuidы объектов, отфильтрованные по признаку "Исполнитель" (для атрибута "Руководитель" в командах).
     */
    private Set<String> filterPerformers(ResolveContext context, Set<String> uuidsToResolve)
    {
        final ObjectResolveContext ctx = (ObjectResolveContext)context;
        if (!Team.FQN.equals(ctx.sourceFqn) || !Team.LEADER.equals(ctx.attributeCode))
        {
            return uuidsToResolve;
        }

        uuidsToResolve.retainAll(daoHelper.getPerformerUUIDs(Team.FQN));
        return uuidsToResolve;
    }
}