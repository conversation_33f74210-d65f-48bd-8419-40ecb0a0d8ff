package ru.naumen.core.server.script.spi.resolvers;

import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.hibernate.query.Query;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import com.google.common.base.Splitter;
import com.google.common.collect.HashMultimap;

import java.util.ArrayList;
import java.util.HashMap;

import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;

import java.util.HashSet;

import com.google.gson.JsonElement;
import com.google.gson.JsonParseException;
import com.google.gson.JsonParser;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.server.TreeUUIDIdentifiable;
import ru.naumen.core.server.attrdescription.resolvers.UUIDIdentifiableResolver;
import ru.naumen.core.server.attrdescription.resolvers.UUIDResover;
import ru.naumen.core.server.script.spi.AggregateContainerWrapper;
import ru.naumen.core.server.script.spi.LazyScriptTreeDtObject;
import ru.naumen.core.server.treefilter.IsActivePredicate;
import ru.naumen.core.server.util.StopWatchFactory;
import ru.naumen.core.shared.AggregateContainer;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.HasParent;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.ClassFqnHelper;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Обрабатывает результаты работы скрипта фильтрации агрегирующих атрибутов,
 * строит иерархию {@link LazyScriptTreeDtObject}, не загружая объекты целиком,
 * что позволяет ускорить преобразование и построение дерева.
 * <AUTHOR>
 * @since Jan 28, 2016
 */
@Component
public class ScriptAggregateObjectsResolver extends ScriptTreeObjectsResolverBase
{
    protected class AggregateResolveContext extends ResolveContext
    {
        public Multimap<String, String> uuidMap = null;
        public boolean isTeamPermitted = false;
        public boolean isOuPermitted = false;
    }

    private static final Logger LOG = LoggerFactory.getLogger(ScriptAggregateObjectsResolver.class);

    private static final Splitter COMMA_SPLITTER = Splitter.on(',');
    private static final Splitter COLON_SPLITTER = Splitter.on(':');

    private static final String EMPLOYEE_TEAMS_HQL =
            "SELECT e.id, t.id, t.metaCaseId, t.title FROM employee AS e INNER JOIN e.teams AS t "
            + "WHERE e.id IN (:ids) AND e.removed = false AND t.removed = false";

    @Inject
    private UUIDResover uuidResolver;
    @Inject
    @Named("UUIDIdentifiableResolver")
    private UUIDIdentifiableResolver boResolver;

    /**
     * Строит иерархию для указанных объектов - значений агрегирующих атрибутов.
     * @param rawValue список "сырых" объектов
     * @param isTeamPermitted допускается ли указание команды в агрегирующем атрибуте
     * @param isOuPermitted допускается ли указание отдела в агрегирующем атрибуте
     * @param withFolders признак необходимости загрузки папок для представления дерева
     * @param allowRemoved допустимы ли архивные объекты в результирующей иерархии
     * @return список объектов с загруженной иерархией
     */
    @SuppressWarnings("unchecked")
    public Object resolve(Object rawValue, boolean isTeamPermitted, boolean isOuPermitted, boolean withFolders,
            boolean allowRemoved)
    {
        StopWatch sw = StopWatchFactory.create("ScriptAggregateObjectsResolver", LOG.isDebugEnabled());

        AggregateResolveContext context = new AggregateResolveContext();
        context.isTeamPermitted = isTeamPermitted;
        context.isOuPermitted = isOuPermitted;
        context.withFolders = withFolders;
        context.allowRemoved = allowRemoved;

        rawValue = doResolve(sw, (Collection<Object>)rawValue, context);

        if (LOG.isDebugEnabled())
        {
            LOG.debug("\n" + sw.prettyPrint());
        }

        return rawValue;
    }

    @Override
    protected Set<String> filter(Set<String> uuids, ResolveContext context)
    {
        Set<String> result = new HashSet<>();
        for (String uuid : uuids)
        {
            if (null == context.cache.get(uuid))
            {
                result.add(uuid);
            }
        }
        return result;
    }

    @Override
    protected Set<String> prepareObjects(Collection<Object> rawValue, ResolveContext context)
    {
        Multimap<String, String> uuidMap = resolveAggregateObjects(rawValue, context);
        ((AggregateResolveContext)context).uuidMap = uuidMap;
        Set<String> uuidsToResolve = new HashSet<>();
        for (Map.Entry<String, String> entry : uuidMap.entries())
        {
            if (!context.cache.containsKey(entry.getKey()))
            {
                uuidsToResolve.add(entry.getKey());
            }
            if (null != entry.getValue() && !context.cache.containsKey(entry.getValue()))
            {
                uuidsToResolve.add(entry.getValue());
            }
        }
        return uuidsToResolve;
    }

    @SuppressWarnings("unchecked")
    @Override
    protected void processResult(ResolveContext context)
    {
        List<Object> result = new ArrayList<>();
        Multimap<String, String> uuidMap = ((AggregateResolveContext)context).uuidMap;
        for (Map.Entry<String, String> entry : uuidMap.entries())
        {
            Object object = context.cache.get(entry.getKey());
            if (!(object instanceof IUUIDIdentifiable))
            {
                continue;
            }
            TreeUUIDIdentifiable parent = null;
            if (null != entry.getValue())
            {
                Object parentEntry = context.cache.get(entry.getValue());
                if (!(parentEntry instanceof IUUIDIdentifiable))
                {
                    continue;
                }
                parent = new TreeUUIDIdentifiable((IUUIDIdentifiable)parentEntry, null);
            }
            else if (metainfoService.getClassFqn(object).fqnOfClass().equals(Constants.Employee.FQN))
            {
                if (object instanceof HasParent<?>)
                {
                    parent = new TreeUUIDIdentifiable(((HasParent<IUUIDIdentifiable>)object).getParent(), null);
                }
                else if (object instanceof DtObject)
                {
                    parent = new TreeUUIDIdentifiable(
                            ((DtObject)object).<IUUIDIdentifiable> getProperty(Constants.PARENT_ATTR), null);
                }
            }
            result.add(new TreeUUIDIdentifiable((IUUIDIdentifiable)object, parent));
        }
        context.result = result;
    }

    /**
     * Загружает идентификаторы команд для указанных сотрудников.
     * @param unresolvedEmployeeIds список ID сотрудников
     * @param uuidMap [объект: родитель]
     */
    private void loadTeams(List<Long> unresolvedEmployeeIds, Multimap<String, String> uuidMap, ResolveContext context)
    {
        for (List<Long> batch : Lists.partition(unresolvedEmployeeIds, BATCH_SIZE))
        {
            Query query = sessionFactory.getCurrentSession().createQuery(EMPLOYEE_TEAMS_HQL);
            query.setParameterList("ids", batch).setCacheable(true);
            @SuppressWarnings("unchecked")
            List<Object> objs = query.list();
            String prefix = Constants.Team.FQN.asString();
            for (Object obj : objs)
            {
                Object[] row = (Object[])obj;
                String uuid = UuidHelper.toUuid(row[1].toString(), prefix);
                ClassFqn fqn = ClassFqn.parse(prefix, (String)row[2]);
                String title = (String)row[3];
                SimpleDtObject teamDto = new SimpleDtObject(uuid, title, fqn);
                teamDto.setProperty(Constants.PARENT_ATTR, context.rootUUID);
                uuidMap.put(UuidHelper.toUuid(row[0].toString(), Constants.Employee.FQN.asString()), uuid);
                context.cache.put(uuid, teamDto);
            }
        }
    }

    /**
     * Сохраняет UUID, если он таковым является, в map [UUID prefix: UUID].
     */
    private void putAsUuid(Object rawValue, Map<ClassFqn, String> container, ResolveContext context)
    {
        String uuid = resolveUuid(rawValue, context);
        if (null != uuid)
        {
            ClassFqn fqn = ClassFqnHelper.toClassId(uuid);
            container.put(fqn, uuid);
        }
    }

    /**
     * {@link #putAsUuid(Object, Map, ResolveContext)} для нескольких объектов.
     */
    private void putAsUuids(Iterable<? extends Object> rawValues, Map<ClassFqn, String> container,
            ResolveContext context)
    {
        for (Object rawValue : rawValues)
        {
            putAsUuid(rawValue, container, context);
        }
    }

    /**
     * Извлекает значение агрегирующего атрибута в виде map [FQN: UUID].
     */
    @SuppressWarnings("unchecked")
    private Collection<Map<ClassFqn, String>> resolveAggregateMaps(Object rawValue, ResolveContext context)
    {
        Collection<Map<ClassFqn, String>> result = new ArrayList<>();
        if (rawValue instanceof Map<?, ?> && !(rawValue instanceof DtObject))
        {
            Map<Object, Object> map = (Map<Object, Object>)rawValue;
            for (Map.Entry<Object, Object> entry : map.entrySet())
            {
                Object parent = entry.getKey();
                Object value = entry.getValue();
                Collection<Object> children = value instanceof Collection<?> ? (Collection<Object>)value : Lists
                        .newArrayList(value);
                for (Object child : children)
                {
                    Map<ClassFqn, String> single = new HashMap<>();
                    putAsUuid(parent, single, context);
                    putAsUuid(child, single, context);
                    if (!single.isEmpty())
                    {
                        result.add(single);
                    }
                }
            }
        }
        Map<ClassFqn, String> single = new HashMap<>();
        if (rawValue instanceof AggregateContainer)
        {
            AggregateContainer ac = (AggregateContainer)rawValue;
            single.put(Constants.Employee.FQN, resolveUuid(ac.getEmployee(), context));
            single.put(Constants.OU.FQN, resolveUuid(ac.getOu(), context));
            single.put(Constants.Team.FQN, resolveUuid(ac.getTeam(), context));
        }
        if (rawValue instanceof String)
        {
            String rawString = (String)rawValue;
            try
            {
                rawValue = new JsonParser().parse(rawString);
                if (null == rawValue)
                {
                    return null;
                }
            }
            catch (JsonParseException e)
            {
                // Значения просто разделены запятой или двоеточием.
                if (rawString.contains(","))
                {
                    putAsUuids(COMMA_SPLITTER.split(rawString), single, context);
                }
                else if (rawString.contains(":"))
                {
                    putAsUuids(COLON_SPLITTER.split(rawString), single, context);
                }
                else
                {
                    putAsUuid(rawString, single, context);
                }
            }
        }
        if (rawValue instanceof JsonElement)
        {
            JsonElement obj = (JsonElement)rawValue;
            if (obj.isJsonArray())
            {
                for (JsonElement e : obj.getAsJsonArray())
                {
                    if (!e.isJsonPrimitive())
                    {
                        continue;
                    }
                    putAsUuid(e.getAsString(), single, context);
                }
            }
            else if (obj.isJsonPrimitive())
            {
                putAsUuid(obj.getAsString(), single, context);
            }
        }
        else if (rawValue instanceof AggregateContainerWrapper)
        {
            AggregateContainerWrapper acw = (AggregateContainerWrapper)rawValue;
            single.put(Constants.Employee.FQN, resolveUuid(acw.getEmployee(), context));
            single.put(Constants.OU.FQN, resolveUuid(acw.getOu(), context));
            single.put(Constants.Team.FQN, resolveUuid(acw.getTeam(), context));
        }
        else if (rawValue instanceof List<?>)
        {
            for (Object obj : (List<Object>)rawValue)
            {
                putAsUuid(obj, single, context);
            }
        }
        else
        {
            putAsUuid(rawValue, single, context);
        }
        if (!single.isEmpty())
        {
            result.add(single);
        }
        return result;
    }

    /**
     * Преобразует "сырые" объекты (значения агрегирующих атрибутов) в map [объект: родитель].
     */
    private Multimap<String, String> resolveAggregateObjects(Collection<Object> rawValue, ResolveContext context)
    {
        Multimap<String, String> result = HashMultimap.create();
        List<Long> unresolvedEmployees = new ArrayList<>();
        for (Object obj : rawValue)
        {
            for (Map<ClassFqn, String> value : resolveAggregateMaps(obj, context))
            {
                if (null == value)
                {
                    continue;
                }
                String employeeUuid = value.get(Constants.Employee.FQN);
                String teamUuid = value.get(Constants.Team.FQN);
                String ouUuid = value.get(Constants.OU.FQN);
                if (((AggregateResolveContext)context).isTeamPermitted)
                {
                    if (null == employeeUuid)
                    {
                        if (null != teamUuid)
                        {
                            result.put(teamUuid, null);
                        }
                    }
                    else if (null != teamUuid)
                    {
                        result.put(employeeUuid, teamUuid);
                    }
                    else if (null == ouUuid)
                    {
                        unresolvedEmployees.add(UuidHelper.toId(employeeUuid));
                    }
                }
                if (((AggregateResolveContext)context).isOuPermitted)
                {

                    if (null == employeeUuid)
                    {
                        if (null != ouUuid)
                        {
                            result.put(ouUuid, null);
                        }
                    }
                    else if (null != ouUuid || null == teamUuid)
                    {
                        result.put(employeeUuid, ouUuid);
                    }
                }
            }
        }
        if (!unresolvedEmployees.isEmpty())
        {
            loadTeams(unresolvedEmployees, result, context);
        }
        return result;
    }

    /**
     * Извлекает UUID объекта.
     */
    private String resolveUuid(Object rawValue, ResolveContext context)
    {
        if (isUuid(rawValue))
        {
            return uuidResolver.doResolve(rawValue);
        }
        else if (rawValue instanceof IUUIDIdentifiable)
        {
            IUUIDIdentifiable bo = boResolver.doResolve(rawValue);
            if (!IsActivePredicate.INSTANCE.test(bo))
            {
                // Игнорируем архивные объекты.
                return null;
            }
            context.cache.put(bo.getUUID(), bo);
            context.result.add(bo);
            return bo.getUUID();
        }
        return null;
    }
}
