package ru.naumen.core.server.advlist.templates;

import jakarta.inject.Inject;

import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;

import ru.naumen.metainfo.server.spi.dispatch.sec.BeforeDeleteSecProfileEvent;
import ru.naumen.metainfo.shared.elements.sec.Profile;
import ru.naumen.metainfo.shared.templates.list.ListTemplate;

/**
 * Ищет использование удаляемого профиля в настройках шаблонов списков.
 * В случае если находит - удаляет без предупреждения
 *
 * <AUTHOR>
 * @since 28.04.2018
 */
@Component
public class ListTemplateDelProfileEventListener implements ApplicationListener<BeforeDeleteSecProfileEvent>
{
    @Inject
    private ListTemplateService templateService;

    @Override
    public void onApplicationEvent(BeforeDeleteSecProfileEvent event)
    {
        for (ListTemplate template : templateService.getAll())
        {
            if (template.getTemplate().getProfiles().remove(((Profile)event.getSource()).getCode()))
            {
                templateService.saveTemplate(template);
            }
        }
    }
}
