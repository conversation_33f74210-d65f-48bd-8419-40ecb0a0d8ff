package ru.naumen.core.server.script.api.metainfo;

import ru.naumen.metainfo.shared.elements.wf.Condition;

import com.google.common.base.Function;

public class ConditionWrapper implements IConditionWrapper
{
    public static final Function<Condition, IConditionWrapper> WRAPPER = new Function<Condition, IConditionWrapper>()
    {
        @Override
        public ConditionWrapper apply(Condition input)
        {
            return null == input ? null : new ConditionWrapper(input);
        }
    };

    private final Condition condition;

    public ConditionWrapper(Condition condition)
    {
        this.condition = condition;
    }

    @Override
    public String getCode()
    {
        return condition.getCode();
    }

    @Override
    public IMetaClassWrapper getDeclaredMetaClass()
    {
        return MetaClassWrapper.FQN_WRAPPER.apply(condition.getDeclaredMetaClass());
    }

    @Override
    public String getTitle()
    {
        return condition.getTitle();
    }

    @Override
    public boolean isPre()
    {
        return Boolean.TRUE.equals(condition.isPre());
    }

    @Override
    public String toString()
    {
        return "Condition '" + this.getTitle() + "' (Code: '" + this.getCode() + "')";
    }
}
