package ru.naumen.core.server.script.spi;

import java.util.Map;

import ru.naumen.core.shared.HasParent;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;

import java.util.HashMap;

/**
 * <AUTHOR>
 * @since 3.06.2014
 */
public class LazyScriptTreeDtObject extends AbstractLazyScriptDtObject implements HasParent<IUUIDIdentifiable>
{
    private String parentCode;

    /**
     * Закэшированные значения, чтобы избежать обращений к метаинфе
     */
    private Map<String, Boolean> hasProperies;

    public LazyScriptTreeDtObject(DtObject delegate, ScriptDtOHelper helper, ScriptUtils scriptUtils, String parentCode)
    {
        super(delegate, helper, scriptUtils);
        this.parentCode = parentCode;
    }

    @Override
    public IUUIDIdentifiable getParent()
    {
        return parentCode != null ? (IUUIDIdentifiable)get(parentCode) : null;
    }

    @Override
    public boolean hasProperty(String name)
    {
        Boolean result = hasProperies != null ? hasProperies.get(name) : null;
        return result != null ? result : super.hasProperty(name);
    }

    public void setHasProperty(String name, Boolean value)
    {
        if (hasProperies == null)
        {
            hasProperies = new HashMap<>();
        }
        hasProperies.put(name, value);
    }
}
