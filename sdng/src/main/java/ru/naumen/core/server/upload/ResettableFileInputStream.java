package ru.naumen.core.server.upload;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.Field;

import ru.naumen.commons.shared.FxException;

/**
 * Обертка над файловым потоком, которая умеет переоткрывать его в момент вызова reset()
 * Целесообразность в том, что при работе с BLOB потоково создается BlobProxy, в котором поток закрывается после
 * создания блоба в PgPreparedStatement.createBlob, однако если работать с BLOB в одной транзакции (загрузить и сразу к
 * нему обратиться), то вернется
 * тот же самый прокси с закрытым потоком и будет попытка выполнить reset(). Оригинальный FileInputStream не
 * поддерживает эту операцию, а оборачивать в BufferedInputStream, который поддерживает эту операцию тоже не вариант,
 * потому что при получении внутреннего массива байт есть проверка на закрытие потока. А поток закрыт.
 *
 * <AUTHOR>
 * @since 26.09.19
 *
 **/
public class ResettableFileInputStream extends InputStream
{
    private static String getPath(InputStream is) throws Exception
    {
        final Field field = is.getClass().getDeclaredField("path");
        field.setAccessible(true);
        return (String)field.get(is);
    }

    private final String path;
    private InputStream stream;

    public ResettableFileInputStream(FileInputStream is)
    {
        try
        {
            path = getPath(is);
            stream = new BufferedInputStream(is);
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    @Override
    public void reset() throws IOException
    {
        if (path != null && new File(path).exists())
        {
            this.close();
            stream = new BufferedInputStream(new FileInputStream(path));
        }
        else
        {
            throw new IllegalStateException("Error reopening FileInputStream. Wrong path '" + path + "' or file "
                                            + "does not exist.");
        }
    }

    @Override
    public int read() throws IOException
    {
        return stream.read();
    }

    @Override
    public void close() throws IOException
    {
        if (stream != null)
        {
            stream.close();
        }
    }
}