package ru.naumen.core.server.upload.strategies;

import org.apache.commons.fileupload2.core.FileItem;

import ru.naumen.core.server.upload.spi.UploadFileItemContext;

/**
 * Стратегия загрузки файлов в систему
 *
 * <AUTHOR>
 * @since Aug 31, 2020
 */
public interface UploadStrategy
{
    /**
     * Загрузить файл
     * @param uploadFileItemContext файловый контекст
     */
    void add(final UploadFileItemContext uploadFileItemContext);

    /**
     * Удалить файл
     * @param uuid ууид DBFileItem
     */
    void delete(final String uuid, final boolean reuse);

    /**
     * Получить DBFileItem по уииду
     * @param uuid ууид DBFileItem
     */
    FileItem get(final String uuid);

    /**
     * Узнать размер DBFileItem
     * @param uuid DBFileItem
     */
    long getFileSize(final String uuid);
}