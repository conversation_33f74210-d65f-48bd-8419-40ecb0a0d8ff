package ru.naumen.core.server.script.libraries.scripting.script;

import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.libraries.LibrariesService;
import ru.naumen.core.server.script.storage.ScriptStorageServiceBean;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;
import ru.naumen.metainfo.server.spi.MetainfoServicePersister;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Обработчик результата сканирования аннотаций скриптов в библиотеках
 */
@Component
class ScriptsScannedScriptsSetProcessor
{
    private static final Logger LOG = LoggerFactory.getLogger(ScriptsScannedScriptsSetProcessor.class);
    private final LibrariesService librariesService;
    private final ScriptStorageServiceBean scriptStorageService;
    private final MetainfoServicePersister metainfoService;

    public ScriptsScannedScriptsSetProcessor(LibrariesService librariesService,
            ScriptStorageServiceBean scriptStorageService,
            MetainfoServicePersister metainfoService)
    {
        this.librariesService = librariesService;
        this.scriptStorageService = scriptStorageService;
        this.metainfoService = metainfoService;
    }

    @EventListener
    public void process(ScriptsSetRefreshedEvent scriptsSetRefreshedEvent)
    {
        final Map<String, Set<Script>> libraryPerScriptsCodes = scriptsSetRefreshedEvent.getLibraryPerScripts();
        for (final Entry<String, Set<Script>> entry : libraryPerScriptsCodes.entrySet())
        {
            final String libraryName = entry.getKey();
            final Set<Script> scriptSet = entry.getValue();
            final ScriptLibrary library = librariesService.getLibrary(libraryName);
            if (library != null)
            {
                final Set<String> scripts = library.getScripts();
                scripts.clear();
                scripts.addAll(scriptSet.stream().map(Script::getCode).collect(Collectors.toSet()));
                scriptStorageService.deleteScripts(scripts);
                metainfoService.deleteScriptsByCode(scripts);
                librariesService.saveLibrary(library);
                scriptStorageService.resetScriptUsageCache();
            }
            else
            {
                LOG.warn("There's no library with name {} for scripts' codes addition.", libraryName);
            }
        }
    }
}
