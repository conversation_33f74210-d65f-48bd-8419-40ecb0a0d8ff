package ru.naumen.core.server.script.libraries.scripting.script;

import static ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessorUtils.createScript;
import static ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessorUtils.extractScriptCode;
import static ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessorUtils.extractTitles;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import io.github.classgraph.AnnotationInfo;
import io.github.classgraph.AnnotationParameterValueList;
import io.github.classgraph.ClassInfo;
import io.github.classgraph.ClassInfoList;
import io.github.classgraph.ScanResult;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.script.libraries.ScanProcessResult;
import ru.naumen.core.server.script.libraries.registration.scripts.InternalAutomationScript;
import ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessor;
import ru.naumen.core.server.script.libraries.scripting.DocumentationExtractor;
import ru.naumen.core.server.script.libraries.scripting.ScriptLibraryValidator;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Обработчик результата сканирования аннотаций скриптов в библиотеках.
 */
@Component
public class ScriptClassPathScanProcessor implements ClassPathScanProcessor<Script>
{
    private static final Logger LOG = LoggerFactory.getLogger(ScriptClassPathScanProcessor.class);

    private final ScriptLibraryValidator scriptLibraryValidator;
    private final DocumentationExtractor documentationExtractor;

    @Inject
    public ScriptClassPathScanProcessor(ScriptLibraryValidator scriptLibraryValidator,
            DocumentationExtractor extractDocumentation)
    {
        this.scriptLibraryValidator = scriptLibraryValidator;
        this.documentationExtractor = extractDocumentation;
    }

    @Override
    public ScanProcessResult<Script> process(
            ScanResult scanResult,
            ClassLoader classLoader,
            @Nullable String embeddedApplicationCode)
    {
        // код скрипта -> скрипт как объект
        final Map<String, Script> scriptCodeToScriptObject = new HashMap<>();
        // имя библиотеки -> коллекция скриптов, как объектов, в этой библиотеке
        final Map<String, Set<String>> libraryNameToScriptCodesInCurrentLibrary = new HashMap<>();

        final Set<String> visitedScriptCodes = new HashSet<>();
        final ClassInfoList classesWithAnnotation =
                scanResult.getClassesWithAnnotation(InternalAutomationScript.class.getName());
        for (final ClassInfo classInfo : classesWithAnnotation)
        {
            Pair<String, Script> scriptCodeAndScriptObj = processClass(classLoader, classInfo);
            String scriptCode = scriptCodeAndScriptObj.left;
            Script script = scriptCodeAndScriptObj.right;

            List<LocalizedString> titles = script.getTitle();
            scriptLibraryValidator.validateScriptCode(scriptCode, visitedScriptCodes, titles);

            final String libraryName = classInfo.getClasspathElementFile().getName(); // имя jar-ника
            scriptCodeToScriptObject.put(scriptCode, script);
            libraryNameToScriptCodesInCurrentLibrary.computeIfAbsent(libraryName, k -> new HashSet<>());
            libraryNameToScriptCodesInCurrentLibrary.get(libraryName).add(scriptCode);
        }
        final Map<String, Set<String>> immutableLibraryNameToScriptCodesInCurrentLibrary =
                libraryNameToScriptCodesInCurrentLibrary.entrySet().stream()
                        .collect(Collectors.toMap(
                                Entry::getKey,
                                entry -> Set.copyOf(entry.getValue())
                        ));
        return new ScanProcessResult<>(Map.copyOf(scriptCodeToScriptObject),
                immutableLibraryNameToScriptCodesInCurrentLibrary);
    }

    private Pair<String, Script> processClass(ClassLoader classLoader, ClassInfo classInfo)
    {
        final AnnotationInfo annotationInfo = classInfo.getAnnotationInfo(InternalAutomationScript.class.getName());

        ScriptLibraryValidator.validateSuperclass(classInfo);

        final String annotatedClassName = classInfo.getName();
        LOG.trace("Found an internal automation annotations for the class [{}]", annotatedClassName);

        final AnnotationParameterValueList parameterValues = annotationInfo.getParameterValues();

        final String scriptCode = extractScriptCode(annotatedClassName, parameterValues);
        final ArrayList<LocalizedString> titles = extractTitles(parameterValues, scriptCode);
        scriptLibraryValidator.validateScriptTitle(titles, scriptCode);
        final HashMap<String, String> localeToDoc = documentationExtractor.extractDocumentation(classLoader,
                annotatedClassName, parameterValues, scriptCode, classInfo.getClasspathElementFile().getName());

        final Script script = createScript(classInfo, scriptCode, titles, localeToDoc);

        return Pair.create(scriptCode, script);
    }
}
