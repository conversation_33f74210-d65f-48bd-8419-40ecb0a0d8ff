package ru.naumen.core.server.hquery.criterion;

import java.util.Objects;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * Copyright Naumen Ltd
 * User: Vlad
 * Date: 06.02.2006
 * Time: 12:30:12
 */
public class SimplePropertyCriterion extends AbstractHCriterion
{
    private final String op;
    private final HColumn _property2;

    public SimplePropertyCriterion(HColumn property1, String op, HColumn property2)
    {
        super(property1);
        this.op = op;
        this._property2 = property2;
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        sb.append(property.getHQL(builder));
        sb.append(op);
        sb.append(_property2.getHQL(builder));
    }

    @Override
    public String toString()
    {
        return "" + property + op + _property2;
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new SimplePropertyCriterion(property, op, _property2);
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        SimplePropertyCriterion that = (SimplePropertyCriterion)o;
        if (_property2 == null || that._property2 == null)
        {
            return _property2 == that._property2 && op.equals(that.op);
        }
        /*
         * Используем "_property2.toString()" для разрыва возможных циклических связей между объектами
         * Criteria и Criterion. А иначе StackOverflowError.
         */
        return op.equals(that.op) &&
               Objects.equals(_property2.toString(), that._property2.toString());
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), op, _property2 == null ? 0 : _property2.toString());
    }
}
