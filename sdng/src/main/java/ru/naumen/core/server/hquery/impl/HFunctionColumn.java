package ru.naumen.core.server.hquery.impl;

import jakarta.annotation.Nullable;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HColumn;

/**
 * Колонка-функция над другой колонкой
 *
 * <AUTHOR>
 * @since 19.07.2021
 */
public class HFunctionColumn extends AbstractHColumn
{
    private final String functionPattern;
    private final HColumn column;

    /**
     * Создать колонку-функцию
     * @param functionPattern шаблон функции, должен содержать "%s"
     * @param column колонка или выражение
     * @param alias псевдоним
     */
    public HFunctionColumn(String functionPattern, HColumn column, @Nullable String alias)
    {
        super(alias);
        if (!functionPattern.contains("%s"))
        {
            throw new IllegalArgumentException("Pattern (functionPattern) must contains \"%s\"");
        }
        this.functionPattern = functionPattern;
        this.column = column;
    }

    @Override
    public void setParameters(Query query)
    {
        column.setParameters(query);
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        return String.format(functionPattern, column.getHQL(builder));
    }

    @Override
    public String toString()
    {
        return "HFunctionColumn{" +
               "functionPattern='" + functionPattern + '\'' +
               ", column=" + column +
               ", alias=" + getAlias() +
               '}';
    }
}
