package ru.naumen.core.server.script.api.eventaction;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import com.google.common.collect.Lists;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.api.metainfo.ITagWrapper;
import ru.naumen.core.server.script.api.metainfo.TagWrapper;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.server.userevents.UserEventActionService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.userevents.EventSource;
import ru.naumen.core.shared.userevents.UserEventContext;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.IClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.eventaction.EventAction;

/**
 * Обертка {@link EventAction} для использования в скриптах
 * <AUTHOR>
 * @since 11.03.2021
 */
public class EventActionWrapper implements IEvenActionWrapper
{
    private final EventAction eventAction;
    protected final MessageFacade messages;
    protected final MetainfoUtils metainfoUtils;
    private final TagService tagService;
    private final UserEventActionService userEventActionService;

    EventActionWrapper(EventAction eventAction)
    {
        this.eventAction = eventAction;
        messages = SpringContext.getInstance().getBean(MessageFacade.class);
        metainfoUtils = SpringContext.getInstance().getBean(MetainfoUtils.class);
        tagService = SpringContext.getInstance().getBean(TagService.class);
        userEventActionService = SpringContext.getInstance().getBean(UserEventActionService.class);
    }

    @Override
    public String getTitle()
    {
        return metainfoUtils.getLocalizedValue(eventAction.getTitle());
    }

    @Override
    public String getCode()
    {
        return eventAction.getCode();
    }

    @Override
    public String getDescription()
    {
        return metainfoUtils.getLocalizedValue(eventAction.getDescription());
    }

    @Override
    public List<IClassFqn> getObjects()
    {
        return new ArrayList<>(eventAction.getLinkedClasses());
    }

    @Override
    public String getEvent()
    {
        return messages.getMessage("eventActionType." + getEventCode());
    }

    @Override
    public String getEventCode()
    {
        return eventAction.getEvent().getEventType().name();
    }

    @Override
    public String getAction()
    {
        return messages.getMessage(eventAction.getAction().getActionType().getTitleCode());
    }

    @Override
    public String getActionCode()
    {
        return eventAction.getAction().getActionType().name();
    }

    @Override
    public List<ITagWrapper> getTags()
    {
        return eventAction.getTags().stream()
                .map(tagService::getTag)
                .filter(Objects::nonNull)
                .map(tag -> new TagWrapper(tag, metainfoUtils))
                .collect(Collectors.toList());
    }

    @Override
    public boolean isEnabled()
    {
        return eventAction.isOn();
    }

    @Override
    public List<IActionConditionWrapper> getConditions(boolean syncVerification)
    {
        return eventAction.getConditions(syncVerification).stream()
                .map(ActionConditionWrapper::new)
                .collect(Collectors.toList());
    }

    @Override
    public List<IActionConditionWrapper> getConditions()
    {
        return eventAction.getConditions().stream()
                .map(ActionConditionWrapper::new)
                .collect(Collectors.toList());
    }

    @Override
    public void execute(Object subject)
    {
        UserEventContext userEventContext = new UserEventContext();
        userEventContext.setObjectUuids(Lists.newArrayList(ApiUtils.getUuid(subject)));
        userEventContext.setEventUuid(getEventActionUUID());
        userEventContext.setFromMobile(false);
        userEventContext.setEventSource(EventSource.OBJECT_CARD);
        userEventContext.setObjectOnCardUuid(ApiUtils.getUuid(subject));
        userEventActionService.executeWithReadableException(userEventContext);
    }

    private String getEventActionUUID()
    {
        return UuidHelper.toUuid(eventAction.getId(), eventAction.getEvent().getEventType().toString());
    }

}