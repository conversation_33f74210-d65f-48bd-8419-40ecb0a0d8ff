package ru.naumen.core.server.script.libraries;

import static ru.naumen.core.server.script.libraries.validation.jar.JarSignValidation.INVALID_SIGNATURE_MESSAGE;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.util.Set;
import java.util.jar.JarFile;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.DigestUtils;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.AntivirusValidationException;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.export.ImportTarget;
import ru.naumen.core.server.script.libraries.applications.EmbeddedApplicationLibraryService;
import ru.naumen.core.server.script.libraries.scripting.ScannerScriptsFromLibrariesService;
import ru.naumen.core.server.script.libraries.storage.LibraryStorage;
import ru.naumen.core.server.script.libraries.validation.LibraryContentValidator;
import ru.naumen.core.server.script.modules.compile.AbstractScriptModulesCompilationService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;
import ru.naumen.metainfo.server.spi.MetainfoContainer;
import ru.naumen.metainfo.server.spi.MetainfoModification;
import ru.naumen.metainfo.server.spi.MetainfoModification.MetainfoRegion;
import ru.naumen.sec.server.AntivirusValidationService;

/**
 * Сервис для работы с jar библиотеками
 *
 * <AUTHOR>
 * @since 22.05.2020
 *
 * @see AbstractScriptModulesCompilationService
 */
@Component
public class CommonLibrariesService implements ImportTarget<MetainfoContainer, Boolean>, LibrariesService
{
    private static final Logger LOG = LoggerFactory.getLogger(CommonLibrariesService.class);
    public static final String EXPIRATION_DATE_ATTRIBUTE_NAME = "expirationDate";
    public static final String EXPIRATION_DATE_FORMAT = "dd.MM.yyyy";
    public static final String INVALID_JAR_FILE_MESSAGE_CODE = "library.validation.invalidJarFile";

    private final LibraryStorage libraryStorage;
    private final LibraryContentValidator libraryContentValidator;
    private final ScannerScriptsFromLibrariesService scannerScriptsFromLibrariesService;
    private final MetainfoModification metainfoModification;
    private final EmbeddedApplicationLibraryService embeddedApplicationLibraryService;
    private final AntivirusValidationService antivirusValidationService;
    private final MessageFacade messages;

    @Inject
    public CommonLibrariesService(
            LibraryStorage libraryStorage,
            LibraryContentValidator libraryContentValidator,
            ScannerScriptsFromLibrariesService scannerScriptsFromLibrariesService,
            MetainfoModification metainfoModification,
            EmbeddedApplicationLibraryService embeddedApplicationLibraryService,
            AntivirusValidationService antivirusValidationService,
            MessageFacade messages)
    {
        this.libraryStorage = libraryStorage;
        this.libraryContentValidator = libraryContentValidator;
        this.scannerScriptsFromLibrariesService = scannerScriptsFromLibrariesService;
        this.metainfoModification = metainfoModification;
        this.embeddedApplicationLibraryService = embeddedApplicationLibraryService;
        this.antivirusValidationService = antivirusValidationService;
        this.messages = messages;
    }

    @Override
    public void createLibrary(String name, byte[] content)
    {
        metainfoModification.modify(MetainfoRegion.SCRIPTS);
        final String contentMd5 = DigestUtils.md5DigestAsHex(content);
        final ScriptLibrary scriptLibrary = new ScriptLibrary();
        scriptLibrary.setChecksum(contentMd5);
        scriptLibrary.setContent(content);
        scriptLibrary.setName(name);
        doSaveLibrary(scriptLibrary);
    }

    @Override
    public void saveLibrary(ScriptLibrary library)
    {
        doSaveLibrary(library);
    }

    @Override
    public ScriptLibrary getLibrary(String name)
    {
        return libraryStorage.getLibrary(name);
    }

    /**
     * Непосредственно сохранение библиотеки
     * @param scriptLibrary сохраняемая библиотека
     * @return false, если такая библиотека существует, true если библиотеки не существует и произошло сохранение и
     * публикация события
     */
    private boolean doSaveLibrary(ScriptLibrary scriptLibrary)
    {
        final ScriptLibrary libraryFromStorage = libraryStorage.getLibrary(scriptLibrary.getName());
        final boolean libraryWithSameNameIsStored = libraryFromStorage != null;
        if (libraryWithSameNameIsStored && libraryFromStorage.getChecksum().equals(scriptLibrary.getChecksum()))
        {
            return false;
        }

        if (libraryWithSameNameIsStored && libraryFromStorage.getEmbeddedVersionChecksum() != null
            && scriptLibrary.getEmbeddedVersionChecksum() == null)
        {
            scriptLibrary.setEmbeddedVersionChecksum(libraryFromStorage.getEmbeddedVersionChecksum());
        }

        try
        {
            final Path path = Files.createTempFile("validation", scriptLibrary.getName());//NOSONAR старый код
            antivirusValidationService.verifyFile(scriptLibrary.getName(), Files.probeContentType(path),
                    scriptLibrary.getContent());
            try (JarFile jarFile = new JarFile(Files.write(path, scriptLibrary.getContent()).toFile()))
            {
                libraryContentValidator.validateContent(scriptLibrary, jarFile);
            }
            finally
            {
                Files.delete(path);
            }
        }
        catch (AntivirusValidationException e)
        {
            throw new FxException(messages.getMessage("library.validation.maliciousFile", scriptLibrary.getName()), e);
        }
        catch (SecurityException e)
        {
            throw new FxException(messages.getMessage(INVALID_SIGNATURE_MESSAGE, scriptLibrary.getName()), e);
        }
        catch (IOException e)
        {
            throw new FxException(messages.getMessage(INVALID_JAR_FILE_MESSAGE_CODE, scriptLibrary.getName()), e);
        }
        final Path libPath = libraryStorage.save(scriptLibrary);
        embeddedApplicationLibraryService.upload(libraryStorage.getLibraryPath(scriptLibrary), scriptLibrary);
        scannerScriptsFromLibrariesService.registerNewLibraries(Set.of(libPath), Set.of(), false, true);
        return true;
    }

    @Override
    public void deleteLibrary(String name)
    {
        final ScriptLibrary library = libraryStorage.getLibrary(name);
        if (library == null)
        {
            LOG.info("Library with name {} was not found", name);
            return;
        }
        if (!hasSameLibrary(library))
        {
            embeddedApplicationLibraryService.delete(libraryStorage.getLibraryPath(library), library);
        }
        final boolean isDeleted = libraryStorage.delete(library);
        if (isDeleted)
        {
            scannerScriptsFromLibrariesService.registerNewLibraries(Set.of(), Set.of(name), false, false);
            LOG.debug("Library {} has been deleted.", name);
        }
        else
        {
            LOG.debug("Library {} has not been deleted.", name);
        }
    }

    @Override
    public Set<String> getLibrariesNames()
    {
        return libraryStorage.getLibrariesNames();
    }

    @Override
    public boolean hasSameLibrary(ScriptLibrary library)
    {
        return libraryStorage.getAllLibraries().stream()
                .anyMatch(libraryFromStorage -> !library.getName().equals(libraryFromStorage.getName())
                                                && library.getChecksum().equals(libraryFromStorage.getChecksum()));
    }

    @Override
    public Boolean importObject(MetainfoContainer object)
    {
        boolean hasNewLibraries = false;
        for (final ScriptLibrary library : object.getLibraries())
        {
            boolean isCreated = doSaveLibrary(library);
            if (!isCreated)
            {
                LOG.info("Library with name {} and same content already exist", library.getName());
            }
            hasNewLibraries |= isCreated;
        }
        if (hasNewLibraries)
        {
            LOG.warn("New libraries were imported. Restart may be required in order to take effect");
        }
        return hasNewLibraries;
    }

    @Override
    public void reloadCluster()
    {
        scannerScriptsFromLibrariesService.resetLibraries(Set.copyOf(libraryStorage.reset()));
    }
}
