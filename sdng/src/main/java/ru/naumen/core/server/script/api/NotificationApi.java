package ru.naumen.core.server.script.api;

import static ru.naumen.core.shared.Constants.Modules.MOBILE_API;

import java.util.Collection;
import java.util.HashMap;
import java.util.Map;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import edu.umd.cs.findbugs.annotations.CheckForNull;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.modules.ModulesService;
import ru.naumen.core.server.push.Push;
import ru.naumen.core.server.push.PushService;
import ru.naumen.core.server.push.PushSettingsReloader;
import ru.naumen.core.server.push.ScriptNotificationMobileSender;
import ru.naumen.core.server.push.ScriptNotificationWebSender;
import ru.naumen.core.server.push.ServiceAccountLoader;
import ru.naumen.core.server.push.portal.PushPortal;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.api.push.INotificationApi;
import ru.naumen.core.server.script.api.push.IPushMessage;
import ru.naumen.core.server.style.templates.StyleTemplateStorageService;
import ru.naumen.core.server.style.templates.notifications.NotificationTemplateException;
import ru.naumen.core.server.style.templates.notifications.NotificationTemplateService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.PushStates;
import ru.naumen.core.shared.PushHeaderFormatType;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.shared.eventaction.push.PushPresentationType;

/**
 * API для отправки уведомлений в веб-интерфейс/мобильное приложение
 * <AUTHOR>
 * @since 5.12.18
 */
@Component("notification")
public class NotificationApi implements INotificationApi
{
    private static final Logger LOG = LoggerFactory.getLogger(NotificationApi.class);

    private class PushMessage implements IPushMessage
    {
        private String text;
        private String link;
        private String subject;
        private boolean useHtml = true;
        private PushHeaderFormatType headerFormat = PushHeaderFormatType.SystemNameValue;
        private PushPresentationType presentationType = PushPresentationType.AlwaysInTheInterface;

        public PushMessage(String text)
        {
            this.text = text;
        }

        @Override
        public String getText()
        {
            return text;
        }

        @Override
        public PushMessage setLink(String link)
        {
            this.link = link;
            return this;
        }

        @Override
        public IPushMessage setSubject(String subject)
        {
            this.subject = subject;
            return this;
        }

        @Override
        public IPushMessage setUseHtml(boolean useHtml)
        {
            this.useHtml = useHtml;
            return this;
        }

        @Override
        public IPushMessage showBrowser()
        {
            presentationType = PushPresentationType.OnlyExternalPush;
            return this;
        }

        @Override
        public IPushMessage showInterfaceAlways()
        {
            presentationType = PushPresentationType.AlwaysInTheInterface;
            return this;
        }

        @Override
        public IPushMessage showInterfaceAndBrowser()
        {
            presentationType = PushPresentationType.InterfaceAndBrowserNotices;
            return this;
        }

        @Override
        public IPushMessage showInterfaceOnly()
        {
            presentationType = PushPresentationType.OnlyInTheInterface;
            return this;
        }

        @Override
        public IPushMessage showSubject()
        {
            headerFormat = PushHeaderFormatType.Subject;
            return this;
        }

        @Override
        public IPushMessage showSystemName()
        {
            headerFormat = PushHeaderFormatType.SystemNameValue;
            return this;
        }

        @Override
        public IPushMessage showSystemNameAndSubject()
        {
            headerFormat = PushHeaderFormatType.SystemNameValueAndSubject;
            return this;
        }

        @Override
        public IPushMessage useTemplate(String templateCode)
        {
            return useTemplate(templateCode, null);
        }

        @Override
        public IPushMessage useTemplate(@CheckForNull String templateCode, @Nullable Map<String, Object> context)
        {
            Map<String, Object> bindings = context != null ? new HashMap<>(context) : new HashMap<>();
            if (templateCode == null || storageService.getTemplate(templateCode) == null)
            {
                throw new FxException(messages.getMessage("pushApi.templateNotFound"));
            }
            try
            {
                text = templateService.generate(text, templateCode, new HashMap<>(bindings));
            }
            catch (NotificationTemplateException e)
            {
                throw new FxException(messages.getMessage("eventService.pushGroovyTemplateSyntaxError"), e);
            }
            catch (FxException e)
            {
                throw new FxException(messages.getMessage("eventService.pushGroovySyntaxError", messages.getMessage(
                        "push.body")), e);
            }
            return this;
        }
    }

    private final NotificationTemplateService templateService;
    private final StyleTemplateStorageService storageService;
    private final ScriptNotificationWebSender webSender;
    // провайдер необходим для подъема контекста приложения в DB-тестах в sdng
    private final Provider<ScriptNotificationMobileSender> mobileSender;
    private final MessageFacade messages;
    private final PushService<Push> pushService;
    private final PushService<PushPortal> portalPushService;
    private final ModulesService modulesService;
    private final PushSettingsReloader pushSettingsReloader;
    private final ServiceAccountLoader serviceAccountLoader;

    @Inject
    public NotificationApi(
            final NotificationTemplateService templateService,
            final StyleTemplateStorageService storageService,
            final ScriptNotificationWebSender webSender,
            final Provider<ScriptNotificationMobileSender> mobileSender,
            final MessageFacade messages,
            final PushService<Push> pushService,
            final ModulesService modulesService,
            final PushService<PushPortal> portalPushService,
            final PushSettingsReloader pushSettingsReloader,
            final ServiceAccountLoader serviceAccountLoader)
    {
        this.templateService = templateService;
        this.storageService = storageService;
        this.webSender = webSender;
        this.mobileSender = mobileSender;
        this.messages = messages;
        this.pushService = pushService;
        this.modulesService = modulesService;
        this.portalPushService = portalPushService;
        this.pushSettingsReloader = pushSettingsReloader;
        this.serviceAccountLoader = serviceAccountLoader;
    }

    @Override
    public void archivePushMessages(String eventActionCode, Object subject)
    {
        pushService.updateObjectPushState(ApiUtils.getUuid(subject), eventActionCode, PushStates.ARCHIVED,
                PushStates.READ_BY_USER, PushStates.ARCHIVED);
    }

    @Override
    public IPushMessage createPushMessage(String text)
    {
        return new PushMessage(text);
    }

    @Override
    public boolean sendMobilePush(final String message, final Object recipient)
    {
        return sendMobilePush(new PushMessage(message), recipient);
    }

    @Override
    public boolean sendMobilePush(final IPushMessage pushMessage, final Object recipient)
    {
        if (!modulesService.isInstalled(MOBILE_API))
        {
            LOG.trace("Mobile push was not sent, because mobile module not installed.");
            return false;
        }

        final PushMessage push = (PushMessage)pushMessage;
        return mobileSender.get()
                .sendPush(push.text, push.useHtml, push.headerFormat, push.link, push.subject, recipient);
    }

    @Override
    public boolean sendWebPush(IPushMessage push, Object recipient)
    {
        PushMessage pushImpl = (PushMessage)push;
        return webSender.sendPush(pushImpl.text, pushImpl.useHtml, pushImpl.presentationType, pushImpl.link, recipient);
    }

    @Override
    public boolean sendWebPush(String message, Object recipient)
    {
        PushMessage pushImpl = new PushMessage(message);
        return webSender.sendPush(pushImpl.text, pushImpl.useHtml, pushImpl.presentationType, pushImpl.link, recipient);
    }

    @Override
    public void markEventsAsRead(Collection<String> events)
    {
        for (final String event : events)
        {
            pushService.updatePushState(UuidHelper.toId(event), PushStates.READ_BY_USER);
        }
    }

    @Override
    public void markPortalPushesAsRead(Collection<String> events)
    {
        for (final String event : events)
        {
            portalPushService.updatePushState(UuidHelper.toId(event), PushStates.READ_BY_USER);
        }
    }

    @Override
    public void reloadFcmConfig()
    {
        pushSettingsReloader.reloadFcmConfig();
        serviceAccountLoader.load();
    }

    @Override
    public void reloadRuStoreConfig()
    {
        pushSettingsReloader.reloadRuStoreConfig();
    }

    @Override
    public void reloadHmsConfig()
    {
        pushSettingsReloader.reloadHmsConfig();
    }
}