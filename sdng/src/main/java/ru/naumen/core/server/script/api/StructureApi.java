package ru.naumen.core.server.script.api;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.HashSet;
import java.util.LinkedHashSet;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Set;

import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.hierarchygrid.HierarchyGridConfiguration;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.api.structures.IObjectLinks;
import ru.naumen.core.server.script.api.structures.ObjectLinks;
import ru.naumen.core.server.structuredobjectsviews.StructuredObjectsViewService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.ISDtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.ClassFqnHelper;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.dynaform.server.masseditform.MassEditContextUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeGroup;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.structuredobjectsviews.StructureItemException;
import ru.naumen.metainfo.shared.structuredobjectsviews.StructuredObjectsView;
import ru.naumen.metainfo.shared.structuredobjectsviews.items.StructuredObjectsViewItem;

/**
 * API для работы со структурами объектов в системе
 * <AUTHOR>
 * @since 11.08.2020
 */
@Component("structure")
public class StructureApi implements IStructureApi
{
    private final StructuredObjectsViewService structuredObjectsViewService;
    private final HierarchyGridConfiguration hierarchyGridConfiguration;
    private final SessionFactory sessionFactory;
    private final MessageFacade messages;
    private final MetainfoService metainfoService;
    private static final int IN_BATCH_SIZE = 1000;
    private static final String META_CLASS_FQN = "metaClassFqn";
    private final MetainfoUtils metainfoUtils;
    private final MassEditContextUtils utils;
    private final IPrefixObjectLoaderService objectLoader;

    @Inject
    public StructureApi(StructuredObjectsViewService structuredObjectsViewService,
            HierarchyGridConfiguration hierarchyGridConfiguration,
            @Named("sessionFactory") SessionFactory sessionFactory,
            MessageFacade messages, MetainfoService metainfoService,
            MetainfoUtils metainfoUtils,
            MassEditContextUtils utils,
            IPrefixObjectLoaderService objectLoader
    )
    {
        this.structuredObjectsViewService = structuredObjectsViewService;
        this.hierarchyGridConfiguration = hierarchyGridConfiguration;
        this.sessionFactory = sessionFactory;
        this.messages = messages;
        this.metainfoService = metainfoService;
        this.metainfoUtils = metainfoUtils;
        this.utils = utils;
        this.objectLoader = objectLoader;
    }

    @Override
    public IObjectLinks getObjectParentsAndChildren(String objectUuid, String structureCode,
            @Nullable Integer limitParentAndChildren,
            @Nullable String itemCode)
    {
        int limit = limitParentAndChildren != null
                ? limitParentAndChildren
                : hierarchyGridConfiguration.getApiLookupObjectLimit();
        Map<String, List<String>> result = new HashMap<>();
        StructuredObjectsView structure = structuredObjectsViewService.getStructuredObjectsView(structureCode);
        ClassFqn classFqn = objectLoader.get(objectUuid) instanceof IHasMetaInfo object
                ? object.getMetaClass()
                : ClassFqnHelper.toClassId(objectUuid);

        StructuredObjectsViewItem targetStructureItem = findTargetStructureItem(structure, itemCode, classFqn);
        //Найти родительские элементы структуры
        List<StructuredObjectsViewItem> tempParentItems = getParentStructureItems(structure, targetStructureItem);
        Collections.reverse(tempParentItems);
        List<StructuredObjectsViewItem> parentsStructureItems = new ArrayList<>(tempParentItems);

        //Получить родительские объекты по родительским элементам структуры
        getParentObjectsByStructure(objectUuid, result, targetStructureItem, parentsStructureItems, limit);

        //Получить дочерние объекты по дочерним элементам структуры
        getChildrenObjectsByStructure(objectUuid, result, targetStructureItem, limit);
        return new ObjectLinks(result.get("children"), result.get("parent"));
    }

    @Override
    public IObjectLinks getObjectParentsAndChildren(String objectUuid, ISDtObject structure,
            @Nullable Integer limitParentAndChildren,
            @Nullable ISDtObject structureItem)
    {
        return getObjectParentsAndChildren(objectUuid, (String)structure.getProperty("code"), limitParentAndChildren,
                structureItem == null ? null : (String)structureItem.getProperty("code"));
    }

    @Override
    public IObjectLinks getObjectParentsAndChildren(String objectUuid, ISDtObject structure,
            @Nullable Integer limitParentAndChildren)
    {
        return getObjectParentsAndChildren(objectUuid, (String)structure.getProperty("code"), limitParentAndChildren,
                null);
    }

    @Override
    public IObjectLinks getObjectParentsAndChildren(String objectUuid, String structureCode,
            @Nullable Integer limitParentAndChildren)
    {
        return getObjectParentsAndChildren(objectUuid, structureCode, limitParentAndChildren, null);
    }

    @Override
    public IObjectLinks getObjectParentsAndChildren(String objectUuid, String structureCode, @Nullable String itemCode)
    {
        return getObjectParentsAndChildren(objectUuid, structureCode, null, itemCode);
    }

    @Override
    public IObjectLinks getObjectParentsAndChildren(String objectUuid, ISDtObject structure,
            @Nullable ISDtObject structureItem)
    {
        return getObjectParentsAndChildren(objectUuid, structure, null, structureItem);
    }

    @Override
    public IObjectLinks getObjectParentsAndChildren(String objectUuid, String structureCode)
    {
        return getObjectParentsAndChildren(objectUuid, structureCode, null, null);
    }

    @Override

    public IObjectLinks getObjectParentsAndChildren(String objectUuid, ISDtObject structure)
    {
        return getObjectParentsAndChildren(objectUuid, (String)structure.getProperty("code"), null,
                null);
    }

    public List<ISDtObject> getAllStructures()
    {
        List<ISDtObject> result = new ArrayList<>();
        structuredObjectsViewService.getAll()
                .forEach(structure -> result.add(convertStructureToLightDto(structure)));
        return result;
    }

    @Override
    public ISDtObject getStructure(String structureCode)
    {
        StructuredObjectsView structure = structuredObjectsViewService.getStructuredObjectsView(structureCode);
        if (structure == null)
        {
            String error = messages.getMessage("errorStructureNotFound", structureCode);
            throw new StructureItemException(error);
        }
        ISDtObject result = new SimpleDtObject();
        result.put("code", structure.getCode());
        result.put("name", metainfoUtils.getLocalizedValue(structure.getTitle()));
        result.put("description", metainfoUtils.getLocalizedValue(structure.getDescription()));

        List<ISDtObject> structureItemsLight = new ArrayList<>();
        structure.getAllStructuredObjectsViewItems()
                .forEach(x -> structureItemsLight.add(convertStructureItemToLightDto(x, structure)));
        result.put("items", structureItemsLight);
        return result;
    }

    @Override
    public ISDtObject getStructure(ISDtObject lightStructure)
    {
        return getStructure(lightStructure.getProperty("code").toString());
    }

    @Override
    public ISDtObject getStructureItem(String structureCode, String itemCode)
    {
        StructuredObjectsView structure = structuredObjectsViewService.getStructuredObjectsView(structureCode);
        if (structure == null)
        {
            throw new StructureItemException(messages.getMessage("errorStructureNotFound", structureCode));
        }
        ISDtObject result = new SimpleDtObject();
        StructuredObjectsViewItem structuredObjectsViewItem =
                structure.getAllStructuredObjectsViewItems().stream()
                        .filter(Objects::nonNull)
                        .filter(x -> x.getCode().equals(itemCode))
                        .findFirst()
                        .orElseThrow(() -> new StructureItemException(
                                messages.getMessage("errorStructureItemNotFound", itemCode)));
        StructuredObjectsViewItem parentItem = getParentStructureItem(structure, structuredObjectsViewItem);
        result.put("code", structuredObjectsViewItem.getCode());
        result.put("name", metainfoUtils.getLocalizedValue(structuredObjectsViewItem.getTitle()));
        result.put("parentStructureCode", structure.getCode());
        result.put("parentItemCode", parentItem != null ? parentItem.getCode() : null);
        result.put("attribute",
                structuredObjectsViewItem.getRelAttrFqn() == null ? null :
                        metainfoService.getAttribute(structuredObjectsViewItem.getRelAttrFqn()));
        List<MetaClass> itemMetaClasses = new ArrayList<>();

        structuredObjectsViewItem.getClassFqn()
                .forEach(classFqn -> itemMetaClasses.add(metainfoService.getMetaClass(classFqn)));
        result.put("metaClasses", itemMetaClasses);
        Collection<ClassFqn> classFqns = structuredObjectsViewItem.getClassFqn();
        ClassFqn commonParentFqn = utils.getLeastCommonParent(new HashSet<>(classFqns));
        MetaClass commonMetaClass = metainfoService.getMetaClass(classFqns.size() == 1
                ? classFqns.iterator().next()
                : commonParentFqn);
        AttributeGroup commonAttributeGroup = structuredObjectsViewItem.getAttrGroupCode() == null ? null :
                commonMetaClass.getAttributeGroup(structuredObjectsViewItem.getAttrGroupCode());
        result.put("attributeGroup", commonAttributeGroup);
        return result;
    }

    @Override
    public ISDtObject getStructureItem(ISDtObject lightStructureItem)
    {
        return getStructureItem(lightStructureItem.getProperty("codeStructure"),
                lightStructureItem.getProperty("code"));
    }

    private void getParentObjectsByStructure(String objectUuid, Map<String, List<String>> result,
            StructuredObjectsViewItem targetStructureItem, List<StructuredObjectsViewItem> parentsStructureItems,
            int limit)
    {
        List<String> startUuids = Collections.singletonList(objectUuid);
        List<String> parentUuids = new ArrayList<>();
        List<String> retUuids = getParentFromOneStructureItem(targetStructureItem, parentUuids, startUuids, limit);
        startUuids = processReturnUuids(startUuids, parentUuids, retUuids);
        for (StructuredObjectsViewItem parentStructureItem : parentsStructureItems)
        {
            retUuids = getParentFromOneStructureItem(parentStructureItem, parentUuids, startUuids, limit);
            startUuids = processReturnUuids(startUuids, parentUuids, retUuids);
        }

        //Избавиться от дубликатов
        Set<String> tmpSet = new LinkedHashSet<>(parentUuids);
        parentUuids.clear();
        parentUuids.addAll(tmpSet);
        Collections.reverse(parentUuids);
        result.put("parent", parentUuids);
    }

    private List<String> getParentFromOneStructureItem(StructuredObjectsViewItem parentStructureItem,
            List<String> allParentUuids, List<String> startUuids, int limit)
    {
        List<String> retUuids = getParentObjects(parentStructureItem, startUuids,
                Math.max(limit - allParentUuids.size(), 0));
        if (parentStructureItem.isShowNested())
        {
            startUuids = processReturnUuids(startUuids, allParentUuids, retUuids);
            if (!startUuids.isEmpty())
            {
                List<String> hierRetUuids = getParentHierObjects(parentStructureItem, startUuids,
                        Math.max(limit - allParentUuids.size(), 0));
                Collections.reverse(hierRetUuids);
                if (CollectionUtils.isNotEmpty(hierRetUuids))
                {
                    startUuids = processReturnUuids(startUuids, allParentUuids, hierRetUuids);
                    retUuids = getParentObjects(parentStructureItem, startUuids,
                            Math.max(limit - allParentUuids.size(), 0));
                }
            }
        }
        return retUuids;
    }

    private static List<String> processReturnUuids(List<String> startUuids, List<String> allUuids,
            List<String> returnUuids)
    {
        if (returnUuids.stream().anyMatch(Objects::nonNull))
        {
            allUuids.addAll(returnUuids.stream().filter(Objects::nonNull).toList());
            return returnUuids;
        }
        return startUuids;

    }

    private StructuredObjectsViewItem findTargetStructureItem(StructuredObjectsView structuredObjectsView,
            @Nullable String itemCode, ClassFqn uuidClassFqn)
    {
        List<StructuredObjectsViewItem> foundItems = new ArrayList<>();
        if (null == itemCode)
        {
            for (StructuredObjectsViewItem item : structuredObjectsView.getAllStructuredObjectsViewItems())
            {
                if (item.getClassFqn().stream().anyMatch(classFqn ->
                        classFqn.equals(uuidClassFqn) || (classFqn.getCase() == null &&
                                                          classFqn.getId().equals(uuidClassFqn.getId()))))
                {
                    foundItems.add(item);
                }
            }
        }
        else
        {
            foundItems.addAll(structuredObjectsView.getAllStructuredObjectsViewItems().stream()
                    .filter(item -> item.getCode().equals(itemCode))
                    .toList());
        }
        if (foundItems.size() > 1)
        {
            String error = messages.getMessage("errorMoreOneStructureElement", uuidClassFqn.asString());
            throw new StructureItemException(error);
        }
        else if (foundItems.isEmpty())
        {
            String error = messages.getMessage("errorGetStructureElement", uuidClassFqn.asString());
            throw new StructureItemException(error);
        }
        return foundItems.getFirst();
    }

    private static List<StructuredObjectsViewItem> getParentStructureItems(StructuredObjectsView structure,
            StructuredObjectsViewItem targetStructureItem)
    {
        List<StructuredObjectsViewItem> parentStructureItems = new ArrayList<>();
        Map<String, StructuredObjectsViewItem> tempParentMap = new HashMap<>();
        for (StructuredObjectsViewItem item : structure.getAllStructuredObjectsViewItems())
        {
            if (CollectionUtils.isEmpty(item.getChildren()))
            {
                continue;
            }
            item.getChildren().forEach(child -> tempParentMap.put(child.getCode(), item));
        }
        StructuredObjectsViewItem lastParent = targetStructureItem;

        for (int i = 0; i <= tempParentMap.size(); i++)
        {
            String lastParentCode = lastParent.getCode();
            lastParent = tempParentMap.get(lastParentCode);

            if (lastParent != null)
            {
                parentStructureItems.add(lastParent);
            }
            else
            {
                break;
            }
        }
        Collections.reverse(parentStructureItems);
        return parentStructureItems;
    }

    private void getChildrenObjectsByStructure(String objectUuid, Map<String, List<String>> result,
            StructuredObjectsViewItem structuresItem, int limit)
    {

        List<String> childrenUuids = new ArrayList<>();
        List<String> listObjectUuid = Collections.singletonList(objectUuid);
        limit = increaseLimitForKeepExitLimitCondition(limit, structuresItem, listObjectUuid);
        processStructureForGetChildren(structuresItem, listObjectUuid, childrenUuids, limit);

        //Избавиться от дубликатов
        childrenUuids.removeAll(Collections.singletonList(objectUuid));
        Set<String> tmpSet = new LinkedHashSet<>(childrenUuids);
        childrenUuids.clear();
        childrenUuids.addAll(tmpSet);
        result.put("children", childrenUuids);
    }

    private static int increaseLimitForKeepExitLimitCondition(int limit, StructuredObjectsViewItem structuresItem,
            List<String> listObjectUuid)
    {
        if (structuresItem.isShowNested())
        {
            return limit + listObjectUuid.size();
        }
        else
        {
            return limit;
        }
    }

    private static boolean addObjectsWithLimit(List<String> allObjects, List<String> lastObjects, int limit)
    {
        allObjects.addAll(lastObjects);
        return allObjects.size() >= limit;
    }

    private void processStructureForGetChildren(StructuredObjectsViewItem structureItem, List<String> uuids,
            List<String> allChild, int limit)
    {
        if (structureItem.isShowNested())
        {
            //Получить все по иерархии
            List<String> hierUuids = getChildHierObjects(structureItem, uuids,
                    Math.max(limit - allChild.size(), 0));
            if (addObjectsWithLimit(allChild, hierUuids, limit))
            {
                return;
            }
            uuids = hierUuids.isEmpty() ? uuids : hierUuids;
        }

        if (!CollectionUtils.isEmpty(structureItem.getChildren()))
        {
            for (StructuredObjectsViewItem structuredObjectsViewItemCh : structureItem.getChildren())
            {
                //Получить детей и добавить их в общий список детей
                List<String> lastChild = getChildObjects(structuredObjectsViewItemCh, uuids,
                        Math.max(limit - allChild.size(), 0));
                if (addObjectsWithLimit(allChild, lastChild, limit))
                {
                    return;
                }
                //Запустить рекурсию по детям снова
                processStructureForGetChildren(structuredObjectsViewItemCh, lastChild, allChild, limit);
            }
        }
    }

    private List<String> getParentObjects(StructuredObjectsViewItem structureItem, List<String> uuids, int limit)
    {
        List<String> result = new ArrayList<>();
        if (limit <= 0 || structureItem.getRelAttrFqn() == null)
        {
            return result;
        }
        for (List<String> batch : Lists.partition(uuids, IN_BATCH_SIZE))
        {
            String classId = structureItem.getRelAttrFqn().getClassFqn().toString();
            HCriteria criteria = HHelper.create();
            List<String> sourceFqnClasses = structureItem.getClassFqn().stream()
                    .filter(fqn -> !fqn.toString().equals(classId))
                    .map(ClassFqn::getCase)
                    .toList();
            HCriteria childCriteria = criteria.addSource(classId);
            HCriteria parentCriteria = childCriteria.addLeftJoin(
                    metainfoService.getAttribute(structureItem.getRelAttrFqn()).getPropertyFqn());
            childCriteria.add(HRestrictions.in(childCriteria.getProperty(Constants.IDIdentifiableBase.ID),
                    batch.stream().map(UuidHelper::toId).toList()));
            if (!sourceFqnClasses.isEmpty())
            {
                childCriteria.add(
                        HRestrictions.in(childCriteria.getProperty(AbstractBO.METACASE_ID), sourceFqnClasses));
            }
            criteria.add(HRestrictions.or(HRestrictions.isNull(parentCriteria),
                    HRestrictions.eq(parentCriteria.getProperty(Constants.AbstractBO.REMOVED), false)));
            Query<Object[]> query = childCriteria.createQuery(getSession(), Object[].class);
            query.setMaxResults(limit);
            List<Object[]> queryResult = query.list();
            if (!queryResult.isEmpty())
            {
                List<String> oneCircleResult = Arrays.stream(queryResult.getFirst())
                        .map(row -> row != null ? ((IUUIDIdentifiable)row).getUUID() : null)
                        .filter(uuid -> !batch.contains(uuid))
                        .toList();
                result.addAll(oneCircleResult);
            }
        }

        return result;
    }

    private List<String> getChildObjects(StructuredObjectsViewItem structureItem, List<String> parentUuids, int limit)
    {
        List<String> result = new ArrayList<>();
        if (limit <= 0 || structureItem.getRelAttrFqn() == null)
        {
            return result;
        }
        for (List<String> batch : Lists.partition(parentUuids, IN_BATCH_SIZE))
        {
            HCriteria criteria = HHelper.create();
            String classId = structureItem.getRelAttrFqn().getClassFqn().toString();
            List<String> sourceFqnClasses = structureItem.getClassFqn().stream()
                    .filter(fqn -> !fqn.toString().equals(classId))
                    .map(ClassFqn::getCase)
                    .toList();
            HCriteria childCriteria = criteria.addSource(classId);
            if (!sourceFqnClasses.isEmpty())
            {
                childCriteria.add(
                        HRestrictions.in(childCriteria.getProperty(AbstractBO.METACASE_ID), sourceFqnClasses));
            }
            HCriteria parentCriteria = childCriteria.addLeftJoin(
                    metainfoService.getAttribute(structureItem.getRelAttrFqn()).getPropertyFqn());

            criteria.addColumn(childCriteria.getProperty("id"));
            parentCriteria.add(HRestrictions.in(parentCriteria.getProperty("id"),
                    batch.stream().map(UuidHelper::toId).toList()));
            criteria.add(
                    HRestrictions.eq(parentCriteria.getProperty(Constants.AbstractBO.REMOVED), false));
            Query<Long> query = childCriteria.createQuery(getSession());
            query.setMaxResults(limit);
            List<Long> queryResult = query.list();
            List<String> oneCircleResult = queryResult.stream()
                    .filter(Objects::nonNull)
                    .map(id -> UuidHelper.toUuid(id, classId))
                    .toList();
            result.addAll(oneCircleResult);
        }

        return result;
    }

    private List<String> getChildHierObjects(StructuredObjectsViewItem structureItem, List<String> parentUuids,
            int limit)
    {
        List<String> result = new ArrayList<>();
        if (limit <= 0 || structureItem.getRelAttrFqn() == null)
        {
            return result;
        }
        Attribute relAttr = metainfoService.getAttribute(structureItem.getRelAttrFqn());
        ClassFqn parentClassFqn = ClassFqn.parse(relAttr.getType().getProperty(META_CLASS_FQN).toString());
        if (structureItem.getClassFqn().stream().noneMatch(classFqn ->
                classFqn.getId().equals(parentClassFqn.getId()) || (classFqn.getCase() != null && classFqn.getCase()
                        .equals(parentClassFqn.getCase()))))
        {
            return result;
        }
        for (List<String> batch : Lists.partition(parentUuids, IN_BATCH_SIZE))
        {
            // Критерия по элементу структуры
            String classId = structureItem.getRelAttrFqn().getClassFqn().toString();
            List<String> sourceFqnClasses = structureItem.getClassFqn().stream()
                    .filter(fqn -> !fqn.toString().equals(classId))
                    .map(ClassFqn::getCase)
                    .toList();
            HCriteria criteria = HHelper.create(classId);
            if (!sourceFqnClasses.isEmpty())
            {
                criteria.add(HRestrictions.in(criteria.getProperty(AbstractBO.METACASE_ID), sourceFqnClasses));
            }
            // Критерия иерархии
            HCriteria hier = HHelper.createDownHierarchy(criteria, structureItem.getRelAttrFqn().getClassFqn(),
                    metainfoService.getAttribute(structureItem.getRelAttrFqn()).getPropertyFqn());
            // Для иерархии определяем условие стартового отбора сущностей
            hier.setStartCriterion(HRestrictions.in(hier.getProperty(Constants.IDIdentifiableBase.ID),
                    batch.stream().map(UuidHelper::toId).toList()));

            HCriteria cte = criteria.addCTESource(hier);
            // Связываем источник иерархии с критерией по элементу структуры
            criteria.add(HRestrictions.eqProperty(criteria.getProperty(Constants.IDIdentifiableBase.ID),
                    cte.getProperty(Constants.IDIdentifiableBase.ID)));
            Query<IUUIDIdentifiable> query = criteria.createQuery(getSession());
            query.setMaxResults(limit);
            List<IUUIDIdentifiable> queryResult = query.list();
            List<String> oneCircleResult = queryResult.stream()
                    .filter(Objects::nonNull)
                    .map(IUUIDIdentifiable::getUUID)
                    .toList();
            result.addAll(oneCircleResult);
        }

        return result;
    }

    private List<String> getParentHierObjects(StructuredObjectsViewItem structureItem, List<String> childUuids,
            int limit)
    {
        List<String> result = new ArrayList<>();
        if (limit <= 0 || structureItem.getRelAttrFqn() == null)
        {
            return result;
        }
        Attribute relAttr = metainfoService.getAttribute(structureItem.getRelAttrFqn());
        ClassFqn parentClassFqn = ClassFqn.parse(relAttr.getType().getProperty(META_CLASS_FQN).toString());
        if (structureItem.getClassFqn().stream().noneMatch(classFqn ->
                classFqn.getId().equals(parentClassFqn.getId()) || (classFqn.getCase() != null && classFqn.getCase()
                        .equals(parentClassFqn.getCase()))))
        {
            return result;
        }
        for (List<String> batch : Lists.partition(childUuids, IN_BATCH_SIZE))
        {
            // Критерия по элементу структуры
            String classId = structureItem.getRelAttrFqn().getClassFqn().toString();
            List<String> sourceFqnClasses = structureItem.getClassFqn().stream()
                    .filter(fqn -> !fqn.toString().equals(classId))
                    .map(ClassFqn::getCase)
                    .toList();
            HCriteria criteria = HHelper.create(classId);
            if (!sourceFqnClasses.isEmpty())
            {
                criteria.add(HRestrictions.in(criteria.getProperty(AbstractBO.METACASE_ID), sourceFqnClasses));
            }

            String relAttribute = metainfoService.getAttribute(structureItem.getRelAttrFqn()).getPropertyFqn();
            // Критерия иерархии
            HCriteria hier = HHelper.createUpHierarchy(criteria,
                    structureItem.getRelAttrFqn().getClassFqn(),
                    relAttribute);
            // Для иерархии определяем условие стартового отбора сущностей
            hier.setStartCriterion(HRestrictions.in(hier.getProperty(Constants.IDIdentifiableBase.ID),
                    batch.stream().map(UuidHelper::toId).toList()));

            HCriteria cte = criteria.addCTESource(hier);
            // Связываем источник иерархии с критерией по элементам структуры
            criteria.add(HRestrictions.eqProperty(criteria.getProperty(Constants.IDIdentifiableBase.ID),
                    cte.getProperty(Constants.IDIdentifiableBase.ID)));
            criteria.add(HRestrictions.notIn(criteria.getProperty(Constants.IDIdentifiableBase.ID),
                    batch.stream().map(UuidHelper::toId).toList()));
            Query<IUUIDIdentifiable> query = criteria.createQuery(getSession());
            query.setMaxResults(limit);
            List<IUUIDIdentifiable> queryResult = query.list();
            List<String> oneCircleResult = queryResult.stream()
                    .filter(Objects::nonNull)
                    .map(IUUIDIdentifiable::getUUID)
                    .toList();
            Collections.reverse(oneCircleResult);
            result.addAll(oneCircleResult);
        }

        return result;
    }

    private ISDtObject convertStructureItemToLightDto(StructuredObjectsViewItem structureItem,
            StructuredObjectsView structure)
    {
        ISDtObject result = new SimpleDtObject();
        result.put("code", structureItem.getCode());
        result.put("codeStructure", structure.getCode());
        result.put("name", metainfoUtils.getLocalizedValue(structureItem.getTitle()));
        return result;
    }

    private ISDtObject convertStructureToLightDto(StructuredObjectsView structure)
    {
        ISDtObject result = new SimpleDtObject();
        result.put("code", structure.getCode());
        result.put("name", metainfoUtils.getLocalizedValue(structure.getTitle()));
        return result;
    }

    private static StructuredObjectsViewItem getParentStructureItem(StructuredObjectsView structure,
            StructuredObjectsViewItem structuredObjectsViewItem)
    {
        StructuredObjectsViewItem parentItem = null;
        for (StructuredObjectsViewItem item : structure.getAllStructuredObjectsViewItems())
        {
            if (!CollectionUtils.isEmpty(item.getChildren()))
            {
                StructuredObjectsViewItem foundChildItem = item.getChildren().stream()
                        .filter(childItem -> childItem.getCode().equals(structuredObjectsViewItem.getCode()))
                        .findFirst().orElse(null);
                if (foundChildItem != null)
                {
                    parentItem = item;
                    break;
                }
            }
        }
        return parentItem;
    }

    private org.hibernate.Session getSession()
    {
        return sessionFactory.getCurrentSession();
    }
}
