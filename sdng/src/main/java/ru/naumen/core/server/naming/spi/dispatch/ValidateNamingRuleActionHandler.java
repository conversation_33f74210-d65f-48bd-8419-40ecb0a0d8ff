package ru.naumen.core.server.naming.spi.dispatch;

import jakarta.inject.Inject;

import net.customware.gwt.dispatch.server.AbstractActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.naming.INamingService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.dispatch.EmptyResult;
import ru.naumen.core.shared.dispatch.ValidateNamingRuleAction;

/**
 * Обработчиск {@link ValidateNamingRuleAction команды} проверки правила нумерации
 *
 * <AUTHOR>
 *
 */
@Component
public class ValidateNamingRuleActionHandler extends AbstractActionHandler<ValidateNamingRuleAction, EmptyResult>
{
    @Inject
    private INamingService namingService;
    @Inject
    private MessageFacade messages;

    public ValidateNamingRuleActionHandler()
    {
        super(ValidateNamingRuleAction.class);
    }

    @Override
    public EmptyResult execute(ValidateNamingRuleAction action, ExecutionContext context) throws DispatchException
    {
        if (namingService.checkRule(action.getFqn(), action.getAttrTypeCode(), action.getRule()))
        {
            return EmptyResult.instance;
        }
        throw new FxException(messages.getMessage("naming.ruleNotValid", action.getRule()));
    }
}
