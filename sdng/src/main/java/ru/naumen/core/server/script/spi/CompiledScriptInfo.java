package ru.naumen.core.server.script.spi;

import java.lang.reflect.InvocationTargetException;
import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;

import jakarta.annotation.Nullable;

import javax.script.Bindings;

import org.apache.commons.lang3.exception.ExceptionUtils;
import org.codehaus.groovy.reflection.ClassInfo;

import groovy.lang.Binding;
import groovy.lang.MetaClass;
import groovy.lang.Script;
import ru.naumen.commons.shared.FxException;

/**
 * Скомпилированный скрипт с прикрепленной к нему дополнительной информацией, полученной во время компиляции.
 * Неизменяем после создания.
 *
 * <AUTHOR>
 * @since Apr 1, 2016
 */
public final class CompiledScriptInfo
{
    private final Class<? extends Script> scriptClass;
    private final Set<String> dependencies;

    public CompiledScriptInfo(final Class<? extends Script> scriptClass,
            final Collection<String> subjectDependencies)
    {
        this.scriptClass = scriptClass;
        this.dependencies = new HashSet<>(subjectDependencies);
    }

    /**
     * Получить groovy метакласс текущего скрипта
     */
    public MetaClass getMetaClass()
    {
        return ClassInfo.getClassInfo(scriptClass).getMetaClass();
    }

    /**
     * Создать новый объект скрипта
     */
    public Script newInstance(@Nullable final Bindings bindings)
    {
        Script scriptInstance = newInstanceInternal();
        if (bindings != null)
        {
            scriptInstance.setBinding(new Binding(bindings));
        }
        return scriptInstance;
    }

    private Script newInstanceInternal()
    {
        try
        {
            return scriptClass.getDeclaredConstructor().newInstance();
        }
        catch (InstantiationException | IllegalAccessException
               | NoSuchMethodException | InvocationTargetException e)
        {
            throw new FxException(ExceptionUtils.getRootCauseMessage(e), e);
        }
    }

    public Set<String> getDependencies()
    {
        return Collections.unmodifiableSet(dependencies);
    }

    public Class<? extends Script> getScriptClass()
    {
        return scriptClass;
    }
}
