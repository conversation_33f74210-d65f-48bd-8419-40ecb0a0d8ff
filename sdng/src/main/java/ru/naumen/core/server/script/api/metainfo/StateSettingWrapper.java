package ru.naumen.core.server.script.api.metainfo;

import java.util.function.Function;

import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.shared.elements.workflow.CoreStateSetting;
import ru.naumen.metainfo.shared.elements.wf.StateSetting.FillCodes;

/**
 * Обертка настроек атрибутов в статусе ЖЦ для использования в скриптах
 * {@link IStateSettingWrapper}
 *
 * <AUTHOR>
 * @since 11.02.2021
 */
public final class StateSettingWrapper implements IStateSettingWrapper, HasCode
{
    public static final Function<CoreStateSetting, IStateSettingWrapper> WRAPPER =
            input -> null == input ? null : new StateSettingWrapper(input);

    public static class FillMode implements IFillMode
    {
        private final int fillCode;

        FillMode(int fillCode)
        {
            this.fillCode = fillCode;
        }

        @Override
        public boolean isNoAsk()
        {
            return FillCodes.NO_ASK == fillCode;
        }

        @Override
        public boolean isAsk()
        {
            return FillCodes.ASK == fillCode;
        }

        @Override
        public boolean isRequired()
        {
            return FillCodes.REQUIRED == fillCode;
        }

        @Override
        public String toString()
        {
            return "FillMode [isNoAsk()=" + isNoAsk() + "isAsk()=" + isAsk() + "isRequired()=" + isRequired() + ']';
        }
    }

    private final CoreStateSetting stateSetting;

    private StateSettingWrapper(CoreStateSetting stateSetting)
    {
        this.stateSetting = stateSetting;
    }

    @Override
    public String getCode()
    {
        return stateSetting.getCode();
    }

    @Override
    public IFillMode getPostFill()
    {
        return new FillMode(stateSetting.getPostFill());
    }

    @Override
    public IFillMode getPreFill()
    {
        return new FillMode(stateSetting.getPreFill());
    }

    @Override
    public boolean isCanView()
    {
        return stateSetting.isCanView();
    }

    @Override
    public boolean isCanEdit()
    {
        return stateSetting.isCanEdit();
    }

    @Override
    public boolean isRequiredInState()
    {
        return stateSetting.isRequiredInState();
    }

    @Override
    public String toString()
    {
        return "StateSettingWrapper [getCode()=" + getCode() + ", getPostFill()=" + getPostFill()
               + ", getPreFill()=" + getPreFill() + ", isCanView()=" + isCanView()
               + ", isCanEdit()=" + isCanEdit() + ", isRequiredInState()=" + isRequiredInState() + "]";
    }
}
