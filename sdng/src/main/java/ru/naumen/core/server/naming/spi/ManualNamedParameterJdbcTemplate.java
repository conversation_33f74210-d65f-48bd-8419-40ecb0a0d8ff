package ru.naumen.core.server.naming.spi;

import java.io.Closeable;
import java.sql.SQLException;

import javax.sql.DataSource;

import org.springframework.jdbc.core.JdbcOperations;
import org.springframework.jdbc.core.namedparam.NamedParameterJdbcTemplate;

/**
 * {@link NamedParameterJdbcTemplate} с ручным управлением транзакциями.
 *
 * <AUTHOR>
 * @since Dec 29, 2015
 *
 */
public class ManualNamedParameterJdbcTemplate extends NamedParameterJdbcTemplate implements Closeable
{
    private final SequenceJdbcTemplateFactory factory;

    ManualNamedParameterJdbcTemplate(DataSource dataSource, SequenceJdbcTemplateFactory factory)
    {
        super(dataSource);
        this.factory = factory;
    }

    ManualNamedParameterJdbcTemplate(JdbcOperations classicJdbcTemplate, SequenceJdbcTemplateFactory factory)
    {
        super(classicJdbcTemplate);
        this.factory = factory;
    }

    /**
     * Закрывает нижележащие ресурсы для текущего {@link ManualNamedParameterJdbcTemplate}.
     */
    @Override
    public void close()
    {
        factory.close(this);
    }

    /**
     * Коммит транзакции для текущего {@link ManualNamedParameterJdbcTemplate}.
     * @throws SQLException
     */
    public void commit() throws SQLException
    {
        factory.commit(this);
    }

    /**
     * Откат транзакции для текущего {@link ManualNamedParameterJdbcTemplate}.
     * @throws SQLException
     */
    public void rollback() throws SQLException
    {
        factory.rollback(this);
    }
}
