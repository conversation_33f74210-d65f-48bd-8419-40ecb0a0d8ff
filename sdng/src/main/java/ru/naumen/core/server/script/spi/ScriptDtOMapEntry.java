package ru.naumen.core.server.script.spi;

import java.util.Map;

/**
 * Реализация {@link Map.Entry} для использования в скриптах
 * <AUTHOR>
 *
 * @param <K> тип объектов ключа для {@link Map}
 * @param <V> тип объектов значения для {@link Map}
 */
public class ScriptDtOMapEntry<K, V> extends ScriptObjectBase<Map.Entry<?, ?>> implements Map.Entry<K, V>
{
    public ScriptDtOMapEntry(Map.Entry<?, ?> entry, ScriptDtOHelper helper)
    {
        super(entry, helper);
    }

    @Override
    public K getKey()
    {
        return helper.<K> wrap(delegate.getKey());
    }

    @Override
    public V getValue()
    {
        return helper.<V> wrap(delegate.getValue());
    }

    @Override
    public V setValue(V value)
    {
        return helper.<V> unmodify();
    }
}
