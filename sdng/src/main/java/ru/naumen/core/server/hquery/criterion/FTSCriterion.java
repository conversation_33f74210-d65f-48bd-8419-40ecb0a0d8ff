package ru.naumen.core.server.hquery.criterion;

import static ru.naumen.core.server.flex.codegen.attributes.simple.SemanticFilteringPropertyGeneratorHelper.FTS_PREFIX;

import jakarta.annotation.Nullable;

import ru.naumen.commons.server.utils.StringUtilities;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;
import ru.naumen.fts.server.dbsearch.FTSPostgresqlDialect;

/**
 * Критерий для полнотекстого поиска в БД с учетом морфологии
 *
 * <AUTHOR>
 * @since 21 авг. 2019 г.
 */
public class FTSCriterion extends SimpleCriterion
{
    private static final String HQL_TEMPLATE = FTSPostgresqlDialect.FTS_FUNCTION_NAME + "(%s, :%s) = true";

    public FTSCriterion(HColumn property, @Nullable Object value)
    {
        super(property, StringUtilities.EMPTY, value);
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        paramName = parameterCounter.next();
        sb.append(String.format(HQL_TEMPLATE, getProperty().getHQL(builder) + FTS_PREFIX, paramName));
    }

    @Override
    public String toString()
    {
        return "FTSCriterion{" +
               "property=" + property +
               ", value=" + getValue() +
               '}';
    }
}
