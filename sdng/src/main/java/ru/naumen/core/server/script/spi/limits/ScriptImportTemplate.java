package ru.naumen.core.server.script.spi.limits;

/**
 * Шаблон разрешения на импорт
 *
 * <AUTHOR>
 *
 */
public class ScriptImportTemplate
{
    // Путь импорта
    private final String name;
    // Признак возможна ли в импорте *
    private final boolean star;

    private final String importString;

    public ScriptImportTemplate(String name, boolean star)
    {
        this.name = name;
        this.star = star;
        importString = name.concat(star ? ".*" : "");
    }

    @Override
    public boolean equals(Object obj)
    {

        if (this == obj)
        {
            return true;
        }

        if (obj instanceof ScriptImportTemplate)
        {
            ScriptImportTemplate ai = (ScriptImportTemplate)obj;

            if (this.isStar())
            {
                return ai.getName().startsWith(this.getName());
            }
            else if (ai.isStar())
            {
                return this.getName().startsWith(ai.getName());
            }

            return importString.equals(((ScriptImportTemplate)obj).toString());
        }

        return false;
    }

    public String getName()
    {
        return name;
    }

    @Override
    public int hashCode()
    {
        return toString().hashCode();
    }

    public boolean isStar()
    {
        return star;
    }

    @Override
    public String toString()
    {
        return importString;
    }

}
