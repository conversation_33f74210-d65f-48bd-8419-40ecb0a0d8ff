package ru.naumen.core.server.script.spi.limits;

/**
 * Информация о вызове метода.
 */
public class CallInfo
{
    private String methodName;
    private Integer lineNumber;
    private Integer columnNumber;
    private String assignedTo;

    /**
     * Информация о вызове метода.
     */
    public CallInfo()
    {
    }

    /**
     * Информация о вызове метода.
     *
     * @param methodName наименования вызываемого метода;
     * @param lineNumber строка скрипта, на которой происходит вызов метода;
     * @param columnNumber "колонка" строки, на которой начинается вызов метода.
     */
    public CallInfo(String methodName, Integer lineNumber, Integer columnNumber)
    {
        this.methodName = methodName;
        this.lineNumber = lineNumber;
        this.columnNumber = columnNumber;
    }

    /**
     * Информация о вызове метода.
     *
     * @param methodName наименования вызываемого метода;
     * @param lineNumber строка скрипта, на которой происходит вызов метода;
     * @param columnNumber "колонка" строки, на которой начинается вызов метода.
     */
    public CallInfo(String methodName, Integer lineNumber, Integer columnNumber, String assignedTo)
    {
        this(methodName, lineNumber, columnNumber);
        this.assignedTo = assignedTo;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (obj == null)
        {
            return false;
        }
        if (!(obj instanceof CallInfo))
        {
            return false;
        }
        CallInfo other = (CallInfo)obj;
        if (columnNumber == null)
        {
            if (other.columnNumber != null)
            {
                return false;
            }
        }
        else if (!columnNumber.equals(other.columnNumber))
        {
            return false;
        }
        if (lineNumber == null)
        {
            if (other.lineNumber != null)
            {
                return false;
            }
        }
        else if (!lineNumber.equals(other.lineNumber))
        {
            return false;
        }
        if (methodName == null)
        {
            if (other.methodName != null)
            {
                return false;
            }
        }
        else if (!methodName.equals(other.methodName))
        {
            return false;
        }
        return true;
    }

    public String getAssignedTo()
    {
        return assignedTo;
    }

    public Integer getColumnNumber()
    {
        return columnNumber;
    }

    public Integer getLineNumber()
    {
        return lineNumber;
    }

    public String getMethodName()
    {
        return methodName;
    }

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + ((columnNumber == null) ? 0 : columnNumber.hashCode());
        result = prime * result + ((lineNumber == null) ? 0 : lineNumber.hashCode());
        result = prime * result + ((methodName == null) ? 0 : methodName.hashCode());
        return result;
    }

    public void setAssignedTo(String assignedTo)
    {
        this.assignedTo = assignedTo;
    }

    public void setColumnNumber(Integer columnNumber)
    {
        this.columnNumber = columnNumber;
    }

    public void setLineNumber(Integer lineNumber)
    {
        this.lineNumber = lineNumber;
    }

    public void setMethodName(String methodName)
    {
        this.methodName = methodName;
    }

    @Override
    public String toString()
    {
        return "CallInfo [methodName=" + methodName + ", lineNumber=" + lineNumber + ", columnNumber=" + columnNumber
               + ", assignedTo=" + assignedTo + "]";
    }
}
