package ru.naumen.core.server.script.spi;

import java.lang.reflect.Array;
import java.util.Arrays;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.ITitled;
import ru.naumen.core.shared.IUUIDIdentifiable;

import java.util.ArrayList;

/**
 * Неизменяемая коллекция - обёртка над реальной коллекцией объектов.
 * Предназначена для использования в скриптах.
 * Возвращает объекты для использования в скриптах.
 * <AUTHOR>
 *
 * @param <T> тип элементов коллекции
 * @param <C> тип оборачиваемой коллекции
 */
public class ScriptDtOCollection<T, C extends Collection<Object>> extends ScriptObjectBase<C> implements Collection<T>
{
    public ScriptDtOCollection(C rawCollection, ScriptDtOHelper helper)
    {
        super(rawCollection, helper);
    }

    @Override
    public boolean add(T e)
    {
        helper.unmodify();
        return false;
    }

    @Override
    public boolean addAll(Collection<? extends T> c)
    {
        helper.unmodify();
        return false;
    }

    @Override
    public void clear()
    {
        helper.unmodify();
    }

    @Override
    public boolean contains(Object o)
    {
        if (null == o)
        {
            return false;
        }
        for (Object element : delegate)
        {
            if (o.equals(element))
            {
                return true;
            }
        }
        return false;
    }

    @Override
    public boolean containsAll(Collection<?> c)
    {
        Iterator<?> e = c.iterator();
        while (e.hasNext())
        {
            if (!contains(e.next()))
            {
                return false;
            }
        }
        return true;
    }

    @Override
    public boolean isEmpty()
    {
        return delegate.isEmpty();
    }

    @Override
    public Iterator<T> iterator()
    {
        return helper.wrapIterator(delegate.iterator());
    }

    @Override
    public boolean remove(Object o)
    {
        helper.unmodify();
        return false;
    }

    @Override
    public boolean removeAll(Collection<?> c)
    {
        helper.unmodify();
        return false;
    }

    @Override
    public boolean retainAll(Collection<?> c)
    {
        helper.unmodify();
        return false;
    }

    @Override
    public int size()
    {
        return delegate.size();
    }

    @Override
    public Object[] toArray()
    {
        Object[] r = new Object[size()];
        Iterator<Object> it = delegate.iterator();
        for (int i = 0; i < r.length; i++)
        {
            if (!it.hasNext())
            {
                return Arrays.copyOf(r, i);
            }
            r[i] = helper.wrap(it.next());
        }
        return it.hasNext() ? finishToArray(r, it) : r;
    }

    @Override
    public <E> E[] toArray(E[] a)
    {
        int size = size();
        E[] r = a.length >= size ? a : (E[])Array.newInstance(a.getClass().getComponentType(), size);
        Iterator<Object> it = delegate.iterator();
        for (int i = 0; i < r.length; i++)
        {
            if (!it.hasNext())
            {
                if (a != r)
                {
                    return Arrays.copyOf(r, i);
                }
                r[i] = null;
                return r;
            }
            r[i] = helper.<E> wrap(it.next());
        }
        return it.hasNext() ? finishToArray(r, it) : r;
    }

    @Override
    public String toString()
    {
        int count = helper.getCountValueOfCollectionInString();
        boolean cropOutput = count >= 0;
        List<String> titles = new ArrayList<>();
        for (Object o : delegate)
        {
            if (o instanceof IScriptDtObject)
            {
                titles.add(StringUtilities.toString(o));
            }
            else if (o instanceof ITitled)
            {
                if (count == 0 && cropOutput)
                {
                    titles.add("...");
                    cropOutput = false;
                }
                if (count != 0)
                {
                    count--;
                    titles.add(((ITitled)o).getTitle());
                }
            }
            else if (o instanceof IUUIDIdentifiable)
            {
                titles.add(((IUUIDIdentifiable)o).getUUID());
            }
            else
            {
                titles.add(StringUtilities.toString(o));
            }
        }
        return titles.toString();
    }

    private <E> E[] finishToArray(E[] r, Iterator<Object> it)
    {
        int i = r.length;
        while (it.hasNext())
        {
            int cap = r.length;
            if (i == cap)
            {
                int newCap = (cap / 2 + 1) * 3;
                if (newCap <= cap)
                {
                    if (cap == Integer.MAX_VALUE)
                    {
                        throw new OutOfMemoryError("Required array size too large");
                    }
                    newCap = Integer.MAX_VALUE;
                }
                r = Arrays.copyOf(r, newCap);
            }
            r[i++] = helper.<E> wrap(it.next());
        }
        return (i == r.length) ? r : Arrays.copyOf(r, i);
    }
}
