package ru.naumen.core.server.script.storage.modification.usage;

import java.util.Collection;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.storage.ScriptUsageUtils;
import ru.naumen.metainfo.server.spi.elements.sec.AccessMatrixImpl;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix.Key;
import ru.naumen.metainfo.shared.script.PermissionCodeUtils;

/**
 * Контракт поведения при изменении скрипта в матрице прав доступа
 * Переопределяет специфичные действия при редактировании.
 * Основная логика редактирования содержится в {@link ScriptModifyProcessBase}
 * <AUTHOR>
 * @since 07.12.2015
 */
@Component
public class AccessMatrixScriptModifyProcess extends ScriptModifyProcessSupport<AccessMatrix>
{
    public static final String SEC_DOMAIN_CODE = "secDomainCode";
    public static final String KEY = "key";

    @Override
    protected String getLocation(AccessMatrix holder, ScriptModifyContext context)
    {
        Key key = context.getProperty(KEY);
        String secDomainCode = context.<String> getProperty(SEC_DOMAIN_CODE);
        String profile = key.columnCode;
        String marker = key.rowCode;
        String permissionFullCode = PermissionCodeUtils.createCode(secDomainCode, profile, marker);
        return PermissionCodeUtils.getLocation(permissionFullCode);
    }

    @Override
    protected String getOldScriptCode(AccessMatrix oldHolder, ScriptModifyContext context)
    {
        Key key = context.<Key> getProperty(KEY);
        return oldHolder.getDeclaredScripts().get(key);
    }

    @Override
    protected Collection<ClassFqn> getRelatedMetaClasses(AccessMatrix holder, ScriptModifyContext context)
    {
        String secDomainCode = context.<String> getProperty(SEC_DOMAIN_CODE);
        return ScriptUsageUtils.getPermissionsRelatedFqns(secDomainCode);
    }

    @Override
    protected boolean isUsageChangeable()
    {
        return false;
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, AccessMatrix holder, ScriptModifyContext context)
    {
        Key key = context.getProperty(KEY);

        /*
         * Необходимо для поддержания корявого механизма наследования в {@link AccessMatrixImplMappers} для
         * DeserializationMapper scriptCode != null - флаг, что значение было переопределено и поместить в кеш
         * AccessMatrixImpl.
         * При удалении переопределения скрипта в {@link
         * SecurityScriptModificationUtils#processDeleteAccessMatrixScripts} удаляется key
         */
        if (newScriptCode == null)
        {
            newScriptCode = StringUtilities.EMPTY;
        }

        if (holder instanceof AccessMatrixImpl)
        {
            AccessMatrixImpl cachedMatrix = (AccessMatrixImpl)holder;
            cachedMatrix.setScript(key, newScriptCode);
        }
        else
        {
            holder.getDeclaredScripts().put(key, newScriptCode);
        }
    }
}
