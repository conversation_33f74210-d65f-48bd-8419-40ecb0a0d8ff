package ru.naumen.core.server.script.libraries.validation;

import java.util.jar.JarFile;

import ru.naumen.core.server.script.libraries.ScanProcessResult;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;

/**
 * Валидатор библиотек
 * <AUTHOR>
 * @since 22.05.2020
 */
public interface LibraryContentValidator
{
    /**
     * Провалидировать контент заданной библиотеки
     * @param content библиотека
     * @return результат сканирования библиотек {@link ScanProcessResult}
     * @throws ValidationException если при валидации или сканировании возникнут ошибки
     */
    void validateContent(ScriptLibrary content, JarFile jarFile) throws ValidationException;
}
