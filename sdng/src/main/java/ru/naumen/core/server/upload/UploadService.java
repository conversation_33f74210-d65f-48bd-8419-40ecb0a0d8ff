package ru.naumen.core.server.upload;

import org.apache.commons.fileupload2.core.FileItem;

import ru.naumen.core.server.filestorage.conf.Storage;
import ru.naumen.core.server.web.servlets.UploadServlet;

/**
 * Сервис хранения загруженных на сервер файлов.
 * <p>
 * Т.к. через dispatch-сервлет нет возможности загружать файлы, то  предлагается следующий алгоритм
 * работы с файлами:
 * <ol>
 * <li>Файл загружается на сервер при помощи {@link UploadServlet}, клиенту сообщается идентификатор загруженного
 * файла</li>
 * <li>Клиент обращается к dispatch-сервлету, в команде указывает идентификатор сохранненного файла</li>
 * <li>Обработчик команды получает загруженный файл из сервиса по переданному идентификатору</li>
 * <li>Обработчик команды удаляет файл из сервиса если он больше не нужен</li>
 * </ol> 
 *
 * <p>
 * Сервис может выбрасывать {@link UploadServiceException} в случае ошибки
 *
 * <AUTHOR>
 *
 */
public interface UploadService
{
    /**
     * Добавляет ресурс в сервис
     *
     * @param item ресурс
     * @param system системный ли файл
     * @param moveFromDisk добавляем ли файл, расположенный в нашей файловой системе
     * @return идентификатор ресурса
     */
    String add(FileItem item, boolean system, boolean moveFromDisk);

    /**
     * Добавляет ресурс в сервис
     *
     * @param item ресурс
     * @param system системный ли файл
     * @return идентификатор ресурса
     */
    String add(FileItem item, boolean system);

    /**
     * Добавляет файл напрямую в файловое хранилище
     *
     * @param item ресурс
     * @param storage файловое хранилище
     * @return идентификатор ресурса
     */
    String addDirectly(final FileItem item, final Storage storage);

    /**
     * Удаляет ресурс с сзаданным идентификатором
     *
     * @param uuid идентификатор ресурса
     */
    void delete(String uuid);

    /**
     * Возвращает ресурс с заданным идентификатором
     *
     * @param uuid идентификатор ресурса
     * @return ресурс
     */
    FileItem<?> get(String uuid); //NOSONAR здесь рекурсивное объявление Generic, а возвращаем обобщённый тип

    /**
     * Возвращает размер файла с заданным идентификатором
     *
     * @param uuid идентификатор ресурса
     * @return размер файла в байтах
     */
    long getFileSize(String uuid);
}
