package ru.naumen.core.server.script.spi.mapper;

import java.util.Map;
import java.util.Set;

import org.springframework.stereotype.Component;

import com.google.gson.JsonObject;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.bo.ToJsonTransformer;
import ru.naumen.core.server.mapper.impl.AbstractMapper;
import ru.naumen.core.server.script.spi.ScriptDtOMap;
import ru.naumen.core.shared.criteria.DtoProperties;

/**
 * Преобразует объекты ScriptDtOMap в JSON по тем же правилам, что RestAPI преобразует объект в JSON
 *
 * <AUTHOR>
 * @since 03.02.2020
 */
@Component
public class ScriptDtOMapMapper extends AbstractMapper<ScriptDtOMap, JsonObject>
{
    private final ToJsonTransformer helper;

    @Inject
    public ScriptDtOMapMapper(ToJsonTransformer helper)
    {
        super(ScriptDtOMap.class, JsonObject.class);
        this.helper = helper;
    }

    @Override
    public void transform(ScriptDtOMap from, JsonObject to, @Nullable DtoProperties properties)
    {
        boolean isHasProperties = properties != null && !properties.getProperties().isEmpty();
        for (Map.Entry<Object, Object> entry : (Set<Map.Entry<Object, Object>>)from.entrySet())
        {
            if (!isHasProperties || properties.getProperties().contains(entry.getKey().toString()))
            {
                to.add(entry.getKey().toString(), helper.resolveValue(entry.getValue(), true, true));
            }
        }
    }
}
