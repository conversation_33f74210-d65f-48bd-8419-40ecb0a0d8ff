package ru.naumen.core.server.script.api.accesskeys;

import org.apache.commons.lang3.time.DateUtils;
import org.quartz.DisallowConcurrentExecution;
import org.quartz.JobExecutionException;
import org.quartz.PersistJobDataAfterExecution;

import ru.naumen.core.server.embeddedapplication.accesskey.EmbeddedApplicationKeyDao;
import ru.naumen.core.server.monitoring.stacktrace.DiagnosticsStackTraces;
import ru.naumen.core.server.scheduler.SchedulerProperties;
import ru.naumen.core.server.scheduler.job.annotation.ScheduledJob;
import ru.naumen.core.server.scheduler.job.base.InterruptableAbstractJob;
import ru.naumen.core.server.util.log.container.LogConfiguration;

/**
 * Задача удаления устаревших авторизационных ключей.
 *
 * <AUTHOR>
 * @since 08.06.2012
 */
@ScheduledJob(fixedRate = DateUtils.MILLIS_PER_HOUR, name = "DeleteExpiredAccessKeys")
@PersistJobDataAfterExecution
@DisallowConcurrentExecution
public class DeleteExpiredAccessKeysTask extends InterruptableAbstractJob
{
    private final AccessKeyDao accessKeyDao;
    private final EmbeddedApplicationKeyDao embeddedApplicationKeyDao;

    public DeleteExpiredAccessKeysTask(
            DiagnosticsStackTraces diagnosticsStackTraces,
            SchedulerProperties schedulerProperties,
            LogConfiguration logConfiguration, AccessKeyDao accessKeyDao,
            EmbeddedApplicationKeyDao embeddedApplicationKeyDao)
    {
        super(diagnosticsStackTraces, schedulerProperties, logConfiguration);
        this.accessKeyDao = accessKeyDao;
        this.embeddedApplicationKeyDao = embeddedApplicationKeyDao;
    }

    @Override
    protected void executeInt() throws JobExecutionException
    {
        accessKeyDao.deleteExpiredAccessKeys();
        embeddedApplicationKeyDao.deleteExpiredEmbeddedApplicationKeys();
    }
}
