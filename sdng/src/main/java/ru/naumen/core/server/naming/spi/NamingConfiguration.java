package ru.naumen.core.server.naming.spi;

import jakarta.inject.Inject;

import org.joda.time.DateTime;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.bo.AbstractBO;
import ru.naumen.core.server.bo.servicecall.ServiceCall;
import ru.naumen.core.server.mail.MailLogRecord;
import ru.naumen.core.server.naming.INamingEngine;
import ru.naumen.core.server.naming.INamingUnit;
import ru.naumen.core.server.naming.extractors.ConstantPeriodExtractor;
import ru.naumen.core.server.naming.extractors.MetaClassSequenceExtractorImpl;
import ru.naumen.core.server.naming.extractors.PeriodExtractor;
import ru.naumen.core.server.naming.generators.PeriodicalIDGenerator;
import ru.naumen.core.server.naming.generators.RandomIDGenerator;
import ru.naumen.core.server.naming.spi.units.GeneratorDependentUnit;

/**
 * Основная конфигурация сервиса "именования" (генерации значений) 
 *
 * <AUTHOR>
 *
 */
@Configuration
public class NamingConfiguration
{
    @Inject
    SpringContext springContext;

    /**
     * @return генератор в пределах дня
     */
    @Bean
    INamingUnit getDayNumberUnit()
    {
        MetaClassSequenceExtractorImpl sequenceExtractor = new MetaClassSequenceExtractorImpl("$ND");
        PeriodicalIDGenerator ndGen = new PeriodicalIDGenerator(sequenceExtractor, new PeriodExtractor<AbstractBO>()
        {
            @Override
            public long currentPeriod()
            {
                return extractPeriod(DateTime.now());
            }

            @Override
            public long extractPeriod(AbstractBO obj)
            {
                return extractPeriod(new DateTime(obj.getCreationDate()));
            }

            private long extractPeriod(DateTime dateTime)
            {
                return dateTime.getYear() * 10000l + dateTime.getMonthOfYear() * 100 + dateTime.getDayOfMonth();
            }
        });
        autowareBean(sequenceExtractor);
        springContext.autowireBean(ndGen);
        return new GeneratorDependentUnit(ndGen, "{ND}",
                "ru.naumen.sd.bobjects.nomenclature.designs.DayIdDesign.HelpString");
    }

    /**
     * Конфигурирует {@link INamingEngine} для {@link AbstractBO}. Этот engine будет использоваться
     * для всех метаклассов для которых не переопределена engine  
     */
    @Bean
    INamingEngine getDefaultEngine()
    {
        return new NamingEngineBase(ru.naumen.core.shared.Constants.AbstractBO.FQN);
    }

    /**
     *
     * Конфигурирует {@link INamingEngine} для {@link Mail}.  
     */
    @Bean
    INamingEngine getMailEngine()
    {
        return new NamingEngineBase(ru.naumen.core.shared.Constants.Mail.FQN);
    }

    /**
     *
     * Конфигурирует {@link INamingEngine} для {@link MailLogRecord}.  
     */
    @Bean
    INamingEngine getMailLogRecordEngine()
    {
        return new NamingEngineBase(ru.naumen.core.shared.Constants.MailLogRecord.FQN);
    }

    /**
     * @return "сквозной" генератор, но значения выдаются случайным образом из "пачек" по 1000 шт. 
     */
    @Bean
    INamingUnit getRandomNumberUnit()
    {
        MetaClassSequenceExtractorImpl sequenceExtractor = new MetaClassSequenceExtractorImpl("$RND");
        PeriodicalIDGenerator rndGen = new RandomIDGenerator(sequenceExtractor,
                new ConstantPeriodExtractor<ServiceCall>(0));
        autowareBean(sequenceExtractor);
        springContext.autowireBean(rndGen);
        return new GeneratorDependentUnit(rndGen, "{RND}",
                "ru.naumen.sd.bobjects.nomenclature.designs.RandomIdDesign.HelpString");
    }

    /**
     * @return "сквозной" генератор
     */
    @Bean
    INamingUnit getSerialNumberUnit()
    {
        MetaClassSequenceExtractorImpl sequenceExtractor = new MetaClassSequenceExtractorImpl("$N");
        PeriodicalIDGenerator nGen = new PeriodicalIDGenerator(sequenceExtractor,
                new ConstantPeriodExtractor<ServiceCall>(0));
        autowareBean(sequenceExtractor);
        springContext.autowireBean(nGen);
        return new GeneratorDependentUnit(nGen, "{N}",
                "ru.naumen.sd.bobjects.nomenclature.designs.IdDesign.HelpString");
    }

    private MetaClassSequenceExtractorImpl autowareBean(MetaClassSequenceExtractorImpl sequenceExtractor)
    {
        springContext.autowireBean(sequenceExtractor);
        return sequenceExtractor;
    }
}
