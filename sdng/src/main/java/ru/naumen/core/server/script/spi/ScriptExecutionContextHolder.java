package ru.naumen.core.server.script.spi;

/**
 * Контекст выполнения скрипта.
 *
 * <AUTHOR>
 * @since 23.09.2015
 */
public class ScriptExecutionContextHolder
{
    private static final ThreadLocal<ScriptExecutionResults> scriptExecutionResults = new ThreadLocal<>();

    public final static ScriptExecutionResults getScriptExecutionResults()
    {
        return scriptExecutionResults.get();
    }

    /**
     * Очистка контекста выполнения скрипта.
     * Необходимо производить, чтобы результат не оставался в {@link ThreadLocal} более, чем необходимо.
     */
    public final static void removeScriptExecutionResults()
    {
        scriptExecutionResults.remove();
    }

    public final static void setScriptExecutionResults(ScriptExecutionResults value)
    {
        scriptExecutionResults.set(value);
    }
}
