package ru.naumen.core.server.naming.spi.dispatch;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import net.customware.gwt.dispatch.server.AbstractActionHandler;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.server.naming.INamingService;
import ru.naumen.core.shared.dispatch.GetNamingHelpAction;
import ru.naumen.core.shared.dispatch.GetNamingHelpResponse;
import ru.naumen.metainfo.shared.NamingInfo;

/**
 * Обработчик {@link GetNamingHelpAction команды} получения справки по правилам именования
 *
 * <AUTHOR>
 *
 */
@Component
public class GetNamingHelpActionHandler extends AbstractActionHandler<GetNamingHelpAction, GetNamingHelpResponse>
{
    @Inject
    private INamingService namingService;

    public GetNamingHelpActionHandler()
    {
        super(GetNamingHelpAction.class);
    }

    @Override
    public GetNamingHelpResponse execute(GetNamingHelpAction action, ExecutionContext context) throws DispatchException
    {
        NamingInfo info = namingService.getInfo(action.getFqn(), action.getAttrTypeCode());
        return new GetNamingHelpResponse(info.getNamingHelp());
    }
}
