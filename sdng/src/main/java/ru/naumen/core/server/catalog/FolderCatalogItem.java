package ru.naumen.core.server.catalog;

import static org.hibernate.Length.LONG32;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.Transient;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.HasCase;
import ru.naumen.core.server.flex.HasFlexes;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.FolderCatalog;
import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.Group;
import ru.naumen.metainfo.server.annotations.Groups;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.server.annotations.RequireType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttrGroup;

/**
 * Элемент справочника "Каталоги"
 * Является каталогом бизнес объектов. 
 *
 * Для каждого класса бизнес объектов создается отдельный тип каталога.
 *
 * Элементы каталогов называются папками, при этом физически они являются элементами справочников
 * Не стали их делать папками в терминах {@link #isFolder()} чтобы уменьшить путаницу
 *
 * В сообщениях об ошибках и на формах данные элементы справочников нужно всегда называть Папками,
 * поэтому они рассматриваются отдельным случаем (if или отдельная стратегия)  
 *
 * <AUTHOR>
 * @since 19.12.2012
 */
//@formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Table(name = "tbl_folder", uniqueConstraints = { 
        @UniqueConstraint(name = "tbl_folder_code_case_key", columnNames = {"code", "case_id"})},
        indexes={@jakarta.persistence.Index(name = "idx_folder_parent", columnList="parent")})
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@DiscriminatorFormula("case_id")
@UUIDPrefix(FolderCatalogItem.CLASS_ID)
@Metaclass(id = FolderCatalogItem.CLASS_ID,
        title = { @LStr(value = "Папка"),
                @LStr(lang = "en", value = "Folder"),
                @LStr(lang = "de", value = "Ordner") },
        withCase = true)
@Catalog(code = FolderCatalog.CODE,
        title = { @LStr(value = "Каталоги"),
                @LStr(lang = "en", value = "Directories"),
                @LStr(lang = "de", value = "Kataloge") },
        description = { @LStr(value = "Содержит каталоги бизнес объектов."),
             @LStr(lang = "en", value = "Contains directories of business objects."),
             @LStr(lang = "de", value = "Enthält Kataloge von Geschäftsobjekten.") },
        flat = false, withFolders = true)
@Groups({
    @Group(code = AttrGroup.DISPLAY_GROUP, attrs = {"title", "code", "description"})
})
//@formatter:on
public class FolderCatalogItem extends CatalogItem<FolderCatalogItem> implements HasCase, HasFlexes
{
    public static final String CLASS_ID = FolderCatalog.ITEM_CLASS_ID;

    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link ru.naumen.core.server.flex.FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    @Column(name = "case_id", nullable = false)
    String metaCaseId;

    @Transient
    private ClassFqn fqn;

    @Column(name = "description", length = LONG32)
    @Attribute(code = Constants.FolderCatalog.DESCRIPTION, title = { @LStr(value = "Описание"),
            @LStr(lang = "en", value = "Description"), @LStr(lang = "de", value = "Description") },
            example = { @LStr(value = "Команды поддержки"),
                    @LStr(lang = "en", value = "Support teams"), @LStr(lang = "de", value = "Support teams") })
    private String description;

    public String getDescription()
    {
        return description;
    }

    @Override
    @Attribute(title = { @LStr(value = "Тип объекта"), @LStr(lang = "en", value = "Object type"),
            @LStr(lang = "de", value = "Object type") }, example = {
            @LStr(value = "Запрос") }, required = RequireType.SYSTEM_REQUIRED, editable = false, accessor =
            ru.naumen.metainfo.shared.Constants.Accessors.METACLASS)
    public ClassFqn getMetaClass()
    {
        if (null == fqn)
        {
            fqn = ClassFqn.parse(FolderCatalog.ITEM_CLASS_ID, metaCaseId);
        }
        return fqn;
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }

    public void setDescription(String description)
    {
        this.description = description;
    }

    public void setMetaCaseId(String metaCaseID)
    {
        this.metaCaseId = metaCaseID;
        fqn = null;
    }

    @Override
    public void setMetaClass(ClassFqn fqn)
    {
        this.fqn = fqn;
        this.metaCaseId = fqn.getCase();
    }
}
