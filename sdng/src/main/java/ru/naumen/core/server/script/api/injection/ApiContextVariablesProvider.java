package ru.naumen.core.server.script.api.injection;

import java.util.Date;
import java.util.Map;

import org.slf4j.Logger;
import org.springframework.beans.factory.BeanFactory;

import groovy.lang.Script;
import jakarta.annotation.Nullable;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.Version;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.geo.GeoService;
import ru.naumen.core.server.queueddate.QueuedDateService;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.api.ISearchParams;
import ru.naumen.core.server.script.api.SearchParams;
import ru.naumen.core.server.script.spi.IScriptConditionsApi;
import ru.naumen.core.server.script.spi.IScriptUtils;
import ru.naumen.core.server.script.spi.bindings.BindingsService;
import ru.naumen.core.server.script.spi.bindings.ScriptApiSource;
import ru.naumen.core.server.script.spi.bindings.ScriptBeansBindingsSource;
import ru.naumen.metainfo.shared.mobile.LatLng;

/**
 * Компонент-провайдер контекстных переменных (api, utils и т.д.)
 *
 * <AUTHOR>
 */
public final class ApiContextVariablesProvider
{
    private static final class LazyHolder
    {
        private static final ApiContextVariablesProvider INSTANCE = new ApiContextVariablesProvider();
    }

    public static ApiContextVariablesProvider getInstance()
    {
        return LazyHolder.INSTANCE;
    }

    public static String getAppVersion()
    {
        return Version.getVersion();
    }

    public static ISearchParams getSp()
    {
        return SearchParams.createInstance();
    }

    private final ScriptService scriptService;
    private final QueuedDateService queuedDateService;
    private final GeoService geoService;
    private final ScriptBeansBindingsSource scriptBeansBindingsSource;
    private final BindingsService bindingsService;

    private ApiContextVariablesProvider()
    {
        final SpringContext springContext = SpringContext.getInstance();

        this.scriptService = springContext.getBean(ScriptService.class);
        this.queuedDateService = springContext.getBean(QueuedDateService.class);
        this.geoService = springContext.getBean(GeoService.class);
        scriptBeansBindingsSource = springContext.getBean(ScriptBeansBindingsSource.ID,
                ScriptBeansBindingsSource.class);
        this.bindingsService = springContext.getBean(BindingsService.class);
    }

    public ScriptApiSource getApi()
    {
        return scriptBeansBindingsSource.getScriptApiSource();
    }

    public BeanFactory getBeanFactory()
    {
        return scriptBeansBindingsSource.getBeanFactory();
    }

    public Dispatch getDispatch()
    {
        return scriptBeansBindingsSource.getDispatch();
    }

    public LatLng getGeo()
    {
        return geoService.getGeo();
    }

    public Map<String, Script> getModules(@Nullable String embeddedApplicationCode)
    {
        return scriptService.getModulesReadyForUsingInScript(embeddedApplicationCode);
    }

    public IScriptConditionsApi getOp()
    {
        return scriptBeansBindingsSource.getConditionsApi();
    }

    public Date getQueuedDate()
    {
        return queuedDateService.getQueuedDate();
    }

    public IScriptUtils getUtils()
    {
        return scriptBeansBindingsSource.getUtils();
    }

    public Logger getLogger(@Nullable Class<?> scriptClass)
    {
        return bindingsService.getScriptLogger(scriptClass);
    }
}
