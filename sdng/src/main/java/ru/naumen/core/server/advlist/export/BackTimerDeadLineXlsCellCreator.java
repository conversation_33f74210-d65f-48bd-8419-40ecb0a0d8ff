package ru.naumen.core.server.advlist.export;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Workbook;

import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.core.shared.timer.BackTimerDto;
import ru.naumen.metainfo.shared.Constants.BackTimerAttributeType;
import ru.naumen.metainfo.shared.Constants.Presentations;

/**
 * Представление ячеек Excel для значений типа "Счетчик времени (обратный)" с выбранным представлением "Время
 * окончания".
 * <AUTHOR>
 * @since May 30, 2016
 */
@CellCreatorComponent(codes = { BackTimerAttributeType.CODE + IXlsCellCreatorsRegistry.PRESENTATION_SEPARATOR
                                + Presentations.BACKTIMER_DEADLINE_VIEW })
public class BackTimerDeadLineXlsCellCreator extends DateTimeXlsCellCreator
{
    @Override
    public void setCellValue(Cell cell, Object attributeValue, Workbook workbook, FormatterContext context)
    {
        if (null != attributeValue)
        {
            BackTimerDto dto = (BackTimerDto)attributeValue;
            super.setCellValue(cell, dto.getDeadLineTime(), workbook, context);
        }
    }
}
