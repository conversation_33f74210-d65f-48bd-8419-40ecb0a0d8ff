package ru.naumen.core.server.naming.generators.strategies;

import ru.naumen.commons.shared.utils.Pair;

/**
 * Средство сохранения значения последовательности внутри {@link NextValueGenerationStrategy}.
 *
 * <AUTHOR>
 * @since Dec 23, 2015
 *
 */
public interface SequenceSaver
{
    /**
     * Сохраняет значение последовательности.
     *
     * @param key идентификатор последовательности, период.
     * @param value сохраняемое значение.
     */
    void save(Pair<String, Long> key, int value);
}