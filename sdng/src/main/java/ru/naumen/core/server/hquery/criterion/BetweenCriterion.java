package ru.naumen.core.server.hquery.criterion;

import java.util.Objects;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * Условие сравнения "Между (включая границы)"
 * <AUTHOR>
 */
public class BetweenCriterion extends AbstractHCriterion
{
    private static final String BETWEEN = " BETWEEN "; //$NON-NLS-1$
    private static final String AND = " AND "; //$NON-NLS-1$

    /**
     * Левая граница
     */
    private final Object _left;

    /**
     * Правая граница
     */
    private final Object _right;

    private String _leftName;
    private String _rightName;

    public BetweenCriterion(HColumn property, Object left, Object right)
    {
        super(property);
        _left = left;
        _right = right;
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        _leftName = parameterCounter.next();
        _rightName = parameterCounter.next();

        sb.append(property.getHQL(builder));
        sb.append(BETWEEN);
        sb.append(':');
        sb.append(_leftName);
        sb.append(AND);
        sb.append(':');
        sb.append(_rightName);
    }

    @Override
    public void setParameters(Query q)
    {
        super.setParameters(q);
        q.setParameter(_leftName, _left);
        q.setParameter(_rightName, _right);
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new BetweenCriterion(property, _left, _right);
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        BetweenCriterion that = (BetweenCriterion)o;
        return _left.equals(that._left) &&
               _right.equals(that._right);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), _left, _right);
    }

    @Override
    public String toString()
    {
        return "BetweenCriterion{" +
               "_left=" + _left +
               ", _right=" + _right +
               ", property=" + property +
               '}';
    }
}