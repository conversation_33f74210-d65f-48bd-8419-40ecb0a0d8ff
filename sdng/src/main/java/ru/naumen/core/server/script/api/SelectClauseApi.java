package ru.naumen.core.server.script.api;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.api.criteria.ApiCriteria;
import ru.naumen.core.server.script.api.criteria.IApiCriteria;
import ru.naumen.core.server.script.api.criteria.IApiCriteriaColumn;
import ru.naumen.core.server.script.api.criteria.ICase;
import ru.naumen.core.server.script.api.criteria.ISimpleCase;
import ru.naumen.core.server.script.api.criteria.ISubquery;
import ru.naumen.core.server.script.api.criteria.caseexpression.SearchedCaseExpression;
import ru.naumen.core.server.script.api.criteria.caseexpression.SimpleCaseExpression;
import ru.naumen.core.server.script.api.criteria.column.ApiCriteriaAliasColumn;
import ru.naumen.core.server.script.api.criteria.column.ApiCriteriaCTEAliasColumn;
import ru.naumen.core.server.script.api.criteria.column.ApiCriteriaConstantColumn;
import ru.naumen.core.server.script.api.criteria.column.ApiCriteriaFunctionColumn;
import ru.naumen.core.server.script.api.criteria.column.ApiCriteriaMultiColumn;
import ru.naumen.core.server.script.api.criteria.column.ApiCriteriaPropertyColumn;
import ru.naumen.core.server.script.api.criteria.column.ApiCriteriaSubqueryFunctionColumn;
import ru.naumen.core.server.script.api.criteria.column.IApiCriteriaColumnInternal;
import ru.naumen.core.server.script.api.criteria.column.function.AbsDateTimeDifferenceMultiColumnFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnAggregateFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnCastFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnDateAddFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnDateFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnExtractFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnMathFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnRoundFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnStringFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnTimeZoneFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnTruncDateFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaColumnTruncFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaMultiColumnFunction;
import ru.naumen.core.server.script.api.criteria.column.function.ApiCriteriaSubqueryOperator;
import ru.naumen.core.server.script.api.criteria.criterion.handler.ApiCriterionHandlerService;

/**
 * Имплементация API для получения выражений select clause для
 * {@link ru.naumen.core.server.script.api.criteria.IApiCriteria}
 *
 * <AUTHOR>
 * @since 06.04.20
 */
@Component("selectClause")
public class SelectClauseApi implements ISelectClauseApi
{
    private final ApiCriterionHandlerService criterionHandlerService;

    @Inject
    public SelectClauseApi(ApiCriterionHandlerService criterionHandlerService)
    {
        this.criterionHandlerService = criterionHandlerService;
    }

    @Override
    public IApiCriteriaColumn abs(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnMathFunction.ABS);
    }

    @Override
    public IApiCriteriaColumn absDurationInUnits(IApiCriteriaColumn left, IApiCriteriaColumn right, String units)
    {
        return new ApiCriteriaMultiColumn(cast(new IApiCriteriaColumn[] { left, right }),
                new AbsDateTimeDifferenceMultiColumnFunction(units));
    }

    @Override
    public IApiCriteriaColumn alias(String alias)
    {
        return new ApiCriteriaAliasColumn(alias);
    }

    @Override
    public IApiCriteriaColumn all(ISubquery subquery)
    {
        return new ApiCriteriaSubqueryFunctionColumn(subquery,
                ApiCriteriaSubqueryOperator.ALL);
    }

    @Override
    public IApiCriteriaColumn any(ISubquery subquery)
    {
        return new ApiCriteriaSubqueryFunctionColumn(subquery,
                ApiCriteriaSubqueryOperator.ANY);
    }

    @Override
    public IApiCriteriaColumn atTimeZone(IApiCriteriaColumn dateColumn, String timeZone)
    {
        Objects.requireNonNull(timeZone, "Parameter 'timeZone' must be not null");
        return new ApiCriteriaFunctionColumn(cast(dateColumn),
                new ApiCriteriaColumnTimeZoneFunction(timeZone));
    }

    @Override
    public IApiCriteriaColumn avg(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnAggregateFunction.AVG);
    }

    @Override
    public IApiCriteriaColumn cast(IApiCriteriaColumn column, String resultType)
    {
        return new ApiCriteriaFunctionColumn(cast(column), new ApiCriteriaColumnCastFunction(resultType));
    }

    @Override
    public IApiCriteriaColumn coalesce(IApiCriteriaColumn... columns) throws IllegalArgumentException
    {
        if (columns.length == 0)
        {
            throw new IllegalArgumentException("There must be some columns in coalesce function");
        }
        if (columns.length == 1)
        {
            /*
             * В вырожденном случае с 1 колонкой сразу возвращаем её. Во-первых, чтобы не нагружать БД лишний раз.
             * А во-вторых, потому что MS SQL Server в случае с 1 аргументом выдаёт ошибку.
             */
            return columns[0];
        }
        return new ApiCriteriaMultiColumn(cast(columns), ApiCriteriaMultiColumnFunction.COALESCE);
    }

    @Override
    public IApiCriteriaColumn columnDivide(IApiCriteriaColumn left, IApiCriteriaColumn right)
    {
        return new ApiCriteriaMultiColumn(List.of(cast(left), cast(right)), ApiCriteriaMultiColumnFunction.DIVIDE);
    }

    @Override
    public IApiCriteriaColumn columnMultiply(IApiCriteriaColumn... columns)
    {
        return new ApiCriteriaMultiColumn(cast(columns), ApiCriteriaMultiColumnFunction.MULTIPLY);
    }

    @Override
    public IApiCriteriaColumn columnSubtract(IApiCriteriaColumn left, IApiCriteriaColumn right)
    {
        return new ApiCriteriaMultiColumn(List.of(cast(left), cast(right)), ApiCriteriaMultiColumnFunction.SUBTRACT);
    }

    @Override
    public IApiCriteriaColumn columnSum(IApiCriteriaColumn... columns)
    {
        return new ApiCriteriaMultiColumn(cast(columns), ApiCriteriaMultiColumnFunction.SUM);
    }

    @Override
    public IApiCriteriaColumn concat(IApiCriteriaColumn... columns)
    {
        return new ApiCriteriaMultiColumn(cast(columns), ApiCriteriaMultiColumnFunction.CONCAT);
    }

    @Override
    public IApiCriteriaColumn constant(Object constantValue) throws NullPointerException
    {
        return new ApiCriteriaConstantColumn(constantValue);
    }

    @Override
    public IApiCriteriaColumn count(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnAggregateFunction.COUNT);
    }

    @Override
    public IApiCriteriaColumn countDistinct(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnAggregateFunction.COUNT_DISTINCT);
    }

    @Override
    public IApiCriteriaColumn cteAlias(IApiCriteria cteCriteria, String columnAlias)
    {
        return new ApiCriteriaCTEAliasColumn((ApiCriteria)cteCriteria, columnAlias);
    }

    @Override
    public IApiCriteriaColumn dateAdd(IApiCriteriaColumn dateColumn, String datePart, Number number)
    {
        Objects.requireNonNull(number, "Parameter 'number' must be not null");
        return new ApiCriteriaFunctionColumn(cast(dateColumn),
                new ApiCriteriaColumnDateAddFunction(datePart, number.longValue()));
    }

    @Override
    public IApiCriteriaColumn day(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnDateFunction.DAY);
    }

    @Override
    public IApiCriteriaColumn dayOfWeek(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnDateFunction.DAY_OF_WEEK);
    }

    @Override
    public IApiCriteriaColumn extract(IApiCriteriaColumn column, String extractedField)
    {
        return new ApiCriteriaFunctionColumn(cast(column), new ApiCriteriaColumnExtractFunction(extractedField));
    }

    @Override
    public IApiCriteriaColumn hour(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnDateFunction.HOUR);
    }

    @Override
    public IApiCriteriaColumn lower(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnStringFunction.LOWER);
    }

    @Override
    public IApiCriteriaColumn max(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnAggregateFunction.MAX);
    }

    @Override
    public IApiCriteriaColumn min(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnAggregateFunction.MIN);
    }

    @Override
    public IApiCriteriaColumn minute(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnDateFunction.MINUTE);
    }

    @Override
    public IApiCriteriaColumn month(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnDateFunction.MONTH);
    }

    @Override
    public IApiCriteriaColumn property(String... propertyNames)
    {
        return new ApiCriteriaPropertyColumn(propertyNames);
    }

    @Override
    public IApiCriteriaColumn property(IApiCriteria criteria, String... propertyNames)
    {
        return new ApiCriteriaPropertyColumn((ApiCriteria)criteria, propertyNames);
    }

    @Override
    public IApiCriteriaColumn quarter(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnDateFunction.QUARTER);
    }

    @Override
    public IApiCriteriaColumn round(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column),
                new ApiCriteriaColumnRoundFunction());
    }

    @Override
    public IApiCriteriaColumn round(IApiCriteriaColumn column, IApiCriteriaColumn accuracyColumn)
    {
        return new ApiCriteriaMultiColumn(Arrays.asList(cast(column), cast(accuracyColumn)),
                new ApiCriteriaColumnRoundFunction());
    }

    @Override
    public IApiCriteriaColumn round(IApiCriteriaColumn column, Number accuracy)
    {
        Objects.requireNonNull(accuracy, "Parameter 'accuracy' must be not null");
        return new ApiCriteriaFunctionColumn(cast(column),
                new ApiCriteriaColumnRoundFunction(accuracy.intValue()));
    }

    @Override
    public IApiCriteriaColumn second(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnDateFunction.SECOND);
    }

    private static IApiCriteriaColumnInternal cast(IApiCriteriaColumn column)
    {
        return (IApiCriteriaColumnInternal)column;
    }

    private static List<IApiCriteriaColumnInternal> cast(IApiCriteriaColumn[] columns)
    {
        return Arrays.stream(columns).map(IApiCriteriaColumnInternal.class::cast).collect(Collectors.toList());
    }

    @Override
    public <C, R> ISimpleCase<C, R> selectCase(IApiCriteriaColumn column)
    {
        return new SimpleCaseExpression<>(cast(column));
    }

    @Override
    public <R> ICase<R> selectCase()
    {
        return new SearchedCaseExpression<>(criterionHandlerService);
    }

    @Override
    public IApiCriteriaColumn some(ISubquery subquery)
    {
        return new ApiCriteriaSubqueryFunctionColumn(subquery,
                ApiCriteriaSubqueryOperator.SOME);
    }

    @Override
    public IApiCriteriaColumn sum(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnAggregateFunction.SUM);
    }

    @Override
    public IApiCriteriaColumn trunc(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column),
                new ApiCriteriaColumnTruncFunction());
    }

    @Override
    public IApiCriteriaColumn trunc(IApiCriteriaColumn column, Number decimalPlaces)
    {
        Objects.requireNonNull(decimalPlaces, "Parameter 'decimalPlaces' must be not null");
        return new ApiCriteriaFunctionColumn(cast(column),
                new ApiCriteriaColumnTruncFunction(decimalPlaces.intValue()));
    }

    @Override
    public IApiCriteriaColumn truncDate(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column),
                new ApiCriteriaColumnTruncDateFunction());
    }

    @Override
    public IApiCriteriaColumn truncDate(IApiCriteriaColumn column, String field)
    {
        return new ApiCriteriaFunctionColumn(cast(column),
                new ApiCriteriaColumnTruncDateFunction(field));
    }

    @Override
    public IApiCriteriaColumn upper(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnStringFunction.UPPER);
    }

    @Override
    public IApiCriteriaColumn week(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnDateFunction.WEEK);
    }

    @Override
    public IApiCriteriaColumn year(IApiCriteriaColumn column)
    {
        return new ApiCriteriaFunctionColumn(cast(column), ApiCriteriaColumnDateFunction.YEAR);
    }
}
