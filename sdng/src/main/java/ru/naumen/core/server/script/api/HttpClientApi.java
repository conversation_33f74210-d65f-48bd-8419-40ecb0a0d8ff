package ru.naumen.core.server.script.api;

import java.io.IOException;
import java.lang.reflect.Type;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpRequest.BodyPublishers;
import java.net.http.HttpRequest.Builder;
import java.net.http.HttpResponse;
import java.net.http.HttpResponse.BodyHandlers;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.security.SecureRandom;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Map;

import javax.net.ssl.SSLContext;

import org.apache.http.ssl.SSLContexts;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.web.context.request.RequestAttributes;
import org.springframework.web.context.request.RequestContextHolder;

import com.google.common.base.Functions;
import com.google.gson.Gson;
import com.google.gson.JsonSyntaxException;
import com.google.gson.reflect.TypeToken;

import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import jakarta.servlet.http.HttpSession;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.keystore.KeyStoreService;
import ru.naumen.core.server.script.api.http.HttpBodyPublishers;
import ru.naumen.core.server.script.api.http.IHttpBodyPublishers;

/**
 * Минимальная реализация взаимодействия по HTTP протоколу:
 * метод POST c парсингом JSON ответа в Map<String, String>  
 *
 * <AUTHOR>
 * @since ********
 */
@Component("http")
public class HttpClientApi implements IHttpClientApi
{
    private static final Logger LOG = LoggerFactory.getLogger(HttpClientApi.class);
    private static final String ABSENT_SESSION = "Absent http session";
    private static final String PATCH = "PATCH";
    private static final String GET = "GET";
    private static final String PUT = "PUT";
    private static final String POST = "POST";
    private static final String DELETE = "DELETE";

    @Value("${ru.naumen.httpclientapi.postjson.timeout}")
    String configurationTimeout;

    private final KeyStoreService keyStoreService;

    private int timeout = 5000;

    @Inject
    public HttpClientApi(KeyStoreService keyStoreService)
    {
        this.keyStoreService = keyStoreService;
    }

    @Override
    public Map<String, Object> getJSON(String url)
    {
        return getJSON(url, null);
    }

    @Override
    public Map<String, Object> getJSON(String url, @Nullable Map<String, String> headers)
    {
        HttpRequest request = createHttpRequest(GET, url, null, headers);
        return getResponseJSON(request);
    }

    @Override
    public Object getSessionAttribute(String attr)
    {
        RequestAttributes requestAttribute = RequestContextHolder.getRequestAttributes();
        if (requestAttribute != null)
        {
            HttpSession httpSession = (HttpSession)requestAttribute
                    .resolveReference(RequestAttributes.REFERENCE_SESSION);
            if (httpSession != null)
            {
                return httpSession.getAttribute(attr);
            }
        }
        throw new FxException(ABSENT_SESSION);
    }

    public int getTimeout()
    {
        return timeout;
    }

    @Override
    public boolean hasHttpSession()
    {
        RequestAttributes requestAttribute = RequestContextHolder.getRequestAttributes();
        if (requestAttribute != null)
        {
            HttpSession httpSession = (HttpSession)requestAttribute
                    .resolveReference(RequestAttributes.REFERENCE_SESSION);
            return httpSession != null;
        }
        return false;
    }

    @PostConstruct
    public void init()
    {
        if (!StringUtilities.isEmpty(configurationTimeout))
        {
            try
            {
                timeout = Integer.parseInt(configurationTimeout);
            }
            catch (NumberFormatException e)
            {
                LOG.error("Wrong property ru.naumen.httpclientapi.postjson.timeout", e);
                timeout = 5000;
            }
        }
    }

    @Override
    @Nullable
    public SSLContext getDefaultSSLContext()
    {
        SSLContext sslContext = SSLContexts.createDefault();

        try
        {
            sslContext.init(null, keyStoreService.getAllTrustManagers(), new SecureRandom());
        }
        catch (KeyManagementException e)
        {
            LOG.error(e.getMessage(), e);
            return null;
        }

        return sslContext;
    }

    @Override
    public Map<String, Object> postJSON(String url, @Nullable Map<String, Object> data)
    {
        return postJSON(url, data, null);
    }

    @Override
    public Map<String, Object> postJSON(String url, @Nullable Map<String, Object> data,
            @Nullable Map<String, String> headers)
    {
        HttpRequest request = createHttpRequest(POST, url, data, headers);
        return getResponseJSON(request);
    }

    @Override
    public Map<String, Object> patchJSON(String url, @Nullable Map<String, Object> data,
            @Nullable Map<String, String> headers)
    {
        HttpRequest request = createHttpRequest(PATCH, url, data, headers);
        return getResponseJSON(request);
    }

    @Override
    public Map<String, Object> deleteJSON(String url, @Nullable Map<String, String> headers)
    {
        HttpRequest request = createHttpRequest(DELETE, url, null, headers);
        return getResponseJSON(request);
    }

    @Override
    public Map<String, Object> putJSON(String url, @Nullable Map<String, Object> data,
            @Nullable Map<String, String> headers)
    {
        HttpRequest request = createHttpRequest(PUT, url, data, headers);
        return getResponseJSON(request);
    }

    @Override
    public IHttpBodyPublishers getBodyPublishers()
    {
        return new HttpBodyPublishers();
    }

    @Override
    public void removeSessionAttribute(String attr)
    {
        RequestAttributes requestAttribute = RequestContextHolder.getRequestAttributes();
        if (requestAttribute != null)
        {
            HttpSession httpSession = (HttpSession)requestAttribute
                    .resolveReference(RequestAttributes.REFERENCE_SESSION);
            if (httpSession != null)
            {
                httpSession.removeAttribute(attr);
                return;
            }
        }
        throw new FxException(ABSENT_SESSION);
    }

    @Override
    public void setSessionAttribute(String name, Object value)
    {
        RequestAttributes requestAttribute = RequestContextHolder.getRequestAttributes();
        if (requestAttribute != null)
        {
            HttpSession httpSession = (HttpSession)requestAttribute
                    .resolveReference(RequestAttributes.REFERENCE_SESSION);
            if (httpSession != null)
            {
                httpSession.setAttribute(name, value);
                return;
            }
        }
        throw new FxException(ABSENT_SESSION);
    }

    public void setTimeout(int value)
    {
        timeout = value;
    }

    /**
     * Метод создаёт HttpRequest
     * @param method название метода запроса
     * @param uri URI для запроса
     * @param data данные для запроса
     * @param headers заголовки запроса
     * @return HttpRequest
     */
    private HttpRequest createHttpRequest(String method, String uri, @Nullable Map<String, Object> data,
            @Nullable Map<String, String> headers)
    {
        Builder builder = HttpRequest.newBuilder()
                .uri(URI.create(uri))
                .timeout(Duration.ofMillis(this.timeout));

        if (data != null && (POST.equals(method) || PUT.equals(method)))
        {
            builder.method(method, BodyPublishers.ofString(new Gson().toJson(data)));
        }
        else
        {
            builder.method(method, BodyPublishers.noBody());
        }
        if (headers != null)
        {
            headers.forEach(builder::header);
        }
        return builder.build();
    }

    /**
     * Сформировать карту из результатов выполнения запроса
     * @param response результат выполнения запроса
     * @return возвращает карту, сформированную из запроса. В случае, если responseBody
     * отсутствует, возвращает пустую немутабельную карту
     */
    private static Map<String, Object> extractResponse(HttpResponse<String> response)
    {
        String responseBody = response.body();

        if (responseBody == null)
        {
            return Map.of();
        }
        try
        {
            return parseJson(responseBody);
        }
        catch (JsonSyntaxException e)
        {
            //Пишем в лог ответ сервера, который не удалось преобразовать в MAP.
            //Может содержать много текста. Скрыт по умолчанию.
            LOG.debug("Error converting response JSON to Map. Response data: '{}'", responseBody.trim());
            throw new FxException("Error converting response JSON to Map. Cause: " + e.getMessage());
        }
    }

    /**
     * Получить HttpClient с возможностью автоматического подтверждения SSL соединений
     * @return HttpClient
     * @throws NoSuchAlgorithmException если запрашиваемый алгоритм TLS не поддерживается текущей средой
     * @throws KeyManagementException если возникает ошибка при инициализации SSL контекста
     */
    HttpClient getClient() throws NoSuchAlgorithmException, KeyManagementException
    {
        SSLContext sslContext = SSLContext.getInstance("TLS");
        sslContext.init(null, keyStoreService.getAllTrustManagers(), new SecureRandom());
        return HttpClient.newBuilder()
                .connectTimeout(Duration.ofMillis(timeout))
                .sslContext(sslContext)
                .build();
    }

    /**
     * Выполнить http запрос и вернуть карту из JSON ответа сервера
     * @param request метод запроса
     * @return карта из JSON ответа
     */
    private Map<String, Object> getResponseJSON(HttpRequest request)
    {
        try (HttpClient client = getClient())
        {
            HttpResponse<String> response = client.send(request, BodyHandlers.ofString());

            return extractResponse(response);
        }
        catch (InterruptedException e)
        {
            Thread.currentThread().interrupt();
            throw new FxException("HTTP %s request to %s was interrupted".formatted(request.method(), request.uri()),
                    e);
        }
        catch (IOException e)
        {
            throw new FxException("Error during HTTP %s request to %s".formatted(request.method(), request.uri()), e);
        }
        catch (KeyManagementException | NoSuchAlgorithmException e)
        {
            LOG.error(String.format("Error during creation SSLContext: %s", e.getMessage()), e);
            return Map.of();
        }
    }

    /**
     * Выполняет распознание типа JSON и его преобразование в карту
     * @param stringJson исходная JSON строка
     * @return карта, полученная из JSON строки
     */
    private static Map<String, Object> parseJson(String stringJson)
    {
        Gson gson = new Gson();
        //Если JSON не начинается с массива, то преобразуем ее в карту
        //Если JSON начинается с массива, то преобразуем ее в коллекцию карт и потом в объединим в одну карту
        if (!(stringJson.startsWith("[") && stringJson.endsWith("]")))
        {
            return gson.fromJson(stringJson, new TypeToken<Map<String, Object>>()
            {
            }.getType());
        }
        else
        {
            Type mapArray = new TypeToken<ArrayList<Map<String, Object>>>()
            {
            }.getType();
            final ArrayList<Map<String, Object>> data = gson.fromJson(stringJson, mapArray);
            return CollectionUtils.convertToMap(data, input -> String.valueOf(data.indexOf(input)),
                    Functions.identity());
        }
    }
}
