package ru.naumen.core.server.advlist.export.DeferredExport;

import static ru.naumen.reports.shared.Constants.ReportFormat.XLSX;

import java.io.ByteArrayOutputStream;
import java.util.Collections;
import java.util.Date;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Locale;
import java.util.TimeZone;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.context.i18n.LocaleContextHolder;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.commons.server.utils.DateUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.MimeTypeRegistry;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.TimeZoneUtils;
import ru.naumen.core.server.advlist.AdvlistExportParameters;
import ru.naumen.core.server.advlist.export.GetAdvlistXlsFile;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.bo.IDao;
import ru.naumen.core.server.eventaction.jms.Notification;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.service.FileService;
import ru.naumen.core.server.hibernate.measurement.DBTimeMeasurementContainer;
import ru.naumen.core.server.hibernate.measurement.DBTimeMeasurementContainer.DBTimeMeasurement;
import ru.naumen.core.server.i18n.LocaleUtils;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.mail.MimeMailWrapper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.Employee;
import ru.naumen.core.shared.common.Formatters;
import ru.naumen.core.shared.criteria.DtoCriteria;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.mailsender.server.outgoingmailserver.MailSettingsService;
import ru.naumen.mailsender.server.service.IMimeMailWrapperDefinition;
import ru.naumen.mailsender.server.service.MimeMailWrapperDefinition;
import ru.naumen.mailsender.server.service.SimpleSendMailServiceImpl;
import ru.naumen.mailsender.server.service.TransactionalMailServiceImpl;
import ru.naumen.mailsender.shared.MailSenderConstants;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.DateTimeIntervalAttributeType.Interval;
import ru.naumen.sec.server.users.employee.EmployeeUserDetailsService;
import ru.naumen.sec.server.utils.CoreSecurityUtils;

/**
 * Обработка сообщений построения отложенного экспорта.
 * Используется для экспорта через API
 *
 * <AUTHOR>
 */
@Component
public class AdvlistExportPerformer
{
    private static final Logger LOG = LoggerFactory.getLogger(AdvlistExportPerformer.class);

    @Inject
    private SpringContext springContext;
    @Inject
    private DaoFactory dao;
    @Inject
    private CoreSecurityUtils securityUtils;
    @Inject
    private AdvlistExportParameters advlistParameters;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private AdvlistExportService exportService;
    @Inject
    private MessageFacade messages;
    @Inject
    private FileService fileService;
    @Inject
    private Formatters formatters;
    @Inject
    private SimpleSendMailServiceImpl simpleSendMailService;
    @Inject
    @Lazy
    private TransactionalMailServiceImpl transactionalMailSevice;
    @Inject
    private MailSettingsService mailSettings;
    @Inject
    private LocaleUtils localeUtils;
    @Inject
    private EmployeeUserDetailsService employeeUserDetailsService;
    @Inject
    private ConfigurationProperties configurationProperties;

    public String createAdvlistExportFile(DtoCriteria criteria, LinkedHashMap<String, String> properties,
            List<String> attrFqns, TimeZone timeZone)
    {
        return createSendAdvlistExportFile(null, null, criteria, properties, attrFqns, timeZone, null);
    }

    public void createSendAdvlistExportFile(AdvlistExportMessage message, AdvlistExportProcess advExportProcess)
    {
        createSendAdvlistExportFile(message.getUserUUID(), message.getEmail(),
                message.getCriteria(), message.getProperties(), message.getAttrFqns(),
                message.getTimeZone(), advExportProcess);
    }

    private String createSendAdvlistExportFile(@Nullable final String userUUID, @Nullable final String email,
            DtoCriteria criteria, final LinkedHashMap<String, String> properties, List<String> attrFqns,
            final TimeZone timeZone, @Nullable final AdvlistExportProcess advlistExportProcess)
    {
        try (DBTimeMeasurement measurementHolder = DBTimeMeasurementContainer.measure())
        {
            if (userUUID != null && UuidHelper.isValid(userUUID)
                && Employee.CLASS_ID.equals(UuidHelper.toPrefix(userUUID)))
            {
                securityUtils.authByUserPrincipal(employeeUserDetailsService.loadByUUID(userUUID));
            }
            final boolean forApi = advlistExportProcess == null;
            ClassFqn fqn = criteria.getClassFqn();
            String name = metainfoService.getMetaClass(fqn).getTitle();
            IDao<?> iDao = dao.get(forApi ? fqn : fqn.fqnOfClass());
            List<String> uuids = TransactionRunner.call(TransactionType.NEW_READ_ONLY, () -> iDao.listUuids(criteria));

            Locale locale = localeUtils.getUserLocale(userUUID);
            LocaleContextHolder.setLocale(locale);
            securityUtils.authAsSuperUser("advlistExport");

            LOG.info("Start export of object list of class '{}' {}", name, fqn);
            long startDefferedExport = System.currentTimeMillis();

            final ByteArrayOutputStream os = new ByteArrayOutputStream();
            final GetAdvlistXlsFile xls = springContext.createBean(GetAdvlistXlsFile.class);
            xls.prepare(advlistParameters.getExportBatchSize(), uuids, properties, attrFqns, timeZone);
            try
            {
                xls.createWorkbook(advlistExportProcess);
                xls.write(os);
            }
            catch (AdvlistExportInterruptedException ignore)
            {
                throw new AdvlistExportInterruptedException(
                        "Has been interrupt export of object list of class '%s' %s".formatted(name, fqn));
            }
            catch (Exception e)
            {
                throw new FxException(e);
            }
            finally
            {
                xls.dispose();
            }

            String fileUUID = createExportFile(userUUID, timeZone, forApi, os);

            LOG.info("SQL({}) Done({}): End export of object list of class '{}' {}",
                    measurementHolder.getJdbcTime(), System.currentTimeMillis() - startDefferedExport, name, fqn);

            String link = fileService.createFileDownloadLink(fileUUID);
            if (!forApi)
            {
                TransactionRunner.run(TransactionType.NEW, () -> sendEmail(name, uuids.size(), link, timeZone, email));
            }
            return link;
        }
    }

    private String createExportFile(@Nullable String userUUID, TimeZone timeZone, boolean forApi,
            ByteArrayOutputStream os)
    {
        final String fileName = exportService.buildFileName(timeZone);
        LOG.info("Creating exported file : {}", fileName);

        long start = System.currentTimeMillis();
        try (final DBTimeMeasurement measurement = DBTimeMeasurementContainer.measure())
        {
            final File file = fileService.createTemporaryFile(os.toByteArray(), fileName,
                    MimeTypeRegistry.getMimeTypeByFileExtension(XLSX), userUUID, getLifetime(), !forApi, false);

            LOG.info("SQL({}) Done({}): Creating exported file: {}", measurement.getJdbcTime(),
                    System.currentTimeMillis() - start, fileName);
            return file.getUUID();
        }
    }

    /**
     * Миграция на новый параметр времени хранения файла
     *
     * @return
     */
    private int getLifetime()
    {
        return advlistParameters.getLiveDay() != 3
                ? advlistParameters.getLiveDay()
                : fileService.getExportFileLifetime();
    }

    /**
     * Отправка письма с экспортированными данными
     *
     * @param name название класса
     * @param uuidCount количество экспортированных запросов
     * @param link ссылка на файл
     * @param timeZone часовой пояс пользователя
     * @param email адрес пользователя
     */
    private void sendEmail(String name, int uuidCount, String link, TimeZone timeZone, String email)
    {
        Date currentDate = new Date();
        String subject = messages.getMessage("Advlist.export.downloadMail.subject",
                name, TimeZoneUtils.getFormattedDateTime(timeZone, new Date()));

        Date endDate = DateUtils.addDays(currentDate, getLifetime());
        DateTimeInterval dtInterval = new DateTimeInterval(getLifetime(), Interval.DAY);

        String message = messages.getMessage("Advlist.export.downloadMail.description",
                uuidCount, name, link, formatters.formatDateTimeInterval(dtInterval),
                TimeZoneUtils.getFormattedDateTime(timeZone, endDate));

        Notification notification = new Notification.Builder()
                .setParameters(mailSettings.getSendMailParameters(MailSenderConstants.DEFAULT_CONFIG_CODE))
                .setTo(Collections.singletonMap(email, email))
                .setSubject(subject)
                .setContentType(MimeMailWrapper.TEXT_PLAIN)
                .setUseUpperCase(configurationProperties.getUseEmailAllowUppercase())
                .build();

        IMimeMailWrapperDefinition definition = new MimeMailWrapperDefinition()
                .notification(notification)
                .message(message);
        transactionalMailSevice.sendMail(simpleSendMailService.createMimeMailWrapper(definition));

        LOG.info("""
                Mail with link for downloading file with list of {} objects of class '{}' was prepared to send to\
                addressee {}""", uuidCount, name, email);
    }
}
