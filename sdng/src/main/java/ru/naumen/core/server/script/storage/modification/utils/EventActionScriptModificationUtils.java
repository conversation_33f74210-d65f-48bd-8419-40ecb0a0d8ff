package ru.naumen.core.server.script.storage.modification.utils;

import java.util.ArrayList;
import java.util.List;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.core.shared.dto.DtoContainer;
import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.server.script.storage.ScriptStorageService;
import ru.naumen.core.server.script.storage.modification.usage.ActionConditionScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyProcess;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyRegistry;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.metainfo.server.spi.dispatch.eventaction.EventActionScriptModifyContextUtils;
import ru.naumen.metainfo.shared.dispatch2.eventaction.SaveActionConditionAction;
import ru.naumen.metainfo.shared.dispatch2.eventaction.SaveEventAction;
import ru.naumen.metainfo.shared.dispatch2.script.SimpleScriptedResult;
import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.ScriptActionCondition;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptDto;
import ru.naumen.metainfo.shared.script.ScriptDtoFactory;

/**
 * Утилитарные методы для сохранения/удаления скриптов в действия по событиям
 * <AUTHOR>
 * @since Dec 23, 2015
 */
@Component
public class EventActionScriptModificationUtils
{
    @Inject
    private ScriptStorageService scriptStorageService;
    @Inject
    private ScriptDtoFactory scriptDtoFactory;
    @Inject
    private ScriptModifyRegistry scriptModifyRegistry;
    @Inject
    private EventActionService eventActionService;

    /**
     * Проставляет скрипты, используемые в actionCondition, в result в зависимости от флага isWithScript
     * @param isWithScript - если true, то скрипты проставляются
     */
    public void processActionConditionScripts(ActionCondition actionCondition, boolean isWithScript,
            SimpleScriptedResult<DtoContainer<ActionCondition>> result)
    {
        if (actionCondition instanceof ScriptActionCondition && isWithScript)
        {
            result.setWithScripts(true);
            ScriptActionCondition scriptCondition = (ScriptActionCondition)actionCondition;
            Script script = scriptStorageService.getScript(scriptCondition.getScript());
            result.putScript(scriptDtoFactory.create(script));
        }
    }

    /**
     * Производит удаление из скриптов условий действий по событию eventAction'а точек использования
     */
    public List<ScriptAdminLogInfo> processDeleteActionConditionFromEventActionScript(EventAction eventAction)
    {
        if (eventAction.getConditions().isEmpty())
        {
            return new ArrayList<>();
        }

        ScriptModifyProcess<ActionCondition> process = scriptModifyRegistry.getProcess(eventAction.getConditions().get(
                0));
        ScriptModifyContext context = new ScriptModifyContext(
                EventActionScriptModifyContextUtils.getActionConditionScriptCategory(eventAction),
                EventActionScriptModifyContextUtils.getActionConditionScriptHolder(eventAction));
        context.put(ActionConditionScriptModifyProcess.EVENT_ACTION_HOLDER, eventAction);

        for (ActionCondition actionCondition : eventAction.getConditions())
        {
            process.deleteHolder(actionCondition, context);
        }
        return context.getScriptsLogInfo();
    }

    /**
     * Производит удаление из скрипта условия действия по событию точек использования
     */
    public List<ScriptAdminLogInfo> processDeleteActionConditionScript(ActionCondition condition,
            EventAction eventAction)
    {
        if (!(condition instanceof ScriptActionCondition))
        {
            return new ArrayList<ScriptAdminLogInfo>();
        }

        ScriptModifyProcess<ActionCondition> process = scriptModifyRegistry.getProcess(condition);
        ScriptModifyContext context = new ScriptModifyContext(
                EventActionScriptModifyContextUtils.getActionConditionScriptCategory(eventAction),
                EventActionScriptModifyContextUtils.getActionConditionScriptHolder(eventAction));

        context.put(ActionConditionScriptModifyProcess.EVENT_ACTION_HOLDER, eventAction);
        process.deleteHolder(condition, context);
        return context.getScriptsLogInfo();
    }

    public List<ScriptAdminLogInfo> processSaveScripts(final EventAction eventAction)
    {
        final List<ScriptAdminLogInfo> result = new ArrayList<>();
        result.addAll(processDeleteEventActionScript(eventAction));
        result.addAll(processDeleteActionConditionFromEventActionScript(eventAction));

        return result;
    }

    /**
     * Производит удаление из скрипта eventAction'а точек использования
     */
    public List<ScriptAdminLogInfo> processDeleteEventActionScript(EventAction eventAction)
    {
        ScriptModifyProcess<EventAction> process = scriptModifyRegistry.getProcess(eventAction);

        ScriptModifyContext context = new ScriptModifyContext(
                EventActionScriptModifyContextUtils.getEventActionScriptCategory(eventAction),
                EventActionScriptModifyContextUtils.getEventActionScriptHolder(eventAction));

        if (eventActionService.getEventAction(eventAction.getCode()) != null)
        {
            process.deleteHolder(eventAction, context);
        }
        return context.getScriptsLogInfo();
    }

    /**
     * Проставляет скрипт, используемый в eventAction, в result в зависимости от флага isWithScript
     * @param isWithScript - если true, то скрипты проставляются
     */
    public void processEventActionScripts(EventAction eventAction, boolean isWithScript,
            SimpleScriptedResult<DtObject> result)
    {
        if (!isWithScript)
        {
            return;
        }
        result.setWithScripts(true);
        Script script = scriptStorageService.getScript(eventAction.getAction().getScript());
        result.putScript(scriptDtoFactory.create(script));
    }

    /**
     * Производит сохранение скрипта, используемого в условии действия по событиям
     */
    public List<ScriptAdminLogInfo> processSaveActionConditionScript(SaveActionConditionAction action,
            @Nullable ActionCondition oldCondition, ActionCondition newCondition, EventAction eventAction)
    {
        if (!(newCondition instanceof ScriptActionCondition))
        {
            return new ArrayList<>();
        }
        ScriptModifyProcess<ActionCondition> process = scriptModifyRegistry.getProcess(newCondition);

        ScriptModifyContext context = new ScriptModifyContext(
                EventActionScriptModifyContextUtils.getActionConditionScriptCategory(eventAction),
                EventActionScriptModifyContextUtils.getActionConditionScriptHolder(eventAction));

        context.put(ActionConditionScriptModifyProcess.EVENT_ACTION_HOLDER, eventAction);
        process.save(oldCondition, newCondition, action.getScript(), context);
        return context.getScriptsLogInfo();
    }

    /**
     * Производит сохранение скрипта, используемого в действии по событиям
     */
    public List<ScriptAdminLogInfo> processSaveEventActionScripts(SaveEventAction action,
            @Nullable EventAction oldEvent, EventAction newEvent)
    {
        final ScriptDto script = action.getScript();
        if (script == null)
        {
            return List.of();
        }
        ScriptModifyProcess<EventAction> process = scriptModifyRegistry.getProcess(newEvent);

        ScriptModifyContext context = new ScriptModifyContext(
                EventActionScriptModifyContextUtils.getEventActionScriptCategory(newEvent),
                EventActionScriptModifyContextUtils.getEventActionScriptHolder(newEvent));

        process.save(oldEvent, newEvent, script, context);
        return context.getScriptsLogInfo();
    }
}
