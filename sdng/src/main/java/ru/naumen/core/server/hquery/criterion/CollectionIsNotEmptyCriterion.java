package ru.naumen.core.server.hquery.criterion;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

public class CollectionIsNotEmptyCriterion extends AbstractHCriterion
{
    public CollectionIsNotEmptyCriterion(HColumn property)
    {
        super(property);
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        sb.append(property.getHQL(builder)).append(" is not empty");
    }

    @Override
    public String toString()
    {
        return "CollectionIsNotEmptyCriterion{" +
               "_property=" + property +
               '}';
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new CollectionIsNotEmptyCriterion(property);
    }
}
