package ru.naumen.core.server.timing.calculate;

import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.Locale;
import java.util.Map;
import java.util.Map.Entry;
import java.util.NavigableMap;
import java.util.SortedMap;
import java.util.TimeZone;
import java.util.TreeMap;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import jakarta.annotation.Nullable;
import ru.naumen.common.server.range.DateRange;
import ru.naumen.common.server.range.LongRange;
import ru.naumen.commons.server.utils.DateUtils;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * Класс предназначен для работы с периодами времени обслуживания
 * (сервисное время) и вычисления значений связанных с такими данными.
 * <p>
 * Класс хранит и работает с т.н. "Схемой обслуживания".
 * Схема состоит из определения периодов обслуживания на неделе и дней исключений.
 * Подразумевается что для каждой недели периоды обслуживания повторяются,
 * кроме дней исключений, которые нарушают недельную периодичность.
 * </p>
 * Каждый день исключения указывает на определённую конкретную дату.
 * Также в дне исключения могут быть определены свои периоды обслуживания.
 * <p>
 * Класс предназначен для расчёта сл. значений:<ul>
 * <li>Даты начала обслуживания по календарной дате начала </li>
 * <li>Длительности сервисного времени (обслуживания) между двумя календарными датами</li>
 * <li>Дата окончания обслуживания (deadline) по календарной дате начала и
 * требуемой продолжительности времени обслуживания</li>
 * </ul>
 *
 * akargapolov: если будут проблемы с производительностью (текущий алгоритм итерирует по неделям), то можно
 * реализовать новый алгоритм -
 * вводим WeekGroup - объединяем недели в группы, в каждой группе, однотипные недели, сохраняем все такие группы для
 * определенного периода
 * (например, 2000 - 2100), будем знать время начала и конца группы (и сервисное время тоже).
 * Все эти данные постоянно храним для класса обслуживания, тогда все методы по расчету времени превращаются к
 * запросам к такой структуре,
 * первым запросом в мапу мы сможем найти неделю в которую наступит время x, потом рассмотрением одной недели уже
 * узнается результат.
 *
 *
 * <AUTHOR>
 */
public class ServiceTimeCalculator implements ITimingCalculator
{
    /**
     * Определяет направление смещения по временной оси
     * при вычислении сервисного времени
     */
    public enum TimeShifting
    {
        toRight(1), toLeft(-1);

        private final int value;

        TimeShifting(int value)
        {
            this.value = value;
        }

        public int getValue()
        {
            return value;
        }
    }

    /**
     * Длительность дня (суток)
     */
    public static final long MS_IN_DAY = 24L * 60L * 60L * 1000L;

    /**
     * Длительность недели
     */
    public static final long MS_IN_WEEK = 7L * MS_IN_DAY;

    /**
     * Первый день недели
     */
    public static final int WEEK_FIRST_DAY = Calendar.MONDAY;

    /**
     * Диапазоны недели, полностью покрывающие всю неделю, т.е. представляющие режим 24x7
     */
    public static final Ranges RANGES_24x7 = new Ranges(new LongRange(0L, MS_IN_WEEK));

    public static final Logger LOG = LoggerFactory.getLogger(ServiceTimeCalculator.class);

    /**
     * Календарь, используемый для расчёта, хранит {@link Locale} и {@link TimeZone}
     */
    private Calendar calendar;

    /**
     * Диапазоны сервисного времени приведённые к началу недели
     */
    private Ranges weekRanges = RANGES_24x7;

    /**
     * Карта дней исключений, приведённая к пользовательскому календарю
     */
    private TreeMap<Long, Ranges> excludes = null;

    /**
     * Карта дней исключений(пользовательские настройки)
     */
    private Map<Date, Ranges> excludeDates = Collections.emptyMap();

    /**
     * Календарная дата начала
     */
    private Date start = null;

    /**
     * Календарная дата окончания
     */
    private Date end = null;

    /**
     * Длительность времени обслуживания
     */
    private long serviceTime = 0L;

    /**
     * Сервисная дата начала периода обслуживания
     */
    private Date serviceStart = null;

    /**
     * Сервисная дата окончания периода обслуживания
     */
    private Date serviceEnd = null;

    public ServiceTimeCalculator()
    {
        this.calendar = Calendar.getInstance();
        this.calendar.setFirstDayOfWeek(WEEK_FIRST_DAY);
    }

    public ServiceTimeCalculator(Locale locale)
    {
        this.calendar = Calendar.getInstance(locale);
        this.calendar.setFirstDayOfWeek(WEEK_FIRST_DAY);
    }

    public ServiceTimeCalculator(TimeZone timeZone)
    {
        this.calendar = Calendar.getInstance(timeZone);
        this.calendar.setFirstDayOfWeek(WEEK_FIRST_DAY);
    }

    public ServiceTimeCalculator(TimeZone timeZone, Locale locale)
    {
        this.calendar = Calendar.getInstance(timeZone, locale);
        this.calendar.setFirstDayOfWeek(WEEK_FIRST_DAY);
    }

    /**
     * Получение календарной даты требуемого окончания периода обслуживания,
     * ранее установленной методом {@link #setEndDate(Date)}. Эта дата не вычисляется.
     * @return
     */
    public Date getEndDate()
    {
        return end;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Date getServiceEndDate()
    {
        if (this.serviceEnd == null)
        {
            this.serviceEnd = calculateServiceEndDate();
        }
        return this.serviceEnd;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public Date getServiceStartDate()
    {
        if (this.serviceStart == null)
        {
            this.serviceStart = searchServiceStartDate();
        }
        return this.serviceStart;
    }

    /**
     * Получение значение регламентного (сервисного времени),
     * либо ранее установленного методом {@link #setServiceTime(long),
     * либо вычисленного по требуемым календарным датам {@link #setStartDate(Date) начала}
     * и {@link #setEndDate(Date) окончания обслуживания}
     * Используется для вычисления прошедшего сервисного времени
     * между календарными датами начала и окончания обслуживания.
     * @return продолжительность сервисного времени
     */
    @Override
    public long getServiceTime()
    {
        if (this.serviceTime < 0L)
        {
            this.serviceTime = calculateServiceTime();
        }
        return this.serviceTime;
    }

    /**
     * Получение календарной даты требуемого начала периода обслуживания,
     * ранее установленной методом {@link #setStartDate(Date)}. Эта дата не вычисляется.
     * @return календарная дата начала периода обслуживания
     */
    public Date getStartDate()
    {
        return start;
    }

    /**
     * @return Временную зону используемую при вычислениях
     */
    @Override
    public TimeZone getTimeZone()
    {
        return calendar.getTimeZone();
    }

    /**
     * Установка календаря, с помощью которого производятся все вычисления в классе.
     * Календарь влияет на определение моментов времени начала и окончания календарных периодов
     * @param calendar календарь
     * @return <code>this</code>
     */
    public ServiceTimeCalculator setCalendar(Calendar calendar)
    {
        this.calendar = calendar;
        reset();
        return this;
    }

    /**
     * Установка календарных дат требуемых времен начала и конца периода обслуживания
     * @param range диапазон 
     * @return
     */
    public ServiceTimeCalculator setDateRange(DateRange range)
    {
        this.start = range.getStart();
        this.end = range.getEnd();
        reset();
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ServiceTimeCalculator setEndDate(Date end)
    {
        if (!ObjectUtils.equals(end, this.end))
        {
            // проверяем корректность дат запрашиваемого периода
            new DateRange(this.start, end);
            this.end = end;
            this.serviceEnd = null;
            this.serviceTime = -1;
        }
        return this;
    }

    /**
     * Установка параметров "Cхемы обслуживания":
     * 1. Диапазоны сервисного времени на неделе (границы диапазонов указаны относительно
     * начала недели, соотвественно их значений не должны превышать размера недели {@link #MS_IN_WEEK});
     * 2. Карта дат исключений с соотв. им диапазонами сервисного времени дня исключения.
     * Границы диапазонов сервисного времени для дней исключений должны быть указаны
     * относительно начала дня и соответственно не превышать длительности дня {@link #MS_IN_DAY}
     *
     * @param weekRanges диапазоны сервисного времени определённые на неделе
     * @param excludeDates карта дней исключений
     * @return <code>this</code>
     * @throws IllegalArgumentException при выходе диапазонов значений за пределы недели
     *         первого параметра, и за границы суток для любого из диапазонов исключений
     */
    public ServiceTimeCalculator setScheme(Ranges weekRanges, Map<Date, Ranges> excludeDates)
            throws IllegalArgumentException
    {
        if (weekRanges.ceiling(MS_IN_WEEK) != null)
        {
            throw new IllegalArgumentException("Range shall not exceed the week length");
        }
        this.weekRanges = weekRanges.clone();
        for (Ranges rs : excludeDates.values())
        {
            if (rs.ceiling(MS_IN_DAY) != null)
            {
                throw new IllegalArgumentException("Range shall not exceed the day length");
            }
        }
        this.excludeDates = excludeDates;
        this.excludes = null; // нужен перерасчёт
        reset();
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ServiceTimeCalculator setServiceTime(long serviceTime)
    {
        this.serviceTime = serviceTime;
        this.serviceEnd = null;
        return this;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public ServiceTimeCalculator setStartDate(Date start)
    {
        if (!ObjectUtils.equals(start, this.start))
        {
            // проверяем корректность дат запрашиваемого периода
            new DateRange(start, this.end);
            this.start = start;
            reset();
        }
        return this;
    }

    /**
     * Установка пользовательской временной зоны
     * Это влияет на определение границ диапазонов
     * @param timeZone временная зона
     * @return <code>this</code>
     */
    public ServiceTimeCalculator setTimeZone(TimeZone timeZone)
    {
        this.calendar.setTimeZone(timeZone);
        reset();
        return this;
    }

    /**
     * Расчёт даты/времени истечения сервисного времени
     * @return
     */
    private Date calculateServiceEndDate()
    {
        boolean isTraceEnabled = LOG.isTraceEnabled();

        if (this.start == null)
        {
            throw new IllegalStateException("startDate or serviceTime is not set");
        }
        ensureData();
        if (weekRanges.getRangesLength() == 0L)
        { // не определены периоды сервисного времени на неделе, ищем в исключениях
            return calculateServiceEndDateFromExcludes();
        }
        long time = Math.abs(this.serviceTime);
        calendar.setTime(start);
        long fromms = calendar.getTimeInMillis();

        LongRange week = getAbsRange(fromms, Calendar.WEEK_OF_YEAR, TimeShifting.toRight);
        Ranges ranges = weekServiceTimeRanges(week);
        long startWeekTime = getServiceTimeLengthByTimeShiftingDirection(ranges, fromms - week.getStart());

        if (isTraceEnabled)
        {
            LOG.trace("time = {}, fromms = {}, week start = {}, week end = {}, "
                      + "week length = {}, ranges length = {}, startWeekTime = {}",
                    time, fromms, week.getStart(), week.getEnd(),
                    week.length(), ranges.getRangesLength(), startWeekTime);
        }

        if (time > startWeekTime)
        {
            LOG.trace("time > startWeekTime");
            time -= startWeekTime;
        }
        else
        {
            Long offsetTime = calculateTimeByTimeShiftingDirectionFromDistance(ranges, fromms - week.getStart(), time);
            LOG.trace("v = {}", offsetTime);
            if (offsetTime != null)
            {
                return new Date(week.getStart() + offsetTime);
            }
        }

        while (true)
        {
            week = getNextWeekByTimeShiftingDirection(week);
            ranges = weekServiceTimeRanges(week);

            if (isTraceEnabled)
            {
                LOG.trace("seek week start = {}, seek week end = {}, "
                          + "seek week length = {}, seek ranges length = {}",
                        week.getStart(), week.getEnd(), week.length(), ranges.getRangesLength());
            }

            if (time > ranges.getRangesLength())
            {
                LOG.trace("time > ranges.getRangesLength()");
                time -= ranges.getRangesLength();
            }
            else
            {
                Long offsetTime = calculateTimeByTimeShiftingDirection(ranges, time);
                LOG.trace("seek v = {}", offsetTime);
                if (offsetTime != null)
                {
                    return new Date(week.getStart() + offsetTime);
                }
                return new Date(week.getStart());
            }
        }
    }

    /**
     * Расчёт момента истечения сервисного времени только по диапазонам исключений
     * @return
     */
    private Date calculateServiceEndDateFromExcludes()
    {
        boolean isTraceEnabled = LOG.isTraceEnabled();

        calendar.setTime(start);
        long fromms = calendar.getTimeInMillis();
        // остаток времени
        long time = Math.abs(this.serviceTime);

        DateUtils.trunc(calendar, Calendar.DAY_OF_MONTH);
        long dayStart = calendar.getTimeInMillis();
        Ranges ranges = excludes.get(dayStart);
        LOG.trace("fromms = {}, time = {}, dayStart = {}", fromms, time, dayStart);

        if (ranges != null)
        { // если исключения есть в стартовом дне
            long startDayTime = getServiceTimeLengthByTimeShiftingDirection(ranges, fromms - dayStart);
            if (isTraceEnabled)
            {
                LOG.trace("ranges length = {}, startDayTime = {}",
                        ranges.getRangesLength(), startDayTime);
            }
            if (time > startDayTime)
            {
                LOG.trace("time > startDayTime");
                time -= startDayTime;
            }
            else
            {
                Long timeOffset = calculateTimeByTimeShiftingDirectionFromDistance(ranges, fromms - dayStart, time);
                LOG.trace("v = {}", timeOffset);
                if (timeOffset != null)
                {
                    return new Date(dayStart + timeOffset);
                }
            }
        }

        NavigableMap<Long, Ranges> excludes = getExcludesByDirectionOfTimeShifting(dayStart);
        for (Entry<Long, Ranges> e : excludes.entrySet())
        {
            ranges = e.getValue();
            if (time > ranges.getRangesLength())
            {
                time -= ranges.getRangesLength();
            }
            else
            {
                Long offsetTime = calculateTimeByTimeShiftingDirection(ranges, time);
                LOG.trace("seek v = {}", offsetTime);
                if (offsetTime != null)
                {
                    return new Date(e.getKey() + offsetTime);
                }
            }
        }
        return null;
    }

    /**
     * Расчёт длительности прошедшего сервисного времени
     * @return
     */
    private long calculateServiceTime()
    {
        boolean isTraceEnabled = LOG.isTraceEnabled();

        if (this.start == null || this.end == null)
        {
            throw new IllegalStateException("startDate or endDate is not set");
        }
        ensureData();
        if (weekRanges.getRangesLength() == 0L)
        {// не определены периоды сервисного времени на неделе, суммируем исключения
            return calculateServiceTimeFromExcludes();
        }
        calendar.setTime(start);
        long fromms = calendar.getTimeInMillis();
        calendar.setTime(end);
        long toms = calendar.getTimeInMillis();
        long res = 0L;
        LongRange startWeek = getAbsRange(fromms, Calendar.WEEK_OF_YEAR, TimeShifting.toRight);
        Ranges startRanges = weekServiceTimeRanges(startWeek);

        // Время в первой неделе
        res = startRanges.getRangesLengthFrom(fromms - startWeek.getStart());

        if (isTraceEnabled)
        {
            LOG.trace("fromms = {}, toms = {}, startWeek start = {}, startWeek end = {}, "
                      + "startWeek length = {}, startRanges length = {}, res = {}",
                    fromms, toms, startWeek.getStart(), startWeek.getEnd(),
                    startWeek.length(), startRanges.getRangesLength(), res);
        }

        if (startWeek.contains(toms))
        {
            // Если начало и конец в одной неделе
            res += startRanges.getRangesLengthTill(toms - startWeek.getStart());
            res -= startRanges.getRangesLength();
            return res;
        }
        LongRange endWeek = getAbsRange(toms, Calendar.WEEK_OF_YEAR, TimeShifting.toRight);
        Ranges endRanges = weekServiceTimeRanges(endWeek);

        // Время в последней неделе
        res += endRanges.getRangesLengthTill(toms - endWeek.getStart());
        if (isTraceEnabled)
        {
            LOG.trace("endWeek start = {}, endWeek end = {}, "
                      + "endWeek length = {}, endRanges length = {}, res = {}",
                    endWeek.getStart(), endWeek.getEnd(),
                    endWeek.length(), endRanges.getRangesLength(), res);
        }

        if (startWeek.getEnd() >= endWeek.getStart())
        {
            // полных недель нет, неполные уже подсчитали
            return res;
        }
        Long current = startWeek.getEnd();
        LOG.trace("current = {}", current);

        while (true)
        {
            Long ceilExclude = excludes.ceilingKey(current);
            LOG.trace("seek ceilExclude = {}", ceilExclude);

            if (ceilExclude != null && ceilExclude < endWeek.getStart())
            {
                LongRange exclWeek = getAbsRange(ceilExclude, Calendar.WEEK_OF_YEAR, TimeShifting.toRight);
                // cуммируем полные недели без исключений
                res += weekRanges.getRangesLength() * weekCount(current, exclWeek.getStart());
                // суммируем неделю с исключениями
                res += weekServiceTimeRanges(exclWeek).getRangesLength();
                // Перходим к следующей неделе
                current = exclWeek.getEnd();
            }
            else
            {
                // cуммируем оставшиеся полные недели без исключений
                res += weekRanges.getRangesLength() * weekCount(current, endWeek.getStart());
                break;
            }
        }

        LOG.trace("result res = {}", res);
        return res;
    }

    /**
     * Расчёт длительности прошедшего сервисного времени только по диапазонам исключений
     * @return
     */
    private long calculateServiceTimeFromExcludes()
    {
        calendar.setTime(start);
        long fromms = calendar.getTimeInMillis();
        DateUtils.trunc(calendar, Calendar.DAY_OF_MONTH);
        Long dayStart = calendar.getTimeInMillis();
        calendar.setTime(end);
        long toms = calendar.getTimeInMillis();
        DateUtils.trunc(calendar, Calendar.DAY_OF_MONTH);
        Long dayEnd = calendar.getTimeInMillis();
        LOG.trace("fromms = {}, dayStart = {}, toms = {}, dayEnd = {}",
                fromms, dayStart, toms, dayEnd);
        long res = 0L;

        for (Entry<Long, Ranges> e : excludes.subMap(dayStart, toms).entrySet())
        {
            if (dayStart.equals(e.getKey()))
            {// Дата начала попадает на этот день, он может быть не полон
                res += e.getValue().getRangesLengthFrom(fromms - e.getKey());
                if (dayEnd.equals(e.getKey()))
                { // Начало и конец внутри дня
                    res += e.getValue().getRangesLengthTill(toms - e.getKey());
                    res -= e.getValue().getRangesLength();
                }
            }
            else if (dayEnd.equals(e.getKey()))
            { // Дата конца попадает на этот день - он может быть не полон
                res += e.getValue().getRangesLengthTill(toms - e.getKey());
            }
            else
            { // 
                res += e.getValue().getRangesLength();
            }
        }

        LOG.trace("result res = {}", res);
        return res;
    }

    /**
     * Вычисляет значение времени принадлежащее диапазону, в зависимости от направления
     * смещения по временной оси. При движении в сторону увеличения времени - от начала
     * диапазона. При движении в сторону уменьшения времени - от конца диапазона
     * @param serviceTimeRanges - диапазон сервисного времени
     * @param time - значение, откладываемое от границы диапазона
     * @return значение, принадлежащее диапазону, либо null еси такого значения не существует
     */
    @Nullable
    private Long calculateTimeByTimeShiftingDirection(Ranges serviceTimeRanges, long time)
    {
        Long value;
        if (this.serviceTime >= 0)
        {
            value = serviceTimeRanges.getRangeValueFromBegin(time);
        }
        else
        {
            value = serviceTimeRanges.getRangeValueLeftFromEnd(time);
        }
        return value;
    }

    /**
     * Вычисляет значение времени принадлежащее диапазону, находящееся на указанном расстоянии
     * от границы диапазона, в зависимости от направления смещения по временной оси. При движении 
     * в сторону увеличения времени - справа от указанной временной точки.При движении в сторону 
     * уменьшения времени -  слева от указанной временной точки
     *
     * @param serviceTimeRanges - диапазон сервисного времени
     * @param fromTime - точка отсчета
     * @param time - расстояние до вычисляемого значения
     * @return значение, принадлежащее диапазону, либо null еси такого значения не существует
     */
    @Nullable
    private Long calculateTimeByTimeShiftingDirectionFromDistance(Ranges serviceTimeRanges, long fromTime, long time)
    {
        Long value;
        if (this.serviceTime >= 0)
        {
            value = serviceTimeRanges.getRangeValueFromDistance(fromTime, time);
        }
        else
        {
            value = serviceTimeRanges.getRangeValueLeftFromDistance(fromTime, time);
        }
        return value;
    }

    /**
     * Подготовка данных для расчёта
     */
    private void ensureData()
    {
        if (excludes == null)
        { // Приводим к пользовательскому календарю даты исключений
            Calendar def = Calendar.getInstance();
            TreeMap<Long, Ranges> treemap = new TreeMap<>();
            for (Entry<Date, Ranges> e : this.excludeDates.entrySet())
            {
                def.setTime(e.getKey());
                calendar.set(def.get(Calendar.YEAR), def.get(Calendar.MONTH), def.get(Calendar.DAY_OF_MONTH));
                DateUtils.trunc(calendar, Calendar.DAY_OF_MONTH);
                treemap.put(calendar.getTimeInMillis(), e.getValue());
            }
            this.excludes = treemap;
        }
    }

    /**
     * Получение диапазона периода, указанного календарным полем,
     * в абсолютном времени для указанной даты
     * @param millis момент времени
     * @param field календарное поле
     * @param shift сдвиг окончания периода по временной оси(вправо или влево)
     * @return
     */
    private LongRange getAbsRange(long millis, int field, TimeShifting shift)
    {
        calendar.setTimeInMillis(millis);
        DateUtils.trunc(calendar, field);
        long start = calendar.getTimeInMillis();
        calendar.add(field, shift.getValue());
        return start <= calendar.getTimeInMillis() ? new LongRange(start, calendar.getTimeInMillis())
                : new LongRange(calendar.getTimeInMillis(), start);
    }

    /**
     * Возвращает множество исключений в графике обслуживания, ограниченных слева(справа) по временному
     * значению(ключу) в зависимости от направления смещения по временной оси
     * @param time - время которым будет ограничено множество исключений
     */
    private NavigableMap<Long, Ranges> getExcludesByDirectionOfTimeShifting(long time)
    {
        if (this.serviceTime > 0)
        {
            return excludes.tailMap(time, false);
        }
        else
        {
            NavigableMap<Long, Ranges> excludes = new TreeMap<>(Collections.reverseOrder());
            excludes.putAll(this.excludes.headMap(time, false));
            return excludes;
        }
    }

    /**
     * Возвращает диапазон начала/конца следующей недели в зависимости от направления
     * смещения по временной оси
     * @param currentWeek - диапазон начала/конца текущей недели
     * @return диапазон начала/конца следующей недели
     */
    private LongRange getNextWeekByTimeShiftingDirection(LongRange currentWeek)
    {
        LongRange nextWeek;
        if (this.serviceTime >= 0)
        {
            nextWeek = getAbsRange(currentWeek.getEnd(), Calendar.WEEK_OF_YEAR, TimeShifting.toRight);
        }
        else
        {
            nextWeek = getAbsRange(currentWeek.getStart(), Calendar.WEEK_OF_YEAR, TimeShifting.toLeft);
        }
        return nextWeek;
    }

    /**
     * Вычисляет значение сервисного времени в диапазоне времени сервисного обслуживания, 
     * в зависимости от направления смещения по временной оси. При движении в сторону 
     * увеличения времени - от заданного значения до конца диапазона. При движении в 
     * сторону уменьшения времени - от начала диапазона до заданного значения
     * @param serviceTimeRanges - диапазон сервисного времени
     * @param time - заданное значение времени
     * @return вычисленное значение сервисного времени
     */
    private long getServiceTimeLengthByTimeShiftingDirection(Ranges serviceTimeRanges, long time)
    {
        if (this.serviceTime >= 0)
        {
            return serviceTimeRanges.getRangesLengthFrom(time);
        }
        else
        {
            return serviceTimeRanges.getRangesLengthTill(time);
        }
    }

    /**
     * Сброс (обнуление) всех расчётных результатов
     */
    private void reset()
    {
        this.serviceStart = null;
        this.serviceEnd = null;
        this.serviceTime = -1L;
    }

    /**
     * Поиск даты начала сервисного времени
     * @return
     */
    private Date searchServiceStartDate()
    {
        if (this.start == null)
        {
            throw new IllegalStateException("startDate is not set");
        }
        ensureData();
        if (weekRanges.getRangesLength() == 0L)
        {
            // не определены периоды сервисного времени на неделе, ищем в исключениях
            return searchServiceStartDateAmongExcludes();
        }
        calendar.setTime(start);
        long fromms = calendar.getTimeInMillis();
        LOG.trace("fromms = {}", fromms);

        Long result = null;
        while (result == null)
        {
            LongRange weekRange = getAbsRange(fromms, Calendar.WEEK_OF_YEAR, TimeShifting.toRight);
            Ranges rs = weekServiceTimeRanges(weekRange);
            result = rs.ceiling(fromms - weekRange.getStart());
            result = result != null ? result + weekRange.getStart() : null;
            fromms = weekRange.getEnd();
        }

        LOG.trace("result = {}", result);
        return new Date(result);
    }

    /**
     * Поиск даты начала сервисного времени только в исключениях
     */
    private Date searchServiceStartDateAmongExcludes()
    {
        calendar.setTime(start);
        long fromMs = calendar.getTimeInMillis();
        DateUtils.trunc(calendar, Calendar.DAY_OF_MONTH);
        for (Entry<Long, Ranges> e : excludes.tailMap(calendar.getTimeInMillis()).entrySet())
        {
            Long cei = e.getValue().ceiling(fromMs - e.getKey());
            if (cei != null)
            {
                return new Date(e.getKey() + cei);
            }
        }
        return null;
    }

    /**
     * Количество недель между двумя моментами времени,
     * округление срабатывает на неделях с переводом времени на летнее,
     * поскольку длительность таких недель отличается от {@link #MS_IN_WEEK}
     */
    private static long weekCount(long from, long to)
    {
        return Math.round((double)(to - from) / (double)MS_IN_WEEK); //NOPMD
    }

    /**
     * Получение диапазонов сервисного времени для конкретной недели
     * с учётом (наложением) исключений
     * @param week диапазон недели в абсолютном времени
     */
    private Ranges weekServiceTimeRanges(LongRange week)
    {
        SortedMap<Long, Ranges> weekExcludes = excludes.subMap(week.getStart(), week.getEnd());
        if (weekExcludes.isEmpty())
        {
            return this.weekRanges;
        }
        Ranges res = this.weekRanges.clone();
        weekExcludes.forEach((key, value) -> recalculateRangeWithExcludes(key, value, week, res));
        return res;
    }

    private void recalculateRangeWithExcludes(Long key, Ranges value, @Nullable LongRange week, Ranges result)
    {
        LongRange dayRange = getAbsRange(key, Calendar.DAY_OF_MONTH, TimeShifting.toRight);
        // Приводим к началу недели
        if (week != null)
        {
            dayRange.shift(-week.getStart());
        }
        // исключаем из описания недели
        result.remove(dayRange);
        // добавляем переопределения периодов дня
        for (LongRange er : value)
        {
            // Приводим к началу недели
            LongRange lr = new LongRange(er);
            lr.shift(dayRange.getStart());
            result.add(lr);
        }
    }
}
