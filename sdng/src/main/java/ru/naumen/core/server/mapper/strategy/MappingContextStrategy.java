package ru.naumen.core.server.mapper.strategy;

import ru.naumen.core.server.bo.DefaultMetaObjectToDtObjectMapper;
import ru.naumen.core.server.mapper.MappingContext;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Интерфейс стратегий {@link MappingContext}
 * Нужен для дополнительной обработки значений {@link DefaultMetaObjectToDtObjectMapper}
 *
 * <AUTHOR>
 */
public interface MappingContextStrategy
{
    Object process(final Object value, final Attribute attr);
}
