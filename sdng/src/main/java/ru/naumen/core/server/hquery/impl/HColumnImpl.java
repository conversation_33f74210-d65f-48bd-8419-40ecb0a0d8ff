package ru.naumen.core.server.hquery.impl;

import java.util.Objects;

import jakarta.annotation.Nullable;

import org.hibernate.query.Query;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HProperty;
import ru.naumen.core.server.hquery.HSimpleColumn;

public class HColumnImpl extends AbstractHColumn implements HSimpleColumn
{
    @Nullable
    protected HColumn other;
    protected String column;

    protected String overrideHQL = null;

    public HColumnImpl(String column)
    {
        this(column, null);
    }

    public HColumnImpl(String column, @Nullable String alias)
    {
        super(alias);
        this.column = column;
    }

    HColumnImpl(HColumn other, String column, @Nullable String alias)
    {
        super(alias);
        this.other = other;
        this.column = column;
    }

    public HColumn getBase()
    {
        return other;
    }

    @Override
    public void setParameters(Query query)
    {
    }

    /**
     * Возвращает псевдоним родительской сущности
     * <br>
     * Метод необходим, чтобы разрывать циклическую связь при сравнениях и вычислении хэша. Родительская критерия
     * ссылается на колонку, а поле other обычно смотрит на родительскую критерию. Чтобы разорвать круг,
     * считаем в колонке, что для совпадения достаточно, чтобы совпадали псевдонимы родительских сущностей.
     *
     * @return псевдоним родительской сущности или null, если она не задана
     */
    @Nullable
    private String getBaseAlias()
    {
        return other == null ? null : other.getAlias();
    }

    public void setBase(HColumn other)
    {
        this.other = other;
    }

    @Override
    public String getColumn()
    {
        return this.column;
    }

    @Override
    public HProperty getProperty(String property)
    {
        return new HPropertyImpl(this, property, null);
    }

    @Override
    public HProperty getProperty(String property, @Nullable String alias)
    {
        return new HPropertyImpl(this, property, alias);
    }

    public void overrideHQL(String hql)
    {
        this.overrideHQL = hql;
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        if (overrideHQL != null)
        {
            return overrideHQL;
        }
        StringBuilder sb = new StringBuilder();
        if (other != null)
        {
            sb.append(StringUtilities.isEmpty(other.getAlias())
                    ? other.getHQL(builder) : other.getAlias());
            sb.append('.');
        }
        sb.append(this.column);
        return sb.toString();
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        HColumnImpl hColumn = (HColumnImpl)o;
        return Objects.equals(getBaseAlias(), hColumn.getBaseAlias()) &&
               Objects.equals(column, hColumn.column) &&
               Objects.equals(getAlias(), hColumn.getAlias()) &&
               Objects.equals(overrideHQL, hColumn.overrideHQL);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(getBaseAlias(), column, getAlias(), overrideHQL);
    }

    @Override
    public String toString()
    {
        return "HColumnImpl{" +
               "other=" + other +
               ", column='" + column + '\'' +
               ", overrideHQL='" + overrideHQL + '\'' +
               ", alias=" + getAlias() +
               '}';
    }
}
