package ru.naumen.core.server.hquery.criterion;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractPropertySubCriteriaHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;

/**
 * <AUTHOR>
 * @since 05.12.2011
 */
public class NotInSubquery extends AbstractPropertySubCriteriaHCriterion
{
    public NotInSubquery(HCriteria subCriteria, HColumn property)
    {
        super(property, subCriteria);
    }

    @Override
    public String toString()
    {
        return "NotInSubquery{" +
               "property=" + property +
               ", subCriteria=" + subCriteria +
               '}';
    }

    @Override
    protected void append(StringBuilder sb, HBuilder builder, String subQuery)
    {
        super.append(sb, builder, subQuery);
        sb.append("NOT IN ( ").append(subQuery).append(" )");
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new NotInSubquery(subCriteria.createCopy(), getProperty());
    }
}
