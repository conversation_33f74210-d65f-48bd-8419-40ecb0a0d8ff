package ru.naumen.core.server.hquery.impl.source;

import jakarta.annotation.Nullable;
import jakarta.persistence.criteria.JoinType;

import org.hibernate.query.Query;
import org.hibernate.Session;

import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.shared.utils.ObjectUtils;

/**
 * <AUTHOR>
 * @since 04 февр. 2016 г.
 */
public class JoinSource extends TextSource
{
    private final String parentAlias;

    private final JoinType joinType;
    private final HCriterion joinCondition;
    private final boolean fetch;

    public JoinSource(String path, JoinType joinType, @Nullable HCriterion joinCondition, String alias, boolean fetch)
    {
        super(path, alias);
        parentAlias = extractPathAlias(path);
        this.joinType = joinType;
        this.joinCondition = joinCondition;
        this.fetch = fetch;
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }
        if (!super.equals(obj))
        {
            return false;
        }

        JoinSource other = (JoinSource)obj;
        //@formatter:off
            return fetch == other.fetch &&
                    ObjectUtils.equals(joinType, other.joinType) &&
                    ObjectUtils.equals(joinCondition, other.joinCondition);
        //@formatter:on
    }

    @Override
    public String getParentAlias()
    {
        return parentAlias;
    }

    @Override
    public int hashCode()
    {
        return ObjectUtils.hashCode(fetch, joinType, joinCondition) * 17 + super.hashCode();
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        StringBuilder sb = new StringBuilder();
        sb.append(joinType.toString());
        sb.append(" JOIN "); //$NON-NLS-1$
        if (fetch)
        {
            sb.append("FETCH "); //$NON-NLS-1$
        }
        sb.append(getHQLSource());
        sb.append(' ').append(getAlias());
        if (joinCondition != null)
        {
            sb.append(" WITH "); //$NON-NLS-1$
            HBuilder whereCollector = new HBuilder(builder);
            joinCondition.visit(whereCollector);
            sb.append(whereCollector.getWhereStrings().get(0));
        }
        return sb.toString();
    }

    @Override
    public void visit(HBuilder builder)
    {
        builder.from(getHQL(builder), true);
    }

    private static String extractPathAlias(String path)
    {
        int index = path.indexOf('.');
        if (index < 1)
        {
            return "";
        }
        return path.substring(0, index);
    }

    @Override
    public void setParameters(Query q)
    {
        super.setParameters(q);
        if (joinCondition != null)
        {
            joinCondition.setParameters(q);
        }
    }

    @Override
    public void afterQuery(Session session)
    {
        super.afterQuery(session);
        if (joinCondition != null)
        {
            joinCondition.afterQuery(session);
        }
    }
}
