package ru.naumen.core.server.upload.strategies;

import jakarta.inject.Inject;

import org.springframework.stereotype.Service;

import ru.naumen.core.server.filestorage.spi.FileStorageSettingsService;
import ru.naumen.core.server.filestorage.spi.storages.FileHashDao;
import ru.naumen.core.server.upload.spi.DBFileItem;
import ru.naumen.core.server.upload.spi.DBFileItemDao;

/**
 * Стратегия загрузки в груви-хранилище
 *
 * <AUTHOR>
 * @since Aug 31, 2020
 */
@Service
public class GroovyUploadStrategy extends AbstractUploadStrategy
{
    @Inject
    protected GroovyUploadStrategy(final FileHashDao fileHashDao,
            final DBFileItemDao dbFileItemDao,
            final FileStorageSettingsService fileStorageSettingsService)
    {
        super(fileHashDao, dbFileItemDao, fileStorageSettingsService);
    }

    @Override
    protected void setContent(final SetContentContext setContentContext)
    {
        // TODO будет реализовано в NSDPRD-14739
    }

    @Override
    protected void deleteContent(final DBFileItem dbFileItem)
    {
        // TODO будет реализовано в NSDPRD-14739
    }
}