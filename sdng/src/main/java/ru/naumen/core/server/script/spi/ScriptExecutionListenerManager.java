package ru.naumen.core.server.script.spi;

import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.ExecutionResult;
import ru.naumen.core.server.script.ScriptExecutionSource;

/**
 * Менеджер прослушивания выполнения скриптов
 *
 * <AUTHOR>
 * @since Apr 17, 2020
 */
public interface ScriptExecutionListenerManager
{
    /**
     * Зарегистрировать нового слушателя
     */
    void registerListener(final ScriptExecutionListener scriptExecutionListener);

    /**
     * Удалить слушаетелей
     */
    void removeListeners(final List<? extends ScriptExecutionListener> scriptListeners);

    /**
     * Уведомить о начале выполнения скрипта
     *
     * @param source {@link ScriptExecutionSource}
     * @param userUUID UUID пользователя иницирующий выполнение скрипта
     */
    void beforeScriptExecution(final ScriptExecutionSource source, @Nullable final String userUUID);

    /**
     * Уведомить о завершении выполнения скрипта
     *
     * @param source {@link ScriptExecutionSource}
     * @param executionResult {@link ExecutionResult}
     */
    void afterScriptExecution(final ScriptExecutionSource source, final ExecutionResult executionResult);
}
