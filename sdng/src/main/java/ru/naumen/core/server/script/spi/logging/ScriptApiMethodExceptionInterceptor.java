package ru.naumen.core.server.script.spi.logging;

import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.AfterThrowing;
import org.aspectj.lang.annotation.Aspect;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.ScriptApi;
import ru.naumen.core.server.script.spi.ScriptReadableException;

/**
 * Класс-аспект нужен перехватывает и логгирует исключения 
 * при вызове методов классов реализующих {@link ScriptApi}
 *
 * <AUTHOR>
 */
@Component
@Aspect
public class ScriptApiMethodExceptionInterceptor
{
    public static final Logger LOG = LoggerFactory.getLogger(ScriptApiMethodExceptionInterceptor.class);

    /**
     * Логгирование исключений навешивается на public-методы
     * если класс-владелец метода реализует интефейс {@link ScriptApi}
     */
    @AfterThrowing(pointcut = "target(ru.naumen.core.server.script.ScriptApi) && execution(public * *(..))",
            throwing = "e")
    public void logException(JoinPoint joinPoint, Throwable e)
    {
        //Исключение, которое выбрасывает utils.throwReadableException НЕ нужно логировать
        if (e instanceof ScriptReadableException)
        {
            return;
        }
        StringBuilder arguments = new StringBuilder("Exception in api: ").append(getClassWhereThrow(joinPoint))
                .append("; Method: ").append(getMethodWhereThrow(joinPoint)).append("; Args: ");

        setArgsWhenThrow(joinPoint, arguments);

        arguments.append("; Ex info: ").append(getExcetionInfo(e));

        LOG.error(arguments.toString());
        LOG.info(e.getMessage(), e);
    }

    private String getClassWhereThrow(JoinPoint joinPoint)
    {
        return joinPoint.getSourceLocation().getWithinType().getSimpleName();
    }

    private String getExcetionInfo(Throwable e)
    {
        return e.getClass().getSimpleName() + " " + e.getLocalizedMessage();
    }

    private String getMethodWhereThrow(JoinPoint joinPoint)
    {
        return joinPoint.getSignature().getName();
    }

    private void setArgsWhenThrow(JoinPoint joinPoint, StringBuilder arguments)
    {
        boolean isFirst = true;
        for (Object arg : joinPoint.getArgs())
        {
            if (!isFirst)
            {
                arguments.append(", ");
            }
            arguments.append(arg);
            isFirst = false;
        }
    }
}
