/**
 */
package ru.naumen.core.server.naming.generators;

import ru.naumen.core.server.naming.extractors.PeriodExtractor;
import ru.naumen.core.server.naming.extractors.SequenceExtractor;

/**
 * Генератор числовых id.
 *
 * <AUTHOR>
 * @since 16.07.2009
 *
 */
public interface IIdGenerator
{
    /**
     * Генерирует или возвращает ранее сгенерированный для объекта id
     * </p><p>
     * После окончания использования генератора необходимо вызывать unget, чтобы почистить запомненные значения
     * </p>
     */
    int getId(Object obj);

    /**
     * @return {@link PeriodExtractor} для объектов, использующих этот генератор.
     */
    @SuppressWarnings("rawtypes")
    PeriodExtractor getPeriodExtractor();

    /**
     * @return {@link SequenceExtractor} для объектов, использующих этот генератор.
     */
    SequenceExtractor getSequenceExtractor();
}