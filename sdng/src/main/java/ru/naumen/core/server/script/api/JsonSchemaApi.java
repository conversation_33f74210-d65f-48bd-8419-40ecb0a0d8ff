package ru.naumen.core.server.script.api;

import org.springframework.stereotype.Component;

import ru.naumen.jsonschema.IMechanismInfo;
import ru.naumen.jsonschema.JsonSchemaService;

import jakarta.inject.Inject;

import java.util.Collection;
import java.util.List;

/**
 * Методы для работы с json схемой
 *
 * <AUTHOR>
 * @since 03.08.2020
 */
@Component("jsonSchema")
public class JsonSchemaApi implements IJsonSchemaApi
{
    private final JsonSchemaService jsonSchemaService;

    @Inject
    public JsonSchemaApi(JsonSchemaService jsonSchemaService)
    {
        this.jsonSchemaService = jsonSchemaService;
    }

    @Override
    public Collection<String> getMechanismCodes()
    {
        return jsonSchemaService.getMechanismCodes();
    }

    @Override
    public List<IMechanismInfo> getMechanismInfos()
    {
        return jsonSchemaService.getMechanismInfos();
    }

    @Override
    public String generateJsonSchema(Class<?> clazz)
    {
        return jsonSchemaService.registerJsonSchema(clazz);
    }

    @Override
    public String getJsonSchema(String mechanismName)
    {
        return jsonSchemaService.getJsonSchema(mechanismName);
    }

    @Override
    public void removeJsonSchema(String mechanismName)
    {
        jsonSchemaService.removeJsonSchema(mechanismName);
    }
}
