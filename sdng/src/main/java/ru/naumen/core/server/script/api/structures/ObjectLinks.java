package ru.naumen.core.server.script.api.structures;

import java.util.List;

/**
 * Объект, содержащий информацию о дочерних и родительских объектах, полученных по структуре
 * <AUTHOR>
 * @since 11.09.2020
 */
public class ObjectLinks implements IObjectLinks
{
    private final List<String> children;
    private final List<String> parent;

    public ObjectLinks(List<String> children, List<String> parent)
    {
        this.children = children;
        this.parent = parent;
    }

    @Override
    public List<String> getChildren()
    {
        return children;
    }

    @Override
    public List<String> getParent()
    {
        return parent;
    }
}
