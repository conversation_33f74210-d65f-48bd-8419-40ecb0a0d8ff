package ru.naumen.core.server.script.api.criteria;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.core.server.filters.FiltersService;
import ru.naumen.core.server.filters.RuntimeHandler;
import ru.naumen.core.server.script.api.criteria.criterion.handler.ApiCriterionHandlerService;
import ru.naumen.core.shared.Constants.AttributeLink;
import ru.naumen.core.shared.attr.LinkAttributeUtils;
import ru.naumen.core.shared.filters.AndFilter;
import ru.naumen.core.shared.filters.IAndFilter;
import ru.naumen.core.shared.filters.IFilter;
import ru.naumen.core.shared.filters.IObjectFilter;
import ru.naumen.core.shared.filters.InAttributesChainFilter;
import ru.naumen.core.shared.filters.NamedFilter;
import ru.naumen.core.shared.filters.NotFilter;
import ru.naumen.core.shared.filters.OrFilter;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.elements.AttributeImpl;
import ru.naumen.metainfo.shared.AttrReference;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.ObjectAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.AttributeType;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.objectlist.shared.advlist.filtration.chainfilter.AttrOfRelatedObjectChainFilterCreator;

/**
 * Утилитарный класс для использования в {@link ApiCriteria}. Содержит методы со сложной логикой, отвязанной от
 * внутреннего устройства {@link ApiCriteria}.
 *
 * <AUTHOR>
 * @since 10.12.2020
 */
@Component
public class ApiCriteriaUtils
{
    private final ApiCriterionHandlerService criterionHandlerService;
    private final FiltersService filtersService;
    private final MetainfoService metainfoService;

    @Inject
    public ApiCriteriaUtils(ApiCriterionHandlerService criterionHandlerService, FiltersService filtersService,
            MetainfoService metainfoService)
    {
        this.criterionHandlerService = criterionHandlerService;
        this.filtersService = filtersService;
        this.metainfoService = metainfoService;
    }

    /**
     * Применяет к критерии указанное условие фильтрации
     * @param criteria текущая критерия
     * @param criterion применяемое условие фильтрации
     */
    public void applyCriterion(ApiCriteria criteria, IApiCriterion criterion)
    {
        criteria.getCriteria().add(criterionHandlerService.createCriterion(criteria, criterion));
    }

    /**
     * Возвращает обработчик условий фильтрации на основе указанного фильтра filter, применяемого к указанному
     * метаклассу currentMetaClass. Возвращаемый хэндлер инкапсулирует логику преобразования верхнеуровневого
     * фильтра в один или несколько низкоуровневых.
     *
     * @param filter применяемый фильтр
     * @param currentMetaClass метакласс объектов, к которым применяется фильтр
     * @return Обработчик условий фильтрации
     */
    public RuntimeHandler createFilterHandler(IFilter filter, MetaClass currentMetaClass)
    {
        return filtersService.createHandler(currentMetaClass, preprocessFilter(filter, currentMetaClass));
    }

    /**
     * Получение списка кодов наследников (потомков) метакласса
     * @param metaClass метакласс
     * @param includeSpecified включить ли в список код указанного метакласса
     * @return список кодов метаклассов-потомков
     */
    public Collection<String> getDescendantTypeCodes(MetaClass metaClass, boolean includeSpecified)
    {
        return metainfoService.getMetaClassDescendants(metaClass.getFqn(), includeSpecified)
                .stream()
                .map(ClassFqn::getCode)
                .collect(Collectors.toList());
    }

    /**
     * Возвращает метакласс, соответствующий объекту
     *
     * @param obj объект, метакласс которого требуется получить, ClassFqn или fqn в виде строки
     * @return метакласс
     * @throws NullPointerException если переданный объект null
     */
    public MetaClass getMetaClass(Object obj)
    {
        return metainfoService.getMetaClass(obj);
    }

    /**
     * Вычисляет ближайшего общего предка для типов permittedTypes в рамках метакласса currentMetaClass
     *
     * @param currentMetaClass метакласс
     * @param permittedTypes коллекция fqn разрешённых типов
     * @return Ближайший общий тип для коллекции permittedTypes. Если такого нет или коллекция пуста - возвращается
     * currentMetaClass
     */
    public MetaClass getNearestCommonParent(MetaClass currentMetaClass, @Nullable Collection<ClassFqn> permittedTypes)
    {
        if (CollectionUtils.isEmpty(permittedTypes))
        {
            return currentMetaClass;
        }
        List<ClassFqn> commonFqns = permittedTypes.stream()
                .map(fqn ->
                {
                    List<ClassFqn> parentsList = Stream
                            .iterate(fqn, t -> !t.equals(metainfoService.getParentClassFqn(t)),
                                    metainfoService::getParentClassFqn)
                            .collect(Collectors.toList());
                    Collections.reverse(parentsList);
                    return parentsList;
                })
                .reduce((l1, l2) ->
                        l1.stream().filter(l2::contains).collect(Collectors.toList())).orElseGet(ArrayList::new);
        if (commonFqns.contains(currentMetaClass.getFqn()))
        {
            return metainfoService.getMetaClass(commonFqns.getLast());
        }
        return currentMetaClass;
    }

    /**
     * Пред-обработка фильтра перед его непосредственным применением к критерии.
     * <br>
     *
     * @param filter обрабатываемый фильтр
     * @param criteriaMetaClass корневой метакласс текущей критерии
     * @return обработанный фильтр
     */
    private IObjectFilter preprocessFilter(final IFilter filter, final MetaClass criteriaMetaClass)
    {
        if (filter instanceof IAndFilter)
        {
            return new AndFilter(((IAndFilter)filter).getFilters().stream()
                    .map(f -> preprocessFilter(f, criteriaMetaClass))
                    .collect(Collectors.toList()));
        }
        if (filter instanceof OrFilter)
        {
            return new OrFilter(((OrFilter)filter).getFilters().stream()
                    .map(f -> preprocessFilter(f, criteriaMetaClass))
                    .collect(Collectors.toList()));
        }
        if (filter instanceof NotFilter)
        {
            return new NotFilter(((NotFilter)filter).getFilters().stream()
                    .map(f -> preprocessFilter(f, criteriaMetaClass))
                    .collect(Collectors.toList()));
        }
        if (filter instanceof InAttributesChainFilter)
        {
            /*
             * Если ApiCriteria формируется на основе DtoCriteria, сюда может прийти готовый InAttributesChainFilter,
             * тогда его дальше обрабатывать не нужно.
             */
            return (InAttributesChainFilter)filter;
        }
        if (filter instanceof NamedFilter)
        {
            String[] attrNamesChain = ((NamedFilter)filter).name.split(AttributeLink.CHAIN_DELIMITER_REGEX);
            ArrayList<AttrReference> attrReferences = new ArrayList<>();
            MetaClass currentMetaClass = criteriaMetaClass;
            for (int i = 0; i < attrNamesChain.length; i++)
            {
                boolean isLast = i == attrNamesChain.length - 1;
                String currentAttrName = attrNamesChain[i];
                Attribute currentAttr = currentMetaClass.getAttribute(currentAttrName);
                AttributeType currentAttrType = getAttributeTypeWithPermittedTypes(currentAttr);
                if (currentAttrType.isAttributeOfRelatedObject())
                {
                    attrReferences.addAll(AttrOfRelatedObjectChainFilterCreator
                            .createChainFilter(currentAttrType, currentAttr.getFqn().toString())
                            .getAttrChain());
                    if (!isLast)
                    {
                        attrReferences.add(new AttrReference(currentAttrType.getRelatedObjectMetaClass(),
                                currentAttrType.getRelatedObjectAttribute()));
                        currentMetaClass = extractReferencedMetaClass(currentAttrType);
                    }
                }
                else if (!isLast && LinkAttributeUtils.isLinkAttribute(currentAttr))
                {
                    attrReferences.add(new AttrReference(currentMetaClass.getFqn(), currentAttrName));
                    currentMetaClass = extractReferencedMetaClass(currentAttrType);
                }
                if (isLast || !LinkAttributeUtils.isLinkAttribute(currentAttr))
                {
                    if (!attrReferences.isEmpty())
                    {
                        InAttributesChainFilter chainFilter = new InAttributesChainFilter(attrReferences, true);
                        if (currentAttrType.isAttributeOfRelatedObject())
                        {
                            ((NamedFilter)filter).name = currentAttrType.getRelatedObjectAttribute();
                        }
                        else
                        {
                            ((NamedFilter)filter).name = isLast ? currentAttrName :
                                    String.join(".", Arrays.copyOfRange(attrNamesChain, i, attrNamesChain.length));
                        }
                        chainFilter.setEndOfChainFilters((IObjectFilter)filter);
                        return chainFilter;
                    }
                    break;
                }
            }
        }
        return (IObjectFilter)filter;
    }

    /**
     * Возвращает метакласс объектов, на которые ссылается атрибут attributeType
     * @param attributeType атрибут ссылочного типа
     */
    private MetaClass extractReferencedMetaClass(AttributeType attributeType)
    {
        return getNearestCommonParent(getMetaClass(attributeType.getProperty(ObjectAttributeType.METACLASS_FQN)),
                attributeType.getProperty(ObjectAttributeType.PERMITTED_TYPES));
    }

    /**
     * В {@link AttributeImpl#getType()} ограничение по типам перекладывается из свойств атрибута в свойства типа
     * Но т.к.8 метод апи может быть вызван до инициализации после старта, то прежде чем получать тип, нужно убедиться,
     * что ограничения уже присутствуют
     *
     * @return тип атирбута
     */
    public static AttributeType getAttributeTypeWithPermittedTypes(Attribute attribute)
    {
        if (attribute instanceof AttributeImpl attributeImpl)
        {
            attributeImpl.lazyRecalcPermittedTypes();
        }
        return attribute.getType();
    }
}
