package ru.naumen.core.server.mapper.strategy;

import java.util.Collection;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import ru.naumen.common.server.utils.html.HtmlSanitizer;
import ru.naumen.core.shared.Constants;
import ru.naumen.metainfo.shared.Constants.RichTextAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;

/**
 * Стратегия для преобразования значения rtf-атрибута для отображения в списках
 * Производится замена(вырезание) всех картинок(тэг img)
 * В списках комментариев ссылки не заменяются.
 * <AUTHOR>
 * @since 25.05.2014
 */
@Component
public class MappingContextCutImagesStrategy implements MappingContextStrategy
{
    @Inject
    HtmlSanitizer htmlSanitizer;

    @Override
    public Object process(Object value, Attribute attr)
    {
        if (RichTextAttributeType.CODE.equals(attr.getType().getCode())
            && !Constants.Comment.CLASS_ID.equals(attr.getFqn().getClassFqn().getId()))
        {
            if (attr.getType().isAttributeOfRelatedObject() && value instanceof Collection)
            {
                return ((Collection<?>)value).stream().map(String.class::cast).map(htmlSanitizer::cutImages)
                        .collect(Collectors.toSet());
            }

            return htmlSanitizer.cutImages((String)value);
        }
        return value;
    }
}