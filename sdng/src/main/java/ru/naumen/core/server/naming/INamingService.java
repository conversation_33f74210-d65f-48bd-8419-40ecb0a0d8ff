package ru.naumen.core.server.naming;

import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.NamingInfo;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.HasGenarationRule;

/**
 * Сервис именования.
 * <p>
 * Под именованием помимо присвоения названия подразумевается еще и присвоение номера.
 * Как показывает практика данные операции стабильно образуют пару, поэтому тут они связаны узами одного класса.
 * И еще тут {@link INamed} связаны.
 *
 * <AUTHOR>
 * @since 27.12.2010
 *
 */
public interface INamingService
{
    /**
     * Производит проверку правила генерации
     *
     * @param attr атрибут, правило генерации котрого требуется проверить
     * @return true - правило генерации не содержит ошибок, false иначе
     */
    boolean checkRule(Attribute attr, String rule);

    boolean checkRule(ClassFqn fqn, String attrTypeCode, String rule);

    /**
     * Производит генерацию значения атрибута на основание {@link HasGenarationRule#getGenerationRule() правила}
     * генерации значения
     *
     * @param <T>
     * @param object объект значение атрибута которого генерируется
     * @param attr атрибут значение которого генерируется
     * @return сгенерированное значение
     */
    <T> T generate(IHasMetaInfo object, Attribute attr);

    /**
     * Получает информацию по правилам именования объекта
     *
     * @param fqn идентификатор метакласса
     */
    NamingInfo getInfo(ClassFqn fqn, String attrTypeCode);

    INamingUnit getUnit(ClassFqn fqn, String template);

    /**
     * Проверка на использования правила именнования для данного атрибута
     *
     * @param attr
     * @return
     */
    boolean useRule4Attribute(Attribute attr);
}
