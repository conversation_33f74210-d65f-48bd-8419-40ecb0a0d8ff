package ru.naumen.core.server.script.spi;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import jakarta.annotation.PostConstruct;

import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.spi.ScriptServiceStatistic.Sync;

/**
 * Механизм накопления выполняемых скриптов и их статистики
 *
 * <AUTHOR>
 * @since 26.07.2014
 *
 */
@Component
public class ScriptServiceScriptCollector
{
    /** Если в скрипте содержится эта строка, то скрипт не будет сохраняться */
    private static final String NOT_COLLECTED_SCRIPTS = "api.at.";

    /* Флаг включения логирования */
    private boolean collectScipts = false;

    /* Список выполненных скриптов */
    private List<Sync> executedScripts;

    public void addExecutedScripts(Sync script)
    {
        if (!script.getScript().getBody().contains(NOT_COLLECTED_SCRIPTS))
        {
            executedScripts.add(script);
        }
    }

    public void clearScripts()
    {
        getExecutedScripts().clear();
    }

    public List<Sync> getExecutedScripts()
    {
        return executedScripts;
    }

    public List<Sync> getScriptObjs(String script)
    {
        List<Sync> result = new ArrayList<>();
        if (!StringUtilities.isEmptyTrim(script))
        {
            for (Sync executedScript : getExecutedScripts())
            {
                if (executedScript.getScript().getBody().equals(script))
                {
                    result.add(executedScript);
                }
            }
        }
        return result;
    }

    @PostConstruct
    public void initialize()
    {
        executedScripts = Collections.synchronizedList(new ArrayList<Sync>());
    }

    public boolean isCollectScripts()
    {
        return collectScipts;
    }

    public void setCollectScripts(boolean collectEnable)
    {
        collectScipts = collectEnable;
        if (!collectEnable)
        {
            clearScripts();
        }
    }
}