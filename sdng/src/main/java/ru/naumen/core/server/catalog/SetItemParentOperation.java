package ru.naumen.core.server.catalog;

import java.util.Objects;

import jakarta.inject.Inject;

import org.springframework.stereotype.Component;

import ru.naumen.bcp.server.operations.OperationException;
import ru.naumen.bcp.server.operations.context.AtomOperationContext;
import ru.naumen.bcp.server.operations.context.IHasObjectBOContext;
import ru.naumen.core.server.bo.bop.Constants;
import ru.naumen.core.server.bo.bop.SetParentOperation;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants.ValueMapCatalogItem;

/**
 * <AUTHOR>
 *
 */
@Component
public class SetItemParentOperation<CI extends ICatalogItem<CI>> extends SetParentOperation<CI, CI>
{
    @Inject
    MessageFacade messages;

    @Override
    protected void validate(AtomOperationContext<IHasObjectBOContext<CI>> ctx, CI oldValue, CI newParent)
    {
        super.validate(ctx, oldValue, newParent);
        CI child = ctx.getContext().getObject();
        if (newParent != null && child.isFolder() && !newParent.isFolder())
        {
            throw new OperationException(messages.getMessage("SetItemParentOperation.ItemMustNotBeAFolderParent"));
        }
        if (ValueMapCatalogItem.FQN.equals(child.getMetaClass()) && !ctx.getContext().isNewObject()
            && !Objects.equals(oldValue, newParent)
            && Boolean.TRUE.equals(ctx.getContext().getProperty(Constants.IS_ADVIMPORT_PROCESS)))
        {
            throw new OperationException(messages.getMessage("SetAttrValueOperationCreateOnly.AttrValueChangeIsDenied",
                    getAttribute(ctx).getTitle()));
        }
    }
}
