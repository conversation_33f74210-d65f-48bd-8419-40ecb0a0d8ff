package ru.naumen.core.server.script.spi.limits;

import jakarta.annotation.Nullable;

import ru.naumen.commons.shared.utils.StringUtilities;

/**
 * Шаблон разрешения на вызов метода
 *
 * Шаблон может использоваться как разрешение на вызов у любого класса метода toString() 
 * а также как разрешение любых методов у класса Object
 * <AUTHOR>
 *
 */
public class ScriptMethodCallTemplate
{
    // названия класса
    private final String className;
    // название метода
    private final String methodName;

    // строковое представление вызова
    private final String callString;

    public ScriptMethodCallTemplate(@Nullable String className, @Nullable String methodName)
    {
        this.className = className;
        this.methodName = methodName;

        callString = (StringUtilities.isEmpty(className) ? "*" : className) + "."
                     + (StringUtilities.isEmpty(methodName) ? "*" : methodName);
    }

    @Override
    public boolean equals(Object obj)
    {
        if (this == obj)
        {
            return true;
        }

        if (obj instanceof ScriptMethodCallTemplate)
        {
            boolean classEqual = false;
            boolean methodEqual = false;

            ScriptMethodCallTemplate acmc = (ScriptMethodCallTemplate)obj;

            //@formatter:off
            if ((this.getClassName() == null && this.getClassName() == acmc.getClassName()) // NOPMD
                    || (this.getClassName() != null && acmc.getClassName() != null && (this.getClassName().equals(
                            acmc.getClassName())
                    || (acmc.getClassName() != null && this.getClassName() == null)
                    || (this.getClassName() != null && acmc.getClassName() == null)
                    || (this.getClassName().startsWith(acmc.getClassName()) && acmc.getMethodName() == null) 
                    || (acmc.getClassName().startsWith(this.getClassName()) && this.getMethodName() == null))))
            //@formatter:on
            {
                classEqual = true;
            }

            //@formatter:off
            if ((this.getMethodName() == null && this.getMethodName() == acmc.getMethodName()) // NOPMD
                    || (this.getMethodName() != null && acmc.getMethodName() != null && this.getMethodName().equals(
                            acmc.getMethodName())) 
                    || (acmc.getMethodName() == null && this.getMethodName() != null)
                    || (acmc.getMethodName() != null && this.getMethodName() == null))
            //@formatter:on
            {
                methodEqual = true;
            }

            return classEqual && methodEqual;
        }

        return false;
    }

    public String getCallString()
    {
        return callString;
    }

    public String getClassName()
    {
        return className;
    }

    public String getMethodName()
    {
        return methodName;
    }

    @Override
    public int hashCode()
    {
        return toString().hashCode();
    }

    @Override
    public String toString()
    {
        return callString;
    }

}
