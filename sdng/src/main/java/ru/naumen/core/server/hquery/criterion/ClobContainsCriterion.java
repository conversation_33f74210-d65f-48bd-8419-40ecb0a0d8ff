package ru.naumen.core.server.hquery.criterion;

import java.util.Objects;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * Критерия для
 *
 * <AUTHOR>
 * @since 30.01.2013
 */
public class ClobContainsCriterion extends AbstractHCriterion
{
    private final Object value;

    private String paramName;

    public ClobContainsCriterion(HColumn property, Object value)
    {
        super(property);
        this.value = value;
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        paramName = parameterCounter.next();

        sb.append("instr(").append(property.getHQL(builder)).append(", :")
                .append(paramName).append(" ) > 0");
    }

    @Override
    public void setParameters(Query q)
    {
        super.setParameters(q);
        q.setParameter(paramName, value);
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new ClobContainsCriterion(property, value);
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        ClobContainsCriterion that = (ClobContainsCriterion)o;
        return Objects.equals(value, that.value);
    }

    @Override
    public String toString()
    {
        return "ClobContainsCriterion{" +
               "property=" + property +
               ", paramName='" + paramName + '\'' +
               ", value=" + value +
               '}';
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), value);
    }
}
