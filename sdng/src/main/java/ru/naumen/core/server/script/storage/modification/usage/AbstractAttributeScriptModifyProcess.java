package ru.naumen.core.server.script.storage.modification.usage;

import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import jakarta.inject.Inject;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;

import java.util.HashMap;

import ru.naumen.core.server.componform.ComputableOnFormHelper;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.util.DateTimeAttrRestrictionHelper;
import ru.naumen.core.shared.Constants.Scripts;
import ru.naumen.core.shared.script.places.OriginService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.HasScripts;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Абстрактная реализация {@link ScriptModifyProcess} для атрибутов и параметров настраиваемых форм
 *
 * <AUTHOR>
 * @since 6 мая 2016 г.
 */
public abstract class AbstractAttributeScriptModifyProcess<T extends HasScripts> extends ScriptModifyProcessSupport<T>
{
    public static final String ATTR_FQN = "attrFqn";

    @Inject
    protected ScriptService scriptService;
    @Inject
    private ComputableOnFormHelper computableOnFormHelper;
    @Inject
    private DateTimeAttrRestrictionHelper dateTimeAttrRestrictionHelper;
    @Inject
    private OriginService originService;

    protected Map<String, Object> getBindingsForFiltration()
    {
        Map<String, Object> bindings = new HashMap<>();
        bindings.put(ScriptService.Constants.SUBJECT, null);
        return bindings;
    }

    @Override
    protected String getLocation(T holder, ScriptModifyContext context)
    {
        return context.getProperty(ATTR_FQN);
    }

    @Override
    protected Collection<ClassFqn> getRelatedMetaClasses(T holder, ScriptModifyContext context)
    {
        return Lists.newArrayList(holder.getMetaClass().getFqn());
    }

    @Override
    protected boolean isUsageChangeable()
    {
        return false;
    }

    protected void updateComputableOnForm(Script script, T newHolder)
    {
        Map<String, Object> bindings = Maps.newHashMapWithExpectedSize(3);
        bindings.put(ScriptService.Constants.SUBJECT, null);
        bindings.put("form", null);
        bindings.put(Scripts.ATTR_CODE, newHolder.getCode());

        List<String> scriptResult = scriptService.execute(script, bindings);
        if (null == scriptResult)
        {
            scriptResult = Collections.emptyList();
        }

        validateCOFScriptCodes(scriptResult, newHolder);

        newHolder.setAttrsUsedInEditValueScript(scriptResult);
        computableOnFormHelper.assertNoLoopDependency(newHolder);
    }

    protected void updateComputableOnFormAR(T holder)
    {
        holder.setAttrsUsedInEditValueScript(null);
    }

    protected void updateDateTimeRestriction(Script script, T newHolder)
    {
        Map<String, Object> bindings = getBindingsForDateTimeRestriction();
        bindings.put(Scripts.ATTR_CODE, newHolder.getCode());
        bindings.put(Scripts.ORIGIN, originService.getOrigin());

        List<String> scriptResult = scriptService.execute(script, bindings);
        if (null == scriptResult)
        {
            scriptResult = Collections.emptyList();
        }

        validateRestrictionScriptAttributes(scriptResult, newHolder);

        newHolder.setAttrsForDateTimeRestrictionScript(scriptResult);
    }

    abstract Map<String, Object> getBindingsForDateTimeRestriction();

    protected void updateDateTimeRestrictionAR(T holder)
    {
        holder.setAttrsForDateTimeRestrictionScript(Collections.emptyList());
    }

    protected void updateFiltration(Script script, T newHolder)
    {
        Map<String, Object> bindings = getBindingsForFiltration();
        bindings.put(Scripts.ATTR_CODE, newHolder.getCode());
        bindings.put(Scripts.ORIGIN, originService.getOrigin());

        List<String> scriptResult = scriptService.execute(script, bindings);
        if (null == scriptResult)
        {
            scriptResult = Collections.emptyList();
        }

        validateFiltrationScriptCodes(scriptResult, newHolder);

        newHolder.setAttrsUsedInScript(scriptResult);
    }

    protected void updateFiltrationAR(T holder)
    {
        holder.setAttrsUsedInScript(Collections.emptyList());
    }

    protected void validateCOFScriptCodes(List<String> codes, T holder)
    {
    }

    protected void validateFiltrationScriptCodes(List<String> codes, T holder)
    {
    }

    /**
     * Проверяет существование указанных кодов атрибутов
     */
    protected void validateRestrictionScriptAttributes(List<String> attributesCodes, T newHolder)
    {
        dateTimeAttrRestrictionHelper.validateRestrictionScriptAttributes(attributesCodes, newHolder);
    }

}
