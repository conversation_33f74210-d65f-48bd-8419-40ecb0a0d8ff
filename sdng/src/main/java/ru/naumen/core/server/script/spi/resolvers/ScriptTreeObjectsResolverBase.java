package ru.naumen.core.server.script.spi.resolvers;

import java.util.Collection;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.springframework.util.StopWatch;

import com.google.common.base.Preconditions;
import com.google.common.collect.HashMultimap;
import com.google.common.collect.Lists;
import com.google.common.collect.Multimap;

import java.util.HashSet;

import com.google.gson.JsonElement;

import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.common.server.utils.localization.LocalizedTitleChecker;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.LocalizationHelper;
import ru.naumen.core.server.bo.DaoHelper;
import ru.naumen.core.server.bo.root.RootDao;
import ru.naumen.core.server.script.spi.LazyScriptDtObject;
import ru.naumen.core.server.script.spi.LazyScriptTreeDtObject;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.script.spi.ScriptUtils;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.metainfo.shared.elements.adapters.ObjectAttributeTypeStrategy;

/**
 * Предоставляет базовые возможности для извлечения lazy иерархий объектов:
 * загрузку объектов с минимальным набором полей и оборачивание их в {@link LazyScriptTreeDtObject}.
 * <AUTHOR>
 * @since Jan 27, 2016
 */
public abstract class ScriptTreeObjectsResolverBase
{
    /**
     * Внутренний контекст для передачи состояния между непубличными методами.
     */
    protected class ResolveContext
    {
        public Map<String, IUUIDIdentifiable> cache = new HashMap<>(); // NOSONAR
        public Map<String, SimpleDtObject> delegates = new HashMap<>(); // NOSONAR
        public Map<String, Boolean> hasParents = new HashMap<>(); // NOSONAR
        public String rootUUID = rootDao.getCoreRoot().getUUID();
        public Collection<Object> result = new HashSet<>(); // NOSONAR
        public boolean withFolders;
        public boolean allowRemoved;
    }

    protected class ObjectResolveContext extends ResolveContext
    {
        public ClassFqn classFqn;
        /**
         * Fqn источника атрибута
         */
        public ClassFqn sourceFqn;
        public String attributeCode;
    }

    /**
     * Количество объектов, загружаемых за один раз.
     */
    protected static final int BATCH_SIZE = 1000;

    private static final String NOT_REMOVED_CONDITION_SUFFIX = " AND obj.removed = false";
    private static final String REMOVED_FIELD = ", obj.removed";
    private static final String WITH_PARENT = ", obj.parent.id";
    private static final String LOAD_HQL = "SELECT obj.id, obj.metaCaseId, obj.title%s%s%s FROM %s AS obj WHERE obj"
                                           + ".id IN (:ids)%s";

    @Inject
    protected MetainfoService metainfoService;
    @Inject
    protected DaoHelper daoHelper;
    @Inject
    @Named("sessionFactory")
    protected SessionFactory sessionFactory;
    @Inject
    private RootDao rootDao;
    @Inject
    private ScriptDtOHelper helper;
    @Inject
    private ScriptUtils scriptUtils;
    @Inject
    private LocalizedTitleChecker localizedTitleChecker;

    /**
     * Загружает при необходимости указанные объекты и строит для них иерархию.
     * @param sw таймер для измерения времени выполнения в режиме отладки
     * @param rawValue список "сырых" объектов
     * @param context внутренний контекст
     * @return список объектов с построенной иерархией
     */
    protected Collection<Object> doResolve(StopWatch sw, Collection<Object> rawValue, ResolveContext context)
    {
        // Извлекаем UUID из rawValue, дальше будем их обрабатывать.
        Set<String> uuidsToResolve = doResolveUUIDs(sw, rawValue, context);

        context.cache.put(context.rootUUID, rootDao.getCoreRoot());

        // Загрузим всю иерархию в память.
        mergeResult(doResolveHierarchy(sw, uuidsToResolve, context), context);

        // Теперь нужно преобразовать все DTO объекты в lazy tree.
        sw.start("transform " + context.cache.size());
        transformSimpleToTree(context);
        sw.stop();
        // Осталось UUID родителей заменить на lazy tree.
        sw.start("set parents " + context.cache.size());
        setParents(context);
        sw.stop();

        sw.start("process result");
        processResult(context);
        sw.stop();

        return context.result;
    }

    /**
     * Извлекает уиды из сырого значения
     */
    protected Set<String> doResolveUUIDs(StopWatch sw, Collection<Object> rawValue, ResolveContext context)
    {
        // Извлекаем UUID из rawValue, дальше будем их обрабатывать.
        sw.start("prepare " + rawValue.size() + " objects");
        Set<String> uuidsToResolve = prepareObjects(rawValue, context);
        sw.stop();

        sw.start("filter " + uuidsToResolve.size() + " objects");
        uuidsToResolve = filter(uuidsToResolve, context);
        sw.stop();
        return uuidsToResolve;
    }

    /**
     * Выполняет фильтрацию списка идентификаторов (UUID), чтобы впоследствии не грузить заведомо ненужные объекты.
     * @param uuids исходный список идентификаторов
     * @param context
     * @return результирующий список идентификаторов
     */
    protected abstract Set<String> filter(Set<String> uuids, ResolveContext context);

    /**
     * Проверяет, содержит ли объект только UUID.
     * @param obj проверяемый объект
     * @return true, если это UUID или незагруженный {@link LazyScriptTreeDtObject}, иначе false
     */
    protected boolean isUuid(Object obj)
    {
        boolean result = obj instanceof String;
        result = result || obj instanceof JsonElement;

        if (!result && obj instanceof LazyScriptDtObject)
        {
            result = result || ((LazyScriptDtObject)obj).isUuidObject();
        }

        return result;
    }

    /**
     * Проверяет, могут ли объекты данного класса иметь родителей.
     * @param fqn FQN класса
     * @param context внутренний контекст
     * @return true, если объекты указанного класса могут быть вложены в другие объекты, иначе false
     */
    protected boolean isWithParent(ClassFqn fqn, ResolveContext context)
    {
        String classId = fqn.getId();
        Boolean result = context.hasParents.get(classId);
        if (result == null)
        {
            result = metainfoService.getMetaClass(classId).hasAttribute(Constants.PARENT_ATTR);
            context.hasParents.put(classId, result);
        }
        return result;
    }

    /**
     * Сохраняет загруженные объекты во внутреннем контексте.
     * @param dtos загруженные объекты
     * @param context внутренний контекст
     */
    protected void mergeResult(Set<SimpleDtObject> dtos, ResolveContext context)
    {
        context.result.addAll(dtos);
    }

    /**
     * Подготавливает "сырые" объекты, вычисляет, какие из них необходимо загрузить. 
     * @param rawValue список "сырых" объектов
     * @param context внутренний контекст
     * @return список UUID объектов, которые необходимо загрузить
     */
    protected abstract Set<String> prepareObjects(Collection<Object> rawValue, ResolveContext context);

    /**
     * Выполняет пост-обработку загруженной иерархии объектов.
     * @param context внутренний контекст
     */
    protected void processResult(ResolveContext context)
    {
    }

    /**
     * Загружает лайт объекты по UUID, формирует на их основе {@link SimpleDtObject}.
     * Затем извлекает всех родителей загруженных объектов и рекурсивно загружает родителей.
     */
    private Set<SimpleDtObject> doResolveHierarchy(StopWatch sw, Set<String> uuids, ResolveContext context)
    {
        if (uuids.isEmpty())
        {
            return new HashSet<>();
        }

        // Одним запросом в методе будем загружать объекты только одного класса - группируем по классу.
        sw.start("group by class " + uuids.size() + " uuids");
        Multimap<String, String> groupedByClass = HashMultimap.create();
        for (String uuid : uuids)
        {
            groupedByClass.put(UuidHelper.toPrefix(uuid), uuid);
        }
        sw.stop();

        Set<SimpleDtObject> objs = new HashSet<>();
        for (Map.Entry<String, Collection<String>> entry : groupedByClass.asMap().entrySet())
        {
            String prefix = entry.getKey();
            MetaClass metaClass = metainfoService.getMetaClass(prefix);
            Collection<String> value = entry.getValue();
            sw.start("load " + prefix + " " + value.size() + " objects");
            Set<SimpleDtObject> dtos = load(value, context);
            sw.stop();

            if (context.withFolders && metaClass.isHasFolders())
            {
                // Заполним атрибут folders.
                sw.start("load folders for " + prefix + " " + value.size() + " objects");
                daoHelper.loadFolders(dtos, metaClass);
                sw.stop();
            }

            objs.addAll(dtos);
        }

        // Помещаем объекты в контекст, чтобы повторно их уже не загружать.
        sw.start("put to cache " + objs.size() + " objects");
        for (SimpleDtObject obj : objs)
        {
            context.cache.put(obj.getUUID(), obj);
        }
        sw.stop();

        // Извлекаем незагруженных родителей.
        sw.start("get parents for " + objs.size() + " objects");
        Set<String> parents = new HashSet<>();
        for (SimpleDtObject obj : objs)
        {
            String parentUUID = (String)obj.get(Constants.PARENT_ATTR);
            if (!StringUtilities.isEmpty(parentUUID) && !context.cache.containsKey(parentUUID))
            {
                parents.add(parentUUID);
            }
        }
        sw.stop();

        // Загружаем родителей и сохраняем их в контекст, чтобы впоследствии заменить UUID на загруженный объект.
        doResolveHierarchy(sw, parents, context);

        return objs;
    }

    private String generateLoadHqlQuery(String prefix, boolean withParent, boolean allowRemoved, boolean localized)
    {
        String parent = withParent ? WITH_PARENT : StringUtilities.EMPTY;
        String localizedTitle = !localized ? StringUtilities.EMPTY
                : ", " + LocalizationHelper.getPropNameForCurrentLocale(AbstractBO.TITLE);
        String additionalFields = allowRemoved ? REMOVED_FIELD : StringUtilities.EMPTY;
        String conditions = allowRemoved ? StringUtilities.EMPTY : NOT_REMOVED_CONDITION_SUFFIX;
        return String.format(LOAD_HQL, localizedTitle, additionalFields, parent, prefix, conditions);
    }

    /**
     * Выполняет загрузку объектов по UUID пачками по {@link #BATCH_SIZE}.
     * Для каждой пачки запрос с IN-условием, в результат которого попадают id, metaClass, title, removed, parent_id.
     */
    private Set<SimpleDtObject> load(Collection<String> uuids, ResolveContext context)
    {
        String prefix = UuidHelper.toPrefix(uuids.iterator().next());
        MetaClass metaClass = metainfoService.getMetaClass(prefix);

        boolean withParent = metaClass.hasAttribute(Constants.PARENT_ATTR);
        context.hasParents.put(prefix, withParent);

        //@formatter:off
        String parentPrefix = withParent
                ? metaClass.getAttribute(Constants.PARENT_ATTR).getType().<ObjectAttributeTypeStrategy> cast().getRelatedMetaClass().getId()
                : null;
        //@formatter:on

        boolean localized = localizedTitleChecker.hasMetaClassLocalizedTitle(metaClass.getFqn());
        String hql = generateLoadHqlQuery(prefix, withParent, context.allowRemoved, localized);

        List<Long> ids = Lists.newArrayList(uuids.stream().map(UuidHelper.TO_ID).toList());
        // Отсортируем, чтобы для большого неизменного списка работал кэш запросов, чтобы обеспечить повторяемость
        // параметров.
        Collections.sort(ids);

        Set<SimpleDtObject> result = new HashSet<>();
        for (List<Long> batch : Lists.partition(ids, BATCH_SIZE))
        {
            Query query = sessionFactory.getCurrentSession().createQuery(hql);
            query.setParameterList("ids", batch);
            query.setCacheable(true);
            @SuppressWarnings("unchecked")
            List<Object> objs = query.list();

            for (Object obj : objs)
            {
                Object[] array = (Object[])obj;
                String uuid = UuidHelper.toUuid((Long)array[0], prefix);
                ClassFqn fqn = ClassFqn.parse(prefix, (String)array[1]);
                String title = (String)(localized && array[3] != null ? array[3] : array[2]);

                SimpleDtObject dto = new SimpleDtObject(uuid, title, fqn, false);
                String parentUUID = context.rootUUID;

                if (context.allowRemoved && Boolean.TRUE.equals(localized ? array[4] : array[3]))
                {
                    dto.setProperty(AbstractBO.REMOVED, true);
                }

                if (withParent && array[array.length - 1] != null)
                {
                    parentUUID = UuidHelper.toUuid((Long)array[array.length - 1], parentPrefix);
                }
                dto.setProperty(Constants.PARENT_ATTR, parentUUID);
                result.add(dto);
            }
        }

        return result;
    }

    /**
     * Заменяет у всех {@link LazyScriptTreeDtObject} UUID родителя на {@link DtObject} из иерархии.
     */
    private void setParents(ResolveContext context)
    {
        for (IUUIDIdentifiable obj : context.cache.values())
        {
            if (!(obj instanceof LazyScriptTreeDtObject))
            {
                continue;
            }

            LazyScriptTreeDtObject dto = (LazyScriptTreeDtObject)obj;
            Object parent = dto.get(Constants.PARENT_ATTR);

            if (parent instanceof String)
            {
                String parentUUID = (String)parent;
                SimpleDtObject delegate = context.delegates.get(dto.getUUID());
                Preconditions.checkNotNull(delegate);
                delegate.put(Constants.PARENT_ATTR, context.cache.get(parentUUID));
            }
        }
    }

    /**
     * Оборачивает {@link SimpleDtObject} в скриптовый объект {@link LazyScriptTreeDtObject}.
     * Движок фильтрации на основе {@link LazyScriptTreeDtObject} понимает, что иерархия уже загружена внутри объекта.
     */
    private void transformSimpleToTree(ResolveContext context)
    {
        List<IUUIDIdentifiable> values = Lists.newArrayList(context.cache.values());

        for (IUUIDIdentifiable obj : values)
        {
            if (!(obj instanceof SimpleDtObject))
            {
                continue;
            }

            SimpleDtObject simpleDto = (SimpleDtObject)obj;
            String parentAttr = simpleDto.hasProperty(Constants.PARENT_ATTR) ? Constants.PARENT_ATTR : null;
            LazyScriptTreeDtObject treeDto = new LazyScriptTreeDtObject(simpleDto, helper, scriptUtils, parentAttr);
            treeDto.setHasProperty(Constants.PARENT_ATTR, isWithParent(treeDto.getMetainfo(), context));

            if (context.result.contains(simpleDto))
            {
                context.result.remove(simpleDto);
                context.result.add(treeDto);
            }

            context.cache.put(treeDto.getUUID(), treeDto);
            context.delegates.put(simpleDto.getUUID(), simpleDto);
        }
    }
}
