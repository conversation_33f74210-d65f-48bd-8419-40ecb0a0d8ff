package ru.naumen.core.server.script.spi;

import java.util.concurrent.TimeoutException;

import jakarta.inject.Inject;

import org.codehaus.groovy.control.MultipleCompilationErrorsException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import ru.naumen.commons.server.utils.ExceptionsUtil;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.ScriptHelper;
import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.server.util.SQLExceptionUtils;
import ru.naumen.metainfo.shared.script.Script;

/**
 * @see ScriptExceptionsService
 *
 * <AUTHOR>
 * @since Dec 27, 2019
 */
@Service
public class ScriptExceptionsServiceImpl implements ScriptExceptionsService
{
    private static final Logger LOG = LoggerFactory.getLogger(ScriptExceptionsServiceImpl.class);

    private final ScriptHelper scriptHelper;
    private final MessageFacade messages;

    @Inject
    public ScriptExceptionsServiceImpl(final ScriptHelper scriptHelper, final MessageFacade messages)
    {
        this.scriptHelper = scriptHelper;
        this.messages = messages;
    }

    @Override
    public ScriptServiceException createScriptException(
            Script script, boolean hideScriptBody, Throwable cause)
    {
        LOG.debug(cause.getMessage(), cause);

        final String body = script.getBody();
        final String scriptValue = hideScriptBody || body == null ? StringUtilities.EMPTY : body;
        final String scriptCodeAsLink = scriptHelper.getScriptCodeAsLink(script);

        Throwable t = ExceptionsUtil.getException(cause, MultipleCompilationErrorsException.class);
        if (t != null)
        {
            final String msg = t.getMessage();
            return new ScriptServiceException("Script " + scriptCodeAsLink + " compilation error \n" + msg, cause);
        }

        t = ExceptionsUtil.getException(cause, ArithmeticException.class);
        if (t != null)
        {
            final String msg = t.getMessage();

            return new ScriptServiceException("Script " + scriptCodeAsLink + " runtime error" + System.lineSeparator()
                                              + msg + ". Hashcode: " + scriptValue.hashCode(), cause
            );
        }

        final String errorMessage = "Error: " + cause.getClass().getSimpleName() + "."
                                    + " Message: " + cause.getMessage()
                                    + (scriptValue.isEmpty()
                ? ""
                : " in script " + scriptCodeAsLink + " with hashcode " + scriptValue.hashCode()
                  + System.lineSeparator()
                  + System.lineSeparator()
                  + scriptValue + System.lineSeparator());

        t = ExceptionsUtil.getException(cause, ScriptReadableException.class);
        if (t != null)
        {
            return (ScriptReadableException)t;
        }

        final FxException readableException = ExceptionsUtil.getException(cause, true);
        if (readableException != null)
        {
            LOG.info(errorMessage);
            return new ScriptServiceException(readableException.getMessage(), cause);
        }

        if (ExceptionsUtil.getException(cause, jakarta.transaction.RollbackException.class) != null
            || ExceptionsUtil.getException(cause, TimeoutException.class) != null)
        {
            return new ScriptServiceException(messages.getMessage("ScriptServiceException.transactionTimeout"), cause);
        }

        if (SQLExceptionUtils.isDatabaseConnectionException(cause))
        {
            cause = new FxException(cause.getMessage(), true);
        }

        return new ScriptServiceException(
                "Error in script " + scriptCodeAsLink + ". Hashcode: " + scriptValue.hashCode() + ", error:"
                + System.lineSeparator() + cause.getClass().getSimpleName()
                + ", message: " + cause.getMessage(), cause);
    }
}
