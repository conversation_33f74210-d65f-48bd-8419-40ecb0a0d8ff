package ru.naumen.core.server.naming;

import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.NamingInfo;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.HasGenarationRule;

/**
 * "Движок". Именует объекты в соответствии с заданным правилом.  
 *
 * <AUTHOR>
 * @since 27.12.2010
 *
 */
public interface INamingEngine
{
    /**
     * Производит проверку правила генерации
     *
     * @param attr атрибут, правило генерации котрого требуется проверить
     * @return true - правило генерации не содержит ошибок, false иначе
     */
    boolean checkRule(Attribute attr, String rule);

    /**
     * Производит проверку правила генерации
     *
     * @param attrTypeCode - код типа атрибута, правило генерации котрого требуется проверить
     * @return true - правило генерации не содержит ошибок, false иначе
     */
    boolean checkRule(String attrTypeCode, String rule);

    /**
     * Производит генерацию значения атрибута на основаниие {@link HasGenarationRule#getGenerationRule() правила}
     * генерации значения
     *
     * @param <T>
     * @param object объект значение атрибута которого генерируется
     * @param attr атрибу значение которого генерируется
     * @return сгенерированное значение
     */
    String generate(IHasMetaInfo object, Attribute attr);

    /**
     * Получает информацию по правилам именования объекта
     *
     * @return
     */
    NamingInfo getInfo(String typeCode);

    ClassFqn getMetaClass();

    INamingUnit getUnit(String template);
}
