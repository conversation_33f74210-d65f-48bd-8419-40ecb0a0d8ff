package ru.naumen.core.server.script.api.accesskeys;

import java.util.Date;

/**
 * Обёртка для {@link AccessKey} для использования в скриптах
 *
 * <AUTHOR>
 *
 * @since 16.04.2013
 *
 */
public class AccessKeyWrapper implements IAccessKeyWrapper
{
    private AccessKey key;

    public AccessKeyWrapper(AccessKey key)
    {
        this.key = key;
    }

    @Override
    public Date getCreationDate()
    {
        return key.getCreationDate();
    }

    @Override
    public Date getDeadline()
    {
        return key.getDeadline();
    }

    @Override
    public String getDescription()
    {
        return key.getDescription();
    }

    public String getEmployeeUuid()
    {
        return key.getEmployeeUuid();
    }

    @Override
    public Date getLastUsageDate()
    {
        return key.getLastUsageDate();
    }

    @Override
    public AccessKeyType getType()
    {
        return key.getType();
    }

    @Override
    public String getUsername()
    {
        return key.getUsername();
    }

    @Override
    public String getUuid()
    {
        return key.getUuid();
    }

    @Override
    public boolean isActive()
    {
        return key.isActive();
    }

    @Override
    public IAccessKeyWrapper setActive(boolean active)
    {
        return new AccessKeyWrapper(key.setActive(active));
    }

    @Override
    public IAccessKeyWrapper setDeadline(Date deadline)
    {
        return new AccessKeyWrapper(key.setDeadline(deadline));
    }

    @Override
    public IAccessKeyWrapper setDeadlineDays(int days)
    {
        return new AccessKeyWrapper(key.setDeadlineDays(days));
    }

    @Override
    public IAccessKeyWrapper setDeadlineHours(int hours)
    {
        return new AccessKeyWrapper(key.setDeadlineHours(hours));
    }

    @Override
    public IAccessKeyWrapper setDeadlineMinutes(int minutes)
    {
        return new AccessKeyWrapper(key.setDeadlineMinutes(minutes));
    }

    @Override
    public IAccessKeyWrapper setDescription(String description)
    {
        return new AccessKeyWrapper(key.setDescription(description));
    }

    @Override
    public IAccessKeyWrapper setDisposable()
    {
        return new AccessKeyWrapper(key.setDisposable());
    }

    @Override
    public IAccessKeyWrapper setReusable()
    {
        return new AccessKeyWrapper(key.setReusable());
    }

    @Override
    public String toString()
    {
        return "AccessKey [deadline=" + key.getDeadline() + ", type=" + key.getType() + ", username="
               + key.getUsername() + "]";
    }
}
