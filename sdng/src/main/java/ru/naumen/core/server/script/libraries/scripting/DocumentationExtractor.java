package ru.naumen.core.server.script.libraries.scripting;

import static java.nio.charset.StandardCharsets.UTF_8;

import java.io.IOException;
import java.io.InputStream;
import java.net.URL;
import java.util.Enumeration;
import java.util.HashMap;

import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import io.github.classgraph.AnnotationInfo;
import io.github.classgraph.AnnotationParameterValueList;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.common.server.utils.html.ObsoleteHtmlSanitizer;//NOSONAR
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.script.libraries.registration.scripts.Documentation;
import ru.naumen.core.server.script.libraries.registration.scripts.InternalAutomationScript;

/**
 * Класс для извлечения документации по скрипту из аннотации.
 * {@link ru.naumen.core.server.script.libraries.registration.scripts.InternalAutomationScript}
 * <AUTHOR>
 * @since 02.09.2021
 */
@Component
public class DocumentationExtractor
{
    private static final Logger LOG = LoggerFactory.getLogger(DocumentationExtractor.class);

    /**
     * Получить текст документации по URL
     * @param documentationURL URL документации;
     * @param documentationPathInJar путь до документации (применяется для логирования в случае ошибки);
     * @return текст документации
     */
    @Nullable
    private static String getDocumentation(URL documentationURL, String documentationPathInJar)
    {
        try (final InputStream docStream = documentationURL.openConnection().getInputStream())
        {
            final String doc = docStream == null
                    ? null
                    : IOUtils.toString(docStream, UTF_8);

            if (doc != null)
            {
                return doc;
            }
        }
        catch (final IOException e)
        {
            LOG.warn("There's an exception while processing a documentation resource [{}].", documentationPathInJar,
                    e);
        }
        return null;
    }

    /**
     * Получить URL документации
     * @param classLoader загрузчик классов в котором расположена исследуемая библиотека с документацией;
     * @param documentationPath путь до документации, указывается в {@link Documentation#path()};
     * @param jarName Имя библиотеки в которой находится исследуемый скрипт. Необходимо для извлечения документации;
     * @return URL документации
     */
    @Nullable
    private static URL getDocumentationUrlByPath(ClassLoader classLoader, String documentationPath, String jarName)
    {
        try
        {
            final Enumeration<URL> docsResources = classLoader.getResources(documentationPath);
            while (docsResources.hasMoreElements())
            {
                URL resource = docsResources.nextElement();
                if (resource.getPath().contains(jarName))
                {
                    // пути до документации могут совпадать у ряда библиотек, поэтому мы ищем именно такой ресурс,
                    // путь до которого содержит имя нашей библиотеки. Т.е. это выглядит примерно так:
                    // file:.../data/libraries/<hash>/jarName.jar!/docs/ru/doc.html
                    return resource;
                }
            }
        }
        catch (IOException e)
        {
            LOG.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * Получение пути до документации с локалью из аннотации {@link Documentation}, представленной как объект после
     * сканирования библиотеки
     * @param documentation документация;
     * @param annotatedClassName класс, аннотированный аннотацией @Documentation;
     * @param scriptCode код скрипта {@link InternalAutomationScript#code()};
     * @return путь до документации или NULL, если в процессе извлечения возникли ошибки, при этом в лог печатается
     * соответствующее сообщение.
     */
    @Nullable
    private static Pair<String, String> getPathToDocWithLocaleFromAnnotationDocumentation(Object documentation,
            String annotatedClassName, String scriptCode)
    {
        if (!(documentation instanceof AnnotationInfo))
        {
            throw new FxException("Somehow an annotation element of a documentation array of the "
                                  + "InternalAutomation annotation of the annotated class [" + annotatedClassName
                                  + "] is not an AnnotationInfo");
        }

        final AnnotationInfo info = (AnnotationInfo)documentation;
        final AnnotationParameterValueList documentationParameterValues = info.getParameterValues();

        final Object localeObj = documentationParameterValues.getValue("locale");
        if (!(localeObj instanceof String))
        {
            LOG.warn(
                    "Annotation element \"locale\" for the \"documentation\" annotation element "
                    + "for "
                    + "the script [{}] is not an instance of String. Skipping processing "
                    + "of "
                    + "the script [{}].", scriptCode, scriptCode);
            return null;
        }
        final String locale = (String)localeObj;

        final Object pathObj = documentationParameterValues.getValue("path");
        if (!(pathObj instanceof String))
        {
            LOG.warn("Annotation element \"path\" for the \"documentation\" annotation element for "
                     + "the script [{}] is not an instance of String. Skipping processing of "
                     + "the script [{}].", scriptCode, scriptCode);
            return null;
        }
        return Pair.create((String)pathObj, locale);
    }

    private final ObsoleteHtmlSanitizer xssSanitizer;//NOSONAR

    @Inject
    public DocumentationExtractor(ObsoleteHtmlSanitizer xssSanitizer)//NOSONAR
    {
        this.xssSanitizer = xssSanitizer;
    }

    /**
     * Получение документаций к скрипту
     * @param classLoader загрузчик классов
     * @param annotatedClassName имя загружаемого класса скрипта
     * @param parameterValues параметры аннотаций
     * @param scriptCode код скрипта
     * @param jarName имя библиотеки в которой находится исследуемый скрипт. Необходимо для извлечения документации.
     * @return карту документации к скрипту в виде "локаль" -> "путь к документации"
     */
    public HashMap<String, String> extractDocumentation(ClassLoader classLoader, String annotatedClassName,
            AnnotationParameterValueList parameterValues, String scriptCode,
            String jarName)
    {
        Object documentationObj = parameterValues.getValue("documentation");
        //У ранее скомпилированных jarников отсутствует значение документация, делаем поле необязательным
        if (documentationObj == null)
        {
            documentationObj = new Object[0];
        }
        if (!(documentationObj instanceof Object[]))
        {
            throw new FxException(
                    "Somehow documentation element for the annotated class [" + annotatedClassName
                    + "] isn't array.");
        }

        final Object[] documentationAnnotationArray = (Object[])documentationObj;
        LOG.trace("Found an documentation array for the annotated class [{}].", annotatedClassName);

        if (documentationAnnotationArray.length == 0)
        {
            LOG.warn("There's no documentation for the script [{}].", scriptCode);
        }

        final HashMap<String, String> localeToDoc = new HashMap<>();
        for (final Object documentation : documentationAnnotationArray)
        {
            final Pair<String, String> pathToDocWithLocale = getPathToDocWithLocaleFromAnnotationDocumentation(
                    documentation, annotatedClassName, scriptCode);
            if (pathToDocWithLocale == null)
            {
                continue;
            }

            final String path = pathToDocWithLocale.left;
            final String locale = pathToDocWithLocale.right;

            if (path.isEmpty())
            {
                LOG.warn("There's no resource [{}] for the script [{}].", path, scriptCode);
                continue;
            }

            final URL documentationURL = getDocumentationUrlByPath(classLoader, path, jarName);
            if (documentationURL == null)
            {
                LOG.warn("There's no resource [{}] for the script [{}].", path, scriptCode);
                continue;
            }
            final String doc = getDocumentation(documentationURL, path);
            if (documentation == null)
            {
                continue;
            }
            localeToDoc.put(locale, xssSanitizer.sanitize(doc));
        }
        return localeToDoc;
    }
}
