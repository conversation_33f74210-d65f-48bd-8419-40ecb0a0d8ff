package ru.naumen.core.server.upload;

import java.io.IOException;
import java.io.InputStream;

/**
 * FileItem, с контентом в виде потока.
 * <AUTHOR>
 */
public class StreamFileItem extends AbsrtractFileItem<StreamFileItem>
{
    protected final DiskFileInputStream stream;

    public StreamFileItem(DiskFileInputStream content, String name, String contentType)
    {
        super();
        setName(name);
        setContentType(contentType);
        this.stream = content;
    }

    @Override
    public InputStream getInputStream() throws IOException
    {
        return stream;
    }

    @Override
    public long getSize()
    {
        return stream.length();
    }
}
