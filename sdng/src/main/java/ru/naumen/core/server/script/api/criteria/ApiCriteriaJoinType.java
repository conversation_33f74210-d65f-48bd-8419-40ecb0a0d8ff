package ru.naumen.core.server.script.api.criteria;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Типы Join-ов для {@link ApiCriteria}
 *
 * <AUTHOR>
 * @since 14.04.20
 */
public enum ApiCriteriaJoinType
{
    INNER
            {
                @Override
                public HCriteria addJoin(HCriteria criteria, String path, @Nullable HCriterion joinCriterion)
                {
                    return joinCriterion != null
                            ? criteria.addInnerJoin(path, joinCriterion)
                            : criteria.addInnerJoin(path);
                }
            },

    LEFT
            {
                @Override
                public HCriteria addJoin(HCriteria criteria, String path, @Nullable HCriterion joinCriterion)
                {
                    return joinCriterion != null
                            ? criteria.addLeftJoin(path, joinCriterion)
                            : criteria.addLeftJoin(path);
                }
            },

    RIGHT
            {
                @Override
                public HCriteria addJoin(HCriteria criteria, String path, @Nullable HCriterion joinCriterion)
                {
                    return joinCriterion != null
                            ? criteria.addRightJoin(path, joinCriterion)
                            : criteria.addRightJoin(path);
                }
            };

    public abstract HCriteria addJoin(HCriteria criteria, String path, @Nullable HCriterion joinCriterion);
}
