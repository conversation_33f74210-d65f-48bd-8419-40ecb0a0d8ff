package ru.naumen.core.server.catalog;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.ILocalizedText;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.i18n.LocaleUtils;

import java.util.function.Function;

@Component
public class TitleTransformer implements Function<Object, String>
{
    @Override
    public String apply(@Nullable final Object obj)
    {
        return switch (obj)
        {
            case null -> StringUtilities.EMPTY;
            case ILocalizedText localizedText -> localizedText.getText(LocaleUtils.getCurrentLocale().getLanguage());
            case String stringObject -> stringObject;
            default -> obj.toString(); //NOSONAR Сонар ругался на то, что obj может быть null, так как @Nullable
        };
    }
}
