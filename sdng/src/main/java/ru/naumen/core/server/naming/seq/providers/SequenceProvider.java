package ru.naumen.core.server.naming.seq.providers;

import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.core.server.naming.generators.strategies.NextValueGenerationStrategy;
import ru.naumen.core.server.naming.spi.PeriodicalSequence;

/**
 * Предоставляет необходимые функции для работы с последовательностями.
 * <AUTHOR>
 * @since Dec 15, 2015
 *
 */
public interface SequenceProvider
{
    /**
     * Удаление последовательности.
     *
     * @param sequenceId идентификатор удаляемой последовательности.
     * @param period удаляемый период.
     */
    void deleteSequence(String sequenceId, Long period);

    /**
     * Генерирует следующее значение для указанного ключа (последовательности + периода).
     * @param key ключ, для которого необходимо сгенерировать следующее значение.
     * @param nextValStrategy стратегия генерации следующего значения.
     * @return сгенерированное значение.
     */
    int generateId(Pair<String, Long> key, NextValueGenerationStrategy nextValStrategy);

    /**
     * Получает требуемую последовательность.
     *
     * @param sequenceId идентификатор требуемой последовательности.
     * @return требуемая последовательность/
     */
    PeriodicalSequence getSequence(String sequenceId);

    /**
     * Получает текущее значение указанной последовательности.
     *
     * @param sequenceId идентификатор последовательности, для которой получается текущее значение.
     * @return текущее значение указанной последовательности.
     */
    int getSequenceValue(String sequenceId);

    /**
     * Рестарт указанной последовательности и периода с указанного значения.
     * Кроме прочего сбрасывает список возвращённых значений.
     *
     * @param sequenceId идентификатор перезапускаемой последовательности.
     * @param period перезапускаемый период.
     * @param value новое значение.
     */
    void restartSequence(String sequenceId, Long period, int value);

    /**
     * Возвращает сгенерированное значение в список возвращённых значений для переиспользования.
     * Может потребоваться в случае отката транзакции после генерации номера.
     *
     * @param key идентификатор последовательности и период для которых производится возврат значения.
     * @param value возвращаемое значение.
     */
    void returnValue(Pair<String, Long> key, Integer value);

    /**
     * Устанавливает значение последовательности для заданного периода в заданное значение.
     * В отличие от {@link SequenceProvider#restartSequence(String, Long, int)} создаёт последовательность, 
     * если её не существовало до сих пор.
     *
     * @param sequenceId идентификатор устанавливаемой последовательности.
     * @param period устанавливаемый период.
     * @param value устанавливаемое значение.
     */
    void setSequence(String sequenceId, Long period, int value);

    /**
     * Обновляет список возвращённых значений, добавляя в него новое значение.
     *
     * @param key идентификатор последовательности, период, для которых происходит обновление 
     * возвращённых значений.
     * @param value возвращаемое значение.
     */
    void updateReturned(Pair<String, Long> key, Integer value);
}
