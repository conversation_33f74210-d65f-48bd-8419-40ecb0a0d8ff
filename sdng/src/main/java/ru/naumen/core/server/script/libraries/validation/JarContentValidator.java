package ru.naumen.core.server.script.libraries.validation;

import java.util.List;
import java.util.jar.JarFile;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.script.libraries.validation.jar.JarValidator;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;

/**
 * Валидатор библиотек сохраняющий содержимое библиотеки во временный файл.
 * Временный файл открывается как {@link JarFile} и прогоняется через набор известных {@link JarValidator}
 * <AUTHOR>
 * @since 22.05.2020
 */
@Component
public class JarContentValidator implements LibraryContentValidator
{
    private final List<JarValidator> availableJarValidators;

    public JarContentValidator(List<JarValidator> availableJarValidators)
    {
        this.availableJarValidators = availableJarValidators;
    }

    @Override
    public void validateContent(ScriptLibrary content, JarFile jarFile) throws ValidationException
    {
        availableJarValidators.forEach(v -> v.validateJar(content.getName(), jarFile));
    }
}
