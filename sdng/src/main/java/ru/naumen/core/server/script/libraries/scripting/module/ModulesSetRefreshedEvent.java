package ru.naumen.core.server.script.libraries.scripting.module;

import java.util.Map;
import java.util.Set;

import org.springframework.context.ApplicationEvent;

import ru.naumen.core.server.script.modules.storage.ScriptModule;

/**
 * Событие - об изменении модулей в библиотеках
 */
public class ModulesSetRefreshedEvent extends ApplicationEvent
{
    private final Map<String, Set<ScriptModule>> libraryPerModules;

    /**
     * Create a new ApplicationEvent.
     * @param source the object on which the event initially occurred (never {@code null})
     * @param libraryPerModules мапа "название библиотеки" -> "содержащиеся в ней модули"
     */
    public ModulesSetRefreshedEvent(
            Object source,
            Map<String, Set<ScriptModule>> libraryPerModules)
    {
        super(source);
        this.libraryPerModules = libraryPerModules;
    }

    /**
     * @return ассоциативный массив "название библиотеки" -> "содержащиеся в ней модули"
     */
    public Map<String, Set<ScriptModule>> getLibraryPerModules()
    {
        return libraryPerModules;
    }
}
