package ru.naumen.core.server.mapper;

import java.util.Collection;

/**
 * <AUTHOR>
 * @since Mar 3, 2015
 */
public interface BatchPropertyLoader
{
    /**
     * Возвращает коллекцию для которой необходимо выполнить запрос
     * @param <T> тип для коллекции
     * @return коллекцию типа Т
     */
    <T> Collection<T> getBatch();

    /**
     * Возвращает актуальную коллекцию объектов, если obj находится в ней, иначе коллекцию из одного obj
     * @param <T> тип для коллекции
     * @param obj объект для которого происходит запрос зависимостей
     * @return коллекцию типа Т
     */
    <T> Collection<T> getBatch(Object obj);

    /**
     * Возвращает коллекции зависимостей для которых уже выполнен запрос
     * @param key по которому отобраны зависимости
     * @return коллекции зависимостей
     */
    <V> V getProperty(String key);

    /**
     * Проверяет существует ли коллекция значений для которых необходимо запросить зависимости
     * @param obj объект для которого происходит запрос зависимостей
     * @return true -  если существует коллекция таких значений, false - не существует
     */
    boolean isBatchLoad(Object obj);

    /**
     * Проверяет нужно ли получать все связи
     * @return true -  если нужно, false - не нужно
     */
    boolean isRelationsUnlimited();

    /**
     * Удаляет из стека коллекцию для которой выполнен запрос
     * @param batch коллекция для которой выполнен запрос
     */
    void popBatch(Collection<?> batch);

    /**
     * Запоминает в стеке коллекцию для которой необходимо выполнить запрос
     * @param batch коллекция для которой необходимо выполнить запрос
     */
    void putBatch(Collection<?> batch);

    /**
     * Запоминает в стеке коллекцию для которой необходимо выполнить запрос
     * @param batch коллекция для которой необходимо выполнить запрос
     * @param isRelationsUnlimited не ограничивать/ограничивать получение всех связей
     */
    void putBatch(Collection<?> batch, boolean isRelationsUnlimited);

    /**
     * Запоминает коллекции зависимостей для которых уже выполнен запрос
     * @param key по которому отобраны зависимости
     * @param value полученные коллекции зависимостей
     */
    void setProperty(String key, Object value);
}
