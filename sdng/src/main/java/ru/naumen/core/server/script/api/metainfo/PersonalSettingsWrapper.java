package ru.naumen.core.server.script.api.metainfo;

import ru.naumen.core.shared.interfacesettings.StandartInterfaceSettings;
import ru.naumen.core.server.personalsettings.PersonalSettings;
import ru.naumen.core.shared.utils.ILocaleInfo;

/**
 * Обертка для использования в скриптах над персональными настройками пользователя (PersonalSettings)
 *
 * <AUTHOR>
 * @since 07.06.2014
 */
public class PersonalSettingsWrapper implements IPersonalSettingsWrapper
{
    PersonalSettings ps;

    public PersonalSettingsWrapper(PersonalSettings ps)
    {
        this.ps = ps == null ? new PersonalSettings(StandartInterfaceSettings.DEFAULT_ADMIN_THEME,
                StandartInterfaceSettings.DEFAULT_OPERATOR_THEME, ILocaleInfo.DEFAULT_LANG) : ps;
    }

    @Override
    public String getGwtStackMode()
    {
        return ps.getGwtStackMode();
    }

    @Override
    public String getHomePage()
    {
        return ps.getHomePage();
    }

    @Override
    public String getLocale()
    {
        return ps.getLocale();
    }

    @Override
    public String getThemeAdmin()
    {
        return ps.getThemeAdmin();
    }

    @Override
    public String getThemeOperator()
    {
        return ps.getThemeOperator();
    }

    @Override
    public String getTimeZone()
    {
        return ps.getTimeZoneId();
    }

    @Override
    public String toString()
    {
        return "PersonalSettingsWrapper [getLocale()=" + getLocale() + ", getThemeAdmin()=" + getThemeAdmin()
               + ", getThemeOperator()=" + getThemeOperator() + ", getTimeZone()=" + getTimeZone()
               + ", getGwtStackMode()=" + getGwtStackMode() + "]";
    }
}
