package ru.naumen.core.server.script.api;

import java.util.Arrays;
import java.util.Collection;
import java.util.HashMap;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.StreamSupport;

import org.apache.commons.text.StringEscapeUtils;
import org.jsoup.Jsoup;
import org.jsoup.nodes.Document;
import org.jsoup.nodes.Document.OutputSettings;
import org.jsoup.nodes.Element;
import org.jsoup.safety.Safelist;
import org.springframework.stereotype.Component;

import com.google.common.base.Joiner;
import com.google.common.base.Splitter;

import jakarta.annotation.Nullable;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.EnumUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.shared.utils.HtmlUtils;

/**
 * API для работы со строками из скриптов
 *
 * <AUTHOR>
 *
 * @since *******
 */
@Component("string")
public class StringApi implements IStringApi
{
    private static final Pattern ESCAPED_NEW_LINE_REGEX = Pattern.compile("\\\\n");

    @Override
    public String concat(String separator, CharSequence[] strs)
    {
        return concat(separator, Arrays.asList(strs));
    }

    @Override
    public String concat(String separator, Iterable<CharSequence> strs)
    {
        return Joiner.on(separator).join(notEmpty(strs));
    }

    @Override
    public boolean contains(String str, CharSequence s)
    {
        return null != str && str.contains(s);
    }

    @Override
    public boolean containsAnyFromCollection(final String str, Collection<String> substrCollection)
    {
        if (CollectionUtils.isEmpty(substrCollection) || isEmptyTrim(str))
        {
            return false;
        }
        return CollectionUtils.contains(substrCollection, input -> StringUtilities.contains(str, input));
    }

    @Override
    public boolean containsAnyFromCollectionIc(final String str, Collection<String> substrCollection)
    {
        return !CollectionUtils.isEmpty(substrCollection)
               && !isEmptyTrim(str)
               && CollectionUtils.contains(substrCollection,
                input -> StringUtilities.containsIgnoreCaseSafe(str, input));

    }

    @Override
    public String find(String str, String prefix, String format)
    {
        //@formatter:off
        if (isEmptyTrim(format)
            || isEmptyTrim(str)
            || !EnumUtils.isValidEnum(FindFormats.class, format.toUpperCase()))
        //@formatter:on
        {
            return null;
        }
        if (!isEmptyTrim(prefix) && str.contains(prefix))
        {
            str = str.substring(str.indexOf(prefix) + prefix.length());
        }
        String result = null;
        switch (FindFormats.valueOf(format.toUpperCase()))
        {
            case NUMBER:
                result = findByRegExp(str, "\\d+");
                break;
            case WORD:
                result = findByRegExp(str, "\\b[^\\s](.+?)\\b");
                break;
            case STRING:
                result = str;
                break;
        }
        return result;
    }

    @Override
    public String findByRegExp(String str, String regexp)
    {
        return findByRegExp(str, regexp, null);
    }

    @Override
    public String findByRegExp(String str, String regexp, String flags)
    {
        if (isEmptyTrim(str) || isEmptyTrim(regexp))
        {
            return null;
        }
        regexp = isEmptyTrim(flags) ? regexp : flags + regexp;
        Pattern p = Pattern.compile(regexp);
        Matcher m = p.matcher(str);
        if (m.find())
        {
            return m.group();
        }
        return null;
    }

    @Override
    public String getDomain(String email)
    {
        if (isEmptyTrim(email))
        {
            return null;
        }
        int atPos = email.indexOf('@');
        if (atPos > 0)
        {
            return email.substring(atPos + 1);
        }
        return StringUtilities.EMPTY;
    }

    @Override
    public String getFileExtension(String filename)
    {
        if (isEmptyTrim(filename))
        {
            return null;
        }
        int extensionPos = filename.lastIndexOf('.');
        if (extensionPos > 0)
        {
            return filename.substring(extensionPos + 1);
        }
        return StringUtilities.EMPTY;
    }

    @Override
    public String getFileName(String filename)
    {
        if (isEmptyTrim(filename))
        {
            return null;
        }
        int extensionPos = filename.lastIndexOf('.');
        if (extensionPos < 0)
        {
            return filename;
        }
        return filename.substring(0, extensionPos);
    }

    @Override
    public String getMailbox(String email)
    {
        if (isEmptyTrim(email))
        {
            return null;
        }
        int atPos = email.indexOf('@');
        if (atPos > 0)
        {
            return email.substring(0, atPos);
        }
        return email;
    }

    @Override
    public String getTextBeforeDelimiterAsHtml(String htmlBody, String delimiter)
    {
        if (isEmptyTrim(htmlBody))
        {
            return StringUtilities.EMPTY;
        }
        if (!isEmptyTrim(delimiter) && htmlBody.contains(delimiter))
        {
            htmlBody = htmlBody.substring(0, htmlBody.indexOf(delimiter));
            htmlBody = Jsoup.parse(htmlBody).html();
        }
        return htmlBody;
    }

    @Override
    public String htmlToText(String html)
    {
        if (html == null)
        {
            return html;
        }
        Document document = Jsoup.parse(html).outputSettings(new OutputSettings().prettyPrint(false));
        //заменяем на \n только непустые блоки
        insertLineBreakInFilledBlock(document, "div");
        insertLineBreakInFilledBlock(document, "p");
        document.select("br").append("\\n");
        String text = Jsoup.clean(ESCAPED_NEW_LINE_REGEX.matcher(
                        document.body().html()).replaceAll("\n"), "", Safelist.none(),
                new OutputSettings().prettyPrint(false)).trim();
        //убираем html-символы, как &quot; &nbsp; и т.д.
        return StringEscapeUtils.unescapeHtml4(text);
    }

    @Override
    public String escapeHtml(@Nullable String text)
    {
        if (StringUtilities.isEmpty(text))
        {
            return StringUtilities.EMPTY;
        }
        return HtmlUtils.escape(text);
    }

    @Override
    public boolean isEmpty(CharSequence str)
    {
        return StringUtilities.isEmpty(str);
    }

    @Override
    public boolean isEmpty(String str)
    {
        return StringUtilities.isEmpty(str);
    }

    @Override
    public boolean isEmptyTrim(CharSequence str)
    {
        return StringUtilities.isEmptyTrim(str);
    }

    @Override
    public <T extends CharSequence> Iterable<T> notEmpty(@Nullable Iterable<T> strs)
    {
        if (strs == null)
        {
            return null;
        }
        return CollectionUtils.filterNotEmpty(strs);
    }

    @Override
    public String replace(@Nullable String str, String from, String to)
    {
        if (str == null)
        {
            return null;
        }
        return str.replace(from, to);
    }

    @Override
    public String[] splitByDelimiters(String str, String delimiters)
    {
        return ru.naumen.commons.server.utils.StringUtilities.splitByDelimiters(str, delimiters);
    }

    @Override
    public Map<String, String> toMap(String str, String pairDelimiter, String keyValueDelimiter)
    {
        Map<String, String> map = new HashMap<>();
        if (!isEmptyTrim(str) && !isEmptyTrim(pairDelimiter) && !isEmptyTrim(keyValueDelimiter))
        {
            map = Splitter.on(pairDelimiter).withKeyValueSeparator(keyValueDelimiter).split(str);
        }
        return map;
    }

    @Override
    public Iterable<String> trim(Iterable<String> strs)
    {
        if (null == strs)
        {
            return null;
        }
        return StreamSupport.stream(strs.spliterator(), false).map(this::trim).toList();
    }

    @Override
    public String trim(String str)
    {
        return null == str ? null : str.trim();
    }

    private void insertLineBreakInFilledBlock(Document document, String blockTag)
    {
        for (Element element : document.select(blockTag))
        {
            if (!element.text().isEmpty())
            {
                element.prepend("\\n");
            }
        }
    }
}
