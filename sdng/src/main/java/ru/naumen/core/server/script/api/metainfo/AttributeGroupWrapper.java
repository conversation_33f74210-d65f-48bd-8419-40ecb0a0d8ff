package ru.naumen.core.server.script.api.metainfo;

import java.util.List;

import ru.naumen.metainfo.shared.elements.AttributeGroup;

import com.google.common.base.Function;
import com.google.common.collect.Collections2;
import com.google.common.collect.Lists;

public class AttributeGroupWrapper implements IAttributeGroupWrapper
{
    public static final Function<AttributeGroup, IAttributeGroupWrapper> WRAPPER = new Function<AttributeGroup,
            IAttributeGroupWrapper>()
    {
        @Override
        public AttributeGroupWrapper apply(AttributeGroup input)
        {
            return null == input ? null : new AttributeGroupWrapper(input);
        }
    };

    private final AttributeGroup attributeGroup;

    public AttributeGroupWrapper(AttributeGroup attributeGroup)
    {
        this.attributeGroup = attributeGroup;
    }

    @Override
    public List<String> getAttributeCodes()
    {
        return attributeGroup.getAttributeCodes();
    }

    @Override
    public List<IAttributeWrapper> getAttributes()
    {
        return Lists.newArrayList(Collections2.transform(attributeGroup.getAttributes(), AttributeWrapper.WRAPPER));
    }

    @Override
    public String getCode()
    {
        return attributeGroup.getCode();
    }

    @Override
    public MetaClassWrapper getMetaClass()
    {
        return MetaClassWrapper.WRAPPER.apply(attributeGroup.getMetaClass());
    }

    @Override
    public String getTitle()
    {
        return attributeGroup.getTitle();
    }

    @Override
    public boolean isHardcoded()
    {
        return attributeGroup.isHardcoded();
    }

    @Override
    public String toString()
    {
        return "AttributeGroup '" + this.getTitle() + "' (Metaclass: '" + this.getMetaClass() + "', Code: '"
               + this.getCode() + "')";
    }
}
