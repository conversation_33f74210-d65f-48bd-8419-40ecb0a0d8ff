package ru.naumen.core.server.mapper.impl;

import java.util.Collection;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

import java.util.HashMap;

import ru.naumen.core.shared.collect.ArrayListHashCodeCached;

/**
 * <AUTHOR>
 * @since Mar 3, 2015
 */
class BatchPropertyLoaderContext
{
    private List<?> batch;

    /**
     * Текущая пачка в виде Set для более быстрых проверок на contains
     */
    private Set<?> batchSet;
    private Map<String, Object> properties = new HashMap<>();
    private boolean isRelationsUnlimited = false;

    BatchPropertyLoaderContext()
    {
        this.batch = Collections.emptyList();
        this.batchSet = Collections.emptySet();
    }

    BatchPropertyLoaderContext(Collection<?> batch)
    {
        this(batch, false);
    }

    BatchPropertyLoaderContext(Collection<?> batch, boolean isRelationsUnlimited)
    {
        //кэшируем hashCode, чтобы можно было эффективно складывать batch в HashMapы
        this.batch = new ArrayListHashCodeCached<>(batch);
        this.batchSet = new HashSet<>(batch);
        this.isRelationsUnlimited = isRelationsUnlimited;
    }

    List<?> getBatch()
    {
        return batch;
    }

    Map<String, Object> getProperties()
    {
        return properties;
    }

    boolean isBatchLoad(Object obj)
    {
        return batchSet.contains(obj);
    }

    boolean isRelationsUnlimited()
    {
        return this.isRelationsUnlimited;
    }
}
