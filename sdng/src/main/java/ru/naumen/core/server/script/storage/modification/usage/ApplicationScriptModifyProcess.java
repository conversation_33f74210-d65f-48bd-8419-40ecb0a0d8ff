package ru.naumen.core.server.script.storage.modification.usage;

import java.util.Collection;

import jakarta.annotation.Nullable;

import org.springframework.stereotype.Component;

import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;

/**
 * Контракт поведения при изменении скрипта во встроенном приложении
 * Переопределяет специфичные действия при редактировании.
 * Основная логика редактирования содержится в {@link ScriptModifyProcessBase}
 * <AUTHOR>
 * @since 28.10.16
 */
@Component
public class ApplicationScriptModifyProcess extends ScriptModifyProcessSupport<EmbeddedApplication>
{
    @Override
    protected String getLocation(EmbeddedApplication holder, ScriptModifyContext context)
    {
        return holder.getCode();
    }

    @Override
    protected String getOldScriptCode(@Nullable EmbeddedApplication oldHolder, ScriptModifyContext context)
    {
        return oldHolder == null ? null : oldHolder.getScript();
    }

    @Override
    protected Collection<ClassFqn> getRelatedMetaClasses(EmbeddedApplication holder, ScriptModifyContext context)
    {
        return null;
    }

    @Override
    protected boolean isUsageChangeable()
    {
        return false;
    }

    @Override
    protected void setNewScriptCode(@Nullable String newScriptCode, EmbeddedApplication holder,
            ScriptModifyContext context)
    {
        holder.setScript(newScriptCode);
    }
}
