package ru.naumen.core.server.advlist.filtersettings;

import java.util.Collection;
import java.util.List;

import jakarta.annotation.Nullable;

import ru.naumen.sec.server.admin.log.ScriptAdminLogInfo;
import ru.naumen.core.server.script.storage.modification.usage.ScriptModifyContext;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.HasFilterRestrictionStrategy;
import ru.naumen.metainfo.shared.ui.ObjectListBase;

/**
 * Сервис работы с настройками ограничения фильтрации для атрибутов.
 *
 * <AUTHOR>
 * @since 10.07.2017
 */
public interface FilterRestrictionSettingsService
{
    /**
     * Очищает все точки использования скрипта для настроек ограничения фильтрации в данном контенте
     * @param content контент для которого необходимо сделать очистку
     * @param fqn в котором находится контент
     * @param templateCode код шаблона списка, если настройка происходит в шаблоне, иначе null
     * @param formCode код формы
     */
    void removeAllScriptUsagePoints(HasFilterRestrictionStrategy content, ClassFqn fqn, @Nullable String templateCode,
            @Nullable String formCode);

    /**
     * Очищает все точки использования скрипта для настроек ограничения фильтрации в данном контенте
     * и вложенных в него контентов
     * @param content контент для которого необходимо сделать очистку
     * @param fqn в котором находится контент
     * @param formCode код формы
     */
    void removeAllScriptUsagePointsInHierarchical(Content content, ClassFqn fqn, String formCode);

    /**
     * Очищает точку использования скрипта для настроек ограничения фильтрации в данном контенте
     * @param attributeFqn для которого необходимо сделать очистку
     * @param oldFilterRestrictionStrategy - старая стратегия хозяин
     * @param contentOwner контент для которого необходимо сделать очистку
     * @param fqn в котором находится контент
     * @param templateCode код шаблона списка, если настройка происходит в шаблоне, иначе null
     * @param formCode код формы
     * @return информацию об изменениях скриптов для логирования
     */
    List<ScriptAdminLogInfo> removeUsagePoint(AttributeFqn attributeFqn,
            FilterRestrictionStrategy oldFilterRestrictionStrategy, Content contentOwner, ClassFqn fqn,
            @Nullable String templateCode, String formCode);

    /**
     * Возвращает все атрибуты используемые для настройки ограничений фильтрации для контента
     * @param content контент для которого необходимо вернуть список атрибутов используемых для ограничения фильтрации
     * @return список атрибутов для настройки фильтрации
     */
    List<Attribute> getAttributesForFilterSettings(ObjectListBase content);

    /**
     * Очистить в контентах построенных на данных атрибутах
     * настройки ограничения фильтрации
     * @param fqn метакласса
     * @param removedAttrs атрибуты для которых надо почистить настройки ограничения фильтрации
     * @param attrGroup редактируемая группа атрибутов
     */
    void cleanAttributeUsageInContent(ClassFqn fqn, Collection<String> removedAttrs, String attrGroup);

    /**
     * Добавляет все точки использования скрипта для настроек ограничения фильтрации в данном контенте
     * и вложенных в него контентов
     * @param content контент для которого необходимо сделать добавление
     * @param fqn в котором находится контент
     * @param templateCode код шаблона списка, если настройка происходит в шаблоне, иначе null
     * @param formCode код формы
     */
    void addAllScriptUsagePointsInHierarchical(Content content, ClassFqn fqn, @Nullable String templateCode,
            @Nullable String formCode);

    /**
     * Создаёт контекст редактирования скрипта для настроек ограничения фильтрации
     * @param content контент для которого необходимо сделать редактирование
     * @param fqn в котором находится контент
     * @param attrFqn fqn атрибута
     * @param templateCode код шаблона списка, если настройка происходит в шаблоне, иначе null
     * @param formCode код формы
     * @return ScriptModifyContext - контекст для редактирования скрипта
     */
    ScriptModifyContext createScriptModifyContext(Content content, ClassFqn fqn, AttributeFqn attrFqn,
            @Nullable String templateCode, String formCode);

    /**
     * Проверяет контент по местам использования скриптов для настроек ограничения фильтрации,
     * при необходимости корректирует места использования
     * @param fqn в котором находится контент
     * @param oldContent контент до редактирования
     * @param savedContent сохраняемая часть контента
     * @param templateCode код шаблона списка, если настройка происходит в шаблоне, иначе null
     * @param formCode код формы
     */
    void checkFilterRestrictionSettings(ClassFqn fqn, ObjectListBase oldContent, ObjectListBase savedContent,
            @Nullable String templateCode, @Nullable String formCode);
}
