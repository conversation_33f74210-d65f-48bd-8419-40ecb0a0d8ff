package ru.naumen.core.server.script.spi.aspose;

import java.io.BufferedInputStream;
import java.io.InputStream;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import com.aspose.words.License;

import jakarta.annotation.Nullable;

/**
 * Инициализирует лицензию Aspose для использования библиотек Aspose в скриптах.
 * <AUTHOR>
 * @since 03.12.2019
 */
@Lazy
@Component("asposeLicensingService")
public class AsposeLicensingService
{
    private static final Logger LOG = LoggerFactory.getLogger(AsposeLicensingService.class);
    private License license;

    public AsposeLicensingService()
    {
        try (final InputStream resourceStream = new BufferedInputStream(
                this.getClass().getResourceAsStream("/aspose/Aspose.Words.lic")))
        {
            final License license = new License();
            license.setLicense(resourceStream);
            this.license = license;
        }
        catch (Exception e)
        {
            LOG.error("An error has occurred during Aspose license installation.", e);
        }
    }

    @Nullable
    public License getLicense()
    {
        return license;
    }
}
