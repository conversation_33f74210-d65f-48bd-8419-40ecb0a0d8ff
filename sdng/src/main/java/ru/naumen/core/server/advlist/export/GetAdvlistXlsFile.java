package ru.naumen.core.server.advlist.export;

import java.io.IOException;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.TimeZone;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Row.MissingCellPolicy;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.xssf.streaming.SXSSFSheet;
import org.apache.poi.xssf.streaming.SXSSFWorkbook;
import org.apache.poi.xssf.usermodel.XSSFCellStyle;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.hibernate.CacheMode;
import org.hibernate.SessionFactory;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.BeanDefinition;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.TimeZoneUtils;
import ru.naumen.core.server.advlist.export.DeferredExport.AdvlistExportInterruptedException;
import ru.naumen.core.server.advlist.export.DeferredExport.AdvlistExportProcess;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.common.FormattersImpl;
import ru.naumen.core.server.filters.attribute.AttributeResolverHelper;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.criterion.InCriterion;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.util.ComputableAttrsHelperBean;
import ru.naumen.core.shared.Constants.Event;
import ru.naumen.core.shared.Constants.EventCategory;
import ru.naumen.core.shared.IChild;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.advlist.AdvlistConstants;
import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.core.shared.attr.presentation.AttributePresentationExtensionService;
import ru.naumen.core.shared.utils.CommonUtils.UUIDExtractor;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants;
import ru.naumen.metainfo.shared.Constants.Presentations;
import ru.naumen.metainfo.shared.Constants.StateAttributeType;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;

/**
 * Создает xls-файл, соответствующий, выгружаемому адвлисту
 * <AUTHOR>
 * @since 01.12.2011
 */
@Component
@Scope(BeanDefinition.SCOPE_PROTOTYPE)
public class GetAdvlistXlsFile
{
    private static final Logger LOG = LoggerFactory.getLogger(GetAdvlistXlsFile.class);

    @Inject
    private AccessorHelper accessorHelper;
    @Inject
    private AttributeResolverHelper attributeResolverHelper;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private IXlsCellCreatorsRegistry cellCreatorRegistry;
    @Inject
    @Named("sessionFactory")
    private SessionFactory sessionFactory;
    @Inject
    @Named("eventsSessionFactory")
    private SessionFactory eventsSessionFactory;
    @Inject
    private TimeZoneUtils timeZoneUtils;
    @Inject
    private ComputableAttrsHelperBean computableAttrsHelper;
    @Inject
    private ConfigurationProperties configurationProperties;
    @Inject
    private FormattersImpl formatters;
    @Inject
    private AttributePresentationExtensionService presentationExtensionService;

    private int exportBatchSize;
    private List<String> uuids;
    private Map<String, String> properties;
    private List<String> attrFqns;
    private Map<String, CellStyle> styles;
    private TimeZone timeZone;
    private XSSFCellStyle style;

    private SXSSFWorkbook workbook;

    public GetAdvlistXlsFile()
    {
    }

    /**
     * Создает документ xlsx
     * При обработке каждой пачки проверяем не прервана ли операция
     * @param advlistExportProcess процесс экспорта для проверки прерывания операции
     */
    public void createWorkbook(@Nullable AdvlistExportProcess advlistExportProcess)
    {
        workbook = new SXSSFWorkbook();
        final SXSSFSheet worksheet = workbook.createSheet(AdvlistConstants.FILE_NAME);
        worksheet.setRandomAccessWindowSize(configurationProperties.getSubreportXlsxMaxRows());

        style = formatters.customizeAdvlistExportStyle(workbook);
        int rowNumber = 0;
        Row row = worksheet.createRow(rowNumber++);
        for (int i = 0; i < attrFqns.size(); i++)
        {
            final Cell cell = row.getCell(i, MissingCellPolicy.CREATE_NULL_AS_BLANK);
            cell.setCellValue(getPropertyTitle(attrFqns.get(i)));
            worksheet.setDefaultColumnStyle(i, style);
        }
        LOG.info("Amount of objects to export {}", uuids.size());
        final int batchSize = exportBatchSize;
        final List<List<String>> partition = Lists.partition(uuids, batchSize);
        LOG.info("Amount of partitions to process {} with batch size of {}", partition.size(), batchSize);
        for (int batchIndex = 0; batchIndex < partition.size(); batchIndex++)
        {
            if (advlistExportProcess != null && advlistExportProcess.isInterrupted())
            {
                throw new AdvlistExportInterruptedException();
            }
            rowNumber = processWithLog(worksheet, rowNumber, partition, batchIndex);
        }

        worksheet.setAutobreaks(true);
        for (int i = 0; i < properties.size(); i++)
        {
            worksheet.trackColumnForAutoSizing(i);
            worksheet.autoSizeColumn(i);
        }
    }

    /**
     * Создает документ xlsx
     */
    public void createWorkbook()
    {
        createWorkbook(null);
    }

    private int processWithLog(Sheet worksheet, int rowNumber, List<List<String>> partition, int batchIndex)
    {
        long startBatch = -1;
        if (LOG.isDebugEnabled())
        {
            startBatch = System.currentTimeMillis();
            LOG.debug("Processing batch {}", batchIndex);
        }
        int rowAfterBatch = processWithTransaction(partition.get(batchIndex), worksheet, rowNumber);
        if (LOG.isDebugEnabled() && startBatch != -1)
        {
            LOG.debug("Done({}) Processing batch {}", System.currentTimeMillis() - startBatch, batchIndex);
        }
        return rowAfterBatch;
    }

    private int processWithTransaction(List<String> batch, Sheet worksheet, int rowNumber)
    {
        return TransactionRunner.call(TransactionType.NEW_READ_ONLY, () ->
                processBatch(batch, worksheet, rowNumber));
    }

    public void prepare(int exportBatchSize, List<String> uuids, Map<String, String> properties, List<String> attrFqns,
            @Nullable TimeZone timeZone)
    {
        this.exportBatchSize = exportBatchSize;
        this.uuids = uuids;
        this.properties = properties;
        this.attrFqns = attrFqns;
        this.timeZone = timeZone == null ? TransactionRunner.call(TransactionType.NEW_READ_ONLY,
                timeZoneUtils::getCurrentTimeZone) : timeZone;
        this.styles = new HashMap<>();
    }

    public void write(OutputStream out) throws IOException
    {
        workbook.write(out);
    }

    public void dispose()
    {
        workbook.dispose();
        workbook = null;
    }

    private String getPropertyTitle(String attrFqn)
    {
        AttributeFqn attributeFqn = AttributeFqn.parse(attrFqn);
        return attributeResolverHelper.getAttribute(attributeFqn).getTitle();
    }

    @SuppressWarnings("unchecked")
    private Integer processBatch(List<String> batch, Sheet worksheet, int currentRow)
    {
        String fqn = UuidHelper.toPrefix(batch.getFirst());
        SessionFactory currentSessionFactory = getSessionFactory(fqn);

        // параметры для оптимизации hibernate-ом
        currentSessionFactory.getCurrentSession().setCacheMode(CacheMode.GET);
        currentSessionFactory.getCurrentSession().setDefaultReadOnly(true);

        List<Long> ids = new ArrayList<>(batch.size());
        for (String uuid : batch)
        {
            ids.add(UuidHelper.toId(uuid));
        }

        HCriteria criteria = HHelper.create(metainfoService.getFullEntityName(ClassFqn.parse(fqn)));
        /*
           Используется прямой InCriterion для работы через preparedStatement, который может кешироваться
           Если использовать фабричный метод in то там будет создаваться прямой запрос, который будет парсится и
           анализироваться каждый раз.
         */
        criteria.add(new InCriterion(criteria.getProperty("id"), ids));
        List<IUUIDIdentifiable> objs = criteria.createQuery(currentSessionFactory.getCurrentSession()).list();
        final Map<Long, IUUIDIdentifiable> mapped = new HashMap<>(batch.size());
        for (IUUIDIdentifiable obj : objs)
        {
            final Long id = UUIDExtractor.INSTANCE.andThen(UuidHelper.TO_ID).apply(obj);
            mapped.put(id, obj);
        }
        for (Long id : ids)
        {
            writeObject(worksheet.createRow(currentRow++), mapped.get(id));
        }
        currentSessionFactory.getCurrentSession().clear();
        return currentRow;
    }

    /**
     * Записать объект в заданную строку Xls файла
     * @param row строка, куда записать объект
     * @param obj записываемый объект
     */
    private void writeObject(Row row, IUUIDIdentifiable obj)
    {
        int i = 0;
        MetaClass metaClass = metainfoService.getMetaClass(obj);
        for (String attrFqn : attrFqns)
        {
            Cell cell = row.getCell(i, MissingCellPolicy.CREATE_NULL_AS_BLANK);
            if (attributeResolverHelper.hasAttribute(metaClass, attrFqn))
            {
                String attrCode = AttributeFqn.getCode(attrFqn);
                Attribute attribute = attributeResolverHelper.getAttribute(metaClass, attrCode);
                final Object attributeValue = getAttributeValue(obj, attribute);
                final String presentationCode = properties.get(attrFqn);
                final XlsCellCreator cellCreator = getXlsCellCreator(attrCode, attribute, attributeValue,
                        presentationCode);

                final FormatterContext formatterContext = new FormatterContext(attribute, obj);
                formatterContext.setPresentationCode(presentationCode);
                setSystemClassParentToContext(formatterContext, attribute, obj);
                cellCreator.setCellValue(cell, attributeValue, workbook, formatterContext, timeZone);

                // Заполнение стилей отдельно, workbook имеет ограничение на количество стилей ячеек в 4000
                // см. HSSFWorkbook#createCellStyle()
                if (!styles.containsKey(attrFqn))
                {
                    styles.put(attrFqn, GetAdvlistXlsFile.processCellStyle(cellCreator, workbook, style.getFont()));
                }
                if (styles.get(attrFqn) != null)
                {
                    cell.setCellStyle(styles.get(attrFqn));
                }
            }
            i += 1;
        }
    }

    public static CellStyle processCellStyle(XlsCellCreator cellCreator, SXSSFWorkbook workbook, XSSFFont font)
    {
        CellStyle cellStyle = cellCreator.createCellStyle(workbook);
        if (cellStyle != null)
        {
            cellStyle.setFont(font);
        }
        return cellStyle;
    }

    private void setSystemClassParentToContext(FormatterContext formatterContext, Attribute attribute, Object object)
    {
        if (Constants.SYSTEM_METACLASSES.contains(attribute.getFqn().getClassFqn()) && object instanceof IChild)
        {
            formatterContext.setProperty(Presentations.SYSTEM_METACLASS_WITH_PARENT,
                    metainfoService.getMetaClass(((IChild<?>)object).getParent()));
        }
    }

    private XlsCellCreator getXlsCellCreator(String attrCode, Attribute attribute,
            Object attributeValue, String presentationCode)
    {
        final XlsCellCreator cellCreator;
        final String attrTypeCode = attribute.getType().getCode();
        if (presentationExtensionService.mayHaveMultipleValues(attribute.getType(), attribute)
            && attributeValue instanceof Collection<?>
            && !StateAttributeType.CODE.equals(attrTypeCode))
        {
            cellCreator = cellCreatorRegistry.getDefaultCellCreator();
        }
        else
        {
            cellCreator = cellCreatorRegistry.getCellCreator(attrCode, attrTypeCode, presentationCode);
        }
        return cellCreator;
    }

    private Object getAttributeValue(IUUIDIdentifiable obj, Attribute attribute)
    {
        final Object attributeValue;
        if (Boolean.TRUE.equals(attribute.isComputable()))
        {
            attributeValue = computableAttrsHelper.calculateComputableValue(attribute, obj);
        }
        else
        {
            attributeValue = accessorHelper.getAttributeValueWithoutPermission(obj, attribute);
        }
        return attributeValue;
    }

    private SessionFactory getSessionFactory(String classFqn)
    {
        if (Event.FQN.asString().equals(classFqn) || EventCategory.FQN.asString().equals(classFqn))
        {
            return eventsSessionFactory;
        }
        else
        {
            return sessionFactory;
        }
    }
}
