package ru.naumen.core.server.hquery.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.persistence.criteria.JoinType;

import org.hibernate.query.Query;
import org.hibernate.Session;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;
import com.googlecode.functionalcollections.Block;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hquery.AfterQueryHandler;
import ru.naumen.core.server.hquery.BeforeQueryHandler;
import ru.naumen.core.server.hquery.HBuildVisitable;
import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HOrder;
import ru.naumen.core.server.hquery.HPredicate;
import ru.naumen.core.server.hquery.HQueryException;
import ru.naumen.core.server.hquery.IHasQueryParameters;
import ru.naumen.core.server.hquery.impl.source.BaseSource;
import ru.naumen.core.server.hquery.impl.source.CTESource;
import ru.naumen.core.server.hquery.impl.source.JoinSource;
import ru.naumen.core.server.hquery.impl.source.ObjectSource;
import ru.naumen.core.server.hquery.impl.source.TextSource;
import ru.naumen.core.shared.criteria.DtoCriteria;

/**
 * Класс почти аналогичен hibernate Criteria, но здесь создается HQL запрос
 * Особенности:
 * одинаковые Source с одинаковым алиасом можно добавлять много раз, в запросе при этом будет только один
 * <p/>
 * HCriteria
 * -- FROM Contract contract
 * addSource(Class cls, String alias)
 * -- INNER JOIN contract.ObjectRoles roles
 * addInnerJoin(String path, String alias)
 * -- LEFT OUTER JOIN contract.ObjectRoles roles
 * addLeftJoin(String path, String alias)
 * -- RIGHT OUTER JOIN contract.ObjectRoles roles
 * addRightJoin(String path, String alias)
 * <p/>
 * -- ORDER BY contract.number
 * addOrder(Order order)
 * <p/>
 * -- WHERE
 * add(Criterion criterion)
 * <p/>
 * -- SELECT contract.number
 * setProjection(Projection projection)
 * setResultTransformer(ResultTransformer resultTransformer)
 * <p/>
 * Query createQuery()
 */
@SuppressWarnings("java:S3740")
public class HCriteriaDelegate implements HBuildVisitable, AfterQueryHandler,
        BeforeQueryHandler, IHasQueryParameters
{

    private static final String DEFAULT_ALIAS = "obj";
    private static final Logger LOG = LoggerFactory.getLogger(HCriteriaDelegate.class);

    /**
     * @return критерия для класса cls с дефолтным алиасом {@link HCriteriaDelegate#DEFAULT_ALIAS}
     */
    public static HCriteriaDelegate createHCriteria(Class<?> cls)
    {
        return createHCriteria(cls, DEFAULT_ALIAS);
    }

    public static HCriteriaDelegate createHCriteria(Class<?> cls, String alias)
    {
        return new HCriteriaDelegate().addSource(cls, alias);
    }

    private final List<HColumn> columns = new ArrayList<>();
    private List<HColumn> startColumns;

    private final List<HCriterion> criterions = new ArrayList<>();
    private final List<HGroupColumn> groups = new ArrayList<>();
    private final List<HOrder> orders = new ArrayList<>();
    @Nullable
    private HPredicate predicate;
    /**
     * добавлено ipetrov - возникла необходимость
     */
    private String having = ""; //$NON-NLS-1$
    /**
     * alias -> source
     */
    private final Map<String, BaseSource> sources = new HashMap<>();
    /**
     * MultiMap[String parentAlias, Set[Source source]]
     */
    private final Multimap<String, BaseSource> hierarchy = HashMultimap.create();
    private final NameGenerator cteNameGenerator;
    private HCriterion startCriterion = null;
    private HCriterion connectCriterion = null;
    private TextSource priorSource = null;
    @Nullable
    private Integer maxResults = DtoCriteria.NO_LIMIT;
    private int firstResult = 0;
    private NameGenerator aliasGenerator;
    private NameGenerator paramNameGenerator;
    private boolean visited = false;
    private final List<BeforeQueryHandler> beforeHandlers;

    /**
     * Установлены ли параметры методом {@link #setParameters(Query)}<br>
     * Флаг нужен из-за того, что разные критерии, объединённые через join,
     * имеют один и тот же делегат
     */
    private boolean areParametersSet = false;

    HCriteriaDelegate()
    {
        this(new NameGenerator("a_"), new NameGenerator("p_"),
                new NameGenerator(FlexHelper.HCTE_SQLNAME_PREFIX), new ArrayList<>());
    }

    private HCriteriaDelegate(NameGenerator aliasGenerator, NameGenerator paramNameGenerator,
            NameGenerator cteNameGenerator, List<BeforeQueryHandler> beforeHandlers)
    {
        setAliasGenerator(aliasGenerator);
        setParamNameGenerator(paramNameGenerator);
        this.cteNameGenerator = cteNameGenerator;
        this.beforeHandlers = beforeHandlers;
    }

    public void add(HCriterion criterion)
    {
        criterions.add(criterion);
    }

    public void add(Iterable<HCriterion> criterions)
    {
        for (HCriterion criterion : criterions)
        {
            add(criterion);
        }
    }

    public void addBeforeQueryHandler(BeforeQueryHandler handler)
    {
        beforeHandlers.add(handler);
    }

    public void addColumn(HColumn column)
    {
        if (!columns.contains(column))
        {
            columns.add(column);
        }
    }

    public void addStartColumn(HColumn column)
    {
        if (null == startColumns)
        {
            startColumns = new ArrayList<>();
        }
        if (!startColumns.contains(column))
        {
            startColumns.add(column);
        }
    }

    public void addGroupColumn(HGroupColumn groupColumn)
    {
        if (!groups.contains(groupColumn))
        {
            groups.add(groupColumn);
        }
    }

    public HCriteriaDelegate addOrder(HOrder order)
    {
        orders.add(order);
        return this;
    }

    /**
     * Вызывает ВСЕ afterQuery независимо от наличия в одной из них исключений
     */
    @Override
    public void afterQuery(Session session)
    {
        try
        {
            AfterQueryHelper.afterQuery(session, sources.values()).afterQuery(criterions)
                    .afterQuery(startCriterion, connectCriterion).throwIfHasExceptions();
        }
        finally
        {
            this.visited = false;
        }
    }

    @Override
    public void beforeQuery(Session session)
    {
        beforeHandlers.forEach(handler -> handler.beforeQuery(session));
    }

    /**
     * @param connectCriterion the connectCriterion to set
     */
    public HCriterion changeConnectCriterion(HCriterion connectCriterion)
    {
        HCriterion old = this.connectCriterion;
        this.connectCriterion = connectCriterion;
        return old;
    }

    /**
     * @param startCriterion the startCriterion to set
     */
    public HCriterion changeStartCriterion(HCriterion startCriterion)
    {
        HCriterion old = this.startCriterion;
        this.startCriterion = startCriterion;
        return old;
    }

    public HCriteriaDelegate cloneOver()
    {
        HCriteriaDelegate result = new HCriteriaDelegate(this.aliasGenerator, this.paramNameGenerator,
                this.cteNameGenerator, beforeHandlers);
        result.copyFields(this, false);
        return result;
    }

    public HCriteriaDelegate cloneSeparatedNames()
    {
        HCriteriaDelegate result = new HCriteriaDelegate(new NameGenerator(this.aliasGenerator),
                new NameGenerator(this.paramNameGenerator), new NameGenerator(this.cteNameGenerator),
                new ArrayList<>(beforeHandlers));
        result.copyFields(this, true);
        return result;
    }

    public Query createQuery(Session session)
    {
        return createQuery(session, null);
    }

    public Query createQuery(Session session, @Nullable Class<?> expectedResultType)
    {
        if (LOG.isTraceEnabled())
        {
            LOG.trace(toString());
        }

        String hql = generateHQL(session);
        Query<?> q = session.createQuery(hql, expectedResultType);
        setParameters(q);
        // После построения запроса нужно сбросить флаг, чтобы это не мешало другим запросам,
        // которые используют этот же делегат
        areParametersSet = false;

        if (!Objects.equals(DtoCriteria.NO_LIMIT, maxResults))
        {
            q.setMaxResults(maxResults);
        }
        if (firstResult > 0)
        {
            q.setFirstResult(firstResult);
        }
        return new QueryWrapper(q, this, session);
    }

    public List<HColumn> getColumns()
    {
        return columns;
    }

    @Nullable
    public List<HColumn> getStartColumns()
    {
        return startColumns;
    }

    /**
     * @return the connectCriterion
     */
    public HCriterion getConnectCriterion()
    {
        return connectCriterion;
    }

    List<HCriterion> getCriterions()
    {
        return criterions;
    }

    public int getFirstResult()
    {
        return firstResult;
    }

    public List<HGroupColumn> getGroupColumns()
    {
        return groups;
    }

    public String getHaving()
    {
        return having;
    }

    public Integer getMaxResults()
    {
        return maxResults;
    }

    public String getNextAlias()
    {
        return aliasGenerator.next();
    }

    public List<HOrder> getOrders()
    {
        return orders;
    }

    @Nullable
    public HPredicate getPredicate()
    {
        return predicate;
    }

    /**
     * @return the priorSource
     */
    public TextSource getPriorSource()
    {
        if (null == priorSource)
        {
            priorSource = new TextSource(cteNameGenerator.next(), getNextAlias());
        }
        return priorSource;
    }

    public BaseSource getSource(String alias)
    {
        return sources.get(alias);
    }

    public Map<String, BaseSource> getSources()
    {
        return sources;
    }

    /**
     * @return the startCriterion
     */
    public HCriterion getStartCriterion()
    {
        return startCriterion;
    }

    public boolean hasAlias(String alias)
    {
        return sources.containsKey(alias);
    }

    public boolean isHierarchyCriteria()
    {
        return startCriterion != null || connectCriterion != null;
    }

    public void removeSource(String alias)
    {
        BaseSource source = sources.get(alias);
        sources.remove(alias);
        hierarchy.remove(source.getParentAlias(), source);
    }

    public HCriteriaDelegate setFirstResult(int firstResult)
    {
        this.firstResult = firstResult;
        return this;
    }

    /**
     * добавляем условие в конструкцию having(напр., condition="count(*)>2;"
     * сделано на скорую руку, никак не проверяет условие
     * т.е. конечно же агрег. функция должна быть в селекте (рассчитано на smart программистов):)
     */

    public HCriteriaDelegate setHaving(String condition)
    {
        having = condition;
        return this;
    }

    public HCriteriaDelegate setMaxResults(@Nullable Integer maxResults)
    {
        this.maxResults = maxResults;
        return this;
    }

    @Override
    public void setParameters(Query q)
    {
        if (areParametersSet)
        {
            return;
        }
        areParametersSet = true;

        columns.forEach(column -> column.setParameters(q));
        sources.values().forEach(source -> source.setParameters(q));
        criterions.forEach(criterion -> criterion.setParameters(q));
        if (startCriterion != null)
        {
            startCriterion.setParameters(q);
        }
        if (connectCriterion != null)
        {
            connectCriterion.setParameters(q);
        }
        orders.forEach(order -> order.setParameters(q));
        groups.forEach(groupColumn -> groupColumn.setParameters(q));
    }

    public HCriteriaDelegate setPredicate(HPredicate predicate)
    {
        this.predicate = predicate;
        return this;
    }

    @Override
    public String toString()
    {
        return "HCriteriaDelegate[sources=" + sources + ", predicate=" + predicate
               + ", columns=" + columns + ", criterions=" + criterions +
               ", orders=" + orders + ", groups=" + groups + ']';
    }

    @Override
    public void visit(final HBuilder builder)
    {
        visitSourcesDeep(hierarchy.get(null), input -> input.visit(builder));
        columns.forEach(column -> column.visit(builder));
        criterions.forEach(criterion -> criterion.visit(builder));
        orders.forEach(hOrder -> hOrder.visit(builder));
        groups.forEach(groupColumn -> groupColumn.visit(builder));
        this.visited = true;
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        throw new UnsupportedOperationException();
    }

    public boolean wasVisited()
    {
        return visited;
    }

    HCriteriaDelegate addCTE(HCriteriaDelegate cte, String alias)
    {
        if (this == cte)
        {
            throw new IllegalStateException("Cannot use self as Common Table Expression");
        }
        addSource(new CTESource(cte,
                cte.isHierarchyCriteria() ? cte.getPriorSource().getHQLSource() : cteNameGenerator.next(), alias));
        return this;
    }

    HCriteriaDelegate addJoin(String path, String alias, JoinType joinType, @Nullable HCriterion joinCondition)
    {
        return addJoin(path, alias, joinType, joinCondition, false);
    }

    HCriteriaDelegate addJoin(String path, String alias, JoinType joinType, @Nullable HCriterion joinCondition,
            boolean fetch)
    {
        addSource(new JoinSource(path, joinType, joinCondition, alias, fetch));
        return this;
    }

    void addSource(BaseSource source)
    {
        if (source.getAlias().equals(source.getParentAlias()))
        {
            throw new HQueryException("HCriteria.ErrorStatementNotCorrect");
        }

        BaseSource existsource = sources.get(source.getAlias());
        if (existsource != null)
        {
            if (!existsource.equals(source))
            {
                throw new HQueryException("HCriteria.ErrorSuchAliasIsAlreadyExist");
            }
            return;
        }
        sources.put(source.getAlias(), source);
        hierarchy.put(source.getParentAlias(), source);
    }

    HCriteriaDelegate addSource(Class<?> cls, String alias)
    {
        addSource(new ObjectSource(cls, alias));
        return this;
    }

    HCriteriaDelegate addSource(String path, String alias)
    {
        addSource(new TextSource(path, alias));
        return this;
    }

    HCriteriaDelegate createOver()
    {
        return new HCriteriaDelegate(aliasGenerator, paramNameGenerator, cteNameGenerator, beforeHandlers);
    }

    /**
     * Одинаковое ли пространство имён текущего делегата с указанным в параметрах
     */
    public boolean isSameNamespaceWith(HCriteriaDelegate delegate)
    {
        return aliasGenerator == delegate.aliasGenerator
               && paramNameGenerator == delegate.paramNameGenerator
               && cteNameGenerator == delegate.cteNameGenerator
               && beforeHandlers == delegate.beforeHandlers;
    }

    String generateHQL(Session session)
    {
        String hql = new HBuilder(this, session).build();
        LOG.debug(hql);
        return hql;
    }

    NameGenerator getAliasGenerator()
    {
        return aliasGenerator;
    }

    NameGenerator getParamNameGenerator()
    {
        return paramNameGenerator;
    }

    List<HColumn> replaceColumns(List<HColumn> columns)
    {
        List<HColumn> oldColumns = new ArrayList<>(this.columns);
        this.columns.clear();
        this.columns.addAll(columns);
        return oldColumns;
    }

    void setAliasGenerator(NameGenerator aliasGenerator)
    {
        this.aliasGenerator = aliasGenerator;
    }

    void setParamNameGenerator(NameGenerator paramNameGenerator)
    {
        this.paramNameGenerator = paramNameGenerator;
    }

    private void copyFields(HCriteriaDelegate other, boolean copyCriterions)
    {
        this.predicate = other.predicate;
        this.columns.clear();
        this.columns.addAll(other.columns);
        this.startColumns = null == other.startColumns ? null : new ArrayList<>(other.startColumns);
        this.columns.clear();
        this.columns.addAll(other.columns);
        this.sources.clear();
        this.sources.putAll(other.sources);
        this.hierarchy.clear();
        this.hierarchy.putAll(other.hierarchy);

        this.criterions.clear();
        if (copyCriterions)
        {
            other.criterions.stream().map(HCriterion::createCopy).forEach(this.criterions::add);
        }
        else
        {
            this.criterions.addAll(other.criterions);
        }
        this.startCriterion = other.startCriterion;
        this.connectCriterion = other.connectCriterion;
        this.priorSource = other.priorSource;

        this.groups.clear();
        this.groups.addAll(other.groups);
        this.orders.clear();
        this.orders.addAll(other.orders);
        this.having = other.having;
        this.maxResults = other.maxResults;
        this.firstResult = other.firstResult;
    }

    private void visitSourcesDeep(Iterable<BaseSource> localSources, Block<BaseSource> block)
    {
        for (BaseSource hSource : localSources)
        {
            block.apply(hSource);
            visitSourcesDeep(hierarchy.get(hSource.getAlias()), block);
        }
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        HCriteriaDelegate that = (HCriteriaDelegate)o;
        return firstResult == that.firstResult &&
               Objects.equals(columns, that.columns) &&
               Objects.equals(startColumns, that.startColumns) &&
               Objects.equals(criterions, that.criterions) &&
               Objects.equals(groups, that.groups) &&
               Objects.equals(orders, that.orders) &&
               Objects.equals(predicate, that.predicate) &&
               Objects.equals(having, that.having) &&
               Objects.equals(sources, that.sources) &&
               Objects.equals(hierarchy, that.hierarchy) &&
               Objects.equals(startCriterion, that.startCriterion) &&
               Objects.equals(connectCriterion, that.connectCriterion) &&
               Objects.equals(priorSource, that.priorSource) &&
               Objects.equals(maxResults, that.maxResults);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(columns, startColumns, criterions, groups, orders, predicate, having, sources, hierarchy,
                startCriterion, connectCriterion, priorSource, maxResults, firstResult);
    }
}
