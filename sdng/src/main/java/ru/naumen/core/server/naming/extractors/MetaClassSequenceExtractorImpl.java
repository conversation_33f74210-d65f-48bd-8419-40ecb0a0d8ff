package ru.naumen.core.server.naming.extractors;

import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.metainfo.shared.ClassFqn;

/**
 * Генерирует идентификатор последовательности следующего вида: clusterNode# + metaClass.id + suffix.
 *
 * <AUTHOR>
 * @since 02.05.2012
 */
public class MetaClassSequenceExtractorImpl implements MetaClassSequenceExtractor
{
    private String sequenceIdFormat;

    /**
     * Генерирует идентификатор последовательности следующего вида: clusterNode# + metaClass.id + suffix
     *
     * @param sequenceIdSuffix суффикс последовательности
     */
    public MetaClassSequenceExtractorImpl(String sequenceIdSuffix)
    {
        sequenceIdFormat = "%s" + sequenceIdSuffix;
    }

    @Override
    public String getSequenceId(ClassFqn metaClass)
    {
        return String.format(sequenceIdFormat, metaClass.getId());
    }

    @Override
    public String getSequenceId(Object obj)
    {
        IHasMetaInfo hasMetaInfo = (IHasMetaInfo)obj;
        return getSequenceId(hasMetaInfo.getMetaClass());
    }

}
