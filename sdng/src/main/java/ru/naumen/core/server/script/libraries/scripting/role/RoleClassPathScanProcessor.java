package ru.naumen.core.server.script.libraries.scripting.role;

import static ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessorUtils.createScript;
import static ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessorUtils.extractScriptCode;
import static ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessorUtils.extractTitles;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import io.github.classgraph.AnnotationEnumValue;
import io.github.classgraph.AnnotationInfo;
import io.github.classgraph.AnnotationParameterValueList;
import io.github.classgraph.ClassInfo;
import io.github.classgraph.ClassInfoList;
import io.github.classgraph.ScanResult;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.Pair;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.script.libraries.ScanProcessResult;
import ru.naumen.core.server.script.libraries.registration.role.InternalAutomationRole;
import ru.naumen.core.server.script.libraries.registration.role.ScriptRoleCategory;
import ru.naumen.core.server.script.libraries.scripting.ClassPathScanProcessor;
import ru.naumen.core.server.script.libraries.scripting.DocumentationExtractor;
import ru.naumen.core.server.script.libraries.scripting.ScriptLibraryValidator;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.ui.LocalizedString;

/**
 * Обработчик результата сканирования аннотаций скриптов, определяющих роль, в библиотеках.
 */
@Component
public class RoleClassPathScanProcessor implements ClassPathScanProcessor<SecurityRoleScript>
{
    private static final Logger LOG = LoggerFactory.getLogger(RoleClassPathScanProcessor.class);

    private final ScriptLibraryValidator scriptLibraryValidator;
    private final DocumentationExtractor extractDocumentation;

    @Inject
    public RoleClassPathScanProcessor(ScriptLibraryValidator scriptLibraryValidator,
            DocumentationExtractor extractDocumentation)
    {
        this.scriptLibraryValidator = scriptLibraryValidator;
        this.extractDocumentation = extractDocumentation;
    }

    @Override
    public ScanProcessResult<SecurityRoleScript> process(
            ScanResult scanResult,
            ClassLoader classLoader,
            @Nullable String embeddedApplicationCode)
    {
        // код скрипта -> скрипт как объект
        final Map<String, SecurityRoleScript> scriptCodeToScriptObject = new HashMap<>();
        // имя библиотеки -> коллекция кодов скриптов, в этой библиотеке
        final Map<String, Set<String>> libraryNameToScriptCodesInCurrentLibrary = new HashMap<>();
        final Set<String> visitedScriptCodes = new HashSet<>();

        final ClassInfoList classesWithAnnotation =
                scanResult.getClassesWithAnnotation(InternalAutomationRole.class.getName());

        for (final ClassInfo classInfo : classesWithAnnotation)
        {
            Pair<String, SecurityRoleScript> scriptCodeAndScriptObj = processClass(classLoader, classInfo);
            String scriptCode = scriptCodeAndScriptObj.left;
            SecurityRoleScript securityRoleScript = scriptCodeAndScriptObj.right;
            List<LocalizedString> titles = securityRoleScript.getTitles();
            scriptLibraryValidator.validateScriptCode(scriptCode, visitedScriptCodes, titles);

            final String libraryName = classInfo.getClasspathElementFile().getName(); // имя jar-ника
            scriptCodeToScriptObject.put(scriptCode, securityRoleScript);
            libraryNameToScriptCodesInCurrentLibrary.computeIfAbsent(libraryName, k -> new HashSet<>());
            libraryNameToScriptCodesInCurrentLibrary.get(libraryName).add(scriptCode);
        }
        final Map<String, Set<String>> immutableLibraryNameToScriptCodesInCurrentLibrary =
                libraryNameToScriptCodesInCurrentLibrary.entrySet().stream()
                        .collect(Collectors.toMap(
                                Entry::getKey,
                                entry -> Set.copyOf(entry.getValue())
                        ));
        return new ScanProcessResult<>(Map.copyOf(scriptCodeToScriptObject),
                immutableLibraryNameToScriptCodesInCurrentLibrary);
    }

    private Pair<String, SecurityRoleScript> processClass(ClassLoader classLoader, ClassInfo classInfo)
    {
        final AnnotationInfo roleAnnotationInfo = classInfo.getAnnotationInfo(InternalAutomationRole.class.getName());

        ScriptLibraryValidator.validateSuperclass(classInfo);

        final String annotatedClassName = classInfo.getName();
        LOG.trace("Found an internal automation annotations for the class [{}]", annotatedClassName);

        final AnnotationParameterValueList parameterValues = roleAnnotationInfo.getParameterValues();
        final Object scriptInfoObj = parameterValues.getValue("scriptInfo");
        if (!(scriptInfoObj instanceof AnnotationInfo scriptInfo))
        {
            throw new FxException(
                    "Somehow internal automation role scripInfo for class [" + annotatedClassName
                    + "] isn't AnnotationInfo.");
        }
        final AnnotationParameterValueList scriptParameterValues = scriptInfo.getParameterValues();

        final String scriptCode = extractScriptCode(annotatedClassName, scriptParameterValues);

        final ArrayList<LocalizedString> titles = extractTitles(scriptParameterValues,
                scriptCode);
        scriptLibraryValidator.validateScriptTitle(titles, scriptCode);

        final HashMap<String, String> localeToDoc = extractDocumentation.extractDocumentation(classLoader,
                annotatedClassName, scriptParameterValues, scriptCode, classInfo.getClasspathElementFile().getName());

        final Script script = createScript(classInfo, scriptCode, titles, localeToDoc);
        final AnnotationParameterValueList roleParameterValues = roleAnnotationInfo.getParameterValues();
        final SecurityRoleScript securityRoleScript = getSecurityRoleScript(roleParameterValues, script);
        return Pair.create(scriptCode, securityRoleScript);
    }

    private static SecurityRoleScript getSecurityRoleScript(
            AnnotationParameterValueList roleInfoParameterValues,
            Script script)
    {
        final SecurityRoleScript securityRoleScript = new SecurityRoleScript();
        securityRoleScript.setScript(script);
        securityRoleScript.setTitles(extractTitles(roleInfoParameterValues, script.getCode()));

        final Object categoryEnumValueObject = roleInfoParameterValues.getValue("category");
        if (!(categoryEnumValueObject instanceof AnnotationEnumValue categoryEnumValue))
        {
            throw new FxException(
                    "Somehow internal automation role category for the script [" + script.getCode()
                    + "] isn't Enum value");
        }
        securityRoleScript.setCategory(ScriptRoleCategory.valueOf(categoryEnumValue.getValueName()));

        final Object codeObject = roleInfoParameterValues.getValue("code");
        if (!(codeObject instanceof String))
        {
            throw new FxException(
                    "Somehow internal automation role code for the script [" + script.getCode()
                    + "] isn't string.");
        }
        securityRoleScript.setCode((String)codeObject);

        final Object classFqnObject = roleInfoParameterValues.getValue("classFqn");
        if (classFqnObject instanceof String classFqnObjectString)
        {
            final ClassFqn classFqn = StringUtilities.isEmptyTrim(classFqnObjectString)
                    ? null
                    : ClassFqn.parse(classFqnObjectString);
            securityRoleScript.setClassFqn(classFqn);
        }
        else
        {
            LOG.warn("Somehow internal automation role class FQN for the script [{}] isn't string.", script.getCode());
        }
        boolean isOnlyForGloballyLicensed = roleInfoParameterValues.containsName("isOnlyForGloballyLicensed")
                                            && (boolean)roleInfoParameterValues.getValue("isOnlyForGloballyLicensed");
        securityRoleScript.setOnlyForGloballyLicensed(isOnlyForGloballyLicensed);
        return securityRoleScript;
    }
}
