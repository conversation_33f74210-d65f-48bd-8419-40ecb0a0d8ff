package ru.naumen.core.server.hquery.impl;

import java.util.List;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HColumn;

/**
 * Колонка-функция над несколькими колонками
 *
 * <AUTHOR>
 * @since 19.07.2021
 */
public class HFunctionMultiColumn extends AbstractHColumn
{
    private final String prefix;
    private final String delimiter;
    private final String suffix;

    private final List<? extends HColumn> columns;

    public HFunctionMultiColumn(String functionPrefix, String delimiter, String suffix,
            List<? extends HColumn> columns, @Nullable String alias)
    {
        super(alias);
        this.prefix = functionPrefix;
        this.delimiter = delimiter;
        this.suffix = suffix;
        this.columns = columns;
    }

    @Override
    public void setParameters(Query query)
    {
        columns.forEach(column -> column.setParameters(query));
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        return columns.stream()
                .map(column -> column.getHQL(builder))
                .collect(Collectors.joining(delimiter, prefix, suffix));
    }

    @Override
    public String toString()
    {
        return "HFunctionMultiColumn{" +
               "prefix='" + prefix + '\'' +
               ", delimiter='" + delimiter + '\'' +
               ", suffix='" + suffix + '\'' +
               ", columns=" + columns +
               ", alias=" + getAlias() +
               '}';
    }
}
