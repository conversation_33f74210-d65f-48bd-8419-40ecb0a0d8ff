package ru.naumen.core.server.script.libraries.scripting;

import static java.util.function.Function.identity;
import static java.util.stream.Collectors.toMap;
import static java.util.stream.Collectors.toSet;
import static org.infinispan.context.Flag.IGNORE_RETURN_VALUES;

import java.util.Collection;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Optional;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Stream;

import org.infinispan.Cache;

import groovy.lang.GroovyClassLoader;
import io.github.classgraph.ScanResult;
import jakarta.annotation.Nullable;
import ru.naumen.core.server.script.libraries.ScanProcessResult;
import ru.naumen.core.shared.HasCode;

/**
 * Общая инфраструктура хранения для скриптов из библиотек.
 * @param <T> тип скрипта для хранения;
 * @param <V> тип возвращаемого объекта из {@link ClassPathScanProcessor#process(ScanResult, ClassLoader, String)}
 *
 * @see ru.naumen.core.server.script.libraries.scripting.module.LibraryModulesProcessingConfig
 * @see ru.naumen.core.server.script.libraries.scripting.role.LibraryRoleProcessingConfig
 * @see ru.naumen.core.server.script.libraries.scripting.script.LibraryScriptsProcessingConfig
 */
public class StorageService<T extends HasCode, V extends HasCode>
{
    private final Cache<String, T> scriptCodeToScriptCache;
    private final Cache<String, Set<String>> libraryNameToScriptCodeCache;
    private final ClassPathScanProcessor<V> classpathScanProcessor;
    private final ScannedScriptsSetProcessor<V> scannedScriptsSetProcessor;
    private final Function<V, T> scannedScriptConverter;

    public StorageService(
            Cache<String, T> scriptCodeToScriptCache,
            Cache<String, Set<String>> libraryNameToScriptCodeCache,
            ClassPathScanProcessor<V> classpathScanProcessor,
            ScannedScriptsSetProcessor<V> scannedScriptsSetProcessor,
            Function<V, T> scannedScriptConverter)
    {
        this.scriptCodeToScriptCache = scriptCodeToScriptCache;
        this.libraryNameToScriptCodeCache = libraryNameToScriptCodeCache;
        this.classpathScanProcessor = classpathScanProcessor;
        this.scannedScriptsSetProcessor = scannedScriptsSetProcessor;
        this.scannedScriptConverter = scannedScriptConverter;
    }

    /**
     * Получение скрипта по его коду.
     *
     * @param code код желаемого скрипта;
     */
    public Optional<T> getScript(String code)
    {
        return Optional.ofNullable(scriptCodeToScriptCache.get(code));
    }

    /**
     * Проверить существует ли скрипт с данным кодом
     */
    public boolean isScriptExist(String code)
    {
        return getScript(code).isPresent();
    }

    /**
     * Проверить, что не существует скрипта с данным кодом
     */
    public boolean isScriptNotExist(String code)
    {
        return !isScriptExist(code);
    }

    /**
     * Сохранение скрипта используется, например, при изменении его мест использования.
     *
     * @param script сохраняемый скрипт;
     */
    public void saveScript(T script)
    {
        scriptCodeToScriptCache.put(script.getCode(), script);
    }

    /**
     * Сохранение скриптов используется, например, при изменении их мест использования.
     *
     * @param scripts сохраняемые скрипты;
     */
    public void saveScripts(Collection<T> scripts)
    {
        final Map<String, T> scriptMap = scripts.stream()
                .collect(toMap(T::getCode, identity()));
        scriptCodeToScriptCache.putAll(scriptMap);
    }

    /**
     * @return все скрипты из текущего хранилища;
     */
    public Collection<T> getScripts()
    {
        return scriptCodeToScriptCache
                .values()
                .stream()
                .toList();
    }

    /**
     * Удаляет из кэшей данные относительно изменяющихся библиотек.
     *
     * @param jarsToAdd добавленные библиотеки;
     * @param jarsToDelete библиотеки для удаления;
     */
    public void deleteScriptsFromChangingLibraries(Set<String> jarsToAdd, Set<String> jarsToDelete)
    {
        final Set<String> scriptsToDelete = Stream.concat(jarsToDelete.stream(), jarsToAdd.stream())
                .flatMap(lib ->
                {
                    final Set<String> scripts = libraryNameToScriptCodeCache.get(lib);
                    return scripts == null ? Stream.empty() : scripts.stream();
                })
                .collect(toSet());

        scriptsToDelete.forEach(scriptCodeToScriptCache::remove);
        jarsToDelete.forEach(libraryNameToScriptCodeCache::remove);
    }

    /**
     * Сканирует класслоадер на предмет аннотированных скриптов в библиотеках.
     * Найденные скрипты заносятся в местный кэш.
     *
     * @param classLoader             класслоадер для сканирования;
     * @param refreshCache            признак того, что выполняется перезагрузка кэша, а значит изменять
     *                                метаинформацию нельзя
     * @param embeddedApplicationCode
     */
    public void registerScriptsFromLibraries(
            GroovyClassLoader classLoader,
            ScanResult scanResult,
            boolean refreshCache,
            @Nullable String embeddedApplicationCode)
    {
        final ScanProcessResult<V> scripts = classpathScanProcessor.process(
                scanResult,
                classLoader,
                embeddedApplicationCode);

        // код скрипта -> скрипт (как объект)
        Map<String, T> codeScriptToScriptForStorage = scripts.getCodeScriptToScriptForStorage(scannedScriptConverter);
        for (Entry<String, T> entry : codeScriptToScriptForStorage.entrySet())
        {
            scriptCodeToScriptCache.put(entry.getKey(), entry.getValue());
        }
        // код либы -> коды скриптов
        libraryNameToScriptCodeCache.putAll(scripts.getLibraryNameToCodeScripts());
        if (!refreshCache)
        {
            // имя либы -> скрипты из этой библиотеки. Постобработка загруженных библиотек
            scannedScriptsSetProcessor.process(scripts.getLibraryNameToScriptObjects());
        }
    }

    /**
     * Инвалидировать кэш соответствия по имени библиотеки - набор кодов скриптов из данной библиотеки
     */
    public void invalidateLibraryNameToScriptCodeCache()
    {
        Cache<String, Set<String>> cache = libraryNameToScriptCodeCache.getAdvancedCache()
                .withFlags(IGNORE_RETURN_VALUES);
        cache.keySet().forEach(cache::remove);
    }

    /**
     * Инвалидировать кэш соответствия по коду скрипта - скрипта как объекта
     */
    public void invalidateScriptCodeToScriptCache()
    {
        Cache<String, T> cache = scriptCodeToScriptCache.getAdvancedCache()
                .withFlags(IGNORE_RETURN_VALUES);
        cache.keySet().forEach(cache::remove);
    }
}
