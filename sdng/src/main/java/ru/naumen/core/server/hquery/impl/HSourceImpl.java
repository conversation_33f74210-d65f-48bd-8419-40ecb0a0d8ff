package ru.naumen.core.server.hquery.impl;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.hquery.HSource;

/**
 * <AUTHOR>
 */
public class HSourceImpl extends HColumnImpl implements HSource
{
    public HSourceImpl(String source)
    {
        super(source);
    }

    public HSourceImpl(String source, @Nullable String alias)
    {
        super(source, alias);
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        return getAlias();
    }

    @Override
    public void visit(HBuilder builder)
    {
        builder.select(getHQL(builder));
    }

    @Override
    public String toString()
    {
        return "HSourceImpl{" +
               "source='" + column + '\'' +
               ", overrideHQL='" + overrideHQL + '\'' +
               ", alias=" + getAlias() +
               '}';
    }
}
