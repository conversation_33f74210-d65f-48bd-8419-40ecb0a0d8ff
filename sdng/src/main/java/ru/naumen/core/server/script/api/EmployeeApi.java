package ru.naumen.core.server.script.api;

import static ru.naumen.sec.server.users.CurrentEmployeeContext.isCurrentUserAdminLite;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

import org.hibernate.SessionFactory;
import org.hibernate.query.NativeQuery;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.core.server.bo.employee.EmployeeAuthInfoService;
import ru.naumen.core.server.bo.ou.OU;
import ru.naumen.core.server.bo.ou.OUDao;
import ru.naumen.core.server.catalog.timezone.TimeZoneCatalogItemImpl;
import ru.naumen.core.server.cluster.readonlysettings.ReadOnlyClusterSettingsService;
import ru.naumen.core.server.common.AccessorHelper;
import ru.naumen.core.server.dispatch.PersonalSettingsHelper;
import ru.naumen.core.server.license.LicensingService;
import ru.naumen.core.server.license.conf.AbstractLicenseGroup;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.personalsettings.PersonalSettings;
import ru.naumen.core.server.personalsettings.PersonalSettingsService;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.api.metainfo.IPersonalSettingsWrapper;
import ru.naumen.core.server.script.api.metainfo.PersonalSettingsWrapper;
import ru.naumen.core.server.script.spi.ScriptDtOHelper;
import ru.naumen.core.server.settings.AdminLiteSettingsService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.ISDtObject;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.sec.server.users.UserPrincipal;
import ru.naumen.sec.server.users.employee.EmployeeUser;
import ru.naumen.sec.server.users.employee.EmployeeUserBase;
import ru.naumen.sec.server.users.superuser.SuperUser;
import ru.naumen.sec.server.users.superuser.SuperUserDetailsService;

/**
 * Скриптовое API для работы с сотрудниками
 *
 * <AUTHOR>
 * @since 7.06.2014
 */
@Component("employee")
public class EmployeeApi implements IEmployeeApi
{
    public static final String GWT_STACK_MODE_STRIP = "strip";
    public static final String GWT_STACK_MODE_EMULATED = "emulated";
    private final CommonUtils utils;
    private final ScriptDtOHelper wrapper;
    private final MetainfoService metainfoService;
    private final IPrefixObjectLoaderService prefixObjectLoaderService;
    private final MessageFacade messages;
    private final OUDao ouDao;
    private final AccessorHelper accessor;
    private final ApiUtils apiUtils;
    private final SessionFactory sessionFactory;
    private final PersonalSettingsHelper helper;
    private final EmployeeAuthInfoService employeeAuthInfoService;
    private final LicensingService licensingService;

    private final PersonalSettingsHelper personalSettingsHelper;
    private final PlannedVersionHomePageValidator plannedVersionHomePageValidator;

    private final PersonalSettingsService personalSettingsService;
    private final ReadOnlyClusterSettingsService settingsService;

    private final SuperUserDetailsService superUserDetailsService;
    private final AdminLiteSettingsService adminLiteSettingsService;

    @Inject
    public EmployeeApi(CommonUtils utils, ScriptDtOHelper wrapper,
            MetainfoService metainfoService, IPrefixObjectLoaderService prefixObjectLoaderService,
            MessageFacade messages, OUDao ouDao, AccessorHelper accessor,
            ApiUtils apiUtils, SessionFactory sessionFactory, PersonalSettingsHelper helper,
            EmployeeAuthInfoService employeeAuthInfoService, LicensingService licensingService,
            PersonalSettingsHelper personalSettingsHelper,
            PlannedVersionHomePageValidator plannedVersionHomePageValidator,
            PersonalSettingsService personalSettingsService,
            ReadOnlyClusterSettingsService settingsService,
            SuperUserDetailsService superUserDetailsService,
            AdminLiteSettingsService adminLiteSettingsService)
    {
        this.utils = utils;
        this.wrapper = wrapper;
        this.metainfoService = metainfoService;
        this.prefixObjectLoaderService = prefixObjectLoaderService;
        this.messages = messages;
        this.ouDao = ouDao;
        this.accessor = accessor;
        this.apiUtils = apiUtils;
        this.sessionFactory = sessionFactory;
        this.helper = helper;
        this.employeeAuthInfoService = employeeAuthInfoService;
        this.licensingService = licensingService;
        this.personalSettingsHelper = personalSettingsHelper;
        this.plannedVersionHomePageValidator = plannedVersionHomePageValidator;
        this.personalSettingsService = personalSettingsService;
        this.settingsService = settingsService;
        this.superUserDetailsService = superUserDetailsService;
        this.adminLiteSettingsService = adminLiteSettingsService;
    }

    @Override
    public void disableEmulatedStack(String employeeUuid)
    {
        try
        {
            setStackMode(employeeUuid, GWT_STACK_MODE_STRIP);
        }
        catch (Exception e)
        {
            throw new FxException(messages.getMessage("EmployeeApi.cannotSetStrippedStack"), e);
        }
    }

    @Override
    public void enableEmulatedStack(String employeeUuid)
    {
        try
        {
            setStackMode(employeeUuid, GWT_STACK_MODE_EMULATED);
        }
        catch (Exception e)
        {
            throw new FxException(messages.getMessage("EmployeeApi.cannotSetEmulatedStack"), e);
        }
    }

    /**
     * Метод для поиска активных пользователей
     * @return активные пользователи
     */
    @Override
    public Collection<ISDtObject> getActiveEmployees()
    {
        return wrapper.wrapLazy(getActiveEmployeesUUIDs());
    }

    /**
     * Метод для поиска активных пользователей
     * @return UUID'ы активных юзеров
     */
    @Override
    public Set<String> getActiveEmployeesUUIDs()
    {
        return employeeAuthInfoService.getAllOnlineUsers().stream()
                .filter(EmployeeUserBase.class::isInstance)
                .map(IUUIDIdentifiable::getUUID)
                .collect(Collectors.toSet());
    }

    /**
     * Метод для поиска числа активных пользователей, отстортированных по лицензиям
     * @return мапа, где ключ - код лицензии, значение - число активных пользователей
     */
    @Override
    public Map<String, Integer> getLicensesUsage()
    {
        Map<String, Set<Object>> license2principals = new HashMap<>();
        for (AbstractLicenseGroup licenseGroup : licensingService.getLicenseGroups())
        {
            license2principals.put(licenseGroup.getId(), new HashSet<>());
        }
        //добавим отдельно нелицензированную группу т.к. ее нет в сервисе
        license2principals.put("notLicensed", new HashSet<>());

        for (UserPrincipal principal : employeeAuthInfoService.getAllOnlineUsers())
        {
            if (principal instanceof EmployeeUser employeeUser)
            {
                Set<String> licenses =
                        licensingService.getActualLicenseGroups(employeeUser.getEmployee().getLicense());
                if (licenses.isEmpty())
                {
                    license2principals.get("notLicensed").add(principal);
                }
                for (String licenseCode : licenses)
                {
                    if (license2principals.containsKey(licenseCode))
                    {
                        license2principals.get(licenseCode).add(principal);
                    }
                }
            }
            if (principal instanceof SuperUser && license2principals.containsKey("superuser"))
            {
                license2principals.get("superuser").add(principal);
            }

        }

        Map<String, Integer> licenseGroup2ActiveEmployees = new HashMap<>();
        for (Map.Entry<String, Set<Object>> entry : license2principals.entrySet())
        {
            licenseGroup2ActiveEmployees.put(entry.getKey(), entry.getValue().size());
        }
        return licenseGroup2ActiveEmployees;
    }

    @Override
    @SuppressWarnings("unchecked")
    public Map<String, List<String>> getNotUniqueLoginEmployees()
    {
        NativeQuery<Object[]> query = sessionFactory.getCurrentSession()
                .createNativeQuery(
                        "select lowerlogin, id from tbl_employee inner join ( " +
                        "select lower(login) as lowerlogin from tbl_employee " +
                        "where login is not null group by lowerlogin having count(*) > 1 " +
                        ") as loginkeys on lowerlogin = lower(login)");
        List<Object[]> results = query.list();
        Map<String, List<String>> map = new HashMap<>();
        if (results.isEmpty())
        {
            return map;
        }

        String key = "";
        for (Object[] result : results)
        {
            String login = result[0].toString();
            if (!key.equals(login))
            {
                key = login;
                map.put(key, new ArrayList<>());
            }

            map.get(key).add("employee$" + result[1].toString());
        }
        return map;
    }

    @Override
    public boolean hasWritePermissions(@Nullable String uuid)
    {
        return hasWritePermissions(apiUtils.getObject(uuid));
    }

    @Override
    public boolean hasWritePermissions(@Nullable IUUIDIdentifiable object)
    {
        String currentUserLogin = CurrentEmployeeContext.getCurrentUserLogin(object);
        if (hasSuperUserPermissions(currentUserLogin))
        {
            return true;
        }
        return object != null && settingsService.hasEmployeeWritePermissions(object.getUUID());
    }

    @Override
    public boolean hasSuperUserPermissions(@Nullable String login)
    {
        if (StringUtilities.isEmpty(login))
        {
            return false;
        }
        return superUserDetailsService.hasSuperUser(login)
               || superUserDetailsService.isAssociatedWithSuperUserEmployee(login)
               || (adminLiteSettingsService.isAdminLiteSettingsEnabled() && isCurrentUserAdminLite());
    }

    @Override
    public IPersonalSettingsWrapper getPersonalSettings(String employeeUuid)
    {
        PersonalSettings personalSettings = personalSettingsHelper.getSettings(employeeUuid);
        return new PersonalSettingsWrapper(
                personalSettings != null ? personalSettings : helper.getSettings(employeeUuid));
    }

    @Override
    @Nullable
    public DtObject getTimeZone(String employeeUuid)
    {
        String timeZoneCode = getPersonalSettings(employeeUuid).getTimeZone();
        if (timeZoneCode == null)
        {
            return null;
        }
        Map<String, Object> props = new HashMap<>();
        props.put(Constants.CatalogItem.ITEM_CODE, timeZoneCode);
        IUUIDIdentifiable timeZone = utils.get(Constants.TimezoneCatalogItem.CLASS_ID, props);
        return wrapper.wrap(timeZone);
    }

    /**
     * Не рекомендуется к использованию. Использовать setHomePage
     * @param object объект сотрудника или его uuid.
     * @param homePageInterface модуль домашней страницы (устаревший параметр)
     * @param homePage домашняя страница
     * @deprecated use setHomePage
     */
    @Deprecated(since = "4.16.0")
    @Override
    public void setFullHomePage(Object object, String homePageInterface, String homePage)
    {
        if (!Constants.OPERATOR_ALIAS.equals(homePageInterface))
        {
            return;
        }
        setHomePage(object, homePage);
    }

    @Override
    public void setHomePage(Object object, String homePage)
    {
        plannedVersionHomePageValidator.validateHomePageURL(homePage);
        String employeeUuid = ApiUtils.getUuid(object);
        personalSettingsHelper.setHomePage(employeeUuid, homePage);
    }

    @Override
    public void setLocale(Object object, String locale)
    {
        String employeeUuid = ApiUtils.getUuid(object);
        personalSettingsHelper.setLocale(employeeUuid, locale);
    }

    @Override
    public void setTheme(Object object, String themeCode)
    {
        String employeeUuid = ApiUtils.getUuid(object);
        personalSettingsHelper.setTheme(employeeUuid, themeCode);
    }

    @Override
    public void setTheme(Object object, String themeCode, String module)
    {
        String employeeUuid = ApiUtils.getUuid(object);
        personalSettingsHelper.setTheme(employeeUuid, themeCode, module);
    }

    @Override
    public void setTimeZone(Object object, String timeZoneId)
    {
        String employeeUuid = ApiUtils.getUuid(object);
        personalSettingsHelper.setTimeZone(employeeUuid, timeZoneId);
    }

    @Override
    public String setTimeZoneAccordingToLocation(Object object, String timeZoneAttrCode,
            @Nullable String relatedLocationAttrCode)
    {
        String result = "";
        OU ouObject = obtainObject(object);
        String timeZoneId = obtainTimeZoneId(ouObject, timeZoneAttrCode, relatedLocationAttrCode);
        if (timeZoneId != null)
        {
            Collection<String> employeesUuids = ouDao.getChildEmployees(ouObject);
            if (!CollectionUtils.isEmpty(employeesUuids))
            {
                for (String uuid : employeesUuids)
                {
                    setTimeZone(uuid, timeZoneId);
                }
            }
            result = messages.getMessage("metainfo.SetTimeZoneForRelatedEmployees");
        }
        return result;
    }

    @Override
    public String setTimeZoneEntireOu(Object object, String timeZoneAttrCode)
    {
        return setTimeZoneAccordingToLocation(object, timeZoneAttrCode, null);
    }

    /**
     * Метод для получения идентификатора часового пояса.
     *
     * @param parentObject объект, в котором находится атрибут со значением часового пояса.
     * @param attributeCode код атрибута, в котором хранится информация о часовом поясе.
     * @param relatedLocationAttrCode код атрибута типа "Ссылка на БО", в котором
     * хранится информация об объекте с указанным часовым поясом.
     * @return идентификатора часового пояса.
     */
    private String getTimeZoneFromAttribute(@Nullable IUUIDIdentifiable parentObject, String attributeCode,
            @Nullable String relatedLocationAttrCode)
    {
        IUUIDIdentifiable relLocationObject = parentObject;
        MetaClass metaClass = (relLocationObject != null) ? metainfoService.getMetaClass(relLocationObject) : null;
        if (relatedLocationAttrCode != null && metaClass != null)
        {
            Attribute relatedLocation = metaClass.getAttribute(relatedLocationAttrCode);
            relLocationObject = relatedLocation != null
                    ? accessor.getAttributeValue(parentObject, relatedLocation)
                    : null;
            metaClass = relLocationObject != null ? metainfoService.getMetaClass(relLocationObject) : null;
        }
        if (metaClass == null)
        {
            throw new FxException("MetaClass is not found!");
        }
        Attribute timeZone = metaClass.getAttribute(attributeCode);
        if (timeZone == null)
        {
            throw new FxException("Attribute 'timeZone' not found!");
        }
        TimeZoneCatalogItemImpl timeZoneValues = accessor.getAttributeValue(relLocationObject, timeZone);
        return timeZoneValues.getTimeZone().getID();
    }

    /**
     * Метод для получения объекта отдела по uuid'у.
     *
     * @param object объект или его uuid.
     * @return объект однозначно идентифицируемый своим уникальным идентификатором.
     */
    private OU obtainObject(Object object)
    {
        IUUIDIdentifiable ouObject = null;
        if (object instanceof String str)
        {
            ouObject = prefixObjectLoaderService.getSafe(str);
        }
        if (object instanceof IUUIDIdentifiable iuuidIdentifiable)
        {
            ouObject = iuuidIdentifiable;
        }
        return (OU)ouObject;
    }

    /**
     * Метод для получения идентификатора часового пояса.
     *
     * @param object родительский объект.
     * @param timeZoneAttrCode код атрибута типа "Элемент справочника", в котором хранится
     * информация о часовом поясе.
     * @param relatedLocationAttrCode код атрибута типа "Ссылка на БО", в котором
     * хранится информация об объекте с указанным часовым поясом.
     * @return timeZoneId идентификатор часового пояса.
     */
    @Nullable
    private String obtainTimeZoneId(@Nullable IUUIDIdentifiable object, String timeZoneAttrCode,
            @Nullable String relatedLocationAttrCode)
    {
        if (object == null)
        {
            return null;
        }
        return getTimeZoneFromAttribute(object, timeZoneAttrCode, relatedLocationAttrCode);
    }

    private void setStackMode(String employeeUuid, String gwtStackMode)
    {
        PersonalSettings personalSettings = helper.getSettings(employeeUuid);
        personalSettings.setGwtStackMode(gwtStackMode);
        personalSettingsService.save(personalSettings);
        sessionFactory.getCurrentSession().flush();
    }
}
