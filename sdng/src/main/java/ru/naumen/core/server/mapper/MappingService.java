package ru.naumen.core.server.mapper;

import java.util.ArrayList;
import java.util.Collection;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.criteria.DtoProperties;

/**
 * Сервис преобразования объектов (DTO mapper).
 *
 * <p>
 * Каждое конкретное преобразование производит {@link Mapper}.
 *
 * <AUTHOR>
 *
 */
public interface MappingService
{
    /**
     * Создает экземпляр объекта в который необходимо преобразовать элемент коллекции.
     *
     * @see MappingService#transform(Collection, ObjectCreator)
     *
     * @param <T>
     *            тип объектов экземпляры которых создает {@link ObjectCreator}
     */
    interface ObjectCreator<F, T>
    {
        /**
         * Создает объект для преобразования
         *
         * @param from
         *            преобразовываемый элемент коллекции
         * @return объект для преобразования
         */
        T create(F from);
    }

    /**
     * Производит поиск {@link Mapper}'а, который может преобразовать значение одного объекта в значение другого
     *
     * @param <F>
     * @param <T>
     * @param from
     *            класс исходного объекта
     * @param to
     *            класс объекта назначения
     * @return {@link Mapper} или null если подходящий {@link Mapper} не найден
     */
    <F, T> Mapper<F, T> find(Class<?> from, Class<?> to);

    /**
     * Преобразовывает список объектов одного типа в объекты другого типа.
     *
     * @param <F>
     * @param <T>
     * @param from
     *            исходная коллекция
     * @param creator
     *            создает объекты в которые будут преобразовываться объекты исходной коллекции
     * @return коллекция преобразованных объектов
     */
    <C, F extends C, T> ArrayList<T> transform(Collection<F> from, ObjectCreator<C, T> creator);

    /**
     * Преобразовывает список объектов одного типа в объекты другого типа.
     *
     * @param <F>
     * @param <T>
     * @param from
     *            исходная коллекция
     * @param creator
     *            создает объекты в которые будут преобразовываться объекты исходной коллекции
     * @param properties описание атрибутов, которые необходимо преобразовать
     * @return коллекция преобразованных объектов
     */
    <C, F extends C, T> ArrayList<T> transform(Collection<F> from, ObjectCreator<C, T> creator,
            @Nullable DtoProperties properties);

    /**
     * Преобразовывает список объектов одного типа в объекты другого типа.
     *
     * @param <F>
     * @param <T>
     * @param from
     *            исходная коллекция
     * @param creator
     *            создает объекты в которые будут преобразовываться объекты исходной коллекции
     * @param mappingContext описание атрибутов, которые необходимо преобразовать
     * @return коллекция преобразованных объектов
     */
    <C, F extends C, T> ArrayList<T> transform(Collection<F> from, ObjectCreator<C, T> creator,
            MappingContext mappingContext);

    /**
     * Преобразовывает значение одного типа в значение другого типа.
     *
     * <p>
     * Типичной ситуацией является преобразование объекта состоящего в иерархии наследования. В этом случае не имеет
     * смысла дублировать код преобразования родительского класса, а достаточно из класса потомка вызвать преобразование
     * для родительского класса.
     *
     * <p>
     * Например: псть B расширяет A и нам надо B преобразовать в С. В этом случае мы регистрируем два преобразователя: A
     * в C и B в C, при этом из преобразователя B в C вызываем преобразователь из A в C. Т.е.
     *
     * transform(objB, B.class objC, A.class)
     *
     * @param <F>
     * @param <T>
     * @param from
     * @param fromClass
     * @param to
     * @param toClass
     * @param properties
     * @return
     */
    <FP, F extends FP, TP, T extends TP> T transform(F from, Class<FP> fromClass, T to, Class<TP> toClass,
            DtoProperties properties);

    /**
     * Преобразовывает значение одного типа в значение другого типа.
     *
     * <p>
     * Типичной ситуацией является преобразование объекта состоящего в иерархии наследования. В этом случае не имеет
     * смысла дублировать код преобразования родительского класса, а достаточно из класса потомка вызвать преобразование
     * для родительского класса.
     *
     * <p>
     * Например: псть B расширяет A и нам надо B преобразовать в С. В этом случае мы регистрируем два преобразователя: A
     * в C и B в C, при этом из преобразователя B в C вызываем преобразователь из A в C. Т.е.
     *
     * transform(objB, B.class objC, A.class)
     *
     * @param <F>
     * @param <T>
     * @param from
     * @param fromClass
     * @param to
     * @param toClass
     * @param mappingContext
     * @return
     */
    <FP, F extends FP, TP, T extends TP> T transform(F from, Class<FP> fromClass, T to, Class<TP> toClass,
            MappingContext mappingContext);

    /**
     * Преобразовывает значения одного типа в значения другого типа.
     *
     * <p>
     * Если в сервисе нет {@link Mapper преобразователя} из класса объекта from в класс объекта to, то пытается
     * произвести преобразование для родительского класса объекта from.
     *
     * @param <F>
     *            тип преобразовываемого значение
     * @param <T>
     *            тип значения назначения
     * @param from
     *            преобразовываемый объект
     * @param to
     *            объект назначения
     */
    <T, F> T transform(F from, T to);

    /**
     * Преобразовывает значения одного типа в значения другого типа.
     *
     * <p>
     * Если в сервисе нет {@link Mapper преобразователя} из классо объекта from в класс объекта to, то пытается
     * произвести преобразование для родительского класса объекта from.
     *
     * @param <F>
     *            тип преобразовываемого значение
     * @param <T>
     *            тип значения назначения
     * @param from
     *            преобразовываемый объект
     * @param to
     *            объект назначения
     * @param properties описание атрибутов, которые необходимо преобразовать
     */
    <T, F> T transform(F from, T to, @Nullable DtoProperties properties);

    /**
     * Преобразовывает значения одного типа в значения другого типа.
     *
     * <p>
     * Если в сервисе нет {@link Mapper преобразователя} из классо объекта from в класс объекта to, то пытается
     * произвести преобразование для родительского класса объекта from.
     *
     * @param <F>
     *            тип преобразовываемого значение
     * @param <T>
     *            тип значения назначения
     * @param from
     *            преобразовываемый объект
     * @param to
     *            объект назначения
     * @param mappingContext
     */
    <T, F> T transform(F from, T to, MappingContext mappingContext);
}
