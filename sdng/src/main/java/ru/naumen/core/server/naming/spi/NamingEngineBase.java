package ru.naumen.core.server.naming.spi;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Objects;
import java.util.function.Predicate;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;

import org.springframework.beans.factory.ListableBeanFactory;

import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.naming.INamingEngine;
import ru.naumen.core.server.naming.INamingUnit;
import ru.naumen.core.server.naming.spi.units.NumericUnit;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.IntegerAttributeType;
import ru.naumen.metainfo.shared.NamingInfo;
import ru.naumen.metainfo.shared.elements.Attribute;

import com.google.common.base.Preconditions;

/**
 * <AUTHOR>
 * @since 28.12.2010
 *
 */
public class NamingEngineBase implements INamingEngine
{
    private static final Predicate<INamingUnit> NUMERIC_RULE_PREDICATE = NumericUnit.class::isInstance;

    @Inject
    ListableBeanFactory beanFactory;
    @Inject
    MessageFacade messages;

    private final ClassFqn fqn;
    private final List<INamingUnit> units;

    public NamingEngineBase(ClassFqn fqn)
    {
        this.fqn = fqn;
        this.units = new ArrayList<INamingUnit>();
    }

    public void addUnit(INamingUnit unit)
    {
        units.add(unit);
    }

    @Override
    public boolean checkRule(Attribute attr, String rule)
    {
        return checkRule(attr.getType().getCode(), rule);
    }

    @Override
    public boolean checkRule(String attrTypeCode, String rule)
    {
        if (StringUtilities.isEmptyTrim(rule))
        {
            return false;
        }

        for (INamingUnit u : getUnits())
        {
            rule = u.checkRule(rule);
        }

        if (IntegerAttributeType.CODE.equals(attrTypeCode))
        {
            if (StringUtilities.isEmpty(rule))
            {
                return true;
            }
            try
            {
                Long.parseLong(rule);
            }
            catch (NumberFormatException e)
            {
                return false;
            }
        }
        return true;
    }

    @Override
    public String generate(IHasMetaInfo object, Attribute attr)
    {
        String rule = attr.getGenerationRule();
        for (INamingUnit u : getUnits())
        {
            rule = u.processRule(rule, object);
        }
        return rule;
    }

    public String getHelpString(String code)
    {
        return messages.getMessage(code, new Object[0]);
    }

    @Override
    public NamingInfo getInfo(String typeCode)
    {
        if (IntegerAttributeType.CODE.equals(typeCode))
        {
            return new NamingInfo(getNumerationHelp());
        }
        return new NamingInfo(getNamingHelp());
    }

    @Override
    public ClassFqn getMetaClass()
    {
        return fqn;
    }

    @Override
    public INamingUnit getUnit(final String template)
    {
        INamingUnit unit = getUnits().stream()
                .filter(input -> null != input && Objects.equals(input.getTemplate(), template))
                .findFirst()
                .orElse(null);
        Preconditions.checkNotNull(unit, "Naming unit '%s' is not found", template);
        return unit;
    }

    @PostConstruct
    public void init()
    {
        units.addAll(beanFactory.getBeansOfType(INamingUnit.class).values());
    }

    protected List<String> getNamingHelp()
    {
        return getHelp(getUnits());
    }

    protected List<String> getNumerationHelp()
    {
        return getHelp(getNumUnits());
    }

    protected List<INamingUnit> getNumUnits()
    {
        List<INamingUnit> result = new ArrayList<>();
        CollectionUtils.select(getUnits(), NUMERIC_RULE_PREDICATE, result);
        return result;
    }

    protected Collection<INamingUnit> getUnits()
    {
        return units;
    }

    private List<String> getHelp(Collection<INamingUnit> designs)
    {
        List<String> help = new ArrayList<>();
        for (INamingUnit d : designs)
        {
            help.add(getHelpString(d.getHelpCode()));
        }
        help.add(getHelpString("ru.naumen.sd.bobjects.nomenclature.designs.LengthHelp"));
        return help;
    }
}
