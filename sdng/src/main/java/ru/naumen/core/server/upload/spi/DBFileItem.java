package ru.naumen.core.server.upload.spi;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.sql.Blob;
import java.sql.SQLException;
import java.util.Date;

import org.apache.commons.fileupload2.core.FileItemHeaders;
import org.apache.commons.io.IOUtils;

import jakarta.annotation.Nullable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Lob;
import jakarta.persistence.ManyToOne;
import jakarta.persistence.Table;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.filestorage.spi.storages.FileHash;
import ru.naumen.core.server.hibernate.BlobField;
import ru.naumen.core.server.upload.AbsrtractFileItem;
import ru.naumen.core.server.upload.InputStreamWithLogger;

/**
 * Позволяет хранить загруженные файлы на сервере БД. Это необходимо в кластерной конфигурации когда файл может быть
 * загружен на один нод кластера, а бизнес операция его использующая выполняться на другом ноде. 
 *
 * <AUTHOR>
 *
 */
@Entity
@Table(name = DBFileItem.TABLE_NAME,
        indexes = { @Index(name = "idx_fileItem_filehash", columnList = "hash_id") })
//Не кешируем, потому что есть Blob поле
public class DBFileItem extends AbsrtractFileItem<DBFileItem>
{
    public static final String TABLE_NAME = "tbl_sys_uploaded";

    @Id
    @Column(name = "uuid")
    private String uuid;

    @Lob
    @BlobField
    @Column(name = "file_content")
    private Blob content;

    @Column(name = "content_type")
    private String contentType;

    @Column(name = "name")
    private String name;

    @Column(name = "file_size")
    private long size;

    @Column(name = "field_name")
    private String fieldName;

    @Column(name = "form_field")
    private boolean formField;

    @Column(name = "upload_date")
    private Date date = new Date();

    @Column(name = "storage_id")
    private String storageId;

    @Column(name = "compressed")
    private boolean compressed;

    @ManyToOne(targetEntity = FileHash.class)
    private FileHash hash;

    private transient byte[] bytes;

    @Override
    public DBFileItem delete()
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public byte[] get()
    {
        if (bytes == null)
        {
            try (InputStream inputStream = getInputStream())
            {
                bytes = IOUtils.toByteArray(inputStream);
            }
            catch (IOException e)
            {
                throw new FxException(e);
            }
        }
        return bytes;
    }

    public Blob getContent()
    {
        return content;
    }

    @Override
    public String getContentType()
    {
        return contentType;
    }

    public Date getDate()
    {
        return date;
    }

    @Override
    public String getFieldName()
    {
        return fieldName;
    }

    @Override
    public FileItemHeaders getHeaders()
    {
        return null;
    }

    @Override
    public InputStream getInputStream()
    {
        return getStream();
    }

    @Override
    public String getName()
    {
        return name;
    }

    @Override
    public OutputStream getOutputStream()
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public long getSize()
    {
        return size;
    }

    public InputStream getStream()
    {
        try
        {
            return InputStreamWithLogger.getInputStream(content.getBinaryStream());
        }
        catch (SQLException e)
        {
            throw new FxException(e);
        }
    }

    @Override
    public String getString()
    {
        return new String(get(), StandardCharsets.UTF_8);
    }

    @Override
    public String getString(Charset encoding) throws IOException
    {
        return new String(get(), encoding);
    }

    public String getUuid()
    {
        return uuid;
    }

    @Override
    public boolean isFormField()
    {
        return formField;
    }

    public void setContent(Blob content)
    {
        this.content = content;
    }

    @Override
    public void setContentType(String contentType)
    {
        this.contentType = contentType;
    }

    public void setDate(Date date)
    {
        this.date = date;
    }

    @Override
    public DBFileItem setFieldName(String name)
    {
        fieldName = name;
        return this;
    }

    @Override
    public DBFileItem setFormField(boolean state)
    {
        formField = state;
        return this;
    }

    @Override
    public void setName(String name)
    {
        this.name = name;
    }

    public void setSize(long size)
    {
        this.size = size;
    }

    public void setUuid(String uuid)
    {
        this.uuid = uuid;
    }

    @Nullable
    public String getStorageId()
    {
        return storageId;
    }

    public void setStorageId(@Nullable final String storage)
    {
        this.storageId = storage;
    }

    public boolean isCompressed()
    {
        return compressed;
    }

    public void setCompressed(final boolean isCompressed)
    {
        this.compressed = isCompressed;
    }

    @Nullable
    public FileHash getFileHash()
    {
        return hash;
    }

    public void setFileHash(@Nullable final FileHash fileHash)
    {
        this.hash = fileHash;
    }
}
