package ru.naumen.core.server.script.api.ea;

import static ru.naumen.metainfo.shared.Constants.UI.FORM_TYPES;

import java.util.ArrayDeque;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.Queue;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.sec.server.autorize.AuthorizationService;
import ru.naumen.core.server.embeddedapplication.EmbeddedApplicationService;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.SecConstants;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.server.spi.dispatch.embeddedapplication.EmbeddedApplicationParametersService;
import ru.naumen.metainfo.server.spi.ui.UIContentProcessorService;
import ru.naumen.metainfo.server.spi.ui.UIProcessorContext;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.UI;
import ru.naumen.metainfo.shared.ContentInfo;
import ru.naumen.metainfo.shared.MetainfoUtils;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.ui.Content;
import ru.naumen.metainfo.shared.ui.EmbeddedApplicationContent;
import ru.naumen.metainfo.shared.ui.Tab;

/**
 * API для работы с встроенными приложениями
 *
 * <AUTHOR>
 * @since ********
 * @see IEmbeddedApplicationsApi
 */
@Component("apps")
public class EmbeddedApplicationsApi implements IEmbeddedApplicationsApi
{
    private static final Logger LOG = LoggerFactory.getLogger(EmbeddedApplicationsApi.class);
    private final EmbeddedApplicationService applicationService;
    private final MetainfoService metainfoService;
    private final MetainfoUtils metainfoUtils;
    private final EmbeddedApplicationParametersService parametersService;
    private final ApiUtils apiUtils;
    private final UIContentProcessorService uiProcessorService;
    private final AuthorizationService authorizationService;
    private final IPrefixObjectLoaderService loaderService;

    @Inject
    public EmbeddedApplicationsApi(EmbeddedApplicationService applicationService, MetainfoService metainfoService,
            MetainfoUtils metainfoUtils, EmbeddedApplicationParametersService parametersService, ApiUtils apiUtils,
            UIContentProcessorService uiProcessorService, AuthorizationService authorizationService,
            IPrefixObjectLoaderService loaderService)
    {
        this.applicationService = applicationService;
        this.metainfoService = metainfoService;
        this.metainfoUtils = metainfoUtils;
        this.parametersService = parametersService;
        this.apiUtils = apiUtils;
        this.uiProcessorService = uiProcessorService;
        this.authorizationService = authorizationService;
        this.loaderService = loaderService;
    }

    @Override
    public Collection<IAppContentInfo> listContents(String applicationCode)
    {
        return listContentsInt(applicationCode, null);
    }

    @Override
    public Collection<IAppContentInfo> listContents(final String applicationCode, final @Nullable Object employeeUuid,
            final Object objectUuid)
    {
        Preconditions.checkNotNull(objectUuid, "Object must be not null");
        if (employeeUuid != null)
        {
            return apiUtils.callAsEmployee(employeeUuid, () -> listContentsInt(applicationCode, objectUuid));
        }

        return listContentsInt(applicationCode, objectUuid);
    }

    private Collection<IAppContentInfo> listContentsInt(String applicationCode, @Nullable Object objectUuid)
    {
        EmbeddedApplication embeddedApplication = applicationService.getApplications()
                .stream()
                .filter(app -> app.getCode().equalsIgnoreCase(applicationCode))
                .findFirst()
                .orElse(null);

        if (embeddedApplication == null)
        {
            LOG.warn(String.format("Embedded application with code '%s' not found", applicationCode));
            return Collections.emptyList();
        }
        ClassFqn classFqn;
        if (objectUuid != null)
        {
            String uuid = ApiUtils.getUuid(objectUuid);
            IUUIDIdentifiable object = loaderService.get(uuid);
            classFqn = metainfoService.getClassFqn(object);
        }
        else
        {
            classFqn = null;
        }

        Collection<IAppContentInfo> contentDescriptions = new ArrayList<>();
        Collection<ContentInfo> uiForms;
        if (null == classFqn)
        {
            uiForms = metainfoService.getUiForms();
        }
        else
        {
            uiForms = FORM_TYPES.stream()
                    .map(formCode -> metainfoService.getNullableUiForm(classFqn, formCode))
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());
        }
        for (ContentInfo contentInfo : uiForms)
        {
            if (!FORM_TYPES.contains(contentInfo.getFormId()))
            {
                continue;
            }
            Queue<Content> queue = new ArrayDeque<>();
            queue.add(contentInfo.getContent());
            while (!queue.isEmpty())
            {
                Content content = queue.poll();

                if (!(content instanceof Tab) || null == objectUuid || checkRights(objectUuid, contentInfo, content))
                {
                    queue.addAll(content.getChilds());
                }

                if (!(content instanceof EmbeddedApplicationContent) ||
                    !((EmbeddedApplicationContent)content).getApplication().equals(embeddedApplication.getCode()) ||
                    objectUuid != null && !checkRights(objectUuid, contentInfo, content))
                {
                    continue;
                }
                String tabUuids = getTabs(content).stream()
                        .map(Content::getUuid)
                        .collect(Collectors.joining(StringUtilities.COMMA));

                AppContentInfo contentDescription = new AppContentInfo(
                        contentInfo.getDeclaredMetaclass().toString(),
                        contentInfo.getFormId(),
                        content.getUuid(),
                        metainfoUtils.getLocalizedValue(content.getCaption()),
                        tabUuids.isEmpty() ? null : tabUuids);

                contentDescriptions.add(contentDescription);
            }
        }

        return contentDescriptions;
    }

    private boolean checkRights(Object objectUuid, ContentInfo contentInfo, Content content)
    {
        IHasMetaInfo object = (IHasMetaInfo)Objects.requireNonNull(apiUtils.getObject(objectUuid));
        if (UI.WINDOW_KEY.equals(contentInfo.getFormId()) &&
            !authorizationService.hasPermission(object, SecConstants.ServiceCall.VIEW_OBJECT_CARD))
        {
            return false;
        }

        UIProcessorContext context = new UIProcessorContext(object);
        context.setCheckPermission(true);
        context.setNeedCheckVisibilityConditions(true);
        context.setFormCode(contentInfo.getFormId());

        return uiProcessorService.isVisible(content, context);
    }

    private List<Content> getTabs(Content content)
    {
        List<Content> tabs = new ArrayList<>();
        Content currentParent = content.getParent();
        while (currentParent != null)
        {
            if (currentParent instanceof Tab)
            {
                tabs.add(0, currentParent);
            }
            currentParent = currentParent.getParent();
        }
        return tabs;
    }

    @Override
    public Map<String, Object> contentParameters(String fqn, String formType, String contentUuid)
    {
        return parametersService.getEmbeddedApplicationParametersValuesFromContent(
                ClassFqn.parse(fqn), formType, contentUuid);
    }
}
