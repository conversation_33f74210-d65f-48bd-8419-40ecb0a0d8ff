package ru.naumen.core.server.naming.spi;

import java.sql.Connection;
import java.sql.SQLException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.jdbc.datasource.SingleConnectionDataSource;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;

/**
 * Предоставляет {@link ManualNamedParameterJdbcTemplate} на основе соединения из изолированного {@link DataSource},
 * управляет транзакциями для полученных соединений, а так же самими соединениями.
 *
 * <AUTHOR>
 * @since Dec 29, 2015
 *
 */
@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class SequenceJdbcTemplateFactory
{
    private static final Logger LOG = LoggerFactory.getLogger(SequenceJdbcTemplateFactory.class);
    private final DataSource sequenceDataSource;
    private final ConcurrentMap<ManualNamedParameterJdbcTemplate, Connection> transactions = new ConcurrentHashMap<>();

    /**
     * Предоставляет {@link ManualNamedParameterJdbcTemplate} на основе соединения из изолированного {@link DataSource}.
     *
     * @param sequenceDataSource изолированный {@link DataSource}, из которого будут извлекаться используемые
     *                           соединения.
     */
    @Inject
    public SequenceJdbcTemplateFactory(@Named("sequenceDataSource") DataSource sequenceDataSource)
    {
        this.sequenceDataSource = sequenceDataSource;
    }

    /**
     * @return новый {@link ManualNamedParameterJdbcTemplate}.
     */
    public ManualNamedParameterJdbcTemplate getTemplate()
    {
        try
        {
            // Соединение сохраняется в transactions и закрывается использующим кодом.
            final Connection connection = sequenceDataSource.getConnection(); //NOPMD
            connection.setAutoCommit(false);
            final SingleConnectionDataSource ds = new SingleConnectionDataSource(connection, true);
            final ManualNamedParameterJdbcTemplate template = new ManualNamedParameterJdbcTemplate(ds, this);
            transactions.put(template, connection);
            return template;
        }
        catch (SQLException e)
        {
            throw new FxException(e);
        }
    }

    /**
     * Закрывает соединение для текущего {@link ManualNamedParameterJdbcTemplate}.
     * После этого использование текущего {@link ManualNamedParameterJdbcTemplate} должно быть прекращено.
     * @param template {@link ManualNamedParameterJdbcTemplate} для которого происходит закрытие используемого
     *                                                         соединения.
     */
    void close(ManualNamedParameterJdbcTemplate template)
    {
        final Connection connection = transactions.get(template);//NOPMD Ниже и так явно вызывается close.
        try
        {
            connection.close();
        }
        catch (SQLException e)
        {
            LOG.error("Cannot close JDBC connection.", e);
        }
        finally
        {
            transactions.remove(template);
        }
    }

    /**
     * Совершить коммит транзакции для текущего {@link ManualNamedParameterJdbcTemplate}.
     * @param template {@link ManualNamedParameterJdbcTemplate} для которого необходимо совершить коммит.
     * @throws SQLException
     */
    void commit(final ManualNamedParameterJdbcTemplate template) throws SQLException
    {
        // Соединение сохраняется в transactions и закрывается использующим кодом.
        final Connection connection = transactions.get(template); //NOPMD
        connection.commit();
    }

    /**
     * Совершить откат транзакции для текущего {@link ManualNamedParameterJdbcTemplate}.
     * @param template {@link ManualNamedParameterJdbcTemplate} для которого необходимо совершить откат.
     * @throws SQLException
     */
    void rollback(final ManualNamedParameterJdbcTemplate template) throws SQLException
    {
        // Соединение сохраняется в transactions и закрывается использующим кодом.
        final Connection connection = transactions.get(template); //NOPMD
        connection.rollback();
    }
}
