package ru.naumen.core.server.hquery.impl;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HColumn;

/**
 * Колонка для группировки.<br>
 * Не может содержать псевдоним. Строится поверх обычной колонки.
 *
 * <AUTHOR>
 * @since 22.07.2021
 */
public class HGroupColumn extends AbstractHColumn
{
    private final HColumn baseColumn;

    public HGroupColumn(HColumn baseColumn)
    {
        super(null);
        this.baseColumn = baseColumn;
    }

    @Override
    public void setParameters(Query query)
    {
        baseColumn.setParameters(query);
    }

    @Override
    public void visit(HBuilder builder)
    {
        builder.groupBy(getHQL(builder));
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        return baseColumn.getHQL(builder);
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }

        HGroupColumn that = (HGroupColumn)o;

        return baseColumn.equals(that.baseColumn);
    }

    @Override
    public int hashCode()
    {
        return baseColumn.hashCode();
    }

    @Override
    public String toString()
    {
        return "HGroupColumn{" +
               "baseColumn=" + baseColumn +
               ", alias=" + getAlias() +
               '}';
    }
}
