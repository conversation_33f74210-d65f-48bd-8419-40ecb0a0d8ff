package ru.naumen.core.server.script.api;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.Callable;
import java.util.stream.Collectors;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nonnull;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.CollectionUtils;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.bo.employee.EmployeeDao;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.objectloader.PrefixObjectLoaderException;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.server.script.api.accesskeys.AccessKey;
import ru.naumen.core.server.script.api.accesskeys.AccessKeyDao;
import ru.naumen.core.server.script.api.accesskeys.AccessKeyWrapper;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.sec.server.autorize.events.LogoutAllUsersEvent;
import ru.naumen.sec.server.autorize.events.LogoutUserEvent;
import ru.naumen.sec.server.jwt.mobile.storage.JwtToken;
import ru.naumen.sec.server.jwt.mobile.storage.JwtTokenStorageService;
import ru.naumen.sec.server.session.LogoutReason;
import ru.naumen.sec.server.users.CurrentEmployeeContext;
import ru.naumen.sec.server.users.employee.EmployeeUserBase;
import ru.naumen.sec.server.users.employee.EmployeeUserDetailsService;
import ru.naumen.sec.server.users.superuser.SuperUser;
import ru.naumen.sec.server.users.superuser.SuperUserDao;
import ru.naumen.sec.server.users.superuser.SuperUserDetailsService;

/**
 * АПИ для работы с авторизацией
 *
 * <AUTHOR>
 *
 * @since 4.0.1.0
 */
@Component("auth")
public class AuthenticationApi implements IAuthenticationApi
{
    private static final Logger LOG = LoggerFactory.getLogger(AuthenticationApi.class);
    private static final String[] USER_UUID_PREFIXES = new String[] {
            Constants.Employee.CLASS_ID + UuidHelper.DELIMITER,
            Constants.SuperUser.CLASS_ID + UuidHelper.DELIMITER
    };
    private static final String AUTHENTICATION_API_USER_NOT_FOUND = "AuthenticationApi.userNotFound";
    private static final String AUTHENTICATION_API_USER_NOT_FOUND_BY_UUID = "AuthenticationApi.userNotFoundByUUID";

    private final AccessKeyDao accessKeyDao;
    private final SuperUserDetailsService superUserDetailsService;
    private final EmployeeUserDetailsService employeeUserDetailsService;
    private final MessageFacade messages;
    private final IPrefixObjectLoaderService objectLoaderService;
    private final EmployeeDao<Employee> employeeDao;
    private final ApiUtils apiUtils;
    private final JwtTokenStorageService jwtTokenStorageService;
    private final MetainfoService metainfoService;
    private final ApplicationEventPublisher eventPublisher;
    private final SuperUserDao superUserDao;

    @Inject
    public AuthenticationApi(
            final AccessKeyDao accessKeyDao,
            final SuperUserDetailsService superUserDetailsService,
            final EmployeeUserDetailsService employeeUserDetailsService,
            final MessageFacade messages,
            final ApplicationEventPublisher eventPublisher,
            final IPrefixObjectLoaderService objectLoaderService,
            final EmployeeDao<Employee> employeeDao,
            final ApiUtils apiUtils,
            final JwtTokenStorageService jwtTokenStorageService,
            final MetainfoService metainfoService,
            final SuperUserDao superUserDao)
    {
        this.accessKeyDao = accessKeyDao;
        this.superUserDetailsService = superUserDetailsService;
        this.employeeUserDetailsService = employeeUserDetailsService;
        this.messages = messages;
        this.eventPublisher = eventPublisher;
        this.objectLoaderService = objectLoaderService;
        this.employeeDao = employeeDao;
        this.apiUtils = apiUtils;
        this.jwtTokenStorageService = jwtTokenStorageService;
        this.metainfoService = metainfoService;
        this.superUserDao = superUserDao;
    }

    @Override
    public <T> T callAs(Object employee, Callable<T> callable)
    {
        return apiUtils.callAsEmployee(employee, callable);
    }

    @Override
    public void activateAccessKey(String uuid)
    {
        setAccessKeyActiveState(uuid, true);
    }

    @Override
    public void deactivateAccessKey(String uuid)
    {
        setAccessKeyActiveState(uuid, false);
    }

    @Override
    public Collection<String> findAccessKeysByEmployeeLogin(String employeeLogin)
    {
        checkNotNull(employeeLogin);
        checkAccessKeyPermission(employeeLogin);
        checkUserDetails(employeeLogin);
        final List<AccessKey> accessKeys = new ArrayList<>(accessKeyDao.getByLogin(employeeLogin));
        return CollectionUtils.isEmpty(accessKeys) ? new ArrayList<>()
                : accessKeys.stream().map(AccessKey::getUuid).toList();
    }

    @Override
    public Collection<String> findAccessKeysByEmployeeUUID(String employeeUuid)
    {
        checkNotNull(employeeUuid);
        checkAccessKeyPermission(employeeUuid);
        checkUserDetailsByUuid(employeeUuid);
        final List<AccessKey> accessKeys = new ArrayList<>();
        if (Boolean.TRUE.equals(UuidHelper.isValid(employeeUuid)))
        {
            accessKeys.addAll(accessKeyDao.getByUUID(employeeUuid));
        }
        return CollectionUtils.isEmpty(accessKeys) ? new ArrayList<>()
                : accessKeys.stream().map(AccessKey::getUuid).toList();
    }

    @Override
    public AccessKeyWrapper getAccessKey(@Nonnull String username)
    {
        return new AccessKeyWrapper(accessKeyDao.save(createAccessKey(username)));
    }

    @Override
    public AccessKeyWrapper getAccessKey(@Nonnull String username, int years) throws FxException
    {
        AccessKey accessKey = createAccessKey(username);
        accessKey.setDeadlineYears(years);
        return new AccessKeyWrapper(accessKeyDao.save(accessKey));
    }

    @Override
    public AccessKeyWrapper getAccessKeyByUUID(String uuid)
    {
        Employee employee = objectLoaderService.getSafe(uuid);
        if (employee == null)
        {
            throw new FxException("No such employee!");
        }
        AccessKey accessKey = createAccessKeyByUUID(uuid);
        return new AccessKeyWrapper(accessKeyDao.save(accessKey));
    }

    @Override
    public AccessKeyWrapper getAccessKeyByUUID(@Nonnull String uuid, int years) throws FxException
    {
        AccessKey accessKey = createAccessKeyByUUID(uuid);
        accessKey.setDeadlineYears(years);
        return new AccessKeyWrapper(accessKeyDao.save(accessKey));
    }

    @Override
    public Map<String, Object> getAccessKeyInfo(String uuid)
    {
        checkAccessKeyPermission(StringUtilities.EMPTY);
        return accessKeyDao.getAccessKeyInfo(uuid);
    }

    @Override
    public Collection<Object> getAllAccessKeysInfo(int firstResult, int maxResults)
    {
        checkAccessKeyPermission(StringUtilities.EMPTY);
        String vendor = superUserDao.getVendor().getLogin();
        if (hasAccessKeyPermission(vendor))
        {
            return accessKeyDao.getAllAccessKeysInfo(null, null, firstResult, maxResults);
        }
        else
        {
            return accessKeyDao.getAllAccessKeysInfoExceptForSpecified(vendor, firstResult, maxResults);
        }
    }

    @Override
    public Collection<Object> getEmployeeAccessKeysInfoByLogin(String employeeLogin)
    {
        checkNotNull(employeeLogin);
        checkAccessKeyPermission(employeeLogin);
        checkUserDetails(employeeLogin);
        return accessKeyDao.getAllAccessKeysInfo(null, employeeLogin);
    }

    @Override
    public Collection<Object> getEmployeeAccessKeysInfoByLogin(String employeeLogin, int firstResult, int maxResults)
    {
        checkNotNull(employeeLogin);
        checkAccessKeyPermission(employeeLogin);
        checkUserDetails(employeeLogin);
        return accessKeyDao.getAllAccessKeysInfo(null, employeeLogin, firstResult, maxResults);
    }

    @Override
    public Collection<Object> getEmployeeAccessKeysInfoByUUID(String employeeUuid)
    {
        checkNotNull(employeeUuid);
        checkAccessKeyPermission(employeeUuid);
        checkUserDetailsByUuid(employeeUuid);
        return accessKeyDao.getAllAccessKeysInfo(employeeUuid, null);
    }

    @Override
    public Collection<Object> getEmployeeAccessKeysInfoByUUID(String employeeUuid, int firstResult, int maxResults)
    {
        checkNotNull(employeeUuid);
        checkAccessKeyPermission(employeeUuid);
        checkUserDetailsByUuid(employeeUuid);
        return accessKeyDao.getAllAccessKeysInfo(employeeUuid, null, firstResult, maxResults);
    }

    @Override
    public void logoutAllUsers()
    {
        checkAccessKeyPermission(StringUtilities.EMPTY);
        eventPublisher.publishEvent(new LogoutAllUsersEvent());
    }

    @Override
    public void logoutUser(String loginOrUUID)
    {
        checkAccessKeyPermission(loginOrUUID);
        Objects.requireNonNull(loginOrUUID, "User '" + loginOrUUID + "' not found.");
        String userUUID = StringUtils.containsAny(loginOrUUID, USER_UUID_PREFIXES)
                ? loginOrUUID
                : getUserUuid(loginOrUUID);
        eventPublisher.publishEvent(new LogoutUserEvent(userUUID, LogoutReason.API_LOGOUT));
    }

    @Override
    public List<String> removeAccessKeys(String login)
    {
        checkAccessKeyPermission(login);
        checkUserDetails(login);
        if (superUserDetailsService.hasSuperUser(login))
        {
            return removeAccessKeysForSuperUser(superUserDetailsService.loadUserByUsername(login)).stream()
                    .map(AccessKey::getUuid).toList();
        }
        Employee employee = employeeDao.getByLogin(login);
        if (employee == null)
        {
            throw new FxException(messages.getMessage(AUTHENTICATION_API_USER_NOT_FOUND, login), true);
        }
        return removeAccessKeysForEmployee(employee).stream().map(AccessKey::getUuid).toList();
    }

    @Override
    public List<String> removeAccessKeysByUUID(String uuid)
    {
        final Employee employee = objectLoaderService.getSafe(uuid);
        if (employee == null)
        {
            throw new FxException(messages.getMessage(AUTHENTICATION_API_USER_NOT_FOUND_BY_UUID, uuid), true);
        }
        checkAccessKeyPermission(employee.getLogin());
        return removeAccessKeysForEmployee(employee).stream().map(AccessKey::getUuid).toList();
    }

    /**
     * Проверяет наличие прав на выполнение действий с ключами доступа. Такими правами обладают только
     * суперпользователи. Получить ключ доступа от суперпользователя naumen может только сам naumen.
     * @param loginOrUuid имя или UUID пользователя, операции с ключом доступа которого предполагается выполнить
     */
    private void checkAccessKeyPermission(String loginOrUuid)
    {
        if (!hasAccessKeyPermission(loginOrUuid))
        {
            throw new FxException(messages.getMessage("AuthorizationService.accessDenied"), true);
        }
    }

    private void checkNotNull(String username)
    {
        if (StringUtilities.isEmpty(username))
        {
            throw new FxException(messages.getMessage("AuthenticationApi.getAccessKeyForEmptyLogin"), true);
        }
    }

    private void checkUserDetails(String username) throws FxException
    {
        try
        {
            if (superUserDetailsService.hasSuperUser(username))
            {
                superUserDetailsService.loadUserByUsername(username);
            }
            else
            {
                employeeUserDetailsService.loadUserByUsername(username);
            }
        }
        catch (final UsernameNotFoundException ex)
        {
            throw new FxException(messages.getMessage(AUTHENTICATION_API_USER_NOT_FOUND, username), true, ex);
        }
    }

    private void checkUserDetailsByUuid(String uuid) throws FxException
    {
        try
        {
            if (superUserDetailsService.loadUserByUuid(uuid) == null)
            {
                employeeUserDetailsService.loadByUUID(uuid);
            }
        }
        catch (final UsernameNotFoundException | PrefixObjectLoaderException ex)
        {
            throw new FxException(messages.getMessage(AUTHENTICATION_API_USER_NOT_FOUND_BY_UUID, uuid), true, ex);
        }
    }

    private AccessKey createAccessKey(String username)
    {
        checkNotNull(username);
        checkAccessKeyPermission(username);
        checkUserDetails(username);
        AccessKey accessKey = new AccessKey();
        accessKey.setUsername(username);
        accessKey.setEmployeeUuid(getUserUuid(username));
        return accessKey;
    }

    private AccessKey createAccessKeyByUUID(String uuid)
    {
        try
        {
            Employee employee = objectLoaderService.get(uuid);
            checkAccessKeyPermission(employee.getLogin());
            AccessKey accessKey = new AccessKey();
            accessKey.setEmployeeUuid(uuid);
            accessKey.setUsername(employee.getLogin());
            return accessKey;
        }
        catch (PrefixObjectLoaderException ex)
        {
            throw new FxException(messages.getMessage(AUTHENTICATION_API_USER_NOT_FOUND_BY_UUID, uuid), true, ex);
        }
    }

    /**
     * Метод выполняет поиск сотрудника по accessKey.
     * @param accessKey {@link AccessKey} ключ авторизации
     * @return строка - уникальный идентификатор сотрудника
     */
    private String findUserUuidByAccessKey(final AccessKey accessKey)
    {
        return accessKeyDao.getUserPrincipal(accessKey).getUUID();
    }

    /**
     * Метод возвращает уникальный идентификатор сотрудника по его логину.
     * @param username логин сотрудника
     * @return строка - уникальный идентификатор сотрудника
     */
    private String getUserUuid(String username)
    {
        try
        {
            if (superUserDetailsService.hasSuperUser(username))
            {
                return superUserDetailsService.loadUserByUsername(username).getUUID();
            }
            else
            {
                return employeeUserDetailsService.loadUserByUsername(username).getUUID();
            }
        }
        catch (final UsernameNotFoundException ex)
        {
            throw new FxException(messages.getMessage(AUTHENTICATION_API_USER_NOT_FOUND, username), true, ex);
        }
    }

    private boolean hasAccessKeyPermission(String loginOrUuid)
    {
        Authentication authentication = CurrentEmployeeContext.getCurrentUserAuth();
        //FIXME: RESEARCH-14, ответственный за задачу Игорь Щепочкин
        return
                // Пользователь авторизован И
                null != authentication && authentication.isAuthenticated()
                // Super может все
                && (CurrentEmployeeContext.isCurrentUserSuper()
                    // Admin не может запрашивать access key Super пользователей, в остальных случая можно
                    || CurrentEmployeeContext.isCurrentUserAdmin()
                       && !superUserDetailsService.isSuperUserVendor(loginOrUuid)
                    // Пользователь может получить ключ для самого себя
                    || isCurrentUser(authentication, loginOrUuid));
    }

    private static boolean isCurrentUser(Authentication authentication, String loginOrUuid)
    {
        return authentication.getPrincipal() instanceof EmployeeUserBase employee
               && (loginOrUuid.equals(employee.getUUID()) || loginOrUuid.equals(employee.getUsername()));
    }

    /**
     * Удалить все AccessKey сотрудника, и пометить все открытые AccessKey сессии как просроченные
     * @param employee - сотрудник
     * @return список удаленных AccessKey
     */
    private List<AccessKey> removeAccessKeysForEmployee(Employee employee)
    {
        List<AccessKey> accessKeys = accessKeyDao.getByEmployee(employee);
        eventPublisher.publishEvent(new LogoutUserEvent(employee.getUUID(),
                accessKeys.stream().map(AccessKey::getUuid).collect(Collectors.toSet()), LogoutReason.API_LOGOUT));
        accessKeys.forEach(accessKeyDao::delete);
        return accessKeys;
    }

    /**
     * Удалить все AccessKey суперпользователя, и пометить все открытые AccessKey сессии как просроченные
     * @param userDetails - информация о суперпользователе
     * @return список удаленных AccessKey
     */
    private List<AccessKey> removeAccessKeysForSuperUser(SuperUser userDetails)
    {
        List<AccessKey> accessKeys = accessKeyDao.getByLoginOrUUID(userDetails.getUUID(), userDetails.getLogin());
        eventPublisher.publishEvent(new LogoutUserEvent(userDetails.getUUID(),
                accessKeys.stream().map(AccessKey::getUuid).collect(Collectors.toSet()), LogoutReason.API_LOGOUT));
        accessKeys.forEach(accessKeyDao::delete);
        return accessKeys;
    }

    /**
     * Установить статус активности ключа авторизации по его uuid.
     * В том случае, если ключ авторизации деактивируется, то все
     * связанные с ним открытые сессии помечаются как просроченные.
     *
     * @param uuid уникальный идентификатор ключа доступа
     * @param isActive устанавливаемый статус ключа доступа
     */
    private void setAccessKeyActiveState(@Nonnull String uuid, boolean isActive)
    {
        AccessKey accessKey = accessKeyDao.get(uuid);
        if (accessKey == null)
        {
            throw new FxException(messages.getMessage("AccessKeyDaoImpl.notFound", uuid), true);
        }
        accessKeyDao.update(accessKey.setActive(isActive));
        if (!isActive)
        {
            String userUuid = findUserUuidByAccessKey(accessKey);
            if (StringUtilities.isNotEmpty(userUuid))
            {
                eventPublisher.publishEvent(new LogoutUserEvent(userUuid, LogoutReason.API_LOGOUT));
            }
        }
    }

    @Override
    public void revokeAllJwtTokens()
    {
        checkAccessKeyPermission(StringUtilities.EMPTY);
        jwtTokenStorageService.revokeTokens();
    }

    @Override
    public void revokeJwtTokensForEmployee(final Object employee)
    {
        final String employeeUuid = getEmployeeUuid(employee);
        checkAccessKeyPermission(employeeUuid);
        jwtTokenStorageService.revokeTokensForEmployee(employeeUuid);
    }

    private String getEmployeeUuid(final Object employee)
    {
        if (employee instanceof IUUIDIdentifiable
            && Constants.Employee.FQN.isSameClass(metainfoService.getClassFqn(employee)))
        {
            return ApiUtils.getUuid(employee);
        }
        if (!(employee instanceof final String employeeIdentifier))
        {
            throw new IllegalArgumentException("Wrong argument type - " + employee.getClass().getName());
        }

        return (!StringUtils.containsAny(employeeIdentifier, USER_UUID_PREFIXES))
                ? getUserUuid(employeeIdentifier)
                : employeeIdentifier;
    }

    @Override
    public void revokeJwtToken(String token)
    {
        checkAccessKeyPermission(StringUtilities.EMPTY);
        JwtToken jwtToken = jwtTokenStorageService.getToken(token);
        if (jwtToken == null)
        {
            LOG.trace("Unknown token - {}", token);
            return;
        }

        jwtTokenStorageService.revokeTokenWithLinked(jwtToken);
    }
}
