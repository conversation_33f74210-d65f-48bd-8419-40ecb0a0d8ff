package ru.naumen.core.server.script.api;

import java.util.Objects;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Provider;

import org.springframework.stereotype.Component;

import ru.naumen.core.server.push.ScriptDataPushMobileSender;
import ru.naumen.core.server.script.api.push.ILocationApi;

/**
 * Релизация {@link ILocationApi}
 *
 * <AUTHOR>
 * @since 14.06.19
 */
@Component("location")
public class LocationApi implements ILocationApi
{
    /* Время, в течении которого push-уведомление актуально */
    private static final int DEFAULT_TIME_TO_LIVE = 300;

    private final Provider<ScriptDataPushMobileSender> pushMobileSender;

    @Inject
    public LocationApi(Provider<ScriptDataPushMobileSender> pushMobileSender)
    {
        this.pushMobileSender = pushMobileSender;
    }

    @Override
    public boolean getMobileLocation(String recipient)
    {
        return getMobileLocation(recipient, null);
    }

    @Override
    public boolean getMobileLocation(String recipient, @Nullable Integer timeToLive)
    {
        return pushMobileSender.get()
                .getLocation(recipient, Objects.requireNonNullElse(timeToLive, DEFAULT_TIME_TO_LIVE));
    }
}
