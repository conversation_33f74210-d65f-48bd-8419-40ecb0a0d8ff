package ru.naumen.core.server.advlist.export;

import java.lang.annotation.Documented;
import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

import org.springframework.stereotype.Component;

/**
 * Аннотация для автообнаружения {@link XlsCellCreator} при помощи Spring-а
 *
 * <AUTHOR>
 *
 */
@Component
@Target(ElementType.TYPE)
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface CellCreatorComponent
{
    /**
     * @return коды представлений для которых может использоваться форматер
     */
    String[] codes();
}
