package ru.naumen.core.server.script.spi.dispatch;

import static ru.naumen.metainfo.shared.Constants.SINGLE_UUIDIDENTIFIABLE_TYPES;

import org.springframework.stereotype.Component;

import jakarta.inject.Inject;
import net.customware.gwt.dispatch.server.ExecutionContext;
import net.customware.gwt.dispatch.server.TransactionalReadActionHandler;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.metainfo.server.spi.MetainfoServiceBean;
import ru.naumen.metainfo.shared.dispatch2.CheckIfSingleObjectAttrChainAction;

/**
 * Обработчик {@link CheckIfSingleObjectAttrChainAction}
 *
 * <AUTHOR>
 * @since 12.09.18
 */
@Component
public class CheckIfSingleObjectAttrChainActionHandler extends
        TransactionalReadActionHandler<CheckIfSingleObjectAttrChainAction, SimpleResult<Boolean>>
{
    private final MetainfoServiceBean metainfoService;

    @Inject
    public CheckIfSingleObjectAttrChainActionHandler(MetainfoServiceBean metainfoService)
    {
        this.metainfoService = metainfoService;
    }

    @Override
    public SimpleResult<Boolean> executeInTransaction(
            CheckIfSingleObjectAttrChainAction action, ExecutionContext context)
            throws DispatchException
    {
        return new SimpleResult<>(action.getAttrChain().stream()
                .allMatch(attrFqn ->
                        SINGLE_UUIDIDENTIFIABLE_TYPES.contains(
                                metainfoService.getAttribute(attrFqn).getType().getCode())));
    }
}
