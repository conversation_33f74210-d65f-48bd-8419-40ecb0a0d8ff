package ru.naumen.core.server.upload;

import java.io.IOException;
import java.io.InputStream;

import org.apache.commons.io.input.ProxyInputStream;
import org.apache.commons.lang3.exception.ExceptionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Если включено логирование на уровне DEBUG, то создаёт прокси над переданным InputStream'ом
 * и пишет в лог сообщение, если этот поток не был закрыт.
 *
 * <AUTHOR>
 *
 */
public final class InputStreamWithLogger extends ProxyInputStream
{
    private static final Logger LOG = LoggerFactory.getLogger(InputStreamWithLogger.class);

    public static InputStream getInputStream(InputStream original)
    {
        if (isDebugEnabled())
        {
            return new InputStreamWithLogger(original);
        }
        return original;
    }

    private static boolean isDebugEnabled()
    {
        return LOG.isDebugEnabled();
    }

    private boolean closed = false;
    private long created = -1;
    private String stack = null;

    private InputStreamWithLogger(InputStream in)
    {
        super(in);
        created = System.currentTimeMillis();
        stack = ExceptionUtils.getStackTrace(new Throwable());
    }

    @Override
    public void close() throws IOException
    {
        super.close();
        closed = true;
    }

    @Override
    protected void finalize() throws Throwable
    {
        if (!closed)
        {
            int index = UnclosedInputStreamsCounter.get().countUnclosedStream(stack);

            LOG.debug("Unclosed stream #" + index + ". Input was created: " + created
                      + ", and doesnt closed. Finalized at: " + System.currentTimeMillis());
            LOG.debug("Stack trace by creating: " + stack);
            LOG.debug("----- end of stream ------");

        }
        super.finalize();
    }
}
