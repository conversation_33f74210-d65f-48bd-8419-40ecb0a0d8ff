package ru.naumen.core.server.script.crawler;

import java.util.Collection;

import jakarta.annotation.Nullable;

import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;

import ru.naumen.core.server.advlist.filtersettings.FilterRestrictionSettingsServiceImpl;
import ru.naumen.core.server.script.storage.ScriptUsageUtils;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.script.Script;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;
import ru.naumen.metainfo.shared.ui.Content;

/**
 * Собирает сведения о скриптах из элементов информации о скриптах из
 * метаинформации для кеша скриптохранилища.
 */
@Component
@Scope(value = ConfigurableBeanFactory.SCOPE_PROTOTYPE)
public class ScriptUsageCacheVisitor extends ScriptsVisitorSupport
{
    private final MetainfoService metainfoService;
    private final Multimap<String, ScriptUsagePoint> cacheEntries = HashMultimap.create();

    public ScriptUsageCacheVisitor(MetainfoService metainfoService)
    {
        this.metainfoService = metainfoService;
    }

    public Multimap<String, ScriptUsagePoint> getCacheEntries()
    {
        return cacheEntries;
    }

    @Override
    public void visitAttributeComputableOnFormScript(@Nullable Script script, AttributeFqn attributeFqn)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getAttributeComputableOnFormScriptUsage(attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitAttributeComputationScript(@Nullable Script script, AttributeFqn attributeFqn)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getAttributeComputationScriptUsage(attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitSmiaModelPostprocessScript(@Nullable Script script, String uuid)
    {
        if (script == null)
        {
            return;
        }
        final ScriptUsagePoint smiaModelPostprocessUsagePoint = ScriptUsageUtils.getSmiaModelPostprocessUsagePoint(
                uuid);
        fillCacheEntry(smiaModelPostprocessUsagePoint, script);
    }

    @Override
    public void visitSmiaModelPreprocessScript(@Nullable Script script, String uuid)
    {
        if (script == null)
        {
            return;
        }
        final ScriptUsagePoint smiaModelPreprocessUsagePoint = ScriptUsageUtils.getSmiaModelPreprocessUsagePoint(uuid);
        fillCacheEntry(smiaModelPreprocessUsagePoint, script);

    }

    @Override
    public void visitAttributeDateTimeRestrictionScript(@Nullable Script script, AttributeFqn attributeFqn)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getAttributeDateTimeRestrictionScriptUsage(attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitAttributeDefaultValueScript(@Nullable Script script, AttributeFqn attributeFqn)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getAttributeDefaultValueScriptUsage(attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitAttributeFiltrationScript(@Nullable Script script, AttributeFqn attributeFqn)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getAttributeFiltrationScriptUsage(attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitCtiScript(@Nullable Script script)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getCtiScriptUsage();
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitDataPreparationScript(@Nullable Script script, String code)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getLearningProcessDataPreparationScriptUsage(code);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitEmbeddedApplicationScript(@Nullable Script script, String code)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getApplicationScriptUsage(code);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitEscalationConditionScript(@Nullable Script script, String code,
            @Nullable Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getEscalationConditionScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitEscalationNotificationCustomizationScript(@Nullable Script script, String code,
            Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getEscalationNotificationCustomizationScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitEscalationScript(@Nullable Script script, String code, Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getEscalationScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitEventActionConditionScript(@Nullable Script script, String code, Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getEventActionConditionScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitEventActionNotificationCustomizationScript(@Nullable Script script, String code,
            Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getEventActionNotificationCustomizationScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitEventActionWsmessageCustomizationScript(@Nullable Script script, String code,
            Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getEventActionWsmessageCustomizationScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitEventActionScript(@Nullable Script script, String code, Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getEventActionScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitEventActionIntegrationScript(@Nullable Script script, String code, Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        final ScriptUsagePoint usagePoint = ScriptUsageUtils.getEventActionIntegrationScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitFilterRestrictionScript(@Nullable Script script, AttributeFqn attributeFqn, ClassFqn classFqn,
            Content content, @Nullable String templateCode, @Nullable String formCode)
    {
        if (script == null)
        {
            return;
        }
        //для проверки наличия атрибута в метаинформации при загрузке
        metainfoService.getAttribute(attributeFqn);
        String key = FilterRestrictionSettingsServiceImpl.getKey(content, attributeFqn, templateCode, formCode);
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getFilterRestrictionScriptUsage(key, classFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitFormParameterComputableOnFormScript(@Nullable Script script, AttributeFqn attributeFqn)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getFormParameterComputableOnFormScriptUsage(attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitFormParameterComputeAnyCatalogElementsScriptUsage(@Nullable Script script,
            AttributeFqn attributeFqn)
    {
        if (null == script)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getFormParameterComputeAnyCatalogElementsScriptUsage(
                attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitFormParameterDateTimeRestrictionScript(@Nullable Script script, AttributeFqn attributeFqn)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getFormParameterDateTimeRestrictionScriptUsage(attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitFormParameterDefaultValueScript(@Nullable Script script, AttributeFqn attributeFqn)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getFormParameterDefaultValueScriptUsage(attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitFormParameterFiltrationScript(@Nullable Script script, AttributeFqn attributeFqn)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getFormParameterFiltrationScriptUsage(attributeFqn);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitGatheringDataScript(@Nullable Script script, String code)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getLearningProcessGatheringDataScriptUsage(code);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitLearningAndValidationScript(@Nullable Script script, String code)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getLearningProcessLearningAndValidationScriptUsage(code);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitMailProcessorRuleScript(@Nullable Script script, String code)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getMailProcessorRuleScriptUsage(code);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitMonitoringScript(@Nullable Script script)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getMonitoringScriptUsage();
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitPermissionScript(@Nullable Script script, String location)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getPermissionScriptUsage(location);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitReportTemplateScript(@Nullable Script script, String code)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getReportTemplateScriptUsage(code);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitRoleAccessKeyScript(@Nullable Script script, String code, @Nullable Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getRoleAccessKeyScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitRoleOwnersKeyScript(@Nullable Script script, String code, @Nullable Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getRoleOwnersKeyScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitRoleScriptedListFilterScript(@Nullable Script script, String code,
            @Nullable Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getRoleScriptedListFilterScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitRoleScriptedFastLinkRightsScript(@Nullable Script script, String code,
            @Nullable Collection<ClassFqn> fqns)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getRoleScriptedFastLinkRightsScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitSaveDataScript(@Nullable Script script, String code)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getLearningProcessSaveDataScriptUsage(code);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitSchedulerTaskScript(@Nullable Script script, String code)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getSchedulerTaskScriptUsage(code);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitSCParamAgreementScript(@Nullable Script agreementScript)
    {
        if (agreementScript == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getSCParamAgreementScriptUsage();
        fillCacheEntry(usagePoint, agreementScript);
    }

    @Override
    public void visitSCParamCasesScript(@Nullable Script casesScript)
    {
        if (casesScript == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getSCParamCasesScriptUsage();
        fillCacheEntry(usagePoint, casesScript);
    }

    @Override
    public void visitSCParamServicesScript(@Nullable Script serviceScript)
    {
        if (serviceScript == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getSCParamServicesScriptUsage();
        fillCacheEntry(usagePoint, serviceScript);
    }

    @Override
    public void visitTimerPauseConditionScript(@Nullable Script pauseCondition, String code, Collection<ClassFqn> fqns)
    {
        if (pauseCondition == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getTimerConditionScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, pauseCondition);
    }

    @Override
    public void visitTimerResumeCondition(@Nullable Script resumeCondition, String code, Collection<ClassFqn> fqns)
    {
        if (resumeCondition == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getTimerConditionScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, resumeCondition);
    }

    @Override
    public void visitTimerStartConditionScript(@Nullable Script startCondition, String code, Collection<ClassFqn> fqns)
    {
        if (startCondition == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getTimerConditionScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, startCondition);
    }

    @Override
    public void visitTimerStopConditionScript(@Nullable Script stopCondition, String code, Collection<ClassFqn> fqns)
    {
        if (stopCondition == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getTimerConditionScriptUsage(code, fqns);
        fillCacheEntry(usagePoint, stopCondition);
    }

    @Override
    public void visitWorkflowPostActionScript(@Nullable Script script, String location)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getWorkflowActionScriptUsage(location);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitWorkflowPostConditionScript(@Nullable Script script, String location)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getWorkflowConditionScriptUsage(location);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitWorkflowPreActionScript(@Nullable Script script, String location)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getWorkflowActionScriptUsage(location);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitWorkflowPreConditionScript(@Nullable Script script, String location)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint = ScriptUsageUtils.getWorkflowConditionScriptUsage(location);
        fillCacheEntry(usagePoint, script);
    }

    @Override
    public void visitVoiceProcessingScript(@Nullable Script script, String code,
            ClassFqn fqnOfClass)
    {
        if (script == null)
        {
            return;
        }
        ScriptUsagePoint usagePoint =
                ScriptUsageUtils.getVoiceProcessingScriptUsage(code, fqnOfClass);
        fillCacheEntry(usagePoint, script);
    }

    /**
     * Заполнение записи кэша информации о скриптах.
     *
     * @param usagePoint место использования;
     * @param script скрипт;
     */
    private void fillCacheEntry(ScriptUsagePoint usagePoint, Script script)
    {
        cacheEntries.put(script.getCode(), usagePoint);
    }
}