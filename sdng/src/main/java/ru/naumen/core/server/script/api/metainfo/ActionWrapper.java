package ru.naumen.core.server.script.api.metainfo;

import ru.naumen.metainfo.shared.elements.wf.Action;

import com.google.common.base.Function;

public class ActionWrapper implements IActionWrapper
{
    public static final Function<Action, IActionWrapper> WRAPPER = new Function<Action, IActionWrapper>()
    {
        @Override
        public ActionWrapper apply(Action input)
        {
            return null == input ? null : new ActionWrapper(input);
        }
    };

    private final Action action;

    public ActionWrapper(Action action)
    {
        this.action = action;
    }

    @Override
    public String getCode()
    {
        return action.getCode();
    }

    @Override
    public IMetaClassWrapper getDeclaredMetaClass()
    {
        return MetaClassWrapper.FQN_WRAPPER.apply(action.getDeclaredMetaClass());
    }

    @Override
    public String getTitle()
    {
        return action.getTitle();
    }

    @Override
    public boolean isPre()
    {
        return Boolean.TRUE.equals(action.isPre());
    }

    @Override
    public String toString()
    {
        return "Action '" + this.getTitle() + "' (Code: '" + this.getCode() + "')";
    }
}
