package ru.naumen.core.server.script.api.accesskeys;

import java.util.Date;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.GeneratedValue;
import jakarta.persistence.Id;
import jakarta.persistence.Index;
import jakarta.persistence.Table;

import org.apache.commons.lang3.time.DateUtils;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.UuidGenerator;

@Entity
@Table(name = "tbl_sys_access_key",
        indexes = { @Index(name = "idx_accesskey_deadline", columnList = "deadline") })
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
public class AccessKey implements IAccessKey
{
    @Id
    @Column(name = "uuid", nullable = false)
    @GeneratedValue(generator = "uuid")
    @UuidGenerator
    private String uuid;

    @Column(name = "deadline")
    private Date deadline = new Date(System.currentTimeMillis()
                                     + ru.naumen.core.shared.utils.DateUtils.MILLISECONDS_IN_A_WEEK);

    @Column(name = "creation_date")
    private Date creationDate = new Date();

    @Column(name = "last_usage_date")
    private Date lastUsageDate;

    @Column(name = "type_")
    private AccessKeyType type = AccessKeyType.REUSABLE;

    @Column(name = "username")
    private String username;

    @Column(name = "employee_uuid")
    private String employeeUuid;

    @Column(name = "description")
    private String description;

    @Column(name = "active")
    private Boolean active = Boolean.TRUE;

    public Date getCreationDate()
    {
        return creationDate;
    }

    public Date getDeadline()
    {
        return deadline;
    }

    public String getDescription()
    {
        return description;
    }

    public String getEmployeeUuid()
    {
        return employeeUuid;
    }

    public Date getLastUsageDate()
    {
        return lastUsageDate;
    }

    public AccessKeyType getType()
    {
        return type;
    }

    public String getUsername()
    {
        return username;
    }

    @Override
    public String getUuid()
    {
        return uuid;
    }

    public boolean isActive()
    {
        return active;
    }

    public AccessKey setActive(boolean active)
    {
        this.active = active;
        return this;
    }

    public AccessKey setDeadline(Date deadline)
    {
        this.deadline = deadline;
        return this;
    }

    public AccessKey setDeadlineDays(int days)
    {
        this.deadline = DateUtils.addDays(new Date(), days);
        return this;
    }

    public AccessKey setDeadlineHours(int hours)
    {
        this.deadline = DateUtils.addHours(new Date(), hours);
        return this;
    }

    public AccessKey setDeadlineMinutes(int minutes)
    {
        this.deadline = DateUtils.addMinutes(new Date(), minutes);
        return this;
    }

    public AccessKey setDeadlineYears(int years)
    {
        this.deadline = DateUtils.addYears(new Date(), years);
        return this;
    }

    public AccessKey setDescription(String description)
    {
        this.description = description;
        return this;
    }

    public AccessKey setDisposable()
    {
        return setType(AccessKeyType.DISPOSABLE);
    }

    public void setEmployeeUuid(String employeeUuid)
    {
        this.employeeUuid = employeeUuid;
    }

    public AccessKey setLastUsageDate(Date lastUsageDate)
    {
        this.lastUsageDate = lastUsageDate;
        return this;
    }

    public AccessKey setReusable()
    {
        return setType(AccessKeyType.REUSABLE);
    }

    public AccessKey setType(AccessKeyType type)
    {
        this.type = type;
        return this;
    }

    public AccessKey setUsername(String username)
    {
        this.username = username;
        return this;
    }

    public void setUuid(String uuid)
    {
        this.uuid = uuid;
    }

    @Override
    public String toString()
    {
        return "AccessKey [deadline=" + deadline + ", type=" + type + ", username=" + username + "]";
    }
}
