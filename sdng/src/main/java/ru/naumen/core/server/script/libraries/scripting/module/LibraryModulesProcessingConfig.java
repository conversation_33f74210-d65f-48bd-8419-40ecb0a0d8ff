package ru.naumen.core.server.script.libraries.scripting.module;

import java.util.Set;

import org.infinispan.Cache;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.inject.Named;
import ru.naumen.core.server.InfinispanCacheManager;
import ru.naumen.core.server.script.libraries.scripting.StorageService;
import ru.naumen.core.server.script.modules.storage.ScriptModule;

/**
 * Конфигурация бинов для работы с библиотечными модулями.
 */
@Configuration
public class LibraryModulesProcessingConfig
{
    private static final String MODULE_CODE_TO_MODULE_CACHE_NAME = "moduleCodeToModuleCache";
    private static final String LIBRARY_NAME_TO_MODULE_CODE_CACHE_NAME = "libraryNameToModuleCodeCache";

    /**
     * @return кэш для хранения соответствия кода модуля и его содержимого;
     */
    @Bean(MODULE_CODE_TO_MODULE_CACHE_NAME)
    public Cache<String, ScriptModule> scriptCodeToScriptCache(InfinispanCacheManager cacheManager)
    {
        return cacheManager.getManager().getCache(MODULE_CODE_TO_MODULE_CACHE_NAME);
    }

    /**
     * @return кэш для хранения соответствия названия библиотеки и кодов модулей, содержащихся в ней;
     */
    @Bean(LIBRARY_NAME_TO_MODULE_CODE_CACHE_NAME)
    public Cache<String, Set<String>> libraryNameToScriptCodeCache(InfinispanCacheManager cacheManager)
    {
        return cacheManager.getManager().getCache(LIBRARY_NAME_TO_MODULE_CODE_CACHE_NAME);
    }

    /**
     *
     * @param codeToScriptCache кэш для хранения соответствия кода модуля и его содержимого;
     * @param libraryToScriptCodeCache кэш для хранения соответствия названия библиотеки и кодов модулей,
     *                                 содержащихся в ней;
     * @param classpathScanProcessor обработчик результата сканирования аннотаций модулей в библиотеках;
     * @param eventPublisher публикатор событий используется для событий {@link ModulesSetRefreshedEvent};
     * @return сервис хранения модулей из библиотек;
     */
    @Bean("libraryModuleStorageService")
    StorageService<ScriptModule, ScriptModule> libraryScriptStorageService(
            @Named(MODULE_CODE_TO_MODULE_CACHE_NAME) Cache<String, ScriptModule> codeToScriptCache,
            @Named(LIBRARY_NAME_TO_MODULE_CODE_CACHE_NAME) Cache<String, Set<String>> libraryToScriptCodeCache,
            ModuleClassPathScanProcessor classpathScanProcessor,
            ApplicationEventPublisher eventPublisher)
    {
        return new StorageService<>(
                codeToScriptCache,
                libraryToScriptCodeCache,
                classpathScanProcessor,
                libraryPerScriptsCodes -> eventPublisher.publishEvent(
                        new ModulesSetRefreshedEvent(this, libraryPerScriptsCodes)),
                scriptModule -> scriptModule);
    }
}
