package ru.naumen.core.server.script.spi;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.script.places.OriginService;
import ru.naumen.core.shared.utils.ClassFqnHelper;
import ru.naumen.metainfo.shared.ClassFqn;

@Component
public class OriginServiceImpl implements OriginService
{
    private static final ThreadLocal<Origin> ORIGIN = new ThreadLocal<>();

    @Override
    public void setOrigin(@Nullable Origin origin)
    {
        if (origin != null)
        {
            ORIGIN.set(origin);
        }
    }

    @Override
    public Origin getOrigin()
    {
        return ORIGIN.get();
    }

    @Override
    public void setOriginEditForm(@Nullable String uuid)
    {
        setOrigin(Origin.EDIT);
        if (uuid == null)
        {
            return;
        }
        ClassFqn fqn = ClassFqnHelper.toClassId(uuid);
        if (Constants.Comment.FQN.equals(fqn))
        {
            setOrigin(Origin.EDIT_COMMENT);
        }
        if (Constants.File.FQN.equals(fqn))
        {
            setOrigin(Origin.EDIT_FILE);
        }
    }

    @Override
    public void setOriginAddForm(@Nullable ClassFqn fqn)
    {
        setOrigin(Origin.ADD);
        if (fqn == null)
        {
            return;
        }

        if (fqn.equals(Constants.Comment.FQN))
        {
            setOrigin(Origin.ADD_COMMENT);
        }
        if (fqn.equals(Constants.File.FQN))
        {
            setOrigin(Origin.ADD_FILE);
        }
    }
}