package ru.naumen.core.server.script.api;

import static ru.naumen.metainfo.shared.ui.Constants.CURRENT_OBJECT;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Predicate;
import java.util.stream.Collectors;

import jakarta.annotation.Nullable;
import ru.naumen.core.server.script.IListLinkDefinition;
import ru.naumen.core.server.script.IListLinkDefinition.IAttrChain;
import ru.naumen.core.server.util.JsonUtils;
import ru.naumen.metainfo.shared.AttributeFqn;
import ru.naumen.metainfo.shared.Constants.ObjectList;

/**
 * Описание "цепи связей через атрибуты к списку Связанных объектов/Вложенных объектов"
 * <AUTHOR>
 * @since 07.02.2020
 */
public class AttrChain implements IAttrChain
{
    private final IListLinkDefinition linkDefinition;
    private List<String> attributesChain = new ArrayList<>();
    private String attrLinkCode;
    @Nullable
    private String nestedHierarchyAttrFqn;
    @Nullable
    private String nestedAttrLinkFqn;

    public AttrChain(ListLinkDefinition linkDefinition)
    {
        this.linkDefinition = linkDefinition;
    }

    public AttrChain(List<String> attributesChain, @Nullable String nestedHierarchyAttrFqn,
            @Nullable String nestedAttrLinkFqn)
    {
        this.attributesChain = attributesChain;
        this.nestedHierarchyAttrFqn = nestedHierarchyAttrFqn;
        this.nestedAttrLinkFqn = nestedAttrLinkFqn;
        linkDefinition = null;
    }

    public AttrChain(List<String> attributeSelectBeforeHierarchy, String nestedAttrLinkFqn,
            List<String> attributeSelectAfterHierarchy)
    {
        this.nestedAttrLinkFqn = nestedAttrLinkFqn;
        transform(attributeSelectBeforeHierarchy, attributeSelectAfterHierarchy);
        linkDefinition = null;
    }

    private void transform(List<String> attributeSelectBeforeHierarchy, List<String> attributeSelectAfterHierarchy)
    {
        nestedHierarchyAttrFqn = attributeSelectBeforeHierarchy.get(attributeSelectBeforeHierarchy.size() - 1);
        if (AttributeFqn.parse(nestedHierarchyAttrFqn).getCode().equals(CURRENT_OBJECT))
        {
            nestedHierarchyAttrFqn = CURRENT_OBJECT;
        }
        attributesChain.clear();
        Predicate<String> pred = a -> !AttributeFqn.parse(a).getCode().equals(CURRENT_OBJECT);
        attributesChain.addAll(attributeSelectBeforeHierarchy.stream().filter(pred).collect(Collectors.toList()));
        attributesChain.addAll(attributeSelectAfterHierarchy.stream().filter(pred).collect(Collectors.toList()));
    }

    @Override
    public IAttrChain attributesChain(String... str)
    {
        attributesChain.addAll(Arrays.asList(str));
        return this;
    }

    @Override
    public IAttrChain attrLinkCode(String attrLinkCode)
    {
        this.attrLinkCode = attrLinkCode;
        return this;
    }

    @Override
    public IAttrChain nestedHierarchyAttrFqn(String nestedHierarchyAttrFq)
    {
        this.nestedHierarchyAttrFqn = nestedHierarchyAttrFq;
        return this;
    }

    @Override
    public IAttrChain nestedAttrLinkFqn(String nestedAttrLinkFqn)
    {
        this.nestedAttrLinkFqn = nestedAttrLinkFqn;
        return this;
    }

    @Override
    public IListLinkDefinition apply()
    {
        return linkDefinition;
    }

    public List<String> getAttributesChain()
    {
        return attributesChain;
    }

    private String getAttrLinkCode()
    {
        return attrLinkCode;
    }

    @Nullable
    public String getNestedHierarchyAttrFqn()
    {
        return nestedHierarchyAttrFqn;
    }

    @Nullable
    public String getNestedAttrLinkFqn()
    {
        return nestedAttrLinkFqn;
    }

    public String toJSONObject()
    {
        Map<String, Object> jObject = HashMap.newHashMap(4);
        jObject.put(ObjectList.JSON_KEY_ATTRIBUTES_CHAIN, this.getAttributesChain());
        jObject.put(ObjectList.JSON_KEY_ATTR_LINK_CODE, this.getAttrLinkCode() == null ? "" : this.getAttrLinkCode());
        jObject.put(ObjectList.JSON_KEY_NESTED_HIERARCHY_ATTR_FQN,
                this.getNestedHierarchyAttrFqn() == null ? "" : this.getNestedHierarchyAttrFqn());
        jObject.put(ObjectList.NESTED_ATTR_LINK_FQN,
                this.getNestedAttrLinkFqn() == null ? "" : this.getNestedAttrLinkFqn());
        return JsonUtils.toJson(jObject);
    }
}
