package ru.naumen.core.server.script.api;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.Callable;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.function.Supplier;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.google.common.collect.Collections2;
import com.google.common.collect.Sets;

import jakarta.inject.Inject;
import ru.naumen.common.shared.utils.MapProperties;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.CommonUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.bo.DaoFactory;
import ru.naumen.core.server.filestorage.File;
import ru.naumen.core.server.filestorage.FileDao;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderService;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.utils.UuidHelper;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.elements.Attribute;
import ru.naumen.metainfo.shared.elements.MetaClass;
import ru.naumen.sec.server.autorize.AuthorizationRunnerService;
import ru.naumen.soap.server.SoapServiceConfiguration;
import ru.naumen.soap.server.SoapServiceHelper;
import ru.naumen.soap.server.smev3.elements.AnyTypeInResponse;
import ru.naumen.soap.server.smev3.elements.EntryForResponse;
import ru.naumen.soap.server.smev3.elements.FilesResponse;
import ru.naumen.soap.server.smev3.elements.ManyObjectsResponse;
import ru.naumen.soap.server.smev3.elements.MessageResponse;
import ru.naumen.soap.server.smev3.elements.ObjectResponse;
import ru.naumen.soap.server.smev3.elements.ObjectUUIDResponse;
import ru.naumen.soap.server.smev3.elements.Objects;
import ru.naumen.soap.server.smev3.elements.SDRootResponse;
import ru.naumen.soap.server.smev3.elements.Smev3Helper;

/**
 * Реализация {@link ISMEV3Api}
 *
 * <AUTHOR>
 * @since 09.09.2015
 */
@Component("smev3")
public class SMEV3Api implements ISMEV3Api
{
    private static final String SMEV3_NAMESPACE_TO_REPLACE = "http://naumen.ru/soap/server/versionToReplace";
    private static final String SEND_RESPONSE_TO_SMEV3_METHOD = "sendResponseToSmev3";
    private static final String GET_REQUESTS_FROM_SMEV3_METHOD = "getRequestsFromSmev3";
    private static final Logger LOG = LoggerFactory.getLogger(SMEV3Api.class);
    public static final String RESPONDING_FOR_REQUEST_MESSAGE = "Responding for request #{}...";
    @Inject
    private ScriptService scriptService;
    @Inject
    private AuthorizationRunnerService authorizationService;
    @Inject
    private SoapServiceHelper soapHelper;
    @Inject
    private XmlUtils xmlUtils;
    @Inject
    private MessageFacade messages;
    @Inject
    private MetainfoService metainfoService;
    @Inject
    private IPrefixObjectLoaderService loaderService;
    @Inject
    private SoapServiceConfiguration soapConfig;
    @Inject
    private CommonUtils commonUtils;
    @Inject
    private DaoFactory daoFactory;
    @Inject
    private Smev3Helper smev3Helper;
    @Inject
    private ConfigurationProperties configurationProperties;

    @Value("${ru.naumen.soap.smev3-namespace}")
    private String smev3namespaces;

    @Override
    public String getRequests(final String namespace)
    {
        String signModuleCode = getSignatureModuleCode();

        Supplier<String> supplier = () -> scriptService.executeModuleFunction(
                signModuleCode, GET_REQUESTS_FROM_SMEV3_METHOD, namespace);
        try
        {
            return authorizationService.callAsSuperUserWithTimeout("script", supplier,
                    soapConfig.getSmev3Timeout(), TimeUnit.MINUTES);
        }
        catch (Exception e)
        {
            LOG.error("Error while getting requests", e);
            return StringUtilities.EMPTY;
        }
    }

    /**
     * Обработка запросов на добавление файлов. теги AddFileRequest и AddFileToAttrRequest
     * @param document список всех запросов к SD в виде DOM3-документа
     * @param requestTagName название тэга для запроса на добавление файла
     * "AddFileRequest" - добавление файла к объекту
     * "AddFileToAttrRequest" - добавление файла к атрибуту типа "Файл" объекта
     */
    public void processAddFileRequests(Document document, String requestTagName)
    {
        final NodeList addFileRequests = document.getElementsByTagNameNS("*", requestTagName);
        for (int i = 0; i < addFileRequests.getLength(); i++)
        {
            LOG.info(RESPONDING_FOR_REQUEST_MESSAGE, i + 1);
            final Element request = (Element)addFileRequests.item(i);
            final String namespace = request.getNamespaceURI();
            try
            {
                Callable<String> callable = new Callable<String>()
                {
                    @Override
                    public String call() throws Exception
                    {
                        soapHelper.addFileToObject(request, namespace);
                        MessageResponse response = new MessageResponse();
                        response.setMessage(messages.getMessage("ru.naumen.soap.server.SoapServiceHelper.file_added"));
                        SDRootResponse rootResponse = new SDRootResponse();
                        rootResponse.setMessageResponse(response);
                        Document doc = xmlUtils.toDocument(rootResponse, SDRootResponse.class.getPackage().getName(),
                                configurationProperties.isProcessingExternalEntityInXML());
                        return XmlUtils.docToString(doc);
                    }
                };
                getAndSendResponse(namespace, document, request, callable);
            }
            catch (Exception e)
            {
                LOG.error("Error while processing request '{}': \n{}\n Error: {}", requestTagName,
                        XmlUtils.elementToString(request), e.getMessage(), e);
            }
        }
    }

    /**
     * Обработка запросов на создание объектов. Тэг CreateRequest
     * @param document список всех запросов к SD в виде DOM3-документа
     */
    public void processCreateRequests(Document document)
    {
        final NodeList addFileRequests = document.getElementsByTagNameNS("*", "CreateRequest");
        for (int i = 0; i < addFileRequests.getLength(); i++)
        {
            LOG.info(RESPONDING_FOR_REQUEST_MESSAGE, i + 1);
            final Element request = (Element)addFileRequests.item(i);
            final String namespace = request.getNamespaceURI();
            try
            {
                Callable<String> callable = new Callable<String>()
                {
                    @Override
                    public String call() throws Exception
                    {
                        Map<String, Object> attrs = soapHelper.getAttrsFromElement(request, namespace);
                        String fqn = soapHelper.getElementFirstTagTextContent(request, "fqn", namespace);
                        ObjectUUIDResponse response = smev3Helper.createObject(fqn, attrs);
                        SDRootResponse rootResponse = new SDRootResponse();
                        rootResponse.setObjectUUIDResponse(response);
                        Document doc = xmlUtils.toDocument(rootResponse, SDRootResponse.class.getPackage().getName(),
                                configurationProperties.isProcessingExternalEntityInXML());
                        return XmlUtils.docToString(doc);
                    }
                };
                getAndSendResponse(namespace, document, request, callable);
            }
            catch (Exception e)
            {
                LOG.error("Error while processing create request : \n{}\n Error: {}", XmlUtils.elementToString(request),
                        e.getMessage(), e);
            }
        }
    }

    /**
     * Обработка запросов на удаление объектов. Тэг DeleteRequest
     * @param document список всех запросов к SD в виде DOM3-документа
     */
    public void processDeleteRequests(Document document)
    {
        final NodeList deleteRequests = document.getElementsByTagNameNS("*", "DeleteRequest");
        for (int i = 0; i < deleteRequests.getLength(); i++)
        {
            LOG.info(RESPONDING_FOR_REQUEST_MESSAGE, i + 1);
            final Element request = (Element)deleteRequests.item(i);
            final String namespace = request.getNamespaceURI();
            try
            {
                Callable<String> callable = new Callable<String>()
                {
                    @Override
                    public String call() throws Exception
                    {
                        commonUtils.delete(loaderService
                                .get(soapHelper.getElementFirstTagTextContent(request, "uuid", namespace)));
                        MessageResponse response = new MessageResponse();
                        response.setMessage(
                                messages.getMessage("ru.naumen.soap.server.SoapServiceHelper.object_deleted"));
                        SDRootResponse rootResponse = new SDRootResponse();
                        rootResponse.setMessageResponse(response);
                        Document doc = xmlUtils.toDocument(rootResponse, SDRootResponse.class.getPackage().getName(),
                                configurationProperties.isProcessingExternalEntityInXML());
                        return XmlUtils.docToString(doc);
                    }
                };
                getAndSendResponse(namespace, document, request, callable);
            }
            catch (Exception e)
            {
                LOG.error("Error while processing delete request : \n{}\n Error: {}", XmlUtils.elementToString(request),
                        e.getMessage(), e);
            }
        }
    }

    /**
     * Обработка запросов на редактирование объектов. Тэг EditRequest
     * @param document список всех запросов к SD в виде DOM3-документа
     */
    public void processEditRequests(Document document)
    {
        final NodeList deleteRequests = document.getElementsByTagNameNS("*", "EditRequest");
        for (int i = 0; i < deleteRequests.getLength(); i++)
        {
            LOG.info(RESPONDING_FOR_REQUEST_MESSAGE, i + 1);
            final Element request = (Element)deleteRequests.item(i);
            final String namespace = request.getNamespaceURI();
            try
            {
                Callable<String> callable = new Callable<String>()
                {
                    @Override
                    public String call() throws Exception
                    {
                        IUUIDIdentifiable obj = loaderService
                                .get(soapHelper.getElementFirstTagTextContent(request, "uuid", namespace));
                        MetaClass metaClass = metainfoService.getMetaClass(obj);
                        Map<String, Object> attrs = soapHelper.getAttrsFromElement(request, namespace);
                        MapProperties properties = soapHelper.preparePropertiesFromRequestAttrsMap(metaClass, attrs,
                                false, false);
                        commonUtils.edit(obj, properties);
                        MessageResponse response = new MessageResponse();
                        response.setMessage(
                                messages.getMessage("ru.naumen.soap.server.SoapServiceHelper.object_edited"));
                        SDRootResponse rootResponse = new SDRootResponse();
                        rootResponse.setMessageResponse(response);
                        Document doc = xmlUtils.toDocument(rootResponse, SDRootResponse.class.getPackage().getName(),
                                configurationProperties.isProcessingExternalEntityInXML());
                        return XmlUtils.docToString(doc);
                    }
                };
                getAndSendResponse(namespace, document, request, callable);
            }
            catch (Exception e)
            {
                LOG.error("Error while processing edit request : \n{}\n Error: {}", XmlUtils.elementToString(request),
                        e.getMessage(), e);
            }
        }
    }

    /**
     * Обработка запросов на поиск объектов. Тэг FindRequest
     * @param document список всех запросов к SD в виде DOM3-документа
     */
    public void processFindRequests(Document document)
    {
        final NodeList deleteRequests = document.getElementsByTagNameNS("*", "FindRequest");
        for (int i = 0; i < deleteRequests.getLength(); i++)
        {
            LOG.info(RESPONDING_FOR_REQUEST_MESSAGE, i + 1);
            final Element request = (Element)deleteRequests.item(i);
            final String namespace = request.getNamespaceURI();
            try
            {
                Callable<String> callable = new Callable<String>()
                {
                    @Override
                    public String call() throws Exception
                    {
                        ClassFqn fqn = ClassFqn
                                .parse(soapHelper.getElementFirstTagTextContent(request, "fqn", namespace));
                        final MetaClass metaClass = metainfoService.getMetaClass(fqn);
                        MapProperties properties = soapHelper.preparePropertiesFromRequestAttrsMap(metaClass,
                                soapHelper.getAttrsFromElement(request, namespace), true, true);

                        Set<String> attrsForResponse = Sets.newHashSet(ru.naumen.core.shared.Constants.AbstractBO.UUID);
                        if (metaClass.hasAttribute(ru.naumen.core.shared.Constants.AbstractBO.TITLE))
                        {
                            attrsForResponse.add(ru.naumen.core.shared.Constants.AbstractBO.TITLE);
                        }
                        List<IUUIDIdentifiable> objects = new ArrayList<>(commonUtils.find(fqn, properties,
                                soapConfig.getMaxFindObjects() + 1, attrsForResponse, 0));
                        if (objects.size() > soapConfig.getMaxFindObjects())
                        {
                            throw new FxException(
                                    messages.getMessage("ru.naumen.soap.server.SoapServiceHelper.find_method_error",
                                            soapConfig.getMaxFindObjects()));
                        }
                        final Set<Attribute> attributes = new HashSet<>(
                                Collections2.transform(attrsForResponse, new MetaClass.AttributeExtractor(metaClass)));
                        ManyObjectsResponse response = new ManyObjectsResponse();
                        /**
                         * Функция для преобразования IUUIDIdentifiable в ObjForResponse
                         * В преобразованном объекте будут только те атрибуты, которые указаны в переменной attributes
                         */
                        Function<IUUIDIdentifiable, Objects> toObjectForResponseFunction = input ->
                        {
                            Map<String, AnyTypeInResponse> attrsMap = smev3Helper.abstractBOToStringMap(metaClass,
                                    input, attributes);
                            Objects obj = new Objects();
                            List<EntryForResponse> attrsEntries = smev3Helper.getEntries(attrsMap);
                            obj.getEntry().addAll(attrsEntries);
                            return obj;
                        };
                        Collection<Objects> transformedObjects = objects.stream()
                                .map(toObjectForResponseFunction)
                                .toList();
                        response.getObjects().addAll(transformedObjects);
                        SDRootResponse rootResponse = new SDRootResponse();
                        rootResponse.setManyObjectsResponse(response);
                        Document doc = xmlUtils.toDocument(rootResponse, SDRootResponse.class.getPackage().getName(),
                                configurationProperties.isProcessingExternalEntityInXML());
                        return XmlUtils.docToString(doc);
                    }
                };
                getAndSendResponse(namespace, document, request, callable);
            }
            catch (Exception e)
            {
                LOG.error("Error while processing find request : \n{}\n Error: {}", XmlUtils.elementToString(request),
                        e.getMessage(), e);
            }
        }
    }

    /**
     * Обработка запросов на получение файлов. теги GetFileRequest и GetObjectFilesRequest
     * @param document список всех запросов к SD в виде DOM3-документа
     * @param requestTagName название тэга для запроса на добавление файла
     * "GetFileRequest" - получение информации о файле
     * "GetObjectFilesRequest" - получение списка файлов, связанных с объектом
     */
    public void processGetFileRequests(Document document, String requestTagName)
    {
        final NodeList deleteRequests = document.getElementsByTagNameNS("*", requestTagName);
        for (int i = 0; i < deleteRequests.getLength(); i++)
        {
            LOG.info(RESPONDING_FOR_REQUEST_MESSAGE, i + 1);
            final Element request = (Element)deleteRequests.item(i);
            final String namespace = request.getNamespaceURI();
            try
            {
                Callable<String> callable = new Callable<String>()
                {
                    @Override
                    public String call() throws Exception
                    {
                        String uuid = soapHelper.getElementFirstTagTextContent(request, "uuid", namespace);
                        IUUIDIdentifiable obj = loaderService.get(uuid);
                        if (uuid.startsWith("file$"))
                        {
                            File file = (File)obj;
                            if (soapConfig.getMaxFileSizeBytes() > 0
                                && file.getFileSize() > soapConfig.getMaxFileSizeBytes())
                            {
                                throw new FxException("Big file size for SOAP response");
                            }
                            ru.naumen.soap.server.smev3.elements.File fileForResponse = smev3Helper
                                    .getFileForResponse(file);
                            FilesResponse response = new FilesResponse();
                            response.setFile(fileForResponse);
                            SDRootResponse rootResponse = new SDRootResponse();
                            rootResponse.setFilesResponse(response);
                            Document doc = xmlUtils.toDocument(rootResponse,
                                    SDRootResponse.class.getPackage().getName(),
                                    configurationProperties.isProcessingExternalEntityInXML());

                            return XmlUtils.docToString(doc);
                        }
                        FileDao dao = daoFactory.get(ru.naumen.core.shared.Constants.File.FQN);
                        Collection<String> files =
                                dao.get(uuid, null).stream().map(UuidHelper.SAFE_UUID_EXTRACTOR).toList();
                        FilesResponse response = new FilesResponse();
                        response.getFilesUUIDS().addAll(files);
                        SDRootResponse rootResponse = new SDRootResponse();
                        rootResponse.setFilesResponse(response);
                        Document doc = xmlUtils.toDocument(rootResponse, SDRootResponse.class.getPackage().getName(),
                                configurationProperties.isProcessingExternalEntityInXML());
                        return XmlUtils.docToString(doc);
                    }
                };
                getAndSendResponse(namespace, document, request, callable);
            }
            catch (Exception e)
            {
                LOG.error("Error while processing request '{}': \n{}\n Error: {}", requestTagName,
                        XmlUtils.elementToString(request), e.getMessage(), e);
            }

        }
    }

    /**
     * Обработка запросов на получение значений атрибутов объекта. Тэг GetRequest
     * @param document список всех запросов к SD в виде DOM3-документа
     */
    public void processGetRequests(Document document)
    {
        final NodeList getRequests = document.getElementsByTagNameNS("*", "GetRequest");
        for (int i = 0; i < getRequests.getLength(); i++)
        {
            LOG.info(RESPONDING_FOR_REQUEST_MESSAGE, i + 1);
            final Element request = (Element)getRequests.item(i);
            final String namespaceURI = request.getNamespaceURI();
            try
            {
                Callable<String> callable = new Callable<String>()
                {
                    @Override
                    public String call() throws Exception
                    {
                        String uuid = soapHelper.getElementFirstTagTextContent(request, "uuid", namespaceURI);
                        if (uuid.startsWith("file$"))
                        {
                            throw new FxException(
                                    messages.getMessage("ru.naumen.soap.server.SoapServiceHelper.get_method_error"));
                        }
                        IUUIDIdentifiable obj = loaderService.get(uuid);
                        MetaClass metaClass = metainfoService.getMetaClass(obj);
                        Collection<Attribute> allObjectAttrs = metaClass.getAttributes();
                        Map<String, AnyTypeInResponse> json = smev3Helper.abstractBOToStringMap(metaClass, obj,
                                allObjectAttrs);

                        ObjectResponse response = new ObjectResponse();
                        response.setAttributes(smev3Helper.getAttributesFromStringMap(json));
                        SDRootResponse rootResponse = new SDRootResponse();
                        rootResponse.setObjectResponse(response);
                        Document doc = xmlUtils.toDocument(rootResponse, SDRootResponse.class.getPackage().getName(),
                                configurationProperties.isProcessingExternalEntityInXML());
                        return XmlUtils.docToString(doc);
                    }
                };
                getAndSendResponse(namespaceURI, document, request, callable);
            }
            catch (Exception e)
            {
                LOG.error("Error while processing edit request : \n{}\n Error: {}", XmlUtils.elementToString(request),
                        e.getMessage(), e);
            }
        }
    }

    @Override
    public boolean processRequests()
    {
        LOG.info("Processing requests ...");
        boolean hasRequests = false;
        for (String namespace : smev3namespaces.split(";"))
        {
            LOG.info("Processing requests for namespace {} ...", namespace);
            String requestsStr = getRequests(namespace);
            if (StringUtilities.isEmptyTrim(requestsStr) || !requestsStr.contains("SDRootRequest"))
            {
                if (!StringUtilities.contains(requestsStr, "GetRequestResponse"))
                {
                    LOG.error("Namespace: {}.Process requests. Error:\n{}", namespace,
                            requestsStr == null ? StringUtilities.EMPTY : requestsStr);
                }
                LOG.info("Namespace: {}.Process requests. No requests returned. See previous log.", namespace);
                continue;
            }
            LOG.info("Namespace {} has request(s) : {}", namespace, requestsStr);
            hasRequests = true;
            try
            {
                Document document = XmlUtils.getDocument(XmlUtils.toInputStream(requestsStr),
                        configurationProperties.isProcessingExternalEntityInXML());
                LOG.info("processAddFileRequests(AddFileRequest) for namespace {} ...", namespace);
                processAddFileRequests(document, "AddFileRequest");
                LOG.info("processAddFileRequests(AddFileToAttrRequest) for namespace {} ...", namespace);
                processAddFileRequests(document, "AddFileToAttrRequest");
                LOG.info("processGetRequests for namespace {} ...", namespace);
                processGetRequests(document);
                LOG.info("processCreateRequests for namespace {} ...", namespace);
                processCreateRequests(document);
                LOG.info("processDeleteRequests for namespace {} ...", namespace);
                processDeleteRequests(document);
                LOG.info("processEditRequests for namespace {} ...", namespace);
                processEditRequests(document);
                LOG.info("processFindRequests for namespace {} ...", namespace);
                processFindRequests(document);
                LOG.info("processGetFileRequests(GetFileRequest) for namespace {} ...", namespace);
                processGetFileRequests(document, "GetFileRequest");
                LOG.info("processGetFileRequests(GetObjectFilesRequest) for namespace {} ...", namespace);
                processGetFileRequests(document, "GetObjectFilesRequest");
                LOG.info("Done.");
            }
            catch (Exception e)
            {
                LOG.error(e.getMessage(), e);
            }
        }
        return hasRequests;
    }

    @Override
    public String sendResponse(final String response, final String replyTo)
    {
        LOG.debug("Sending response: {}", response);
        String signModuleCode = getSignatureModuleCode();
        final Supplier<String> supplier = () ->
                scriptService.executeModuleFunction(
                        signModuleCode, SEND_RESPONSE_TO_SMEV3_METHOD, response, replyTo);

        return authorizationService.callAsSuperUserWithTimeout(
                "script", supplier, soapConfig.getSmev3Timeout(), TimeUnit.MINUTES);
    }

    @Override
    public void setSmev3TimeoutInMinutes(int smev3Timeout)
    {
        soapConfig.setSmev3Timeout(smev3Timeout);
    }

    private void getAndSendResponse(final String namespace, Document document, final Element request,
            Callable<String> callable)
    {
        String responseAsStr = smev3Helper
                .executeInTransactionBySoapServiceUser(soapHelper.getAccessKeyFromReq(request, namespace), callable);
        String response = responseAsStr.replace(SMEV3_NAMESPACE_TO_REPLACE, namespace);
        LOG.info("sending response for namespace {}. Response : {}", namespace, response);
        sendResponse(response, document.getDocumentElement());
    }

    private String getSignatureModuleCode()
    {
        String signatureModuleCode = soapConfig.getSignatureModule();
        if (signatureModuleCode == null
            || !scriptService.isModuleExists(signatureModuleCode))
        {
            throw new FxException("No module with code '" + signatureModuleCode + "' presents on stand",
                    true);
        }
        return signatureModuleCode;
    }

    private void sendResponse(String response, Element docWithRequestElem)
    {
        String replyTo = soapHelper.getElementFirstTagTextContent(docWithRequestElem, "ReplyTo", "*");
        sendResponse(response, replyTo);
    }

    @Override
    public void setSmev3Namespace(String smev3namespace)
    {
        this.smev3namespaces = smev3namespace;
    }

    @Override
    public String getSmev3Namespace()
    {
        return smev3namespaces;
    }

}
