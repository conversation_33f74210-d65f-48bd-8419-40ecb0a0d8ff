package ru.naumen.core.server.hquery.impl;

import jakarta.annotation.Nullable;

import org.apache.commons.lang3.StringUtils;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HProperty;

/**
 * Базовый класс для всех колонок
 *
 * <AUTHOR>
 * @since 21.07.2021
 */
public abstract class AbstractHColumn implements HColumn
{
    @Nullable
    private final String alias;

    protected AbstractHColumn(@Nullable String alias)
    {
        this.alias = alias;
    }

    @Override
    public final String getAlias()
    {
        return this.alias != null ? this.alias : "";
    }

    @Override
    public HProperty getProperty(String property)
    {
        throw new UnsupportedOperationException();
    }

    @Override
    public HProperty getProperty(String property, @Nullable String alias)
    {
        throw new UnsupportedOperationException();
    }

    /**
     * Построить HQL<br>
     * Нужен для построения вложенных элементов, когда их не нужно добавлять в builder.<br>
     * <b>Не содержит псевдоним (alias)!</b> alias добавляется в методе {@link #visit(HBuilder)}.
     * Сделано так, чтобы было удобно реализовать этот метод,
     * без необходимости помнить о псевдониме.
     * @param builder построитель запроса
     */
    @Override
    public abstract String getHQL(HBuilder builder);

    @Override
    public void visit(HBuilder builder)
    {
        String hql = getHQL(builder);
        if (StringUtils.isNotBlank(alias))
        {
            hql = hql + " as " + alias;
        }
        builder.select(hql);
    }
}
