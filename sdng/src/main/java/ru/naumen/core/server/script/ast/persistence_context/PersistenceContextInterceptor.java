package ru.naumen.core.server.script.ast.persistence_context;

import java.util.Iterator;

import jakarta.inject.Inject;

import org.hibernate.SessionFactory;
import org.hibernate.engine.spi.PersistenceContext;
import org.hibernate.engine.spi.SessionFactoryImplementor;
import org.hibernate.engine.spi.SessionImplementor;
import org.hibernate.type.Type;
import org.springframework.stereotype.Component;

import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.DepthLoggingTransactionManager;
import ru.naumen.core.server.flex.spi.SessionFactoryType;
import ru.naumen.core.server.hibernate.IInterceptor;
import ru.naumen.core.server.hibernate.SessionFactoryAware;

/**
 * Interceptor, который обновляет состояние счетчика PersistenceContext'а
 *
 * <AUTHOR>
 * @since Dec 3, 2019
 */
@Component
public class PersistenceContextInterceptor implements IInterceptor, SessionFactoryAware
{
    private final ConfigurationProperties configurationProperties;
    private SessionFactory sessionFactory;

    @Inject
    public PersistenceContextInterceptor(final ConfigurationProperties configurationProperties)
    {
        this.configurationProperties = configurationProperties;
    }

    @Override
    public boolean onPersist(Object entity, Object id, Object[] state, String[] propertyNames, Type[] types)
    {
        updateCachedEntriesCount();
        return false;
    }

    @Override
    public void postFlush(Iterator entities)
    {
        updateCachedEntriesCount();
    }

    @Override
    public Object getEntity(String entityName, Object id)
    {
        updateCachedEntriesCount();
        return null;
    }

    @Override
    public void setSessionFactory(SessionFactoryImplementor sessionFactory, SessionFactoryType sessionFactoryType)
    {
        this.sessionFactory = sessionFactory;
    }

    private void updateCachedEntriesCount()
    {
        if (!configurationProperties.isPersistenceContextASTEnabled())
        {
            return;
        }

        final SessionImplementor sessionImplementor = (SessionImplementor)sessionFactory.getCurrentSession();
        final PersistenceContext persistenceContext = sessionImplementor.getPersistenceContext();

        final int transactionDepth = DepthLoggingTransactionManager.getCurrentTransactionsDepth();
        final int cachedEntriesCount = persistenceContext.getEntitiesByKey().size();

        PersistenceContextScriptInterruptionService.put(transactionDepth, cachedEntriesCount);
    }
}
