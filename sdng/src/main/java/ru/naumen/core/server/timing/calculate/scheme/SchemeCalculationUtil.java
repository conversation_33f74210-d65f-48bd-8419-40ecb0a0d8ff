/**
 */
package ru.naumen.core.server.timing.calculate.scheme;

import java.util.Date;
import java.util.Map.Entry;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.timing.calculate.ServiceTimeCalculator;

/**
 * Утильный класс для вычисления рабочего времени
 *
 * <AUTHOR>
 * @since 05.09.2008
 * @deprecated SchemeCalculationUtil является частью устаревшего механизма SchemeTimingCalculator. 
 * На данный момент используется усовершенствованная версия калькулятора - {@link ServiceTimeCalculator}.
 */
@Deprecated
public class SchemeCalculationUtil
{
    /**
     * Найти дату окончания нормативного времени по стартовой дате,
     * нормативному периоду и схеме вычислений рабочего времени
     * @param startTime стартовая дата
     * @param resolutionTime нормативный период в миллисекундах
     * @param scheme схема вычислений рабочего времени
     * @return финальная нормативная дата
     */
    public static Date findNormativeDate(Date startTime, long resolutionTime, TimingScheme scheme)
    {
        long startTimeMillis = startTime.getTime();

        Entry<Long, SchemeNode> nodeEntry = scheme.getEntry(startTimeMillis);
        long dayBegin = nodeEntry.getKey();
        SchemeNode node = nodeEntry.getValue();

        long dayOffset = startTimeMillis - dayBegin;
        for (Entry<Long, Long> entry : node.getPeriods().entrySet())
        {
            long periodStart = entry.getKey();
            long periodEnd = entry.getValue();
            if (dayOffset < periodStart)
            {
                dayOffset = periodStart;
            }
            if (dayOffset < periodEnd)
            {
                long periodLength = periodEnd - periodStart;
                if (resolutionTime > periodLength)
                {
                    resolutionTime -= periodLength;
                }
                else
                {
                    return new Date(dayOffset + resolutionTime + dayBegin);
                }
                dayOffset = periodEnd;
            }
        }

        Entry<Long, SchemeNode> nextEntry = scheme.getNextEntry(nodeEntry);
        Entry<Long, SchemeNode> result = step(nextEntry, resolutionTime, scheme);

        // Если результат - выходной, то ищем ближайший рабочий день
        if (result.getValue().getNodeWorkTime() == 0)
        {
            Date starttime = getStartTime(new Date(result.getKey()), scheme);
            result = scheme.getEntry(starttime.getTime());
        }

        long truncateResult = result.getKey();
        SchemeNode resultNode = result.getValue();

        resolutionTime -= resultNode.getWorkTimeOffset() - nextEntry.getValue().getWorkTimeOffset();

        dayOffset = 0;
        for (Entry<Long, Long> entry : resultNode.getPeriods().entrySet())
        {
            long periodStart = entry.getKey();
            long periodEnd = entry.getValue();
            if (dayOffset < periodStart)
            {
                dayOffset = periodStart;
            }
            if (dayOffset < periodEnd)
            {
                long periodLength = periodEnd - dayOffset;
                if (resolutionTime > periodLength)
                {
                    resolutionTime -= periodLength;
                }
                else
                {
                    return new Date(dayOffset + resolutionTime + truncateResult);
                }
                dayOffset = periodEnd;
            }
        }

        throw new FxException("calculation error see above");
    }

    /**
     * Получить ближайшую дату начала рабочего времени, большую заданной даты
     * @param date дата
     * @param scheme схема вычислений рабочего времени
     * @return ближайшая дата начала рабочего времени, большая заданной даты
     */
    public static Date getStartTime(Date date, TimingScheme scheme)
    {
        Date findStartTime = findNormativeDate(date, 1, scheme);
        long startMillis = findStartTime.getTime() - 1;
        return new Date(startMillis);
    }

    /**
     * Получить рабочее время между from и to по схеме рабочего времени scheme
     * @param startTime стартовая дата
     * @param endTime финальная дата
     * @param scheme схемы вычислений рабочего времени
     * @return рабочее время в миллисекундах
     */
    public static long getWorkingTime(Date startTime, Date endTime, TimingScheme scheme)
    {
        long startTimeMillis = startTime.getTime();
        long endTimeMillis = endTime.getTime();

        Entry<Long, SchemeNode> fromEntry = scheme.getEntry(startTimeMillis);
        Entry<Long, SchemeNode> toEntry = scheme.getEntry(endTimeMillis);

        SchemeNode fromNode = fromEntry.getValue();
        SchemeNode toNode = toEntry.getValue();

        long fromDayOffset = startTimeMillis - fromEntry.getKey();
        long toDayOffset = endTimeMillis - toEntry.getKey();

        long result = toNode.getWorkTimeOffset() - fromNode.getWorkTimeOffset();

        long entryValue, entryKey;

        for (Entry<Long, Long> entry : fromNode.getPeriods().descendingMap().entrySet())
        {
            entryKey = entry.getKey();
            entryValue = entry.getValue();

            if (fromDayOffset > entryValue)
            {
                fromDayOffset = entryValue;
            }
            if (fromDayOffset > entryKey)
            {
                result -= fromDayOffset - entryKey;
                fromDayOffset = entryKey;
            }
        }

        for (Entry<Long, Long> entry : toNode.getPeriods().descendingMap().entrySet())
        {
            entryKey = entry.getKey();
            entryValue = entry.getValue();

            if (toDayOffset > entryValue)
            {
                toDayOffset = entryValue;
            }
            if (toDayOffset > entryKey)
            {
                result += toDayOffset - entryKey;
                toDayOffset = entryKey;
            }
        }

        return result;
    }

    /**
     * Сделать рекурсивный шаг для вычисления финальной нормативной даты
     * @param from пара: начало дня - элемент схемы вычислений
     * @param normativePeriod нормативный период
     * @param scheme схема вычислений
     * @return следующая пара начало: дня - элемент схемы вычислений,
     * которая находится ближе к результату, чем предыдущая
     * step(RESULT)==RESULT
     * |RESULT-step(X)|<|RESULT-X|
     */
    private static Entry<Long, SchemeNode> step(Entry<Long, SchemeNode> from, long normativePeriod, TimingScheme scheme)
    {
        long truncateDate = from.getKey();
        SchemeNode node = from.getValue();
        if (normativePeriod >= node.getNodeWorkTime() || normativePeriod < 0)
        {
            long jumpLength = truncateDate + Math.round(node.getAdaptiveMultiplier() * normativePeriod);
            Entry<Long, SchemeNode> newEntry = scheme.getEntry(jumpLength);

            long diff = newEntry.getValue().getWorkTimeOffset() - node.getWorkTimeOffset();
            long newNormativePeriod = normativePeriod - diff;

            long absNorm = normativePeriod > 0 ? normativePeriod : -normativePeriod;
            long absNewNorm = newNormativePeriod > 0 ? newNormativePeriod : -newNormativePeriod;

            if (absNewNorm < absNorm)
            {
                return step(newEntry, newNormativePeriod, scheme);
            }
            else
            {
                newEntry = from;
                if (normativePeriod > 0)
                {
                    newEntry = scheme.getPrevEntry(newEntry);
                    while (normativePeriod - (newEntry.getValue().getWorkTimeOffset() - node.getWorkTimeOffset())
                           > newEntry
                                   .getValue().getNodeWorkTime())
                    {
                        newEntry = scheme.getNextEntry(newEntry);
                    }
                }
                else if (normativePeriod < 0)
                {
                    while (normativePeriod - (newEntry.getValue().getWorkTimeOffset() - node.getWorkTimeOffset()) < 0)
                    {
                        newEntry = scheme.getPrevEntry(newEntry);
                    }
                }

                return newEntry;
            }
        }

        return from;
    }
}