package ru.naumen.core.server.script.api.accesskeys;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.hibernate.type.StandardBasicTypes;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.inject.Named;
import ru.naumen.commons.shared.FxException;
import ru.naumen.commons.shared.SilentFxException;
import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.actioncontext.ActionContextHolder;
import ru.naumen.core.server.bo.employee.Employee;
import ru.naumen.core.server.embeddedapplication.accesskey.RelatedToSessionKeyService;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.utils.DateUtils;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.sec.server.users.UserPrincipal;
import ru.naumen.sec.server.users.employee.EmployeeUserDetailsService;
import ru.naumen.sec.server.users.superuser.SuperUserDetailsService;

/**
 * <AUTHOR>
 * @since 06.06.2012
 */
@Component
public class AccessKeyDaoImpl implements AccessKeyDao
{
    /**
     * Сколько времени хранить ключи после истечения срока действия
     * (нужно, чтобы пользователь мог увидеть, что он опоздал с выполнением действия)
     */
    public static final long LIFETIME_AFTER_EXPIRED = DateUtils.MILLISECONDS_IN_A_WEEK;
    private static final int DEFAULT_FIRST_RESULT = 0;
    private static final int DEFAULT_MAX_RESULTS = 100;

    public static final String EMPLOYEE_UUID = "employeeUuid";
    public static final String USERNAME = "username";
    private static final String DEADLINE = "deadline";

    private static final List<String> columns =
            List.of("uuid", "active", "creationDate", DEADLINE,
                    "description", EMPLOYEE_UUID, "lastUsageDate", "type", USERNAME);

    private static List<Object> getObjects(List<Object[]> results)
    {
        List<Object> mappedResults = new ArrayList<>();

        for (Object[] result : results)
        {
            Map<String, Object> map = new HashMap<>();
            for (int i = 0; i < columns.size(); i++)
            {
                map.put(columns.get(i), result[i]);
            }
            mappedResults.add(map);
        }

        return mappedResults;
    }

    private final MessageFacade messages;
    private final SessionFactory sessionFactory;
    private final EmployeeUserDetailsService employeeUserDetailService;
    private final SuperUserDetailsService superUserDetailService;
    private final RelatedToSessionKeyService relatedToSessionKeyService;
    private final ActionContextHolder actionContextHolder;
    private final ConfigurationProperties configurationProperties;

    @Inject
    public AccessKeyDaoImpl(MessageFacade messages,
            @Named("sessionFactory") SessionFactory sessionFactory,
            EmployeeUserDetailsService employeeUserDetailService,
            SuperUserDetailsService superUserDetailService, RelatedToSessionKeyService relatedToSessionKeyService,
            ActionContextHolder actionContextHolder, ConfigurationProperties configurationProperties)
    {
        this.messages = messages;
        this.sessionFactory = sessionFactory;
        this.employeeUserDetailService = employeeUserDetailService;
        this.superUserDetailService = superUserDetailService;
        this.relatedToSessionKeyService = relatedToSessionKeyService;
        this.actionContextHolder = actionContextHolder;
        this.configurationProperties = configurationProperties;
    }

    @Override
    @Transactional
    public void delete(AccessKey accessKey)
    {
        getSession().remove(accessKey);
    }

    @Override
    @Transactional
    public void delete(String accessKeyUuid)
    {
        AccessKey accessKey = sessionFactory.getCurrentSession().get(AccessKey.class, accessKeyUuid);
        if (accessKey != null)
        {
            delete(accessKey);
        }
    }

    @Override
    @Transactional
    public void deleteExpiredAccessKeys()
    {
        Date deadline = new Date(System.currentTimeMillis() - LIFETIME_AFTER_EXPIRED);

        if (countExpiredAccessKeys(deadline) > 0)
        {
            Query<?> query = getSession().createQuery(
                    "delete " + AccessKey.class.getName() + " where deadline < :deadline");
            query.setParameter(DEADLINE, deadline, StandardBasicTypes.DATE);
            query.executeUpdate();
        }
    }

    @Override
    @Transactional
    public AccessKey get(String uuid)
    {
        return sessionFactory.getCurrentSession().get(AccessKey.class, uuid);
    }

    @SuppressWarnings("unchecked")
    @Override
    public Map<String, Object> getAccessKeyInfo(String uuid)
    {
        HCriteria criteria = HHelper.create().addSource(AccessKey.class.getName());
        for (String column : columns)
        {
            criteria.addColumn(criteria.getProperty(column));
        }
        criteria.add(HRestrictions.eq(criteria.getProperty("uuid"), uuid));
        Object[] accessKeyInfo = (Object[])criteria.createQuery(getSession()).uniqueResult();

        if (accessKeyInfo == null || accessKeyInfo.length == 0)
        {
            return Map.of();
        }

        Map<String, Object> map = new HashMap<>();
        for (int i = 0; i < columns.size(); i++)
        {
            map.put(columns.get(i), accessKeyInfo[i]);
        }
        return map;
    }

    @Override
    public List<Object> getAllAccessKeysInfo(String uuid, String login)
    {
        return getAllAccessKeysInfo(uuid, login, DEFAULT_FIRST_RESULT, DEFAULT_MAX_RESULTS);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Object> getAllAccessKeysInfo(@Nullable String uuid, @Nullable String login, int firstResult,
            int maxResults)
    {
        HCriteria criteria = HHelper.create().addSource(AccessKey.class.getName());
        for (String column : columns)
        {
            criteria.addColumn(criteria.getProperty(column));
        }
        if (StringUtilities.isNotEmpty(uuid) && StringUtilities.isNotEmpty(login))
        {
            criteria.add(HRestrictions.or(HRestrictions.eq(criteria.getProperty(EMPLOYEE_UUID), uuid),
                    HRestrictions.eq(criteria.getProperty(USERNAME), login)));
        }
        else if (StringUtilities.isNotEmpty(uuid))
        {
            criteria.add(HRestrictions.eq(criteria.getProperty(EMPLOYEE_UUID), uuid));
        }
        else if (StringUtilities.isNotEmpty(login))
        {
            criteria.add(HRestrictions.eq(criteria.getProperty(USERNAME), login));
        }
        criteria.setFirstResult(firstResult);
        criteria.setMaxResults(maxResults);
        List<Object[]> results = criteria.createQuery(getSession()).list();

        return getObjects(results);
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<Object> getAllAccessKeysInfoExceptForSpecified(String login, int firstResult, int maxResults)
    {
        HCriteria criteria = HHelper.create().addSource(AccessKey.class.getName());
        for (String column : columns)
        {
            criteria.addColumn(criteria.getProperty(column));
        }
        criteria.add(HRestrictions.not(HRestrictions.eq(criteria.getProperty(USERNAME), login)));
        criteria.setFirstResult(firstResult);
        criteria.setMaxResults(maxResults);
        List<Object[]> results = criteria.createQuery(getSession()).list();
        return getObjects(results);
    }

    @Override
    public List<AccessKey> getByEmployee(Employee employee)
    {
        return getByLoginOrUUID(employee.getUUID(), employee.getLogin());
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<AccessKey> getByLogin(String login)
    {
        HCriteria criteria = HHelper.create(AccessKey.class);
        criteria.add(HRestrictions.eq(criteria.getProperty(USERNAME), login));
        return criteria.createQuery(getSession()).list();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<AccessKey> getByLoginOrUUID(String uuid, String login)
    {
        HCriteria criteria = HHelper.create(AccessKey.class);
        HCriterion uuidCriterion = HRestrictions.eq(criteria.getProperty(EMPLOYEE_UUID), uuid);
        HCriterion loginCriterion = HRestrictions.eq(criteria.getProperty(USERNAME), login);
        criteria.add(HRestrictions.or(uuidCriterion, loginCriterion));
        return criteria.createQuery(getSession()).list();
    }

    @SuppressWarnings("unchecked")
    @Override
    public List<AccessKey> getByUUID(String uuid)
    {
        HCriteria criteria = HHelper.create(AccessKey.class);
        criteria.add(HRestrictions.eq(criteria.getProperty(EMPLOYEE_UUID), uuid));
        return criteria.createQuery(getSession()).list();
    }

    @Override
    public UserPrincipal getUserPrincipal(AccessKey accessKey)
    {
        String username = accessKey.getUsername();
        String employeeUuid = accessKey.getEmployeeUuid();

        if (!ObjectUtils.isEmpty(username) && superUserDetailService.hasSuperUser(username))
        {
            return superUserDetailService.loadUserByUsername(username);
        }
        if (!ObjectUtils.isEmpty(employeeUuid))
        {
            return employeeUserDetailService.loadByUUID(employeeUuid);
        }
        return employeeUserDetailService.loadUserByUsername(username);
    }

    @Override
    @Transactional
    public AccessKey obtain(String uuid)
    {
        AccessKey accessKey = get(uuid);
        if (accessKey == null)
        {
            // Не логируем данную ошибку, если это мобильный клиент
            boolean needLog = !actionContextHolder.getActionContext().isMobile();
            throw new SilentFxException(messages.getMessage("AccessKeyDaoImpl.notFound", uuid), true, needLog,
                    false); // логируем без записи стектрейса
        }
        //проверка активности ключа доступа
        if (!accessKey.isActive())
        {
            throw new FxException(messages.getMessage("AccessKeyDaoImpl.inactive", uuid), true);
        }
        //проверка истечения срока действия
        if (new Date().after(accessKey.getDeadline()))
        {
            throw new FxException(messages.getMessage("AccessKeyDaoImpl.lifetimeExpire", uuid), true);
        }
        //если ключ одноразовый, то удаляем его
        if (accessKey.getType() == AccessKeyType.DISPOSABLE)
        {
            delete(accessKey);
        }
        else if (!configurationProperties.getAccessKeysNonUpdatableUsageDateSet().contains(accessKey.getUuid()))
        {
            //обновление даты последнего использования ключа доступа
            update(accessKey.setLastUsageDate(new Date()));
        }
        if (accessKey.getType() == AccessKeyType.RELATED_TO_SESSION && !relatedToSessionKeyService.isValid(accessKey))
        {
            throw new FxException(messages.getMessage("AccessKeyDaoImpl.lifetimeExpire", uuid), true);
        }
        return accessKey;
    }

    @Override
    @Transactional
    public AccessKey save(AccessKey accessKey)
    {
        getSession().persist(accessKey);
        return accessKey;
    }

    @Override
    @Transactional
    public AccessKey update(AccessKey accessKey)
    {
        return getSession().merge(accessKey);
    }

    private Session getSession()
    {
        return sessionFactory.getCurrentSession();
    }

    private long countExpiredAccessKeys(Date deadline)
    {
        Query<?> query = getSession().createQuery(
                "select count(*) from " + AccessKey.class.getName() + " where deadline < :deadline");
        query.setParameter(DEADLINE, deadline, StandardBasicTypes.DATE);
        return (Long)query.uniqueResult();
    }
}
