package ru.naumen.core.server.script.api;

import static ru.naumen.core.server.script.api.scheduler.TriggerStatusWrapper.TRIGGER_STATUS_WRAPPER;

import java.text.DateFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import org.quartz.SchedulerException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeansException;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import net.customware.gwt.dispatch.shared.ActionException;
import net.customware.gwt.dispatch.shared.DispatchException;
import ru.naumen.common.shared.utils.DateTimeInterval;
import ru.naumen.common.shared.utils.IDateTimeInterval;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.dispatch.Dispatch;
import ru.naumen.core.server.scheduler.SchedulerProperties;
import ru.naumen.core.server.scheduler.manager.QuartzSchedulerManager;
import ru.naumen.core.server.scheduler.storage.SchedulerUserTaskStorageService;
import ru.naumen.core.server.script.api.scheduler.ISchedulerTaskWrapper;
import ru.naumen.core.server.script.api.scheduler.ITriggerInfo;
import ru.naumen.core.server.script.api.scheduler.ITriggerStatus;
import ru.naumen.core.server.script.api.scheduler.ITriggerWrapper;
import ru.naumen.core.server.script.api.scheduler.SchedulerTaskWrapper;
import ru.naumen.core.server.tags.TagService;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.core.shared.dispatch.SimpleResult;
import ru.naumen.mailreader.server.queue.InboundMailDao;
import ru.naumen.mailreader.shared.task.ReceiveMailTask;
import ru.naumen.metainfo.shared.dispatch2.scheduler.DeleteSchTaskAction;
import ru.naumen.metainfo.shared.scheduler.ConcreteDateTrigger;
import ru.naumen.metainfo.shared.scheduler.PeriodicTrigger;
import ru.naumen.metainfo.shared.scheduler.SchedulerTask;
import ru.naumen.metainfo.shared.scheduler.Trigger;
import ru.naumen.metainfo.shared.scheduler.Trigger.CalculateStrategies;
import ru.naumen.metainfo.shared.scheduler.Trigger.Periods;

/**
 * Реализация ISchedulerApi
 *
 * <AUTHOR>
 */
@Component("scheduler")
public class SchedulerApi implements ISchedulerApi
{
    private class TriggerInfo implements ITriggerInfo
    {
        private final String triggerName;
        private final String period;
        private final Date date;
        private final boolean isEnabled;

        TriggerInfo(Trigger trigger)
        {
            triggerName = trigger.getCode();
            if (trigger instanceof PeriodicTrigger periodicTrigger)
            {
                Periods prd = periodicTrigger.getPeriod();
                period = prd == null ? periodicTrigger.getInterval().toString() : prd.toString();
                date = trigger.getPlanExecutionDate();
            }
            else if (trigger instanceof ConcreteDateTrigger)
            {
                period = messages.getMessage("scheduler.triggerIsConcreteDate");
                date = trigger.getPlanExecutionDate();
            }
            else
            {
                throw new FxException(messages.getMessage("scheduler.unknownTypeOfTrigger"), true);
            }
            isEnabled = trigger.isEnabled();
        }

        @Override
        public Date getDate()
        {
            return date;
        }

        @Override
        public String getPeriod()
        {
            return period;
        }

        @Override
        public String getTriggerName()
        {
            return triggerName;
        }

        @Override
        public boolean isEnabled()
        {
            return isEnabled;
        }

        @Override
        public String toString()
        {
            return "<br>"
                   + messages.getMessage("scheduler.triggerName", triggerName)
                   + "<br>"
                   + messages.getMessage("scheduler.period", period)
                   + "<br>"
                   + messages.getMessage("scheduler.date", date == null ? "never" : date)
                   + "<br>"
                   + messages.getMessage("scheduler.isEnabled", isEnabled)
                   + "<br>";
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(SchedulerApi.class);
    private static final String FORMAT_PATTERN = "%s %s";
    private static final String SCHEDULER_PERIODIC_TRIGGER_PREFIX = "scheduler.periodicTriggerPrefix";

    private final SchedulerManagerApi oldApi;
    private final QuartzSchedulerManager quartzSchedulerManager;
    private final MessageFacade messages;
    private final SchedulerUserTaskStorageService schedulerUserTaskStorageService;
    private final SchedulerProperties schedulerProperties;
    private final TagService tagService;
    private final InboundMailDao inboundMailDao;
    private final Dispatch dispatch;

    @Inject
    public SchedulerApi(
            SchedulerManagerApi oldApi,
            QuartzSchedulerManager quartzSchedulerManager,
            MessageFacade messages,
            SchedulerUserTaskStorageService schedulerUserTaskStorageService,
            SchedulerProperties schedulerProperties,
            TagService tagService,
            InboundMailDao inboundMailDao,
            Dispatch dispatch)
    {
        this.oldApi = oldApi;
        this.quartzSchedulerManager = quartzSchedulerManager;
        this.messages = messages;
        this.schedulerUserTaskStorageService = schedulerUserTaskStorageService;
        this.schedulerProperties = schedulerProperties;
        this.tagService = tagService;
        this.inboundMailDao = inboundMailDao;
        this.dispatch = dispatch;
    }

    @Override
    @SuppressWarnings("java:S2629")
    public void disableTrigger(String triggerName)
    {
        switchTrigger(triggerName, false);
        LOG.info(messages.getMessage("scheduler.triggerDisabled",
                triggerName));
    }

    @Override
    @SuppressWarnings("java:S2629")
    public void enableTrigger(String triggerName)
    {
        switchTrigger(triggerName, true);
        LOG.info(messages.getMessage("scheduler.triggerEnabled",
                triggerName));
    }

    private void switchTrigger(String triggerName, boolean isEnable)
    {
        try
        {
            SchedulerTask schedulerTask = schedulerUserTaskStorageService.getSchedulerTasks().stream()
                    .filter(task -> task.getTrigger(triggerName) != null)
                    .findFirst()
                    .orElseThrow(SchedulerException::new);
            Trigger trigger = Objects.requireNonNull(schedulerTask.getTrigger(triggerName));
            if (trigger.isEnabled() == isEnable)
            {
                return;
            }
            quartzSchedulerManager.switchUserTaskTrigger(schedulerTask, trigger, isEnable);
        }
        catch (SchedulerException e)
        {
            throw new FxException(messages.getMessage(
                    isEnable ? "scheduler.cantEnable" : "scheduler.cantDisable", triggerName), true, e);
        }
    }

    @Override
    public List<ITriggerStatus> getFullStatus()
    {
        return quartzSchedulerManager.getSchedulerTaskTriggerStatuses().stream()
                .map(TRIGGER_STATUS_WRAPPER)
                .filter(Objects::nonNull)
                .toList();
    }

    @Nullable
    @Override
    public ITriggerStatus getFullStatus(String groupName, String jobName)
    {
        return quartzSchedulerManager.getSchedulerTaskTriggerStatuses().stream()
                .filter(triggerStatus ->
                        jobName.equals(triggerStatus.getJobName()) && groupName.equals(triggerStatus.getGroupName()))
                .findFirst()
                .map(TRIGGER_STATUS_WRAPPER)
                .orElse(null);
    }

    @Override
    public List<ISchedulerTaskWrapper> getStatus()
    {
        return schedulerUserTaskStorageService.getSchedulerTasks().stream()
                .map(SchedulerTaskWrapper.SCHEDULER_TASK_WRAPPER)
                .collect(Collectors.toList());
    }

    @Override
    public ISchedulerTaskWrapper getStatus(String uuid)
    {
        return SchedulerTaskWrapper.SCHEDULER_TASK_WRAPPER.apply(
                schedulerUserTaskStorageService.getSchedulerTaskWithPlanExecutionDate(uuid));
    }

    @Override
    public ITriggerWrapper getStatus(String uuid, String triggerName)
    {
        return SchedulerTaskWrapper.SCHEDULER_TASK_WRAPPER.apply(
                        schedulerUserTaskStorageService.getSchedulerTaskWithPlanExecutionDate(uuid))
                .getTrigger(triggerName);
    }

    @Override
    public List<ITriggerInfo> getTriggersInfo(String uuid, boolean onlyEnabled)
    {
        SchedulerTask task = schedulerUserTaskStorageService.getSchedulerTaskWithPlanExecutionDate(uuid);
        if (!tagService.isElementEnabled(task))
        {
            return new ArrayList<>();
        }

        List<Trigger> triggers = task.getTrigger();
        if (onlyEnabled)
        {
            return triggers.stream().filter(Trigger::isEnabled).map(TriggerInfo::new).collect(Collectors.toList());
        }
        else
        {
            return triggers.stream().map(TriggerInfo::new).collect(Collectors.toList());
        }
    }

    @Override
    public void interruptJob(String uuid)
    {
        ITriggerStatus trigger = findTriggerByUuid(uuid);

        interruptJob(trigger.getGroupName(), trigger.getJobName());
    }

    @Override
    @SuppressWarnings("java:S2629")
    public void interruptJob(String groupName, String jobName)
    {
        try
        {
            quartzSchedulerManager.interruptJob(groupName, jobName);
        }
        catch (SchedulerException e)
        {
            throw new FxException(messages.getMessage("scheduler.interruptingError", jobName), true, e);
        }

        LOG.info(messages.getMessage(
                "scheduler.successInterrupt"));
    }

    @Override
    public void recover()
    {
        oldApi.recover();
    }

    @Override
    public void resume()
    {
        try
        {
            quartzSchedulerManager.resumeScheduler();
        }
        catch (BeansException | SchedulerException e)
        {
            throw new FxException(messages.getMessage("scheduler.resumingError"), true, e);
        }

        LOG.info(messages.getMessage("scheduler.resumeSuccess")); //NOSONAR передаваемый аргумент не должен вычисляться
    }

    @Override
    public void run(String uuid)
    {
        quartzSchedulerManager.forceRunUserTask(uuid);
    }

    @Override
    public void setTriggerDate(String taskUuid, String triggerName, Date date)
    {
        validateDate(date);
        SchedulerTask task = schedulerUserTaskStorageService.getSchedulerTaskWithPlanExecutionDate(taskUuid);
        Trigger trigger = task.getTrigger(triggerName);

        updateTitle(trigger, date);

        if (trigger instanceof ConcreteDateTrigger concreteDateTrigger)
        {
            concreteDateTrigger.setExecutionDate(date);
            reschedule(task, trigger);
        }
        else
        {
            throw new FxException(messages.getMessage("scheduler.concreteDateError", triggerName), true);
        }

        LOG.info(messages.getMessage("scheduler.triggerChanged")); //NOSONAR передаваемый аргумент не должен вычисляться
    }

    @Override
    public void setTriggerInterval(String taskUuid, String triggerName, IDateTimeInterval interval, String strategy,
            Date startingDate)
    {
        SchedulerTask task = schedulerUserTaskStorageService.getSchedulerTaskWithPlanExecutionDate(taskUuid);
        Trigger trigger = task.getTrigger(triggerName);

        updateTitle(trigger, interval);

        CalculateStrategies str = getStrategy(strategy);
        if (trigger instanceof PeriodicTrigger periodicTrigger)
        {
            periodicTrigger.setInterval((DateTimeInterval)interval);
            periodicTrigger.setStrategy(str);
            periodicTrigger.setStartDate(startingDate);
            reschedule(task, trigger);
        }

        LOG.info(messages.getMessage("scheduler.triggerChanged")); //NOSONAR передаваемый аргумент не должен вычисляться
    }

    @Override
    public void setTriggerPeriod(String taskUuid, String triggerName, String period, String strategy, Date startingDate)
    {
        SchedulerTask task = schedulerUserTaskStorageService.getSchedulerTaskWithPlanExecutionDate(taskUuid);
        Trigger trigger = task.getTrigger(triggerName);

        updateTitle(trigger, period);

        Periods prd = getPeriod(period);

        CalculateStrategies str = getStrategy(strategy);

        if (trigger instanceof PeriodicTrigger periodicTrigger)
        {
            periodicTrigger.setPeriod(prd);
            periodicTrigger.setStrategy(str);
            periodicTrigger.setStartDate(startingDate);
            reschedule(task, trigger);
        }
        else
        {
            throw new FxException(messages.getMessage("scheduler.periodicTriggerError", triggerName), true);
        }

        LOG.info(messages.getMessage("scheduler.triggerChanged")); //NOSONAR передаваемый аргумент не должен вычисляться
    }

    @Override
    public void suspend(boolean force)
    {
        try
        {
            quartzSchedulerManager.suspendScheduler(force);
        }
        catch (SchedulerException | InterruptedException e)
        {
            throw new FxException(messages.getMessage("scheduler.suspendingError"), true, e);
        }

        LOG.info(messages.getMessage("scheduler.suspendSuccess")); //NOSONAR передаваемый аргумент не должен вычисляться
    }

    @Override
    @SuppressWarnings("java:S2629")
    public void deleteTask(String taskCode)
    {
        try
        {
            if (taskCode.startsWith(ReceiveMailTask.NAME))
            {
                inboundMailDao.deleteForTask(taskCode);
                LOG.info(messages.getMessage("scheduler.deletedMailsForTask",
                        taskCode));
            }
            SimpleResult<String> result = dispatch.execute(new DeleteSchTaskAction(Collections.singleton(taskCode)));
            if (null != result.get())
            {
                throw new ActionException(result.get());
            }
            LOG.info(messages.getMessage(
                    "scheduler.deleteSchedulerTaskSuccess"));
        }
        catch (DispatchException e)
        {
            throw new FxException(messages.getMessage("scheduler.deleteSchedulerTaskError"), true, e);
        }
    }

    @Override
    public void setDataSourceConnectionTimeout(int connectionTimeout)
    {
        schedulerProperties.setSchedulerDataSourceConnectionTimeout(connectionTimeout);
    }

    @Override
    public int getDataSourceConnectionTimeout()
    {
        return schedulerProperties.getSchedulerDataSourceConnectionTimeout();
    }

    private List<ITriggerStatus> findAllTriggersByUuid(String uuid)
    {
        return quartzSchedulerManager.getSchedulerTaskTriggerStatuses().stream()
                .filter(status -> status.getJobName().equals(uuid))
                .map(TRIGGER_STATUS_WRAPPER)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
    }

    private ITriggerStatus findTriggerByUuid(String uuid)
    {
        return findAllTriggersByUuid(uuid).iterator().next();
    }

    private Periods getPeriod(String period)
    {
        return switch (period.toLowerCase())
        {
            case "daily" -> Periods.DAILY;
            case "weekly" -> Periods.WEEKLY;
            case "monthly" -> Periods.MONTHLY;
            case "yearly" -> Periods.YEARLY;
            default -> throw new FxException(messages.getMessage("scheduler.invalidPeriod"), true);
        };
    }

    private CalculateStrategies getStrategy(String strategy)
    {
        return switch (strategy.toLowerCase())
        {
            case "from_start" -> CalculateStrategies.FROM_START;
            case "from_last_execution" -> CalculateStrategies.FROM_LAST_EXECUTION;
            default -> throw new FxException(messages.getMessage("scheduler.invalidStrategy"), true);
        };
    }

    private void reschedule(SchedulerTask task, Trigger trigger)
    {
        task.addTrigger(trigger);
        schedulerUserTaskStorageService.saveSchedulerTask(task);
        quartzSchedulerManager.resetUserTask(task, trigger);
    }

    private void updateTitle(Trigger trigger, Date date)
    {
        DateFormat df = new SimpleDateFormat("dd.MM.yyyy HH:mm");
        trigger.setTitle(messages.getMessage("scheduler.concreteDateTriggerPrefix") + df.format(date));
    }

    private void updateTitle(Trigger trigger, IDateTimeInterval interval)
    {
        String timeInterval = interval.getInterval().name();
        Long duration = interval.getLength();

        switch (timeInterval.toLowerCase())
        {
            case "second" -> trigger.setTitle(String.format(FORMAT_PATTERN, messages.getMessage(
                            SCHEDULER_PERIODIC_TRIGGER_PREFIX),
                    messages.getMessage("dateTimeInterval.SECOND", duration)));
            case "minute" -> trigger.setTitle(String.format(FORMAT_PATTERN, messages.getMessage(
                            SCHEDULER_PERIODIC_TRIGGER_PREFIX),
                    messages.getMessage("dateTimeInterval.MINUTE", duration)));
            case "hour" -> trigger.setTitle(String.format(FORMAT_PATTERN, messages.getMessage(
                            SCHEDULER_PERIODIC_TRIGGER_PREFIX),
                    messages.getMessage("dateTimeInterval.HOUR", duration)));
            default -> throw new IllegalStateException("Unexpected value: " + timeInterval.toLowerCase());
        }
    }

    private void updateTitle(Trigger trigger, String period)
    {
        switch (period.toLowerCase())
        {
            case "daily" -> trigger.setTitle(String.format(FORMAT_PATTERN, messages.getMessage(
                            SCHEDULER_PERIODIC_TRIGGER_PREFIX),
                    messages.getMessage("scheduler.daily")));
            case "weekly" -> trigger.setTitle(String.format(FORMAT_PATTERN, messages.getMessage(
                            SCHEDULER_PERIODIC_TRIGGER_PREFIX),
                    messages.getMessage("scheduler.weekly")));
            case "monthly" -> trigger.setTitle(String.format(FORMAT_PATTERN, messages.getMessage(
                            SCHEDULER_PERIODIC_TRIGGER_PREFIX),
                    messages.getMessage("scheduler.monthly")));
            case "yearly" -> trigger.setTitle(String.format(FORMAT_PATTERN, messages.getMessage(
                            SCHEDULER_PERIODIC_TRIGGER_PREFIX),
                    messages.getMessage("scheduler.yearly")));
            default -> throw new IllegalStateException("Unexpected value: " + period.toLowerCase());
        }
    }

    private void validateDate(Date date)
    {
        if (date.before(new Date()))
        {
            throw new FxException(messages.getMessage("scheduler.invalidDateError"), true);
        }
    }

}