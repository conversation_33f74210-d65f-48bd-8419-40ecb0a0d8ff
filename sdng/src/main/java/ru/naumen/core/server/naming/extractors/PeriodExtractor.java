package ru.naumen.core.server.naming.extractors;

import ru.naumen.core.server.naming.generators.PeriodicalIDGenerator;

/**
 * Определяет период для которого требуется сформировать значение. Используется 
 * для {@link PeriodicalIDGenerator}
 * <p>
 * Например, может извлекать дату создания объекта, чтобы сгенерированный номер
 * последовательности относился к конкретному дню.
 *
 * @param <T> тип объекта, для которого происходит определение периода.
 * <AUTHOR>
 *
 */
public interface PeriodExtractor<T>
{
    /**
     * @return текущий период (на основании текущей даты)
     */
    long currentPeriod();

    /**
     * Возвращает период к которому относится объект
     *
     * @param obj
     * @return порядковый номер периода
     */
    long extractPeriod(T obj);
}
