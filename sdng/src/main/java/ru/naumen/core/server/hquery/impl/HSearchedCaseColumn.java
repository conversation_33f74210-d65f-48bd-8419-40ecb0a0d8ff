package ru.naumen.core.server.hquery.impl;

import java.util.LinkedHashMap;

import jakarta.annotation.Nullable;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;

/**
 * Колонка для поискового выражения с условием<br>
 * Имеет HQL вид: case when ... then ... else ... end
 *
 * <AUTHOR>
 * @since 21.07.2021
 */
public class HSearchedCaseColumn extends AbstractHColumn
{
    private final LinkedHashMap<HCriterion, HColumn> conditions;
    private final HColumn elseResult;

    /**
     * Колонка для поискового выражения с условием
     * @param conditions условия в виде упорядоченного Map<условие фильтрации,
     *                   результат при выполнении условия в виде колонки
     * @param elseResult результат, когда не выполнилось ни одно условие
     * @param alias псевдоним
     */
    public HSearchedCaseColumn(LinkedHashMap<HCriterion, HColumn> conditions,
            HColumn elseResult, @Nullable String alias)
    {
        super(alias);
        this.conditions = conditions;
        this.elseResult = elseResult;
    }

    @Override
    public void setParameters(Query query)
    {
        conditions.forEach((criterion, column) ->
        {
            criterion.setParameters(query);
            column.setParameters(query);
        });
        elseResult.setParameters(query);
    }

    @Override
    public String getHQL(HBuilder builder)
    {
        StringBuilder sb = new StringBuilder("case");
        conditions.forEach((key, value) ->
                sb.append(" when ").append(key.getHQL(builder))
                        .append(" then ").append(value.getHQL(builder)));
        sb.append(" else ").append(elseResult.getHQL(builder));
        sb.append(" end");
        return sb.toString();
    }

    @Override
    public String toString()
    {
        return "HSearchedCaseColumn{" +
               "conditions=" + conditions +
               ", elseResult=" + elseResult +
               ", alias=" + getAlias() +
               '}';
    }
}
