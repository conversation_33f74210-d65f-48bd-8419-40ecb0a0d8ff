package ru.naumen.core.server.script.api.metainfo;

import java.util.Collection;
import java.util.function.Function;
import java.util.function.Predicate;

import com.google.common.collect.Collections2;

import ru.naumen.core.server.SpringContext;
import ru.naumen.core.server.script.ApiUtils;
import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.server.ClassMetainfoServiceException;
import ru.naumen.metainfo.shared.elements.adapters.TransitionAdapter;
import ru.naumen.metainfo.shared.elements.wf.State;
import ru.naumen.metainfo.shared.elements.wf.Transition;
import ru.naumen.metainfo.shared.elements.wf.Workflow;

/**
 * Обёртка жизненного цикла для использования в скриптах.
 *
 * <AUTHOR>
 *
 * @since 26.09.2012
 *
 */
public final class WorkflowWrapper implements IWorkflowWrapper
{
    private static final Predicate<State> STATE_ENABLED_PREDICATE = new Predicate<>()
    {
        private ApiUtils apiUtils;

        @Override
        public boolean test(State state)
        {
            ensureInitialized();
            return state.isEnabled() && apiUtils.isElementEnabled(state);
        }

        private void ensureInitialized()
        {
            if (null == apiUtils)
            {
                apiUtils = SpringContext.getInstance().getBean(ApiUtils.class);
            }
        }
    };

    public static final Function<Workflow, WorkflowWrapper> WRAPPER =
            input -> null == input ? null : new WorkflowWrapper(input);

    private final Workflow workflow;

    private WorkflowWrapper(Workflow workflow)
    {
        this.workflow = workflow;
    }

    @Override
    public IActionWrapper getAction(String stateCode, String actCode)
    {
        return ActionWrapper.WRAPPER.apply(workflow.getAction(stateCode, actCode));
    }

    @Override
    public IConditionWrapper getCondition(String stateCode, String conditionCode)
    {
        return ConditionWrapper.WRAPPER.apply(workflow.getCondition(stateCode, conditionCode));
    }

    @Override
    public IStateWrapper getEndState()
    {
        return StateWrapper.WRAPPER.apply(workflow.getEndState());
    }

    @Override
    public IStateWrapper getOriginalState()
    {
        return StateWrapper.WRAPPER.apply(workflow.getOriginalState());
    }

    @Override
    public IStateWrapper getState(String code)
    {
        return StateWrapper.WRAPPER.apply(workflow.getState(code));
    }

    @Override
    public Collection<IStateWrapper> getStates()
    {
        return Collections2.transform(workflow.getStates(), StateWrapper.WRAPPER::apply);
    }

    @Override
    public ITransitionWrapper getTransition(String from, String to)
    {
        return TransitionWrapper.WRAPPER.apply(workflow.getTransition(from, to));
    }

    @Override
    public Collection<ITransitionWrapper> getTransitions()
    {
        return Collections2.transform(workflow.getActiveTransitions(), TransitionWrapper.WRAPPER);
    }

    @Override
    public boolean isInherit()
    {
        return workflow.isInherit();
    }

    @Override
    public boolean isTransitionExists(IStateWrapper beginState, IStateWrapper endState)
    {
        return isTransitionExists(beginState.getCode(), endState.getCode());
    }

    @Override
    public boolean isTransitionExists(String beginStateCode, String endStateCode)
    {
        checkStates(beginStateCode, endStateCode);
        State endState = this.workflow.getState(endStateCode);
        if (!STATE_ENABLED_PREDICATE.test(endState))
        {
            return false;
        }
        Transition transition = TransitionAdapter.process(this.workflow, beginStateCode, endStateCode);
        return transition != null && transition.isEnabled();
    }

    /**
     * Проверить существование статусов
     * @throws ClassMetainfoServiceException в случае, если статуса не существует
     */
    private void checkStates(String... stateCodes)
    {
        Collection<String> states = workflow.getStates().stream().map(HasCode::getCode).toList();
        for (String state : stateCodes)
        {
            this.workflow.checkStateExists(state, states);
        }
    }

    @Override
    public String toString()
    {
        return "Workflow ---";
    }

}
