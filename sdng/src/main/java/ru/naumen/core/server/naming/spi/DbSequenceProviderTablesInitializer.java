package ru.naumen.core.server.naming.spi;

import java.sql.Connection;
import java.sql.SQLException;

import jakarta.inject.Inject;
import jakarta.inject.Named;

import javax.sql.DataSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.hibernate.DDLTool;
import ru.naumen.core.server.hibernate.column.ColumnDescriptions;
import ru.naumen.core.server.hibernate.constraint.ComplexPrimaryKey;
import ru.naumen.core.server.hibernate.table.TableDescription;
import ru.naumen.core.server.naming.seq.providers.SequenceProvider;

/**
 * Бин инициализации таблиц для некэширующего {@link SequenceProvider}.
 *
 * <AUTHOR>
 * @since Jan 14, 2016
 *
 */
@Component
@Lazy
public class DbSequenceProviderTablesInitializer
{
    private static final Logger LOG = LoggerFactory.getLogger(DbSequenceProviderTablesInitializer.class);
    private final DataSource sequenceDataSource;

    /**
     * Инициализация таблиц для некэширующего {@link SequenceProvider}.
     *
     * @param sequenceDataSource источник данных для некэширующего {@link SequenceProvider}.
     */
    @Inject
    public DbSequenceProviderTablesInitializer(@Named("sequenceDataSource") DataSource sequenceDataSource)
    {
        this.sequenceDataSource = sequenceDataSource;
        createReturnedValuesTable();
    }

    /**
     * Создаёт таблицу с возвращёнными значениями, если таковой ещё не существует.
     */
    private void createReturnedValuesTable()
    {
        try (Connection connection = sequenceDataSource.getConnection())
        {
            DDLTool ddlTool = new DDLTool(connection);
            if (!ddlTool.tableExists("tbl_sys_returned_values"))
            {
                final TableDescription tableDescription = new TableDescription("tbl_sys_returned_values");
                tableDescription.addColumn(ColumnDescriptions.string("sequence_id"));
                tableDescription.addColumn(ColumnDescriptions.bigint("period"));
                tableDescription.addColumn(ColumnDescriptions.integer("value")
                        .addConstraint(new ComplexPrimaryKey("sequence_id", "period", "value")));
                ddlTool.createTable(tableDescription);
            }
        }
        catch (SQLException e)
        {
            LOG.error("Exception during retrieval of a connection from datasource.", e);
            throw new FxException(e);
        }
    }
}