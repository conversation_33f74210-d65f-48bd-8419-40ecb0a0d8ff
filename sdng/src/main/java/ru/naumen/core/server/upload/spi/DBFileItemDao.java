package ru.naumen.core.server.upload.spi;

import static ru.naumen.core.server.hquery.HRestrictions.le;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.sql.Blob;
import java.time.Duration;
import java.time.Instant;
import java.util.Date;
import java.util.List;

import org.apache.commons.fileupload2.core.FileItem;
import org.apache.commons.io.IOUtils;
import org.hibernate.BlobProxy;
import org.hibernate.LobHelper;
import org.hibernate.Session;
import org.hibernate.SessionFactory;
import org.hibernate.metamodel.MappingMetamodel;
import org.hibernate.persister.entity.EntityPersister;
import org.hibernate.persister.entity.SingleTableEntityPersister;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.filestorage.MimeTypesUtils;
import ru.naumen.core.server.filestorage.spi.storages.FileHash;
import ru.naumen.core.server.hibernate.DeletePGBlobInterceptor;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HHelper;
import ru.naumen.core.server.upload.ResettableFileInputStream;

/**
 * DAO для работы с {@link DBFileItem}
 *
 * <AUTHOR>
 */
@Component
public class DBFileItemDao
{
    private static final Logger LOG = LoggerFactory.getLogger(DBFileItemDao.class);

    private final SessionFactory sessionFactory;
    private final DeletePGBlobInterceptor deletePGBlobInterceptor;

    @Inject
    public DBFileItemDao(final SessionFactory sessionFactory, final DeletePGBlobInterceptor deletePGBlobInterceptor)
    {
        this.sessionFactory = sessionFactory;
        this.deletePGBlobInterceptor = deletePGBlobInterceptor;
    }

    public void delete(final String uuid, final boolean reuse)
    {
        final Session session = getSession();
        session.doWork(connection ->
        {
            LOG.info("Removing DBFileItem " + uuid + " ...");
            String tableName = getDBFileTableName(session);
            if (!reuse)
            {
                deletePGBlobInterceptor.collectAndDelete(session, connection, "uuid", uuid, DBFileItem.class);
            }

            try (final var psmt = connection.prepareStatement("delete from " + tableName + " where uuid=?"))
            {
                psmt.setString(1, uuid);
                psmt.execute();
            }
            LOG.info("DBFileItem " + uuid + " removal complete.");
        });
    }

    public FileItem get(String uuid)
    {
        return getSession().get(DBFileItem.class, uuid);
    }

    /**
     * Получаем имя таблицы в которой хранятся DBFileItem
     */
    private static String getDBFileTableName(final Session session)
    {
        EntityPersister clsMD = ((MappingMetamodel)session.getSessionFactory().getMetamodel())
                .getEntityDescriptor(DBFileItem.class);
        return ((SingleTableEntityPersister)clsMD).getTableName();
    }

    /**
     * Возвращается коллекция uuid'ов файлов, каждый из которых хрянится в системе более чем {@code
     * hoursSinceUpload} часов
     *
     * @param offset     с которого начать выборку
     * @param maxResults максимальное количество в выборке
     * @param hoursSinceUpload кол-во часов, прошедшее с момента загрузки файла
     */
    @SuppressWarnings("unchecked")
    public List<String> getOverdueUploadedFilesUuids(int offset, int maxResults, int hoursSinceUpload)
    {
        HCriteria criteria = getOverdueUploadedFilesCriteria(hoursSinceUpload);
        criteria.addColumn(criteria.getProperty("uuid"));
        criteria.setFirstResult(offset);
        criteria.setMaxResults(maxResults);
        return criteria.createQuery(getSession()).list();
    }

    public void save(String uuid, File file)
    {
        try (FileInputStream fis = new FileInputStream(file))
        {
            long size = fis.getChannel().size();
            DBFileItem dbItem = new DBFileItem();
            dbItem.setContentType(MimeTypesUtils.resolveMimeType(file));
            dbItem.setFieldName("");
            dbItem.setFormField(false);
            dbItem.setName(file.getName());
            dbItem.setSize(size);
            dbItem.setUuid(uuid);
            dbItem.setContent(BlobProxy.generateProxy(new ResettableFileInputStream(fis), size));
            getSession().persist(dbItem);
            getSession().flush();
        }
        catch (IOException e)
        {
            throw new FxException(e);
        }
    }

    public void save(final UploadFileItemContext uploadFileItemContext, @Nullable final FileHash fileHash)
    {
        final var item = uploadFileItemContext.getFileItem();

        final DBFileItem dbItem = new DBFileItem();
        dbItem.setContentType(item.getContentType());
        dbItem.setFieldName(item.getFieldName());
        dbItem.setFormField(item.isFormField());
        dbItem.setName(item.getName());
        dbItem.setSize(item.getSize());
        dbItem.setUuid(uploadFileItemContext.getUuid());
        dbItem.setStorageId(uploadFileItemContext.getStorageCode());
        dbItem.setCompressed(uploadFileItemContext.isCompress());
        dbItem.setFileHash(fileHash);

        if (uploadFileItemContext.getStorageCode() == null && !uploadFileItemContext.isFileStorageDirectUpload())
        {
            dbItem.setContent(createBlob(item));
        }
        getSession().persist(dbItem);
        getSession().flush();
    }

    private Blob createBlob(FileItem item) // NOPMD
    {
        try
        {
            final LobHelper lobHelper = getSession().getLobHelper();
            if (item.isInMemory())
            {
                return lobHelper.createBlob(item.get());
            }
            else
            {
                try (InputStream itemStream = item.getInputStream())
                {
                    return itemStream instanceof FileInputStream fileInputStream
                            ? BlobProxy.generateProxy(new ResettableFileInputStream(fileInputStream), item.getSize())
                            : lobHelper.createBlob(IOUtils.toByteArray(itemStream));
                }
            }
        }
        catch (IOException e)
        {
            throw new FxException(e);
        }
    }

    private Session getSession()
    {
        return sessionFactory.getCurrentSession();
    }

    private static HCriteria getOverdueUploadedFilesCriteria(int hoursSinceUpload)
    {
        Date overdueDate = Date.from(Instant.now().minus(Duration.ofHours(hoursSinceUpload)));
        HCriteria criteria = HHelper.create().addSource(DBFileItem.class.getName());
        criteria.add(le(criteria.getProperty("date"), overdueDate));
        return criteria;
    }
}
