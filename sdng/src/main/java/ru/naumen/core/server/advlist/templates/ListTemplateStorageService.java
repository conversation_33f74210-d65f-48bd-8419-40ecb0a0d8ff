package ru.naumen.core.server.advlist.templates;

import java.util.List;

import ru.naumen.core.server.cache.CoreClusterCacheService;
import ru.naumen.metainfo.shared.templates.list.ListTemplate;

/**
 * Хранилище шаблонов списков
 * <AUTHOR>
 * @since 06.04.2018
 */
public interface ListTemplateStorageService extends CoreClusterCacheService
{
    /**
     * Сохраняет новый шаблон списка. Проверяет код шаблона на уникальность.
     * @param template шаблон списка
     */
    void addTemplate(ListTemplate template);

    /**
     * Удаляет шаблоны списков с указанными кодами.
     * @param codes коды шаблонов списков
     */
    void deleteTemplates(String... codes);

    /**
     * Возвращает все имеющиеся в системе шаблоны списков.
     * @return все шаблоны списков
     */
    List<ListTemplate> getAll();

    /**
     * Возвращает шаблон списка, имеющий указанный код.
     * @param code код шаблона
     * @return шаблон списка или null, если шаблона с указанным кодом не существует
     */
    ListTemplate getTemplate(String code);

    /**
     * Сохраняет указанный шаблон списка.
     * @param template шаблон списка
     */
    void saveTemplate(ListTemplate template);
}
