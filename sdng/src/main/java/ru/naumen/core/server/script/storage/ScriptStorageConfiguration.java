package ru.naumen.core.server.script.storage;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import jakarta.inject.Inject;
import ru.naumen.commons.server.utils.XmlUtils;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.metastorage.JaxbStorageSerializer;
import ru.naumen.core.server.metastorage.StorageSerializer;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.script.modules.storage.ScriptStorageContainer;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Настройки скриптохранилища
 *
 * <AUTHOR>
 * @since Sep 14, 2015
 */
@Configuration
public class ScriptStorageConfiguration
{
    @Deprecated
    public static final String STORAGE_KEY = "default";
    @Deprecated
    public static final String STORAGE_TYPE = "scriptStorage";

    public static final String SCRIPT_TYPE = "script";
    public static final String SCRIPT_MODULE_TYPE = "scriptModule";

    @Inject
    private XmlUtils xmlUtils;
    @Inject
    private MessageFacade messages;
    @Inject
    private ConfigurationProperties configurationProperties;

    @Bean
    public StorageSerializer<ScriptModule> getScriptModuleSerializer()
    {
        JaxbStorageSerializer<ScriptModule> serializer = new JaxbStorageSerializer<ScriptModule>();
        serializer.setMetaStorageType(SCRIPT_MODULE_TYPE);
        serializer.setJaxbPackage(ScriptModule.class.getPackage().getName());
        serializer.setXmlUtils(xmlUtils);
        serializer.setMessageFacade(messages);
        serializer.setConfigurationProperties(configurationProperties);
        return serializer;
    }

    @Bean
    public StorageSerializer<Script> getScriptSerializer()
    {
        JaxbStorageSerializer<Script> serializer = new JaxbStorageSerializer<Script>();
        serializer.setMetaStorageType(SCRIPT_TYPE);
        serializer.setJaxbPackage(Script.class.getPackage().getName());
        serializer.setXmlUtils(xmlUtils);
        serializer.setMessageFacade(messages);
        serializer.setConfigurationProperties(configurationProperties);
        return serializer;
    }

    @SuppressWarnings("deprecation")
    @Bean
    public StorageSerializer<ScriptStorageContainer> getScriptStorageSerializer()
    {
        JaxbStorageSerializer<ScriptStorageContainer> serializer = new JaxbStorageSerializer<ScriptStorageContainer>();
        serializer.setMetaStorageType(STORAGE_TYPE);
        serializer.setJaxbPackage(ScriptStorageContainer.class.getPackage().getName());
        serializer.setXmlUtils(xmlUtils);
        serializer.setMessageFacade(messages);
        serializer.setConfigurationProperties(configurationProperties);
        return serializer;
    }
}
