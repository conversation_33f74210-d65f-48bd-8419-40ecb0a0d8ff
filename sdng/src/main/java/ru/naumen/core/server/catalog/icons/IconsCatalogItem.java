package ru.naumen.core.server.catalog.icons;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Inheritance;
import jakarta.persistence.InheritanceType;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.catalog.CatalogItem;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.IconsCatalog;
import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.annotations.Group;
import ru.naumen.metainfo.server.annotations.Groups;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttrGroup;

/**
 * Базовый функционал элемента справочников "Иконки для элементов управления"
 *
 * <AUTHOR>
 * @since 04.03.2021
 */
//@formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Table(name = IconsCatalogItem.TABLE, uniqueConstraints = {
        @UniqueConstraint(name = IconsCatalogItem.CODE_UNIQUE_CONSTRAINT, columnNames = {"code"})},
        indexes={@jakarta.persistence.Index(name = "idx_icons_parent", columnList="parent")})
@Inheritance(strategy = InheritanceType.SINGLE_TABLE)
@DiscriminatorFormula("case_id")
@DiscriminatorValue("-")
@Cacheable
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(IconsCatalog.ITEM_CLASS_ID)
@Metaclass(id = IconsCatalog.ITEM_CLASS_ID,
        hasSecDomain = false,
        title = { @LStr(value = "Элемент справочника 'Иконки для элементов управления'"),
                @LStr(lang = "en", value = "'Icons for controls' catalog item"),
                @LStr(lang = "de", value = "Katalogartikel 'Symbole für Steuerelemente'")}
)
@Groups({
        @Group(code = AttrGroup.DISPLAY_GROUP, attrs = {"title", "code"}),
        @Group(code = AttrGroup.EXCLUDE_DISPLAY_GROUP, attrs = {"color"}),
        @Group(code = AttrGroup.SYSTEM, attrs = { "code", "folder", "parent" , "system"})
})
//@formatter:on
public class IconsCatalogItem extends CatalogItem<IconsCatalogItem>
{
    public static final String TABLE = "tbl_sys_icon";
    public static final String CODE_UNIQUE_CONSTRAINT = TABLE + "_code_key";

    @Column(name = "system")
    @Attribute(code = Constants.CatalogItem.ITEM_IS_SYSTEM,
            editable = false,
            title = { @LStr(value = "Системный"), @LStr(lang = "en", value = "System") },
            example = @LStr(value = "false"))
    private Boolean system = false;

    @Column(name = "case_id", nullable = false)
    private String metaCaseId;

    // Это свойство перемещено из IconsForControlsCatalogItem для корректной работы hibernate
    @Deprecated
    @Column(name = "correctColor", nullable = false)
    @Attribute(code = Constants.IconsForControlsCatalog.CORRECT_COLOR, editable = true, title = {
            @LStr(value = "Откорректировать цвет изображения"),
            @LStr(lang = "en", value = "Adjust image color"),
            @LStr(lang = "de", value = "Adjust image color") }, example = @LStr(value = "true"))
    private boolean correctColor = false;

    @Override
    public ClassFqn getMetaClass()
    {
        return IconsCatalog.ITEM_FQN;
    }

    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return IconsCatalog.ITEM_CLASS_ID;
    }

    public static String getUUIDPrefix()
    {
        return IconsCatalog.ITEM_CLASS_ID;
    }

    public Boolean isSystem()
    {
        return system;
    }

    public void setSystem(Boolean system)
    {
        this.system = system;
    }

    public String getMetaCaseId()
    {
        return metaCaseId;
    }

    public void setMetaCaseId(String metaCaseId)
    {
        this.metaCaseId = metaCaseId;
    }

    @Deprecated
    public boolean getCorrectColor()
    {
        return correctColor;
    }

    @Deprecated
    public void setCorrectColor(Boolean correctColor)
    {
        this.correctColor = Boolean.TRUE.equals(correctColor);
    }

    @Override
    public String toString()
    {
        return getMetaCaseId() + ":" + getUUID();
    }
}
