package ru.naumen.core.server.hquery.criterion;

import java.util.Objects;

import org.hibernate.query.Query;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.impl.AbstractHCriterion;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;

/**
 * <AUTHOR>
 * @since 05.06.2007 17:24:09
 */
public class RangeCriterion extends AbstractHCriterion
{
    private static final String AND = " AND "; //$NON-NLS-1$
    private static final String TRUE_CONDITION = " 1=1 "; //$NON-NLS-1$
    private Object minValue = null;
    private Object maxValue = null;
    private boolean strictMode = true;

    private String minValueName;
    private String maxValueName;

    public RangeCriterion(HColumn property, Object minValue, Object maxValue, boolean strictMode)
    {
        super(property);
        this.minValue = minValue;
        this.maxValue = maxValue;
        this.strictMode = strictMode;
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder,
            NameGenerator parameterCounter)
    {
        this.minValueName = parameterCounter.next();
        this.maxValueName = parameterCounter.next();

        sb.append(" ( "); //$NON-NLS-1$

        if (isMinValue())
        {
            sb.append(property.getHQL(builder));
            sb.append('>');
            if (strictMode)
            {
                sb.append('=');
            }
            sb.append(':');
            sb.append(minValueName);
            sb.append(' ');
        }

        if (isMinValue() && isMaxValue())
        {
            sb.append(AND);
        }

        if (isMaxValue())
        {
            sb.append(property.getHQL(builder));
            sb.append('<');
            if (strictMode)
            {
                sb.append('=');
            }
            sb.append(':');
            sb.append(maxValueName);
        }

        if (!isMinValue() && !isMaxValue())
        {
            sb.append(TRUE_CONDITION);
        }

        sb.append(" ) "); //$NON-NLS-1$
    }

    @Override
    public void setParameters(Query q)
    {
        super.setParameters(q);
        if (isMinValue())
        {
            q.setParameter(minValueName, minValue);
        }
        if (isMaxValue())
        {
            q.setParameter(maxValueName, maxValue);
        }
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new RangeCriterion(property, minValue, maxValue, strictMode);
    }

    private boolean isMaxValue()
    {
        return maxValue != null;
    }

    private boolean isMinValue()
    {
        return minValue != null;
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        RangeCriterion that = (RangeCriterion)o;
        return strictMode == that.strictMode &&
               Objects.equals(minValue, that.minValue) &&
               Objects.equals(maxValue, that.maxValue);
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), minValue, maxValue, strictMode);
    }

    @Override
    public String toString()
    {
        return "RangeCriterion{" +
               "minValue=" + minValue +
               ", maxValue=" + maxValue +
               ", strictMode=" + strictMode +
               ", minValueName='" + minValueName + '\'' +
               ", maxValueName='" + maxValueName + '\'' +
               ", property=" + property +
               '}';
    }
}
