package ru.naumen.core.server.catalog;

import ru.naumen.common.shared.SnapshotTransient;
import ru.naumen.core.server.IDisplayableTitled;
import ru.naumen.core.server.objectloader.IPrefixObjectLoaderUUIDIdentifiable;
import ru.naumen.core.shared.IChild;
import ru.naumen.core.shared.ICoreRemovable;
import ru.naumen.core.shared.IHasMetaInfo;
import ru.naumen.core.shared.ILocalizedTitle;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.metainfo.shared.elements.HasCodeAndTitle;
import ru.naumen.metainfo.shared.elements.HasElementId;
import ru.naumen.metainfo.shared.sets.HasSettingsSet;

/**
 * Интерфейс общего элемента справочников.
 * Любой элемент справочника должен реализовывать этот интерфейс.
 * Под элементами справочника в данном случае подразумеваются бизнес сущности,
 * которые редко изменяются и "срок жизни" которых сопоставим со сроком службы приложения
 *
 * @param <T> тип реализация элемента
 * <AUTHOR>
 * @since 24.12.2010
 */
public interface ICatalogItem<T extends ICatalogItem<T>> extends IUUIDIdentifiable, IDisplayableTitled,
        HasCodeAndTitle, ILocalizedTitle, ICoreRemovable, IPrefixObjectLoaderUUIDIdentifiable, IHasMetaInfo, IChild<T>,
        SnapshotTransient, HasSettingsSet, HasElementId
{

    /**
     *
     * @return родительский элемент (item или folder)
     */
    @Override
    T getParent();

    /**
     * Порядковый номер данного элемента
     * среди сёстринских элементов
     * @return порядковый номер
     */
    long getPosition();

    /**
     * Признак того, является ли данный элемент папкой справочника
     * @return
     */
    boolean isFolder();

    /**
     * Установка кода элемента справочника
     * @param code код элемента справочника
     */
    void setCode(String code);

    /**
     * Установка признака того, что данный элемент
     * является папкой справочника
     * @param folder
     */
    void setFolder(boolean folder);

    /**
     * Установка родительского элемента.
     * @param parent
     */
    @Override
    void setParent(T parent);

    /**
     * Установка порядкового номера элемента
     * среди сестринских элементов
     * @param position порядковый номер
     */
    void setPosition(long position);

}
