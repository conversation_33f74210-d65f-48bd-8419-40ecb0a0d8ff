package ru.naumen.core.server.timing.calculate;

import java.util.Calendar;
import java.util.Date;
import java.util.TimeZone;

import org.springframework.context.annotation.Scope;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.commons.server.utils.DateUtils;
import ru.naumen.core.server.catalog.servicetime.ServiceTimeCatalogItem;
import ru.naumen.core.server.catalog.servicetime.ServiceTimeCatalogUtils;
import ru.naumen.core.server.timing.calculate.ServiceTimeCalculator.TimeShifting;

/**
 * Реализация {@link IDateWithServiceTimeOperationsHelper}
 *
 * <AUTHOR>
 * @since 27.06.2018
 */
@Component
@Scope(value = "prototype")
public class DateWithServiceTimeOperationsHelper implements IDateWithServiceTimeOperationsHelper
{
    @Inject
    private ServiceTimeCatalogUtils serviceTimeUtils;

    private ServiceTimeCatalogItem serviceTime;

    private TimeZone timeZone;

    @Override
    public Date addWorkingDays(@Nullable Date date, int amountOfDays)
    {
        setServerTimeZoneIfNull(timeZone);
        TimeShifting timeShifting = getTimeShifting(amountOfDays);
        Date preparedDate = getCurrentDateIfNull(date);
        preparedDate = isServiceTimeInDay(preparedDate, timeShifting)
                ? preparedDate
                : new Date(getNearestServiceTimeFromDayBorder(preparedDate, timeShifting).getTime()
                           - timeShifting.getValue());
        Date targetDayDate = findTargetDay(preparedDate, amountOfDays, timeShifting);
        long serviceEndTime = getServiceEndTime(targetDayDate, timeShifting.getValue()).getTime();
        return new Date(serviceEndTime - timeShifting.getValue());
    }

    @Override
    public Date addWorkingHours(Date date, int amountOfHours)
    {
        final long MSEC_PER_HOUR = 3600000;
        setServerTimeZoneIfNull(timeZone);
        return getServiceEndTime(getCurrentDateIfNull(date), amountOfHours * MSEC_PER_HOUR);
    }

    @Nullable
    @Override
    public Date getEndOfWorkingCalendarField(@Nullable final Date date, int field)
    {
        setServerTimeZoneIfNull(timeZone);
        Date preparedDate = getCurrentDateIfNull(date);
        Date serviceEndDate = getServiceEndTime(
                DateUtils.getLastSecondOfCalendarField(preparedDate, field, timeZone), TimeShifting.toLeft.getValue());
        //По какой-то причине ожидается в одном из тестов, например не 14:59:59 (последняя рабочая секунда), а 15:00:00,
        // по этой причине вычитается TimeShifting.toLeft.getValue()
        Date resultDate = new Date(serviceEndDate.getTime() - TimeShifting.toLeft.getValue());
        return DateUtils.isCalendarFieldEquals(preparedDate, serviceEndDate, field, timeZone)
                ? resultDate
                : null;
    }

    @Override
    public ServiceTimeCatalogItem getServiceTime()
    {
        return serviceTime;
    }

    @Nullable
    @Override
    public Date getStartOfWorkingCalendarField(@Nullable final Date date, int field)
    {
        setServerTimeZoneIfNull(timeZone);
        Date preparedDate = getCurrentDateIfNull(date);
        Date serviceStartDate = getServiceStartTime(
                DateUtils.getFirstSecondOfCalendarField(preparedDate, field, timeZone));
        return DateUtils.isCalendarFieldEquals(preparedDate, serviceStartDate, field, timeZone)
                ? serviceStartDate
                : null;
    }

    @Override
    @Nullable
    public TimeZone getTimeZone()
    {
        return timeZone;
    }

    @Override
    public void setServiceTime(ServiceTimeCatalogItem serviceTime)
    {
        this.serviceTime = serviceTime;
    }

    @Override
    public void setTimeZone(TimeZone timeZone)
    {
        this.timeZone = timeZone;
    }

    private Date findTargetDay(Date date, int amountOfDays, TimeShifting timeShifting)
    {
        int absAmountOfDays = Math.abs(amountOfDays);
        Date nextDayDate = date;
        while (absAmountOfDays > 0)
        {
            nextDayDate = DateUtils.addDays(nextDayDate, timeShifting.getValue());
            if (isServiceTimeInDay(nextDayDate, timeShifting))
            {
                --absAmountOfDays;
            }
        }
        return nextDayDate;
    }

    private Date getCurrentDateIfNull(@Nullable Date date)
    {
        if (date == null)
        {
            return DateUtils.createCalendar(new Date(), timeZone).getTime();
        }
        return date;
    }

    /**
     * Возвращает ближайшее время начала сервисного обслуживания
     *
     * @param date дата, относительно которой происходит поиск ближайшего времени сервисного обслуживания
     * @param timeShifting направление смещения по временной оси
     * @return ближайшее время сервисного обслуживания
     */
    private Date getNearestServiceTimeFromDayBorder(Date date, TimeShifting timeShifting)
    {
        ITimingCalculator calculator = serviceTimeUtils.getCalculator(timeZone, serviceTime);
        if (TimeShifting.toRight == timeShifting)
        {
            calculator.setStartDate(DateUtils.getFirstSecondOfCalendarField(date, Calendar.DAY_OF_WEEK, timeZone));
        }
        else
        {
            calculator.setStartDate(DateUtils.getLastSecondOfCalendarField(date, Calendar.DAY_OF_WEEK, timeZone));
        }
        calculator.setServiceTime(timeShifting.getValue());
        return calculator.getServiceEndDate();
    }

    /**
     * Возвращает ближайшую дату сервисного времени окончания периода обслуживания
     */
    private Date getServiceEndTime(Date date, long serviceTimeLength)
    {
        ITimingCalculator calculator = serviceTimeUtils.getCalculator(timeZone, serviceTime);
        calculator.setStartDate(date);
        calculator.setServiceTime(serviceTimeLength);
        return calculator.getServiceEndDate();
    }

    /**
     * Возвращает ближайшую дату сервисного времени начала периода обслуживания
     */
    private Date getServiceStartTime(Date date)
    {
        ITimingCalculator calculator = serviceTimeUtils.getCalculator(timeZone, serviceTime);
        calculator.setStartDate(date);
        calculator.setServiceTime(0);
        return calculator.getServiceStartDate();
    }

    private static TimeShifting getTimeShifting(int amountOfDays)
    {
        return amountOfDays < 0 ? TimeShifting.toLeft : TimeShifting.toRight;
    }

    private boolean isServiceTimeInDay(Date date, TimeShifting timeShifting)
    {
        Date startServiceInterval = getNearestServiceTimeFromDayBorder(date, timeShifting);
        Date targetDate = DateUtils.getFirstSecondOfCalendarField(date, Calendar.DAY_OF_WEEK, timeZone);
        return DateUtils.isCalendarFieldEquals(startServiceInterval, targetDate, Calendar.DAY_OF_YEAR, timeZone);
    }

    private void setServerTimeZoneIfNull(@Nullable TimeZone timeZone)
    {
        if (null == timeZone)
        {
            this.timeZone = TimeZone.getDefault();
        }
    }
}
