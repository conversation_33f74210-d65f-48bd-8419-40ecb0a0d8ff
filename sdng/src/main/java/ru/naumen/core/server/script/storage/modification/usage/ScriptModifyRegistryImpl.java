package ru.naumen.core.server.script.storage.modification.usage;

import java.util.List;

import org.springframework.stereotype.Component;

import java.util.ArrayList;

import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import ru.naumen.core.server.customforms.FormParameter;
import ru.naumen.core.shared.settings.SCParameters;
import ru.naumen.core.shared.timer.definition.TimerDefinition;
import ru.naumen.mailreader.shared.receiver.MailProcessorRule;
import ru.naumen.metainfo.server.spi.elements.AbstractAttributeInfo;
import ru.naumen.metainfo.shared.elements.sec.AccessMatrix;
import ru.naumen.metainfo.shared.elements.sec.Role;
import ru.naumen.metainfo.shared.elements.wf.WfActionCondition;
import ru.naumen.metainfo.shared.embeddedapplication.EmbeddedApplication;
import ru.naumen.metainfo.shared.eventaction.ActionCondition;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.mobile.addforms.AddForm;
import ru.naumen.metainfo.shared.scheduler.ExecuteScriptTask;
import ru.naumen.metainfo.shared.ui.FilterRestrictionStrategy;
import ru.naumen.reports.shared.ReportTemplate;

/**
 * Реестр всех контрактов редактирования скриптов, способен по классу Т 
 * передаваемого объекта определить требуемый контракт.
 * <AUTHOR>
 * @since Oct 23, 2015
 */
@Component
public class ScriptModifyRegistryImpl implements ScriptModifyRegistry
{
    private static class RegistryElement<T>
    {
        public Class<T> clazz;
        public ScriptModifyProcess<T> process;

        public RegistryElement(Class<T> clazz, ScriptModifyProcess<T> process)
        {
            this.clazz = clazz;
            this.process = process;
        }
    }

    @Inject
    private MailScriptModifyProcess mailRuleStrategy;
    @Inject
    private ApplicationScriptModifyProcess applicationRuleStrategy;
    @Inject
    private AttributeScriptModifyProcess attributeStrategy;
    @Inject
    private ScParamsScriptModifyProcess scParamsStrategy;
    @Inject
    private WfActionConditionScriptModifyProcess workflowStrategy;
    @Inject
    private ExecuteScriptTaskScriptModifyProcess executeScriptTaskStrategy;
    @Inject
    private ReportTemplateScriptModifyProcess reportTemplateStrategy;
    @Inject
    private AccessMatrixScriptModifyProcess accessMatrixStrategy;
    @Inject
    private RoleScriptModifyProcess roleStrategy;
    @Inject
    private EventActionScriptModifyProcess eventActionScriptModifyProcess;
    @Inject
    private ActionConditionScriptModifyProcess actionConditionScriptModifyProcess;
    @Inject
    private TimerDefinitionScriptModifyProcess timerDefinitionStrategy;
    @Inject
    private FormParameterScriptModifyProcess formParameterScriptModifyProcess;
    @Inject
    private FilterRestrictionScriptModifyProcess filterRestrictionStrategy;
    @Inject
    private AddFormScriptModifyProcess addFormScriptModifyProcess;

    private List<RegistryElement<?>> registryData;

    @SuppressWarnings("unchecked")
    @Override
    public <T> ScriptModifyProcess<T> getProcess(T object)
    {
        for (RegistryElement<?> element : registryData)
        {
            if (element.clazz.isInstance(object))
            {
                return (ScriptModifyProcess<T>)element.process;
            }
        }
        return null;
    }

    @PostConstruct
    protected void init()
    {
        registryData = new ArrayList<>();
        registryData.add(new RegistryElement<>(MailProcessorRule.class, mailRuleStrategy));
        registryData.add(new RegistryElement<>(EmbeddedApplication.class, applicationRuleStrategy));
        registryData.add(new RegistryElement<>(AbstractAttributeInfo.class, attributeStrategy));
        registryData.add(new RegistryElement<>(SCParameters.class, scParamsStrategy));
        registryData.add(new RegistryElement<>(WfActionCondition.class, workflowStrategy));
        registryData.add(new RegistryElement<>(ExecuteScriptTask.class, executeScriptTaskStrategy));
        registryData.add(new RegistryElement<>(ReportTemplate.class, reportTemplateStrategy));
        registryData.add(new RegistryElement<>(AccessMatrix.class, accessMatrixStrategy));
        registryData.add(new RegistryElement<>(Role.class, roleStrategy));
        registryData.add(new RegistryElement<>(EventAction.class, eventActionScriptModifyProcess));
        registryData
                .add(new RegistryElement<>(ActionCondition.class, actionConditionScriptModifyProcess));
        registryData.add(new RegistryElement<>(TimerDefinition.class, timerDefinitionStrategy));
        registryData.add(new RegistryElement<>(FormParameter.class, formParameterScriptModifyProcess));
        registryData.add(
                new RegistryElement<>(FilterRestrictionStrategy.class,
                        filterRestrictionStrategy));
        registryData.add(new RegistryElement<>(AddForm.class, addFormScriptModifyProcess));
    }
}
