package ru.naumen.core.server.hquery.impl.source;

import org.hibernate.query.Query;
import org.hibernate.Session;

import ru.naumen.core.server.hquery.AfterQueryHandler;
import ru.naumen.core.server.hquery.HBuildVisitable;
import ru.naumen.core.server.hquery.IHasQueryParameters;
import ru.naumen.core.server.hquery.impl.HBuilder;

/**
 * Базовый класс для определения источников (entity, join attributes, CTEs) в HQL запросах
 *
 * <AUTHOR>
 * @since 04 февр. 2016 г.
 */
public abstract class BaseSource implements HBuildVisitable, AfterQueryHandler, IHasQueryParameters
{
    private final String alias;

    BaseSource(String alias)
    {
        this.alias = alias;
    }

    /**
     * Дополнительные действия после выполнения запроса
     * Гарантируется, что этом метод будет вызван для всех HCriterion и {@link BaseSource},
     * даже если в одном из них произойдет ошибка
     */
    @Override
    public void afterQuery(Session session)
    {
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (!getClass().equals(o.getClass()))
        {
            return false;
        }

        final BaseSource source = (BaseSource)o;

        return alias.equals(source.alias);
    }

    public final String getAlias()
    {
        return alias;
    }

    public abstract String getHQLSource();

    public abstract String getParentAlias();

    @Override
    public int hashCode()
    {
        final int prime = 31;
        int result = 1;
        result = prime * result + (alias == null ? 0 : alias.hashCode());
        return result;
    }

    @Override
    public void visit(HBuilder builder)
    {
        visit(builder, false);
    }

    protected abstract void appendFromClause(StringBuilder sb);

    @Override
    public String getHQL(HBuilder builder)
    {
        StringBuilder sb = new StringBuilder();
        appendFromClause(sb);
        sb.append(' ').append(alias);
        return sb.toString();
    }

    @Override
    public void setParameters(Query query)
    {
    }

    protected void visit(HBuilder builder, boolean join)
    {
        builder.from(getHQL(builder), join);
    }
}
