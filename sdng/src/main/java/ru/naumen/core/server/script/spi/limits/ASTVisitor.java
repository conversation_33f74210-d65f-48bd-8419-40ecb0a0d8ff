package ru.naumen.core.server.script.spi.limits;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.codehaus.groovy.ast.ASTNode;
import org.codehaus.groovy.ast.ClassCodeVisitorSupport;
import org.codehaus.groovy.ast.DynamicVariable;
import org.codehaus.groovy.ast.Variable;
import org.codehaus.groovy.ast.expr.AttributeExpression;
import org.codehaus.groovy.ast.expr.BinaryExpression;
import org.codehaus.groovy.ast.expr.ClassExpression;
import org.codehaus.groovy.ast.expr.ConstructorCallExpression;
import org.codehaus.groovy.ast.expr.DeclarationExpression;
import org.codehaus.groovy.ast.expr.Expression;
import org.codehaus.groovy.ast.expr.GStringExpression;
import org.codehaus.groovy.ast.expr.MethodCall;
import org.codehaus.groovy.ast.expr.MethodCallExpression;
import org.codehaus.groovy.ast.expr.MethodPointerExpression;
import org.codehaus.groovy.ast.expr.PropertyExpression;
import org.codehaus.groovy.ast.expr.StaticMethodCallExpression;
import org.codehaus.groovy.ast.expr.VariableExpression;
import org.codehaus.groovy.control.SourceUnit;
import org.codehaus.groovy.syntax.Types;

import ru.naumen.commons.shared.utils.StringUtilities;

import com.google.common.collect.HashMultimap;
import com.google.common.collect.Multimap;

/**
 * Содержит обработку определённых нод AST дерева, необходимых для задачи анализа используемых
 * вызовов в скриптах.
 */
public class ASTVisitor extends ClassCodeVisitorSupport
{

    private final SourceUnit source;

    private final Multimap<String, CallInfo> dynamicCalls = HashMultimap.<String, CallInfo> create();
    private final Multimap<String, CallInfo> staticCalls = HashMultimap.<String, CallInfo> create();
    private final Multimap<String, CallInfo> constructorCalls = HashMultimap.<String, CallInfo> create();
    private final Multimap<String, CallInfo> otherCalls = HashMultimap.<String, CallInfo> create();
    private final Multimap<String, CallInfo> attributeCalls = HashMultimap.<String, CallInfo> create();
    private final Multimap<String, CallInfo> propertyCalls = HashMultimap.<String, CallInfo> create();
    private final Multimap<String, CallInfo> methodPointerCalls = HashMultimap.<String, CallInfo> create();

    private final Map<CallInfo, String> assignTo = new HashMap<>();

    public ASTVisitor(SourceUnit source)
    {
        this.source = source;
    }

    /**
     * @return обращения к атрибутам через @.
     */
    public Multimap<String, CallInfo> getAttributeCalls()
    {
        return attributeCalls;
    }

    /**
     * @return список вызовов на динамических переменных. Обычно это будут биндинги.
     */
    public Multimap<String, CallInfo> getDynamicCalls()
    {
        return dynamicCalls;
    }

    /**
     * @return список вызовов конструкторов.
     */
    public Multimap<String, CallInfo> getConstructorCalls()
    {
        return constructorCalls;
    }

    /**
     * @return список вызовов указателей на методы.
     */
    public Multimap<String, CallInfo> getMethodPointerCalls()
    {
        return methodPointerCalls;
    }

    /**
     * @return список иных вызовов. Сюда, например, попадают вызовы вида $имя_переменной из
     * GString.
     */
    public Multimap<String, CallInfo> getOtherCalls()
    {
        return otherCalls;
    }

    /**
     * @return список вызовов свойств.
     */
    public Multimap<String, CallInfo> getPropertyCalls()
    {
        return propertyCalls;
    }

    /**
     * @return список статических вызовов.
     */
    public Multimap<String, CallInfo> getStaticCalls()
    {
        return staticCalls;
    }

    /*
     * Извлечение вызовов атрибутов.
     *
     * (non-Javadoc)
     * @see org.codehaus.groovy.ast.CodeVisitorSupport#visitAttributeExpression(org.codehaus.groovy.ast.expr
     * .AttributeExpression)
     */
    @Override
    public void visitAttributeExpression(AttributeExpression expression)
    {
        CallInfo callInfo = new CallInfo(expression.getPropertyAsString(), expression.getLineNumber(),
                expression.getColumnNumber());
        checkAssignments(callInfo);
        attributeCalls.put(expression.getObjectExpression().getText(), callInfo);
        super.visitAttributeExpression(expression);
    }

    /*
     * Из бинарных выражений извлекаются присваивания переменных.
     *
     * (non-Javadoc)
     * @see org.codehaus.groovy.ast.CodeVisitorSupport#visitBinaryExpression(org.codehaus.groovy.ast.expr
     * .BinaryExpression)
     */
    @Override
    public void visitBinaryExpression(BinaryExpression expression)
    {
        if (expression.getOperation().getType() == Types.ASSIGN)
        {
            Expression rexp = expression.getRightExpression();
            Expression lexp = expression.getLeftExpression();
            if (lexp instanceof VariableExpression)
            {
                VariableExpression vExp = (VariableExpression)lexp;
                if (rexp instanceof MethodCall)
                {
                    MethodCall mCall = (MethodCall)rexp;
                    CallInfo callInfo = new CallInfo(mCall.getMethodAsString(), rexp.getLastLineNumber(),
                            rexp.getColumnNumber());
                    assignTo.put(callInfo, vExp.getName());
                }
                else if (rexp instanceof PropertyExpression)
                {
                    PropertyExpression propertyExp = (PropertyExpression)rexp;
                    CallInfo callInfo = new CallInfo(propertyExp.getPropertyAsString(), rexp.getLastLineNumber(),
                            rexp.getColumnNumber());
                    assignTo.put(callInfo, vExp.getName());
                }
                if (rexp instanceof MethodPointerExpression)
                {
                    MethodPointerExpression mPointerExp = (MethodPointerExpression)rexp;
                    CallInfo callInfo = new CallInfo(mPointerExp.getMethodName().getText(),
                            mPointerExp.getLineNumber(), mPointerExp.getColumnNumber());
                    assignTo.put(callInfo, vExp.getName());
                }
            }
        }

        super.visitBinaryExpression(expression);
    }

    /*
     * Извлечение информации о вызове конструктора.
     *
     *  (non-Javadoc)
     * @see org.codehaus.groovy.ast.CodeVisitorSupport#visitConstructorCallExpression(org.codehaus.groovy.ast.expr
     * .ConstructorCallExpression)
     */
    @Override
    public void visitConstructorCallExpression(ConstructorCallExpression call)
    {
        CallInfo callInfo = new CallInfo(call.getMethodAsString(), call.getLineNumber(), call.getColumnNumber());
        checkAssignments(callInfo);
        constructorCalls.put(call.getType().getName(), callInfo);
        super.visitConstructorCallExpression(call);
    }

    /*
     * Извлечение информации о присваивании значения в месте объявления переменной.
     *
     * (non-Javadoc)
     * @see org.codehaus.groovy.ast.ClassCodeVisitorSupport#visitDeclarationExpression(org.codehaus.groovy.ast.expr
     * .DeclarationExpression)
     */
    @Override
    public void visitDeclarationExpression(DeclarationExpression expression)
    {
        if (!expression.isMultipleAssignmentDeclaration())
        {
            VariableExpression vExp = expression.getVariableExpression();
            Expression rexp = expression.getRightExpression();
            if (rexp instanceof MethodCall)
            {
                MethodCall mCall = (MethodCall)rexp;
                CallInfo callInfo = new CallInfo(mCall.getMethodAsString(), rexp.getLastLineNumber(),
                        rexp.getColumnNumber());
                assignTo.put(callInfo, vExp.getName());
            }
        }
        super.visitDeclarationExpression(expression);
    }

    /*
     * Извлечение вызовов из "подвыражений" GString.
     *
     * (non-Javadoc)
     * @see org.codehaus.groovy.ast.CodeVisitorSupport#visitGStringExpression(org.codehaus.groovy.ast.expr
     * .GStringExpression)
     */
    @Override
    public void visitGStringExpression(GStringExpression expression)
    {
        List<Expression> gstringValues = expression.getValues();
        for (Expression subExpression : gstringValues)
        {
            if (subExpression instanceof VariableExpression)
            {
                VariableExpression vExp = (VariableExpression)subExpression;
                CallInfo callInfo = new CallInfo(expression.getText(), expression.getLineNumber(),
                        expression.getColumnNumber());
                checkAssignments(callInfo);
                otherCalls.put(vExp.getName(), callInfo);
                return;
            }
        }
        super.visitGStringExpression(expression);
    }

    /*
     * Извлечение информации о вызове метода. В некоторых случаях и статических.
     *
     * (non-Javadoc)
     * @see org.codehaus.groovy.ast.CodeVisitorSupport#visitMethodCallExpression(org.codehaus.groovy.ast.expr
     * .MethodCallExpression)
     */
    @Override
    public void visitMethodCallExpression(MethodCallExpression call)
    {
        String methodAsString = call.getMethodAsString();
        if (!StringUtilities.isEmpty(methodAsString))
        {
            ASTNode rcv = call.getReceiver();
            if (rcv instanceof VariableExpression)
            {
                VariableExpression varExp = (VariableExpression)rcv;
                Variable accVar = varExp.getAccessedVariable();
                int lineNumber = call.getLineNumber();
                int columnNumber = call.getColumnNumber();

                CallInfo callInfo = new CallInfo(methodAsString, lineNumber, columnNumber);
                checkAssignments(callInfo);
                if (accVar == null)
                {
                    otherCalls.put("none", callInfo);
                }
                else
                {
                    if (accVar instanceof DynamicVariable)
                    {
                        DynamicVariable dynaVar = (DynamicVariable)accVar;
                        dynamicCalls.put(dynaVar.getName(), callInfo);
                    }
                    else
                    {

                        otherCalls.put(accVar.getName(), callInfo);

                    }
                }
            }
            else if (rcv instanceof ClassExpression)
            {
                ClassExpression clsExp = (ClassExpression)rcv;
                CallInfo callInfo = new CallInfo(methodAsString, rcv.getLineNumber(), rcv.getColumnNumber());
                checkAssignments(callInfo);
                staticCalls.put(clsExp.getType().getName(), callInfo);
            }
        }
        super.visitMethodCallExpression(call);
    }

    @Override
    public void visitMethodPointerExpression(MethodPointerExpression expression)
    {
        CallInfo callInfo = new CallInfo(expression.getMethodName().getText(), expression.getLineNumber(),
                expression.getColumnNumber());
        checkAssignments(callInfo);
        methodPointerCalls.put(expression.getExpression().getText(), callInfo);
        super.visitMethodPointerExpression(expression);
    }

    /*
     * Извлечение вызова свойств.
     *
     * (non-Javadoc)
     * @see org.codehaus.groovy.ast.CodeVisitorSupport#visitPropertyExpression(org.codehaus.groovy.ast.expr
     * .PropertyExpression)
     */
    @Override
    public void visitPropertyExpression(PropertyExpression expression)
    {
        CallInfo callInfo = new CallInfo(expression.getPropertyAsString(), expression.getLineNumber(),
                expression.getColumnNumber());
        checkAssignments(callInfo);
        propertyCalls.put(expression.getObjectExpression().getText(), callInfo);
        super.visitPropertyExpression(expression);
    }

    /*
     * Извлечение информации о вызовах статических методов.
     *
     * (non-Javadoc)
     * @see org.codehaus.groovy.ast.CodeVisitorSupport#visitStaticMethodCallExpression(org.codehaus.groovy.ast.expr
     * .StaticMethodCallExpression)
     */
    @Override
    public void visitStaticMethodCallExpression(StaticMethodCallExpression call)
    {
        ASTNode rcv = call.getReceiver();
        String methodAsString = call.getMethodAsString();
        if ((!StringUtilities.isEmpty(methodAsString)) && (rcv instanceof ClassExpression))
        {
            ClassExpression cExp = (ClassExpression)rcv;
            CallInfo callInfo = new CallInfo(methodAsString, cExp.getLineNumber(), cExp.getColumnNumber());
            checkAssignments(callInfo);
            staticCalls.put(cExp.getClass().getName(), callInfo);
        }
        super.visitStaticMethodCallExpression(call);
    }

    @Override
    protected SourceUnit getSourceUnit()
    {
        return source;
    }

    /**
     * Проверка на то, что результат вызова метода присваивается какой-либо
     * переменной и установка сведений о присваивании в информацию о вызове.
     * @param callInfo информация о вызове.
     */
    private void checkAssignments(CallInfo callInfo)
    {
        callInfo.setAssignedTo(assignTo.get(callInfo));
        assignTo.remove(callInfo);
    }
}
