package ru.naumen.core.server.script.storage;

import java.util.Collections;
import java.util.Set;
import java.util.stream.Collectors;

import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import ru.naumen.core.server.eventaction.EventActionService;
import ru.naumen.core.shared.utils.ObjectUtils;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.EventType;
import ru.naumen.metainfo.shared.eventaction.UserEvents;
import ru.naumen.metainfo.shared.script.ScriptUsagePoint;

/**
 * Содержит логику поиска различных сущностей связанных со скриптами используемымы а 
 * параметрах настраиваемых форм 
 *
 * <AUTHOR>
 * @since 10 мая 2016 г.
 */
@Component
public class CustomFormScriptUsageHelper
{
    @Inject
    private EventActionService eventActionService;

    /**
     * Возвращает действие по событию, связанное с местом использования скрипта
     * или null, если место использования не найдено
     *
     * @param usagePoint
     * @return
     */
    @Nullable
    public EventAction getEventAction(ScriptUsagePoint usagePoint)
    {
        String formCode = usagePoint.getRelatedMetaClassFqns().iterator().next().getCode();
        //@formatter:off
        return eventActionService.getEventActions(EventType.userEvent)
                .stream()
                .filter(eventAction -> {
                    String eventFormCode =  ((UserEvents)eventAction.getEvent()).getFormCode();
                    return ObjectUtils.equals(formCode, eventFormCode);
                })
                .findAny().orElse(null);
        //@formatter:on
    }

    /**
     * Возвращает множество fqn классов, связанных с местом использования скрипта
     *
     * @param usagePoint
     * @return
     */
    public Set<ClassFqn> getFqnsOfUsagePoint(ScriptUsagePoint usagePoint)
    {
        EventAction eventAction = getEventAction(usagePoint);
        if (null == eventAction)
        {
            return Collections.emptySet();
        }
        //@formatter:off
        return eventAction.getLinkedClasses().stream()
                .filter(ClassFqn::isClass)
                .collect(Collectors.toSet());
        //@formatter:on
    }
}
