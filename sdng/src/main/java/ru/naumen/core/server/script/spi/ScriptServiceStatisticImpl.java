package ru.naumen.core.server.script.spi;

import java.util.ArrayList;
import java.util.Collection;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Map.Entry;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicLong;
import java.util.stream.Collectors;

import org.apache.commons.math3.stat.descriptive.DescriptiveStatistics;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.SerializationFeature;

import ru.naumen.commons.shared.utils.StringUtilities;
import ru.naumen.core.server.util.JsonUtils;
import ru.naumen.metainfo.shared.script.Script;

/**
 * Реализация ManagedBean для сбора статистики по выполнению скриптов
 * Ведёт подсчёт кол-ва выполненных скриптов, кол-ва скриптов обработанных
 * с ошибкой, суммарного времени компилячции, выполнения и общего времени обработки
 * скриптов, минимального, максимального и медианы времени компиляции
 * <AUTHOR>
 */
public class ScriptServiceStatisticImpl implements ScriptServiceStatistic
{
    /**
     * Стадии выполнения скрипта
     */
    public enum ScriptStatus
    {
        //@formatter:off
        SUCCESS("success"),
        FAIL("fail"),
        END_COMPILE("endCompile"),
        BEFORE_RUN("beforeRun"),
        INIT("init");
        //@formatter:on

        private String text;

        ScriptStatus(String text)
        {
            this.text = text;
        }

        public String getText()
        {
            return text;
        }
    }

    /**
     * Статистика по типу скрипта
     */
    private static final class MimeStat
    {
        private static final Double[] PERCENTILES = new Double[] { 90.D, 95.0D, 99.0D, 99.9D };

        /*Количество вызовов */
        private AtomicLong count = new AtomicLong();
        /*Суммарное время компиляции скриптов */
        /*Количество вызовов скриптов, закончившихся неудачей*/
        private AtomicLong fail = new AtomicLong();

        private DescriptiveStatistics compile = new DescriptiveStatistics(200);
        private DescriptiveStatistics execute = new DescriptiveStatistics(200);
        private DescriptiveStatistics total = new DescriptiveStatistics(200);

        private Map<String, Object> asMap()
        {
            HashMap<String, Object> result = new LinkedHashMap<>();
            result.put("count", count.get());
            result.put("failedCount", fail.get());
            result.put("compileTime", statAsMap(compile));
            result.put("executionTime", statAsMap(execute));
            result.put("totalTime", statAsMap(total));
            return result;
        }

        private Map<String, Object> statAsMap(DescriptiveStatistics stat)
        {
            HashMap<String, Object> result = new LinkedHashMap<>();
            result.put("min", stat.getMin());
            result.put("max", stat.getMax());
            result.put("mean", stat.getMean());
            result.put("stddev", stat.getStandardDeviation());
            for (Double percentile : PERCENTILES)
            {
                result.put("p" + percentile, stat.getPercentile(percentile));
            }
            result.put("sum", stat.getSum());
            result.put("count", stat.getN());
            return result;
        }
    }

    private final class SyncImpl implements Sync
    {
        private final Script script;
        private boolean compiled; // Флаг компилировался ли скрипт или получен из кэша
        private final long start; //Время начала обработки
        private long endCompile; //Время окончания компиляции
        private long beforeRun; //Время перед запуском скрипта
        private String exceptionMessage; //Текст исключения в случае его появления
        private String executionStatus; //Состояние выполнения скрипта

        SyncImpl(Script script)
        {
            this.script = script;
            start = System.currentTimeMillis();
            executionStatus = ScriptStatus.INIT.getText();
            exceptionMessage = "";
        }

        @Override
        public void beforeRun()
        {
            beforeRun = System.currentTimeMillis();
            executionStatus = ScriptStatus.BEFORE_RUN.getText();
        }

        @Override
        public void endCompile()
        {
            endCompile = System.currentTimeMillis();
            executionStatus = ScriptStatus.END_COMPILE.getText();
        }

        @Override
        public void fail(String scriptName, String errorMessage)
        {
            end(scriptName, false);
            executionStatus = ScriptStatus.FAIL.getText();
            exceptionMessage = errorMessage;
        }

        @Override
        public String getExceptionMessage()
        {
            return exceptionMessage;
        }

        @Override
        public String getExecutionStatus()
        {
            return executionStatus;
        }

        @Override
        public Script getScript()
        {
            return script;
        }

        public boolean isCompiled()
        {
            return compiled;
        }

        @Override
        public void scriptWasCompiled()
        {
            compiled = true;
        }

        @Override
        public void success(String scriptName)
        {
            end(scriptName, true);
            executionStatus = ScriptStatus.SUCCESS.getText();
        }

        protected void end(final String scriptName, final boolean success)
        {
            final long end = System.currentTimeMillis();
            String scriptCode = script.getCode();
            if (StringUtilities.isEmpty(scriptCode))
            {
                scriptCode = scriptName;
            }
            processStat(scriptCode, success, end);
            processStat(ALL_SCRIPTS, success, end);
        }

        private void processStat(String key, boolean success, long end)
        {
            stat.compute(key, (s, mimeStat) ->
            {
                MimeStat toWork = mimeStat == null ? new MimeStat() : mimeStat;
                toWork.count.incrementAndGet();
                toWork.compile.addValue(endCompile - start);
                toWork.execute.addValue(end - beforeRun);
                toWork.total.addValue(end - start);
                if (!success)
                {
                    toWork.fail.incrementAndGet();
                }
                return toWork;
            });
        }
    }

    /**
     * Пустая синхронизация. Заглушка
     * <AUTHOR>
     */
    private final static Sync EMPTY_SYNC = new Sync()
    {
    };

    private static final ObjectMapper MAPPER = JsonUtils.createMapper(true)
            .enable(SerializationFeature.INDENT_OUTPUT);
    private static final String ALL_SCRIPTS = "allScripts";
    private final ConcurrentMap<String, MimeStat> stat = new ConcurrentHashMap<>();
    private volatile boolean isEnabled = false;

    @Override
    public void clear()
    {
        stat.clear();
    }

    @Override
    public void disable()
    {
        isEnabled = false;
    }

    @Override
    public void enable()
    {
        isEnabled = true;
    }

    @Override
    public boolean isEnabled()
    {
        return isEnabled;
    }

    @Override
    public String print(String code)
    {
        MimeStat mimeStat = stat.get(code);
        if (mimeStat == null)
        {
            return "[:]";
        }
        return JsonUtils.toJson(MAPPER, mimeStat.asMap());
    }

    @Override
    public String printAll()
    {
        Map<String, Object> collect = stat.entrySet()
                .stream()
                .collect(Collectors.toMap(Entry::getKey, e -> e.getValue().asMap()));
        return JsonUtils.toJson(MAPPER, collect);
    }

    @Override
    public Collection<String> showScriptCodes()
    {
        return new ArrayList<>(stat.keySet());
    }

    @Override
    public Sync start(Script script)
    {
        return isEnabled ? new SyncImpl(script) : EMPTY_SYNC;
    }

}
