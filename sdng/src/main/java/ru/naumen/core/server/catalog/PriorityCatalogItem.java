package ru.naumen.core.server.catalog;

import jakarta.persistence.Cacheable;
import jakarta.persistence.Column;
import jakarta.persistence.DiscriminatorValue;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;
import jakarta.persistence.UniqueConstraint;

import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import org.hibernate.annotations.DiscriminatorFormula;

import ru.naumen.core.server.flex.FlexHelper;
import ru.naumen.core.server.hibernate.uuid.UUIDIdentifiableByteBuddyLazyInitializer;
import ru.naumen.core.server.objectloader.UUIDPrefix;
import ru.naumen.core.shared.Constants;
import ru.naumen.core.shared.Constants.PriorityCatalog;
import ru.naumen.metainfo.server.annotations.Attribute;
import ru.naumen.metainfo.server.annotations.Catalog;
import ru.naumen.metainfo.server.annotations.Group;
import ru.naumen.metainfo.server.annotations.Groups;
import ru.naumen.metainfo.server.annotations.LStr;
import ru.naumen.metainfo.server.annotations.Metaclass;
import ru.naumen.metainfo.server.annotations.RequireType;
import ru.naumen.metainfo.shared.ClassFqn;
import ru.naumen.metainfo.shared.Constants.AttrGroup;

/**
 * Элемент справочника "Приоритеты"
 * <AUTHOR>
 */
//@formatter:off
@Entity
@BatchSize(size = Constants.ENTITY_BATCH_SIZE)
@Table(name = "tbl_priority", uniqueConstraints = { 
        @UniqueConstraint(name = "tbl_priority_code_key", columnNames = {"code"})}, indexes={@jakarta.persistence.Index(name = "idx_priority_parent", columnList="parent")})
@Cacheable
@DiscriminatorValue("-")
@DiscriminatorFormula(FlexHelper.NULL_DISCRIMINATOR_FORMULA)
@Cache(usage = CacheConcurrencyStrategy.TRANSACTIONAL)
@UUIDPrefix(PriorityCatalogItem.CLASS_ID)
@Metaclass(id = PriorityCatalogItem.CLASS_ID, withCase = false,
        title = { @LStr(value = "Элемент справочника 'Приоритеты'"),
                  @LStr(lang = "en", value = "'Priorities' catalog item"),
                  @LStr(lang = "de", value = "Katalogartikel 'Priorität'")})
@Catalog(code = PriorityCatalog.CODE, flat = true, withFolders = true,
        title = { @LStr(value = "Приоритеты"),
                @LStr(lang = "en", value = "Priorities"),
                @LStr(lang = "de", value = "Priorität") },
        description = { @LStr(value = "Содержит приоритеты, в соответствии с которыми определяется порядок выполнения запросов инженерами."),
                        @LStr(lang = "en", value = "Contains priorities defining the order of requests execution"),
                        @LStr(lang = "de", value = "Enthält Prioritäten, die die Reihenfolge der Ausführung von Anforderungen definieren.") })
@Groups({
    @Group(code = AttrGroup.DISPLAY_GROUP, attrs = {"title", "color", "code", "level"})
})        
//@formatter:on
public class PriorityCatalogItem extends CatalogItem<PriorityCatalogItem>
{
    /**
     * Данный статический метод необходимо добавлять во все системные классы. 
     * Иначе некорректно будет работать PrefixObjectLoaderService.
     * @see {@link UUIDIdentifiableByteBuddyLazyInitializer#getUuid()}
     * @see {@link FlexHelper#UUID_STATIC_METHOD}
     */
    public static String getUUIDPrefix()
    {
        return CLASS_ID;
    }

    public static final String CLASS_ID = PriorityCatalog.ITEM_CLASS_ID;

    //@formatter:off
    /**
     * Атрибут "Уровень" предназначен для определения уровня значения элемента справочника по отношению к другим
     * элементам справочника.
     *
     * Используется для определения более высокого приоритета по отношению к искомому. Значение "0" считается наивысшим,
     * все остальные значения должны трактоваться по нисходящей с ростом числа записанного в атрибут "Уровень".
     */
    @Column(name = "priority_level", nullable = false)
    @Attribute(code=Constants.PriorityCatalog.LEVEL, required = RequireType.SYSTEM_REQUIRED, editable = true, 
            title = { @LStr(value = "Уровень"),
                      @LStr(lang = "en", value = "Level"),
                      @LStr(lang = "de", value = "Level") })
    private long level;
    //@formatter:on

    public long getLevel()
    {
        return level;
    }

    @Override
    public ClassFqn getMetaClass()
    {
        return PriorityCatalog.ITEM_FQN;
    }

    /**
     * {@inheritDoc}
     */
    @Override
    public String getPrefixLoaderUUIDPrefix()
    {
        return CLASS_ID;
    }

    public void setLevel(long level)
    {
        this.level = level;
    }
}
