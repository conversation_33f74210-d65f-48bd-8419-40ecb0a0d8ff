package ru.naumen.core.server.hquery.criterion;

import static ru.naumen.core.shared.Constants.MatchMode.MATCH_MODE_BEGIN;
import static ru.naumen.core.shared.Constants.MatchMode.MATCH_MODE_BOTH;
import static ru.naumen.core.shared.Constants.MatchMode.MATCH_MODE_END;
import static ru.naumen.core.shared.Constants.MatchMode.MATCH_MODE_NONE;

import java.util.Objects;

import jakarta.annotation.Nullable;

import ru.naumen.core.server.hquery.HColumn;
import ru.naumen.core.server.hquery.HCriterion;
import ru.naumen.core.server.hquery.escape.EscapeFunctionsProvider;
import ru.naumen.core.server.hquery.impl.HBuilder;
import ru.naumen.core.server.hquery.impl.NameGenerator;
import ru.naumen.core.shared.Constants.CastToType;

/**
 * Фильтр like для запроса в базу данных. Фильтр like проверяет наличие определённой подстроки в значении атрибута.
 * Фильтр поддерживает следующие операции:
 * <ol>
 * <li>Игнорирование регистра значения атрибута</li>
 * <li>Преобразование типа значения атрибута</li>
 * <li>Определение местоположения подстроки в значении атрибута</li>
 * <li>Использование обратной записи фильтра <pre>(_property _op :paramName) -> (:paramName _op _property)</pre></li>
 * <li>Экранирование специальных символов внутри строк, передаваемых в запрос</li>
 * </ol>
 *
 * <AUTHOR>
 * @since 22.08.2011
 */
public final class LikeCriterion extends SimpleCriterion
{
    /**
     * Билдер для создания {@link LikeCriterion}. Все параметры, передаваемые через метод
     * {@link #create(HColumn, String)} являются обязательными. Необязательные параметры устанавливаются через вызовы
     * дополнительных методов.
     *
     * <AUTHOR>
     * @since 22.09.2022
     */
    public static final class LikeCriterionBuilder
    {
        private final HColumn property;
        @Nullable
        private final String value;
        private int matchMode = MATCH_MODE_NONE;
        private boolean revert = false;
        private boolean ignoreCase = false;
        @Nullable
        private EscapeFunctionsProvider escapeProvider;
        private CastToType castTo = CastToType.NONE;

        private LikeCriterionBuilder(@Nullable HColumn property, @Nullable String value)
        {
            this.property = property;
            this.value = value;
        }

        /**
         * Создаёт билдер {@link LikeCriterion}, заполняя обязательные параметры
         *
         * @param property имя колонки в таблице, по значениям которой происходит фильтрация (определяется атрибутом
         *                 метакласса)
         * @param value подстрока или значение, по которому будет производиться фильтрация
         * @return билдер для {@link LikeCriterion}
         */
        public static LikeCriterionBuilder create(@Nullable HColumn property, @Nullable String value)
        {
            return new LikeCriterionBuilder(property, value);
        }

        /**
         * Определяет местоположение <b>value</b> в значении атрибута (начало, конец и т.д.)
         */
        public LikeCriterionBuilder matchMode(int matchMode)
        {
            this.matchMode = matchMode;
            return this;
        }

        /**
         * Установить признак того использовать ли обратную запись при добавлении критерии в запрос
         */
        public LikeCriterionBuilder revert(boolean revert)
        {
            this.revert = revert;
            return this;
        }

        /**
         * Установить признак того нужно ли учитывать регистр символов в значении атрибута
         */
        public LikeCriterionBuilder ignoreCase(boolean ignoreCase)
        {
            this.ignoreCase = ignoreCase;
            return this;
        }

        /**
         * Установить обработчик, позволяющий экранировать специальные символы внутри строк, передаваемых в запрос.
         */
        public LikeCriterionBuilder escapeProvider(@Nullable EscapeFunctionsProvider escapeProvider)
        {
            this.escapeProvider = escapeProvider;
            return this;
        }

        /**
         * Установить тип, к которому нужно выполнить преобразование значения атрибута. Если null - то преобразование
         * выполняться не будет. <b>ВАЖНО</b>: использование этого параметра может иметь side effect (см.
         * AbstractRestrictionStrategy#resolveValueRaw(Attribute, Object, String, CastToType)).
         */
        public LikeCriterionBuilder castTo(CastToType castTo)
        {
            this.castTo = castTo;
            return this;
        }

        /**
         * Создаёт like фильтр с параметрами, которые были заполнены в билдере
         */
        public LikeCriterion build()
        {
            return new LikeCriterion(this);
        }
    }

    private static final String LIKE = " like "; //$NON-NLS-1$

    private final int matchMode;
    private final boolean revert;
    private final EscapeFunctionsProvider escapeProvider;

    private boolean processed = false;

    private LikeCriterion(LikeCriterionBuilder definition)
    {
        super(definition.property, LIKE, definition.value, definition.castTo);
        this.matchMode = definition.matchMode;
        this.revert = definition.revert;
        this.escapeProvider = definition.escapeProvider;
        setIgnoreCase(definition.ignoreCase);
    }

    @Override
    public void append(StringBuilder sb, HBuilder builder, NameGenerator parameterCounter)
    {
        if (revert)
        {
            this.paramName = parameterCounter.next();
            sb.append(':').append(paramName);
            sb.append(op);
            appendAttributeWithRevertedFormat(sb, builder);
        }
        else
        {
            super.append(sb, builder, parameterCounter);
        }
        if (null != escapeProvider && null != escapeProvider.getLikeEscapeChar())
        {
            String escapeSafe = escapeProvider.getLikeEscapeChar().toString()
                    .replace("\\", "\\\\").replace("'", "\\'");
            sb.append(" escape '").append(escapeSafe).append('\'');
        }
    }

    @Override
    protected HCriterion createCopyInstance()
    {
        return new LikeCriterion(LikeCriterionBuilder.create(property, (String)getValue())
                .matchMode(matchMode)
                .revert(revert)
                .escapeProvider(escapeProvider)
                .ignoreCase(ignoreCase)
                .castTo(castTo));
    }

    @Override
    protected void process()
    {
        if (processed)
        {
            return;
        }

        if (!revert)
        {
            String str = (String)getValue();

            if (null != escapeProvider)
            {
                str = escapeProvider.getLikeEscapeFunction().escape(str);
            }

            switch (matchMode)
            {
                case MATCH_MODE_NONE:
                    // do nothing
                    break;
                case MATCH_MODE_BEGIN:
                    str = '%' + str;
                    break;
                case MATCH_MODE_END:
                    str = str + '%';
                    break;
                case MATCH_MODE_BOTH:
                    str = '%' + str + '%';
                    break;
                default:
                    throw new RuntimeException("Unknown matchMode: " + matchMode); //$NON-NLS-1$
            }

            setValue(str);
        }
        processed = true;

        super.process();
    }

    private void appendAttributeWithRevertedFormat(StringBuilder sb, HBuilder builder)
    {
        if (MATCH_MODE_BEGIN == matchMode || MATCH_MODE_BOTH == matchMode)
        {
            sb.append("'%' || ");
        }

        if (escapeProvider != null)
        {
            sb.append(escapeProvider.getLikeEscapeSqlFunctionName());
            sb.append('(');
            appendAttributeWithIgnoreCase(sb, builder);
            sb.append(')');
        }
        else
        {
            appendAttributeWithIgnoreCase(sb, builder);
        }

        if (MATCH_MODE_END == matchMode || MATCH_MODE_BOTH == matchMode)
        {
            sb.append(" || '%'");
        }
    }

    @Override
    public boolean equals(Object o)
    {
        if (this == o)
        {
            return true;
        }
        if (o == null || getClass() != o.getClass())
        {
            return false;
        }
        if (!super.equals(o))
        {
            return false;
        }
        LikeCriterion that = (LikeCriterion)o;
        return matchMode == that.matchMode &&
               revert == that.revert;
    }

    @Override
    public int hashCode()
    {
        return Objects.hash(super.hashCode(), matchMode, revert);
    }

    @Override
    public String toString()
    {
        return "LikeCriterion{" +
               "property=" + property +
               ", matchMode=" + matchMode +
               ", revert=" + revert +
               ", escapeProvider=" + escapeProvider +
               ", castTo=" + castTo +
               ", op='" + op + '\'' +
               ", value='" + getValue() + '\'' +
               '}';
    }
}