package ru.naumen.core.server.script.filtration.stream;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Iterator;
import java.util.List;
import java.util.Set;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import org.hibernate.SessionFactory;
import org.hibernate.query.Query;
import org.infinispan.Cache;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.collect.Lists;

import jakarta.inject.Inject;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.bo.DaoHelper;
import ru.naumen.core.server.cache.CacheBuilder;
import ru.naumen.core.server.hquery.HCriteria;
import ru.naumen.core.server.hquery.HPredicate;
import ru.naumen.core.server.hquery.HRestrictions;
import ru.naumen.core.server.jta.TransactionRunner;
import ru.naumen.core.server.jta.TransactionRunner.TransactionType;
import ru.naumen.core.server.script.IQueryDto;
import ru.naumen.core.shared.Constants.AbstractBO;
import ru.naumen.core.shared.IUUIDIdentifiable;
import ru.naumen.core.shared.dto.DtObject;
import ru.naumen.core.shared.dto.SimpleDtObject;
import ru.naumen.core.shared.utils.UuidHelper;

/**
 * Сервис для сохранения и получения selectable-элементов в кэш
 * <AUTHOR>
 * @since 1.04.18
 */
@Component
public class FiltrationStreamService
{
    private static final Logger LOG = LoggerFactory.getLogger(FiltrationStreamService.class);

    private static final int IDS_BATCH = 10000;

    private static final String SIZE_OF_IDS_KEY = "idsSize";
    private static final String GROUPS_SIZE_KEY = "groupsSize";

    @Inject
    private FiltrationStreamCacheProvider cacheConfig;
    @Inject
    private SessionFactory sessionFactory;

    private final ConcurrentMap<Object, IQueryDto> streamsCache = CacheBuilder.<Object, IQueryDto> newBuilder(
                    "filterStreams").build()
            .asMap();

    public Set<String> filterSelectableUuids(HashMap<String, Object> streamKey, Set<String> uuids)
    {
        Set<String> filtered = new HashSet<>();
        QueryDto stream = (QueryDto)streamsCache.get(streamKey);
        if (stream.isWithQuery())
        {
            return uuids;
        }
        List<Long> ids = uuids.stream().map(UuidHelper::toId).toList();
        for (List<Long> batch : Lists.partition(ids, DaoHelper.BATCH_SIZE))
        {
            HCriteria criteria = stream.getHCriteria().createCopy();
            criteria.add(HRestrictions.in(criteria.getProperty("id"), batch));
            List<?> result = criteria.createQuery(sessionFactory.getCurrentSession()).list();
            result.stream().filter(IUUIDIdentifiable.class::isInstance).map(IUUIDIdentifiable.class::cast)
                    .map(IUUIDIdentifiable::getUUID).forEach(filtered::add);
        }
        return filtered;
    }

    /**
     * Получение количества selectable-элементов, хранящихся в заданном кэше
     * @param filterStreamKey название кэша selectable-элементов
     * @return
     */
    public int getDataSize(String filterStreamKey, HashMap<String, Object> key)
    {
        QueryDto query = (QueryDto)streamsCache.get(key);
        if (query.isWithQuery())
        {
            Object calculatedSize = cacheConfig.getCache(filterStreamKey).get(SIZE_OF_IDS_KEY);
            return calculatedSize == null ? -1 : (int)calculatedSize;
        }
        else
        {
            HCriteria countCriteria = query.getHCriteria().createCopy();
            countCriteria.setPredicate(HPredicate.COUNT_ALL);
            countCriteria.add(HRestrictions.eq(countCriteria.getProperty(AbstractBO.REMOVED), false));
            Number uniqueResult = (Number)countCriteria.createQuery(sessionFactory.getCurrentSession())
                    .setCacheable(true).uniqueResult();
            uniqueResult = uniqueResult == null ? 0 : uniqueResult.intValue();
            return uniqueResult.intValue();
        }
    }

    public List<DtObject> getDtoItems(HashMap<String, Object> streamKey, int first, int maxResults)
    {
        IQueryDto filtrationStream = streamsCache.get(streamKey);
        List<?> items = getItems(((QueryDto)filtrationStream), first, maxResults);
        return items.stream().map(obj -> obj instanceof DtObject dtObject ? new SimpleDtObject(dtObject)
                : new SimpleDtObject((IUUIDIdentifiable)obj)).collect(Collectors.toList());
    }

    /**
     * Получение порции id элементов из бд
     * @param streamKey идентификатор стрима
     * @param first индекс первого элемента
     * @param maxResults количество элементов в выборке
     * @return множество id
     */
    public Set<String> getIds(HashMap<String, Object> streamKey, int first, int maxResults)
    {
        IQueryDto filtrationStream = streamsCache.get(streamKey);
        return transformToIds(getItems(((QueryDto)filtrationStream), first, maxResults));
    }

    /**
     * Получение итератора по всем id selectable-элементов, хранящихся в кэше.
     * @param filtrationStreamId id соответствующего стрима
     * @return
     */
    public Iterator<HashSet<String>> getIdsIterator(String filtrationStreamId)
    {
        Cache<Object, Object> cache = cacheConfig.getCache(filtrationStreamId);
        Set<Object> keySet = new HashSet<>(cache.keySet());
        keySet.remove(GROUPS_SIZE_KEY);
        keySet.remove(SIZE_OF_IDS_KEY);
        return IntStream.range(0, keySet.size()).mapToObj(it ->
        {
            Object object = cache.get(it);
            return (HashSet<String>)object;
        }).iterator();
    }

    /**
     * Сохранение стрима в кэш и запуск асинхронной загрузки selectable-элементов
     * @param stream объект содержащий ограничения скрипта фильтрации
     */
    public void saveStream(QueryDto stream)
    {
        streamsCache.put(stream.getFilterKey(), stream);
        CompletableFuture.runAsync(() ->
        {
            Cache<Object, Object> cache = cacheConfig.getCacheForStream(stream);
            AtomicInteger id = new AtomicInteger(0);
            AtomicInteger groups = new AtomicInteger(0);
            int offset = 0;
            List<?> items;
            cache.startBatch();
            while (!(items = getItems(stream, offset, IDS_BATCH)).isEmpty())
            {
                offset += items.size();
                Set<String> ids = transformToIds(items);
                id.addAndGet(ids.size());
                cache.compute(groups.get(), (key, set) ->
                {
                    Set<String> existing = set == null ? new HashSet<>() : (Set<String>)set;
                    existing.addAll(ids);
                    if (existing.size() == 30000)
                    {
                        groups.incrementAndGet();
                    }
                    return existing;
                });
            }

            LOG.info("Data ready. Size = {}, Groups ={}", id.get(), groups.get());
            cache.put(GROUPS_SIZE_KEY, groups.get());
            cache.put(SIZE_OF_IDS_KEY, id.get());
            cache.endBatch(true);
        }).exceptionally(t ->
        {
            LOG.error(t.toString(), t);
            throw new FxException(String.format("Error in loading batch of selectable: {%s}", t.toString()), true);
        });
    }

    /**
     * Получение порции данных из базы данных
     * @param criteria
     * @param from начальная позиция
     * @param to конечная позиция
     * @return набор данных
     */
    private List<?> getItems(QueryDto stream, int from, int to)
    {
        return TransactionRunner.call(TransactionType.NEW_READ_ONLY, () ->
        {
            Query query = null;
            if (stream.isWithQuery())
            {
                query = sessionFactory.getCurrentSession().createQuery(stream.getHqlQuery());
            }
            else
            {
                HCriteria criteria = stream.getHCriteria().createCopy();
                criteria.add(HRestrictions.eq(criteria.getProperty(AbstractBO.REMOVED), false));
                query = criteria.createQuery(sessionFactory.getCurrentSession());
            }
            List<?> list = query.setFirstResult(from).setMaxResults(to).list();
            return list;
        });
    }

    /**
     * Преобразование списка IUUIDIdentifiable-элементов в список UUID-ов
     * @param items список IUUIDIdentifiable-элементов
     * @return список UUID-ов
     */
    private Set<String> transformToIds(List<?> items)
    {
        return items.stream().filter(IUUIDIdentifiable.class::isInstance).map(
                        it -> ((IUUIDIdentifiable)it).getUUID())
                .collect(Collectors
                        .toCollection(HashSet::new));
    }
}
