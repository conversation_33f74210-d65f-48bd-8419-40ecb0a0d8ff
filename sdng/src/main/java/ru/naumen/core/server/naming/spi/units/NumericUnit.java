package ru.naumen.core.server.naming.spi.units;

import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.function.Function;

/**
 * <AUTHOR>
 *
 *  Обрабатывает правило формирования строки, умеет дополнять лидирующими нулями,
 *  при этом в конкретном Design достаточно указать сам шаблон,
 *  например {N}, при этом выражения {5N} будет корректно обработано
 */
public abstract class NumericUnit extends UnitBase
{

    //Части для поиска через регулярные выражения
    private static final String DIGITS = "\\d*";
    private static final String DIGITS_NOT_EMPTY = "\\d+";
    private static final String L_BRACE = "\\{";
    private static final String R_BRACE = "\\}";

    //Максимальная ширина в символах числа, до которой заполняем нулями
    private static final int MAX_WIDTH = 99;
    private static final String FILLING =
            "000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000000";

    @Override
    protected String replace(String rule, Object obj, Function<Object, String> func)
    {
        String patternTemplate = getTemplate().substring(1, getTemplate().length() - 1);
        Pattern pattern = Pattern.compile(L_BRACE + DIGITS + patternTemplate + R_BRACE);
        Matcher matcher = pattern.matcher(rule);

        while (matcher.find())
        {
            rule = replace(rule, matcher.group(), func.apply(obj));
            matcher = pattern.matcher(rule);
        }

        return rule;
    }

    /**
     * Заменяет в строке1 все вхождения строки2 строкой3
     * @param main
     * @param toReplace заменяемая строка (шаблон а-la {5N})
     * @param replaceBy заменя_ющ_ая строка
     * @return
     */
    String replace(String main, String toReplace, String replaceBy)
    {
        int length = getReplacementLength(toReplace);
        main = main.replace(toReplace, prepend(replaceBy, length));
        return main;
    }

    private int getReplacementLength(String template)
    {
        Matcher matcher = Pattern.compile(DIGITS_NOT_EMPTY).matcher(template);
        String digits = matcher.find() ? matcher.group() : "0";
        return Math.min(Integer.parseInt(digits), MAX_WIDTH);
    }

    /**
     * При необходимости добавляет лидирующие нули.
     * Если длина строки больше {@link #MAX_WIDTH} - строка не обрезается
     */
    private String prepend(String aString, int length)
    {
        int needZeros = length - aString.length();
        if (needZeros > 0)
        {
            return FILLING.substring(0, needZeros) + aString;
        }
        return aString;
    }
}
