package ru.naumen.core.server.script.api.eventaction;

import ru.naumen.core.server.SpringContext;
import ru.naumen.metainfo.server.MetainfoService;
import ru.naumen.metainfo.shared.eventaction.EventAction;
import ru.naumen.metainfo.shared.eventaction.push.PushMobileEventAction;

/**
 * Обертка {@link EventAction} уведомление в МК для использования в скриптах
 * <AUTHOR>
 * @since 12.03.2021
 */
public class PushMobileEventActionWrapper extends PushEventActionWrapper implements IPushMobileEventActionWrapper
{
    private final PushMobileEventAction action;
    private final MetainfoService metainfoService;

    PushMobileEventActionWrapper(EventAction eventAction)
    {
        super(eventAction);
        action = (PushMobileEventAction)eventAction.getAction();
        metainfoService = SpringContext.getInstance().getBean(MetainfoService.class);
    }

    @Override
    public boolean isFormatHTML()
    {
        return action.isHtml();
    }

    @Override
    public String getPushHeaderFormat()
    {
        switch (action.getPushHeaderFormat())
        {
            case SystemNameValue:
                return metainfoService.getProductName();
            case SystemNameValueAndSubject:
                return metainfoService.getProductName() + " + " + messages.getMessage("notification.subj");
            case Subject:
                return messages.getMessage("notification.subj");
            default:
                return "";
        }
    }
}