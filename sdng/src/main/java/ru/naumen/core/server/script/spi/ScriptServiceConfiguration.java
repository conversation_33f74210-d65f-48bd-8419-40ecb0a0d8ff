package ru.naumen.core.server.script.spi;

import java.util.concurrent.ExecutorService;

import org.codehaus.groovy.control.CompilePhase;
import org.codehaus.groovy.control.CompilerConfiguration;
import org.codehaus.groovy.control.customizers.ASTTransformationCustomizer;
import org.codehaus.groovy.control.customizers.ImportCustomizer;
import org.springframework.beans.factory.config.ConfigurableBeanFactory;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Scope;

import jakarta.inject.Named;
import ru.naumen.core.server.ConfigurationProperties;
import ru.naumen.core.server.script.api.injection.InjectApiLocal;
import ru.naumen.core.server.script.ast.persistence_context.PersistenceContextInterrupt;
import ru.naumen.core.server.script.libraries.scripting.StorageService;
import ru.naumen.core.server.script.modules.compile.AllScriptModulesCompilationServiceImpl;
import ru.naumen.core.server.script.modules.compile.CompilationMode;
import ru.naumen.core.server.script.modules.compile.MetaDataScriptModulesService;
import ru.naumen.core.server.script.modules.compile.ModulesClassLoaderManager;
import ru.naumen.core.server.script.modules.compile.OneByOneScriptModulesCompilationServiceImpl;
import ru.naumen.core.server.script.modules.compile.ScriptModulesCompilationService;
import ru.naumen.core.server.script.modules.compile.ValidateIHasApiOverridesCustomizer;
import ru.naumen.core.server.script.modules.storage.ScriptModule;
import ru.naumen.core.server.script.modules.storage.ScriptModulesStorageService;
import ru.naumen.core.server.util.MessageFacade;

@Configuration
public class ScriptServiceConfiguration
{
    @Bean
    @Scope(ConfigurableBeanFactory.SCOPE_SINGLETON)
    public ScriptModulesCompilationService scriptModulesService(
            final ScriptEnvironmentResourcesRegistry<ExecutorService> scriptEnvironmentResourcesRegistry,
            final ScriptModulesStorageService scriptModulesStorageService,
            final MessageFacade messages,
            final ScriptExceptionsService scriptExceptionsService,
            final ConfigurationProperties configurationProperties,
            final ModulesClassLoaderManager modulesClassLoaderManager,
            @Named("libraryModuleStorageService")
            final StorageService<ScriptModule, ScriptModule> librariesScriptModulesService,
            final MetaDataScriptModulesService metaDataScriptModulesService,
            final ApplicationEventPublisher eventPublisher)
    {
        final CompilationMode compilationMode = configurationProperties.getCompilationMode();

        if (CompilationMode.ALL == compilationMode)
        {
            return new AllScriptModulesCompilationServiceImpl(
                    scriptEnvironmentResourcesRegistry,
                    scriptModulesStorageService,
                    messages,
                    configurationProperties,
                    modulesClassLoaderManager,
                    librariesScriptModulesService,
                    metaDataScriptModulesService,
                    eventPublisher);
        }
        else
        {
            return new OneByOneScriptModulesCompilationServiceImpl(
                    scriptEnvironmentResourcesRegistry,
                    scriptModulesStorageService,
                    messages,
                    scriptExceptionsService,
                    modulesClassLoaderManager,
                    librariesScriptModulesService,
                    eventPublisher);
        }
    }

    public static NauCompilerConfiguration createCompilerConfiguration(final boolean persistenceContextASTEnabled,
            final boolean useInvokeDynamic)
    {
        final NauCompilerConfiguration config = new NauCompilerConfiguration();
        if (useInvokeDynamic)
        {
            config.getOptimizationOptions().put(CompilerConfiguration.INVOKEDYNAMIC, true);
        }
        final ImportCustomizer imports = new ImportCustomizer();
        imports.addImports("ru.naumen.core.server.script.dependencies.DependsOn",
                "ru.naumen.core.server.script.api.injection.InjectApi");
        config.addCompilationCustomizers(imports);

        if (persistenceContextASTEnabled)
        {
            config.addCompilationCustomizers(new ASTTransformationCustomizer(PersistenceContextInterrupt.class));
        }

        config.addCompilationCustomizers(
                new ASTTransformationCustomizer(InjectApiLocal.class),
                new ValidateIHasApiOverridesCustomizer(CompilePhase.CONVERSION)
        );

        return config;
    }
}
