package ru.naumen.core.server.script.api;

import java.io.ByteArrayInputStream;
import java.io.ByteArrayOutputStream;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.text.SimpleDateFormat;
import java.util.Base64;
import java.util.Date;
import java.util.Iterator;
import java.util.TimeZone;
import java.util.concurrent.Callable;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.random.RandomGenerator;

import jakarta.activation.DataHandler;
import jakarta.activation.DataSource;

import javax.xml.namespace.QName;

import jakarta.xml.soap.AttachmentPart;
import jakarta.xml.soap.MessageFactory;
import jakarta.xml.soap.SOAPBody;
import jakarta.xml.soap.SOAPConnection;
import jakarta.xml.soap.SOAPConnectionFactory;
import jakarta.xml.soap.SOAPElement;
import jakarta.xml.soap.SOAPEnvelope;
import jakarta.xml.soap.SOAPException;
import jakarta.xml.soap.SOAPFault;
import jakarta.xml.soap.SOAPHeader;
import jakarta.xml.soap.SOAPMessage;
import jakarta.xml.soap.SOAPPart;

import javax.xml.transform.OutputKeys;
import javax.xml.transform.Result;
import javax.xml.transform.Source;
import javax.xml.transform.Transformer;
import javax.xml.transform.TransformerException;
import javax.xml.transform.TransformerFactory;
import javax.xml.transform.dom.DOMSource;
import javax.xml.transform.stream.StreamResult;
import javax.xml.transform.stream.StreamSource;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import ru.naumen.commons.shared.FxException;

/**
 * API для обмена SOAP сообщениями
 *
 * <AUTHOR>
 * @since 4.3.11
 */
@Component("soap")
public class SOAPApi implements ISOAPApi
{
    private record InputStreamDataSource(InputStream is, String contentType, String name) implements DataSource
    {
        @Override
        public InputStream getInputStream()
        {
            return is;
        }

        @Override
        public OutputStream getOutputStream()
        {
            throw new UnsupportedOperationException();
        }

        @Override
        public String getContentType()
        {
            return contentType;
        }

        @Override
        public String getName()
        {
            return name;
        }
    }

    private static final Logger LOG = LoggerFactory.getLogger(SOAPApi.class);

    @Value("${ru.naumen.soap.soap-request-timeout-seconds}")
    private Long soapRequestTimeoutSeconds;

    @Override
    public SOAPMessage addAttachment(SOAPMessage soapMessage, String contentId, String mimeType, InputStream is)
    {
        try
        {
            DataHandler dh = new DataHandler(new InputStreamDataSource(is, mimeType, contentId));
            AttachmentPart attachment = soapMessage.createAttachmentPart(dh);
            attachment.setContentId(contentId);
            soapMessage.addAttachmentPart(attachment);
            soapMessage.saveChanges();
        }
        catch (SOAPException e)
        {
            throw new FxException(e);
        }
        return soapMessage;
    }

    @Override
    public SOAPMessage addWsSecurityHeader(SOAPMessage soapMessage, String login, String password)
    {
        try
        {
            SimpleDateFormat formatter = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:dd.SSS'Z'");
            formatter.setTimeZone(TimeZone.getTimeZone("GMT"));

            RandomGenerator generator = RandomGenerator.getDefault();
            String nonceString = String.valueOf(generator.nextInt(999999999));
            String tokenId = String.valueOf(generator.nextInt(999999999));
            Date timestamp = new Date();

            SOAPPart soapPart = soapMessage.getSOAPPart();
            SOAPEnvelope soapEnvelope = soapPart.getEnvelope();
            SOAPHeader header = soapMessage.getSOAPHeader();
            if (header == null)
            {
                header = soapEnvelope.addHeader();
            }

            SOAPElement security = header
                    .addChildElement("Security", "wsse",
                            "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-secext-1.0.xsd")
                    .addAttribute(new QName("xmlns:wsu"),
                            "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd");

            SOAPElement usernameToken = security.addChildElement("UsernameToken", "wsse")
                    .addAttribute(QName.valueOf("wsu:Id"), "NaumenUsernameToken-" + tokenId);

            SOAPElement userNameSOAPElement = usernameToken.addChildElement("Username", "wsse");
            userNameSOAPElement.addTextNode(login);

            SOAPElement passwordSOAPElement = usernameToken.addChildElement("Password", "wsse").addAttribute(
                    new QName("Type"),
                    "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-username-token-profile-1"
                    + ".0#PasswordDigest");
            passwordSOAPElement
                    .addTextNode(calculatePasswordDigest(nonceString, formatter.format(timestamp), password));

            SOAPElement nonce = usernameToken.addChildElement("Nonce", "wsse").addAttribute(new QName("EncodingType"),
                    "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-soap-message-security-1.0#Base64Binary");
            nonce.addTextNode(Base64.getEncoder().encodeToString(hexEncode(nonceString).getBytes()));

            SOAPElement created = usernameToken.addChildElement("Created", "wsu",
                    "http://docs.oasis-open.org/wss/2004/01/oasis-200401-wss-wssecurity-utility-1.0.xsd");
            created.addTextNode(formatter.format(timestamp)); //formatter formats the date to String
            soapMessage.saveChanges();
        }
        catch (SOAPException e)
        {
            throw new FxException(e);
        }
        return soapMessage;
    }

    @Override
    public SOAPMessage buildSoapMessage(InputStream is)
    {
        SOAPMessage soapMessage;
        try
        {
            MessageFactory messageFactory = MessageFactory.newInstance();
            soapMessage = messageFactory.createMessage();
            SOAPPart soapPart = soapMessage.getSOAPPart();
            soapPart.setContent(new StreamSource(is));
            soapMessage.saveChanges();
        }
        catch (SOAPException e)
        {
            throw new FxException(e);
        }
        return soapMessage;
    }

    @Override
    public SOAPMessage buildSoapMessage(String soapMessage)
    {
        InputStream is = new ByteArrayInputStream(soapMessage.getBytes());
        return buildSoapMessage(is);
    }

    @Override
    public InputStream getAttachment(SOAPMessage soapMessage, String contentId)
    {
        Iterator<?> iterator = soapMessage.getAttachments();
        while (iterator.hasNext())
        {
            AttachmentPart attachment = (AttachmentPart)iterator.next();
            String id = attachment.getContentId();
            if (id.contains(contentId))
            {
                try
                {
                    return attachment.getRawContent();
                }
                catch (SOAPException e)
                {
                    throw new FxException(e);
                }
            }
        }
        return null;
    }

    public Long getSoapRequestTimeoutSeconds()
    {
        return soapRequestTimeoutSeconds;
    }

    @Override
    public InputStream getSOAPMessageAsInputStream(SOAPMessage soapMessage)
    {
        try
        {
            Source source = soapMessage.getSOAPPart().getContent();
            try (ByteArrayOutputStream out = new ByteArrayOutputStream())
            {
                Result outputTarget = new StreamResult(out);
                Transformer transformer = TransformerFactory.newInstance().newTransformer();
                transformer.setOutputProperty(OutputKeys.OMIT_XML_DECLARATION, "yes");
                transformer.transform(source, outputTarget);
                return new ByteArrayInputStream(out.toByteArray());
            }
        }
        catch (Exception e)
        {
            throw new FxException(e);
        }
    }

    @Override
    public String getSOAPMessageAsString(SOAPMessage soapMessage)
    {
        try
        {
            Source source = soapMessage.getSOAPPart().getContent();
            return getPrettyPrintXML(source);
        }
        catch (SOAPException e)
        {
            throw new FxException(e);
        }
    }

    @Override
    public InputStream sendRequest(String url, InputStream is)
            throws InterruptedException, ExecutionException, TimeoutException
    {
        return sendRequest(url, is, soapRequestTimeoutSeconds);
    }

    @Override
    public InputStream sendRequest(String url, InputStream is, long timeout)
            throws InterruptedException, ExecutionException, TimeoutException
    {
        SOAPMessage message = buildSoapMessage(is);
        SOAPMessage response = sendRequest(url, message, timeout);
        return getSOAPMessageAsInputStream(response);
    }

    @Override
    public SOAPMessage sendRequest(String url, SOAPMessage message)
            throws InterruptedException, ExecutionException, TimeoutException
    {
        return sendRequest(url, message, false, soapRequestTimeoutSeconds);
    }

    @Override
    public SOAPMessage sendRequest(String url, SOAPMessage message, long timeout)
            throws InterruptedException, ExecutionException, TimeoutException
    {
        return sendRequest(url, message, false, timeout);
    }

    @Override
    public SOAPMessage sendRequest(String url, SOAPMessage message, boolean canThrows)
            throws InterruptedException, ExecutionException, TimeoutException
    {
        return sendRequest(url, message, canThrows, soapRequestTimeoutSeconds);
    }

    @Override
    public SOAPMessage sendRequest(String url, SOAPMessage message, boolean canThrows, long timeout)
            throws InterruptedException, ExecutionException, TimeoutException
    {
        ExecutorService executor = Executors.newCachedThreadPool();
        Callable<SOAPMessage> request = () -> doSendRequest(url, message, canThrows);
        Future<SOAPMessage> future = executor.submit(request);
        try
        {
            return future.get(timeout, TimeUnit.SECONDS);
        }
        finally
        {
            future.cancel(true);
            executor.shutdown();
        }
    }

    private SOAPMessage doSendRequest(String url, SOAPMessage message, boolean canThrows)
    {
        SOAPConnection soapConnection;
        try
        {
            SOAPConnectionFactory soapConnectionFactory = SOAPConnectionFactory.newInstance();
            soapConnection = soapConnectionFactory.createConnection();
            LOG.debug("SOAP Request:\n{}", getSOAPMessageAsString(message));
        }
        catch (UnsupportedOperationException | SOAPException e)
        {
            throw new FxException(e);
        }

        try
        {
            SOAPMessage response = soapConnection.call(message, url);
            LOG.debug("SOAP Response:\n{}", getSOAPMessageAsString(response));
            SOAPBody body = response.getSOAPPart().getEnvelope().getBody();
            if (body.hasFault() && canThrows)
            {
                SOAPFault fault = body.getFault();
                Source source = new DOMSource(fault.getDetail());
                throw new FxException(fault.getFaultString() + " : \n" + getPrettyPrintXML(source));
            }

            return response;
        }
        catch (SOAPException e)
        {
            throw new FxException("Error while sending SOAP request to " + url, e);
        }
    }

    /**
     * Расчет значения зашифрованного пароля
     * @param nonce случайная строка
     * @param created дата создания SOAP-сообщения
     * @param password пароль в незашифрованном виде
     * @return зашифрованный пароль
     */
    private String calculatePasswordDigest(String nonce, String created, String password)
    {
        String encoded;
        try
        {
            String pass = hexEncode(nonce) + created + password;
            MessageDigest md = MessageDigest.getInstance("SHA1");

            md.update(pass.getBytes());
            byte[] encodedPassword = md.digest();
            encoded = Base64.getEncoder().encodeToString(encodedPassword);
        }
        catch (NoSuchAlgorithmException e)
        {
            throw new FxException(e);
        }
        return encoded;
    }

    /**
     * Получение XML-строки с отступами
     * @param source xml источник
     * @return XML-строка с отступами
     */
    private static String getPrettyPrintXML(Source source)
    {
        try
        {
            TransformerFactory tff = TransformerFactory.newInstance();
            Transformer tf = tff.newTransformer();
            tf.setOutputProperty(OutputKeys.INDENT, "yes");
            tf.setOutputProperty("{http://xml.apache.org/xslt}indent-amount", "2");
            ByteArrayOutputStream streamOut = new ByteArrayOutputStream();
            StreamResult result = new StreamResult(streamOut);
            tf.transform(source, result);
            return streamOut.toString();
        }
        catch (TransformerException e)
        {
            return "";
        }
    }

    /**
     * Преобразование строки через hex
     * @param in входная строка
     * @return зашифрованная строка
     */
    private static String hexEncode(String in)
    {
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < (in.length() - 2) + 1; i = i + 2)
        {
            int c = Integer.parseInt(in.substring(i, i + 2), 16);
            char chr = (char)c;
            sb.append(chr);
        }
        return sb.toString();
    }

    public void setSoapRequestTimeoutSeconds(Long soapRequestTimeoutSeconds)
    {
        this.soapRequestTimeoutSeconds = soapRequestTimeoutSeconds;
    }
}