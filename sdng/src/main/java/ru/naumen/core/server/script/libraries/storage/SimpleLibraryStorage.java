package ru.naumen.core.server.script.libraries.storage;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;
import java.util.jar.JarFile;
import java.util.jar.Manifest;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.DependsOn;
import org.springframework.stereotype.Component;

import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.metastorage.impl.metainfo.MetaStorageService;
import ru.naumen.core.server.script.libraries.CommonLibrariesService;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;

/**
 * Простое хранилище библиотек.
 * Сохраняет библиотеки в метаинформацию и на диск {data.dir}/libraries
 * <AUTHOR>
 * @since 26.05.2020
 */
@Component
@DependsOn("librariesMetaStorageSerializer")
public class SimpleLibraryStorage implements LibraryStorage
{
    private static final Logger LOG = LoggerFactory.getLogger(SimpleLibraryStorage.class);
    private static final String LIBRARIES_DIR = "libraries";
    private final MetaStorageService metaStorageService;
    private final Path libraries;
    private final ConcurrentHashMap<String, Path> pathCache = new ConcurrentHashMap<>();
    private final ConcurrentHashMap<String, Date> expirationDateCache = new ConcurrentHashMap<>();

    public SimpleLibraryStorage(@Value("${data.dir}") String dataDir, MetaStorageService metaStorageService)
    {
        this.libraries = Paths.get(dataDir).resolve(LIBRARIES_DIR);
        this.metaStorageService = metaStorageService;
    }

    @PostConstruct
    public void init()
    {
        reload();
    }

    @Override
    public ScriptLibrary getLibrary(String name)
    {
        ScriptLibrary library = metaStorageService.get(ScriptLibrary.META_TYPE, name, null);
        if (library != null)
        {
            createFileIfNeed(library);
            fillLibraryTransientFields(library);
        }
        return library;
    }

    @Override
    public Path getLibraryPath(ScriptLibrary library)
    {
        return libraries.resolve(library.getChecksum()).resolve(library.getName());
    }

    @Override
    public Stream<ScriptLibrary> getNonExpiredLibraries()
    {
        Date currentDate = new Date();
        return metaStorageService.<ScriptLibrary> get(ScriptLibrary.META_TYPE).stream()
                .map(library ->
                {
                    createFileIfNeed(library);
                    try
                    {
                        fillLibraryTransientFields(library);
                        return library;
                    }
                    catch (Exception e)
                    {
                        //Не будем падать, если library битая по какой-то причине, пропустим ее
                        LOG.error("An error occurred while reading library {}", library.getName(), e);
                    }
                    return null;
                })
                .filter(Objects::nonNull)
                .filter(library -> isLibraryNotExpired(library, currentDate));
    }

    private static boolean isLibraryNotExpired(Path libraryPath, Date currentDate)
    {
        return getLibraryExpirationDate(libraryPath)
                .map(date -> date.after(currentDate))
                .orElse(true);
    }

    private static boolean isLibraryNotExpired(ScriptLibrary library, Date currentDate)
    {
        return isLibraryNotExpired(library.getPath(), currentDate);
    }

    @Override
    public Path save(ScriptLibrary newLibrary)
    {
        LOG.info("Saving library with name {}. Checksum = {}", newLibrary.getName(), newLibrary.getChecksum());
        metaStorageService.save(newLibrary, ScriptLibrary.META_TYPE, newLibrary.getName());
        Path path = saveAtFS(newLibrary);
        fillLibraryTransientFields(newLibrary);
        return path;
    }

    @Override
    public boolean delete(ScriptLibrary library)
    {
        final boolean isRemovedFromMetastorage = metaStorageService.remove(
                ScriptLibrary.META_TYPE, library.getName());

        final Path libraryPath = getLibraryPath(library);

        final boolean isRemovedFromFileSystem;
        if (!libraryPath.toFile().delete())
        {
            isRemovedFromFileSystem = false;
            LOG.error("Library file at path {} was not deleted", libraryPath);
        }
        else
        {
            isRemovedFromFileSystem = true;
        }
        String libraryChecksum = library.getChecksum();
        pathCache.remove(libraryChecksum);
        expirationDateCache.remove(libraryChecksum);

        return isRemovedFromFileSystem || isRemovedFromMetastorage;
    }

    @Override
    public Set<String> getLibrariesNames()
    {
        return metaStorageService.<ScriptLibrary> get(ScriptLibrary.META_TYPE).stream()
                .map(ScriptLibrary::getName)
                .collect(Collectors.toUnmodifiableSet());
    }

    @Override
    public Collection<ScriptLibrary> getAllLibraries()
    {
        return metaStorageService.get(ScriptLibrary.META_TYPE)
                .stream()
                .filter(ScriptLibrary.class::isInstance)
                .map(ScriptLibrary.class::cast)
                //peek может быть не вызван, поэтому map
                .map(this::fillLibraryTransientFields)
                .toList();
    }

    @Override
    public List<Path> reload()
    {
        pathCache.clear();
        expirationDateCache.clear();
        Date currentDate = new Date();
        final Collection<ScriptLibrary> libs = metaStorageService.get(ScriptLibrary.META_TYPE);
        return libs.stream()
                .map(lib ->
                {
                    try
                    {
                        Path path = saveAtFS(lib);
                        fillLibraryTransientFields(lib);
                        return path;
                    }
                    catch (Exception e)
                    {
                        //Если что-то пошло не так, то не стоит блокировать все библиотеки при загрузке приложения.
                        LOG.error("An error occurred while reading library {}", lib.getName(), e);
                        return null;
                    }
                })
                .filter(Objects::nonNull)
                .filter(libPath -> isLibraryNotExpired(libPath, currentDate))
                .collect(Collectors.toCollection(ArrayList::new));
    }

    @Override
    public List<Path> reset()
    {
        pathCache.forEach((key, value) ->
        {
            try
            {
                Files.deleteIfExists(value);
                LOG.info("Deleted the lib by path {}", value);
            }
            catch (IOException e)
            {
                LOG.error("Error deleting path {}", value, e);
            }
        });
        return reload();
    }

    private Path saveAtFS(ScriptLibrary library)
    {
        try
        {
            final Path libraryPath = getLibraryPath(library);
            if (Files.exists(libraryPath))
            {
                return libraryPath;
            }
            Files.createDirectories(libraryPath.getParent());
            return Files.write(libraryPath, library.getContent(), StandardOpenOption.CREATE_NEW);
        }
        catch (final Exception e)
        {
            throw new FxException(e);
        }
    }

    @Nullable
    private Path getFSPath(String checksum, String libraryId)
    {
        final Path libraryPath = libraries.resolve(checksum).resolve(libraryId);
        if (!Files.exists(libraryPath))
        {
            return null;
        }
        return libraryPath;
    }

    private void createFileIfNeed(ScriptLibrary library)
    {
        if (getFSPath(library.getChecksum(), library.getName()) == null)
        {
            saveAtFS(library);
        }
    }

    private ScriptLibrary fillLibraryTransientFields(ScriptLibrary library)
    {
        String libraryChecksum = library.getChecksum();
        if (!pathCache.containsKey(libraryChecksum))
        {
            Path libraryPath = getLibraryPath(library);
            library.setPath(libraryPath);
            Date expirationDate = getLibraryExpirationDate(libraryPath).orElse(null);
            library.setExpirationDate(expirationDate);
            pathCache.put(libraryChecksum, libraryPath);
            if (expirationDate != null)
            {
                expirationDateCache.put(libraryChecksum, expirationDate);
            }
        }
        else
        {
            library.setPath(pathCache.get(libraryChecksum));
            library.setExpirationDate(expirationDateCache.get(libraryChecksum));
        }
        return library;
    }

    private static Optional<Date> getLibraryExpirationDate(Path libPath)
    {
        try (JarFile jarFile = new JarFile(libPath.toFile()))
        {
            return getExpirationDate(jarFile);
        }
        catch (IOException e)
        {
            throw new FxException("Error occurred while reading jar to library %s".formatted(libPath), e);
        }
    }

    /**
     * Возвращает дату действия файла jar из MANIFEST.MF
     */
    public static Optional<Date> getExpirationDate(JarFile jarFile)
    {
        try
        {
            Manifest manifest = jarFile.getManifest();
            String expirationDateStr = manifest == null || manifest.getMainAttributes() == null
                    ? null
                    : manifest.getMainAttributes().getValue(CommonLibrariesService.EXPIRATION_DATE_ATTRIBUTE_NAME);
            return Optional.ofNullable(expirationDateStr)
                    .map(expirationDateString ->
                    {
                        try
                        {
                            return new SimpleDateFormat(CommonLibrariesService.EXPIRATION_DATE_FORMAT)
                                    .parse(expirationDateString);
                        }
                        catch (ParseException e)
                        {
                            throw new FxException("Incorrect date format at expiration date "
                                                  + StringUtils.wrap(expirationDateString, "'"), e);
                        }
                    });
        }
        catch (IOException e)
        {
            throw new FxException("Error occurred while reading jar to library %s".formatted(jarFile.getName()), e);
        }
    }
}
