/**
 */
package ru.naumen.core.server.timing.calculate;

import java.util.Date;
import java.util.TimeZone;

/**
 *
 */
public interface ITimingCalculator
{

    /**
     * Получение вычислимой даты сервисного времени окончания периода обслуживания
     * <p>
     * Дата зависит от календарной даты начала ({@link #setStartDate(Date)}) и
     * требуемой продолжительности времени обслуживания {@link #setServiceTime(long)}
     * </p>
     * Если момен истечения сервисного времени приходится на конец одного из периодов
     * обслуживания, то в качестве результата должен быть выбран именно момент окончания
     * этого сервисного периода, а не начало следующего.
     *
     * @return дата окончания периода обслуживания
     */
    Date getServiceEndDate();

    /**
     * Получение вычислимой даты сервисного времени начала периода обслуживания
     * @return дата начала периода обслуживания
     */
    Date getServiceStartDate();

    /**
     * Получение значение регламентного (сервисного времени),
     * либо ранее установленнного методом {@link #setServiceTime(long),
     * либо вычисленного по требуемым календарным датам {@link #setStartDate(Date) начала}
     * и {@link #setEndDate(Date) окончания обслуживания}
     * Используется для вычисления прошедшего сервсисного времени
     * между календарными датами начала и окончания обслуживания.
     * @return продолжительность сервисного времени
     */
    long getServiceTime();

    /**
     * Часовой пояс
     * @return часовой пояс
     */
    TimeZone getTimeZone();

    /**
     * Установка календарной даты требуемого окончания периода обслуживания
     * @param end календарная дата окончания периода
     * @return <code>this</code>
     */
    ITimingCalculator setEndDate(Date end);

    /**
     * Установка требуемой продолжительности сервисного времени(обслуживания)
     * Используется при вычислении даты окончания периода обслуживания {@link #getServiceEndDate()}
     * @param serviceTime продолжительность периода обслуживания
     * @return <code>this</code>
     */
    ITimingCalculator setServiceTime(long serviceTime);

    /**
     * Установка календарной даты требуемого начала периода обслуживания
     * @param start календарная дата начала периода
     * @return <code>this</code>
     */
    ITimingCalculator setStartDate(Date start);
}