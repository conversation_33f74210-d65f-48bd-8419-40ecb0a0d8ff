package ru.naumen.core.server.script;

import java.util.Date;

import jakarta.inject.Inject;
import jakarta.inject.Singleton;

import org.springframework.stereotype.Component;

import ru.naumen.common.shared.utils.IDateTimeInterval;
import ru.naumen.common.shared.utils.IHyperlink;
import ru.naumen.core.server.common.FormattersSrv;
import ru.naumen.core.shared.IUUIDIdentifiable;

/**
 * wrapper для использования в скриптах методов форматирования
 * <AUTHOR>
 * @since 19.03.2013
 *
 */
@Component
@Singleton
public class FormattersWrapper implements IFormattersWrapper
{
    @Inject
    FormattersSrv formatters;

    @Override
    public Long bytesToKb(Long value, boolean roundUp)
    {
        return formatters.bytesToKb(value, roundUp);
    }

    @Override
    public Long bytesToMb(Long value, boolean roundUp)
    {
        return formatters.bytesToMb(value, roundUp);
    }

    @Override
    public Double bytesToMbDouble(Long value)
    {
        return formatters.bytesToMbDouble(value);
    }

    @Override
    public String bytesToMbStringWithPrecision(Long value, int precision)
    {
        return formatters.bytesToMbStringWithPrecision(value, precision);
    }

    @Override
    public SafeHtmlWrapper escapeHtmlSymbols(String text)
    {
        return new SafeHtmlWrapper(formatters.escapeHtmlSymbols(text));
    }

    @Override
    public String format(IUUIDIdentifiable obj)
    {
        return formatters.format(obj);
    }

    @Override
    public String formatDate(Date time)
    {
        return formatters.formatDate(time);
    }

    @Override
    public String formatDateTime(Date time)
    {
        return formatters.formatDateTime(time);
    }

    @Override
    public String formatDateTime(Date time, String pattern)
    {
        return formatters.formatDateTime(time, null, pattern);
    }

    @Override
    public String formatDateTimeInterval(IDateTimeInterval dateTimeInterval)
    {
        return formatters.formatDateTimeInterval(dateTimeInterval);
    }

    @Override
    public String formatHyperlink(IHyperlink hyperlink)
    {
        return formatters.formatHyperlink(hyperlink);
    }

    @Override
    public SafeHtmlWrapper formatHyperlinkAsHtml(IHyperlink hyperlink)
    {
        return new SafeHtmlWrapper(formatters.formatHyperlinkAsHtml(hyperlink));
    }

    @Override
    public SafeHtmlWrapper formatLongToTime(Long value, boolean roundUp)
    {
        return new SafeHtmlWrapper(formatters.formatLongToTime(value, roundUp));
    }

    @Override
    public String formatTimePeriod(long ms)
    {
        return formatters.formatTimePeriod(ms);
    }

    @Override
    public String oneZeroFormatter(Object obj)
    {
        return formatters.oneZeroFormatter(obj);
    }

    @Override
    public Date strToDate(String time)
    {
        return formatters.strToDate(time);
    }

    @Override
    public Date strToDate(String time, String dateFormat)
    {
        return formatters.strToDate(time, dateFormat);
    }

    @Override
    public Date strToDateTime(String time)
    {
        return formatters.strToDateTime(time);
    }

    @Override
    public String yesNoFormatter(Object obj)
    {
        return formatters.yesNoFormatter(obj);
    }
}
