package ru.naumen.core.server.script.api.scheduler;

import java.util.Date;

import com.google.common.base.Function;

import ru.naumen.core.server.util.JsonUtils;
import ru.naumen.metainfo.shared.scheduler.Trigger;

/**
 * Обертка {@see Trigger} для использования в скриптах
 * <AUTHOR>
 * @since 18.12.2013
 *
 */
public class TriggerWrapper implements ITriggerWrapper
{
    public static final Function<Trigger, ITriggerWrapper> TRIGGER_WRAPPER = new Function<Trigger, ITriggerWrapper>()
    {
        @Override
        public TriggerWrapper apply(Trigger input)
        {
            return null == input ? null : new TriggerWrapper(input);
        }
    };

    private final Trigger trigger;

    public TriggerWrapper(Trigger input)
    {
        this.trigger = input;
    }

    @Override
    public String getCode()
    {
        return trigger.getCode();
    }

    @Override
    public Date getLastExecutionDate()
    {
        return trigger.getLastExecutionDate();
    }

    @Override
    public Date getPlanExecutionDate()
    {
        return trigger.getPlanExecutionDate();
    }

    @Override
    public String getSchTaskCode()
    {
        return trigger.getSchTaskCode();
    }

    @Override
    public String getTitle()
    {
        return trigger.getTitle();
    }

    @Override
    public String getType()
    {
        return trigger.getType().name();
    }

    @Override
    public boolean isEnabled()
    {
        return trigger.isEnabled();
    }

    @Override
    public String prettyPrint()
    {
        Date lastExecutionDate = getLastExecutionDate();
        Date planExecutionDate = getPlanExecutionDate();

        StringBuilder builder = new StringBuilder();

        //formatter:off
        builder
                .append("Trigger name: ")
                .append(getCode())
                .append("<br>")
                .append("Trigger type: ")
                .append(getType())
                .append("<br>")
                .append("Trigger is enabled: ")
                .append(isEnabled())
                .append("<br>");
        //formatter:on

        if (lastExecutionDate == null)
        {
            builder.append("Last execution date: never");
        }
        else
        {
            builder.append("Last execution date: ").append(lastExecutionDate);
        }

        builder.append("<br>");

        if (planExecutionDate == null)
        {
            builder.append("Plan execution date: never");
        }
        else
        {
            builder.append("Plan execution date: ").append(planExecutionDate);
        }

        builder.append("<br>");

        return builder.toString();
    }

    @Override
    public String toString()
    {
        return JsonUtils.toJson(trigger);
    }

}
