package ru.naumen.core.server.script.api;

import static ru.naumen.core.shared.Constants.API_WEBSOCKET_QUEUE_NAME;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.Set;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import com.google.common.base.Preconditions;

import groovy.lang.Closure;
import jakarta.annotation.Nullable;
import jakarta.inject.Inject;
import jakarta.websocket.DeploymentException;
import ru.naumen.commons.shared.FxException;
import ru.naumen.core.server.script.ScriptServiceException;
import ru.naumen.core.server.util.MessageFacade;
import ru.naumen.websocket.server.WebSocketSTOMPService;
import ru.naumen.websocket.server.nccintegration.WebsocketClientEndpointService;
import ru.naumen.websocket.server.nccintegration.settings.WebsocketConnectionConfigService;
import ru.naumen.websocket.shared.WebsocketHelper;
import ru.naumen.websocket.shared.WsConstants;
import ru.naumen.websocket.shared.settings.WebsocketConnectionConfig;

/**
 * Реализация {@link IWebsocketApi}
 * <AUTHOR>
 * @since 31 янв. 2017 г.	
 */
@Component("websocket")
public class WebsocketApi implements IWebsocketApi
{
    private static final Logger LOG = LoggerFactory.getLogger(WebsocketApi.class);
    private static final String WS_CONNECTION_FAILED_MESSAGE_CODE = "websocket.connectionFailed";

    private final WebsocketClientEndpointService endpointService;
    private final WebsocketConnectionConfigService connectionConfigService;
    private final MessageFacade messages;
    private final WebsocketHelper helper;
    private final WebSocketSTOMPService webSocketSTOMPService;

    private final Lock lock = new ReentrantLock();

    @Inject
    public WebsocketApi(WebsocketClientEndpointService endpointService,
            WebsocketConnectionConfigService connectionConfigService, MessageFacade messages,
            WebsocketHelper helper, WebSocketSTOMPService webSocketSTOMPService)
    {
        this.endpointService = endpointService;
        this.connectionConfigService = connectionConfigService;
        this.messages = messages;
        this.helper = helper;
        this.webSocketSTOMPService = webSocketSTOMPService;
    }

    @Override
    public void addMessageHandler(String code, Object messageHandler)
    {
        connect(code, messageHandler);
    }

    @Override
    public void addMessageHandlers(String code, String... handlersNames)
    {
        WebsocketConnectionConfig connectionConfig = connectionConfigService.get(code);

        if (connectionConfig.getModuleName().equals(WsConstants.NOT_CONFIGURED))
        {
            throw new FxException(messages.getMessage("websocket.notInModule"), true);
        }

        try
        {
            endpointService.connect(code,
                    helper.getHandlersMethods(connectionConfig.getModuleName(), Arrays.asList(handlersNames)));
        }
        catch (DeploymentException | IOException e)
        {
            throw new FxException(messages.getMessage("websocket.canNotAddHandlers"), true, e);
        }
        catch (ScriptServiceException e)
        {
            throw new FxException(messages.getMessage("websocket.moduleOrMethodNotFound", code), true, e);
        }

        List<String> handlers = connectionConfig.getHandlersNames();
        handlers.addAll(Arrays.asList(handlersNames));
        connectionConfig.setHandlersNames(handlers);
        connectionConfigService.save(connectionConfig);
    }

    @Override
    public void close(String code)
    {
        endpointService.disconnect(code);
    }

    @Override
    public void connect(String code)
    {
        WebsocketConnectionConfig connectionConfig = connectionConfigService.get(code);
        String moduleName = connectionConfig.getModuleName();

        if (moduleName == null || moduleName.equals(WsConstants.NOT_CONFIGURED))
        {
            throw new FxException(messages.getMessage("websocket.notInModule"), true);
        }

        try
        {
            String initializerName = connectionConfig.getInitializerName();
            if (initializerName.equals(WsConstants.NOT_CONFIGURED))
            {
                endpointService.connect(code, helper.getHandlersMethods(connectionConfig));
                return;
            }
            String messageIdExtractor = connectionConfig.getMessageIdExtractorName();
            endpointService.connect(code,
                    endpoint -> helper.getInitializerMethod(moduleName, initializerName).accept(endpoint),
                    WsConstants.NOT_CONFIGURED.equals(messageIdExtractor)
                            ? null
                            : message -> (String)helper.getMessageIdExtractorMethod(moduleName, messageIdExtractor)
                                    .apply(message),
                    helper.getHandlersMethods(connectionConfig));
        }
        catch (DeploymentException | IOException e)
        {
            throw new FxException(messages.getMessage(WS_CONNECTION_FAILED_MESSAGE_CODE, code), true, e);
        }
        catch (ScriptServiceException e)
        {
            throw new FxException(messages.getMessage("websocket.moduleOrMethodNotFound", code), true, e);
        }
    }

    @SuppressWarnings("unchecked")
    @Override
    public void connect(String code, Object messageHandler)
    {
        Preconditions.checkArgument(messageHandler instanceof Closure, "Wrong argument type");
        try
        {
            endpointService.connect(code,
                    ((Closure<Object>)messageHandler)::call);
        }
        catch (DeploymentException | IOException e)
        {
            throw new FxException(messages.getMessage(WS_CONNECTION_FAILED_MESSAGE_CODE, code), true, e);
        }
    }

    @Override
    public void connect(String code, final Object initializer, final Object messageHandler)
    {
        connect(code, initializer, null, messageHandler);
    }

    @SuppressWarnings("unchecked")
    @Override
    public void connect(String code, final Object initializer, @Nullable final Object messageIdExtractor,
            final Object messageHandler)
    {
        Preconditions.checkArgument(initializer instanceof Closure, "Wrong argument type");
        Preconditions.checkArgument(messageHandler instanceof Closure, "Wrong argument type");
        try
        {
            endpointService.connect(code,
                    ((Closure<Object>)initializer)::call,
                    message -> messageIdExtractor == null
                            ? null
                            : (String)((Closure<Object>)messageIdExtractor).call(message),
                    ((Closure<Object>)messageHandler)::call);
        }
        catch (DeploymentException | IOException e)
        {
            throw new FxException(messages.getMessage(WS_CONNECTION_FAILED_MESSAGE_CODE, code), true, e);
        }
    }

    @Override
    public String create(String url)
    {
        return createWebsocketConnectionConfigInt(null, url, url).getCode();
    }

    @Override
    public void create(String code, String url)
    {
        createWebsocketConnectionConfigInt(code, url, url);
    }

    @Override
    public void create(String code, String title, String url)
    {
        createWebsocketConnectionConfigInt(code, title, url);
    }

    @Override
    public String create(String code, String title, String url, String moduleName, String initializerName,
            @Nullable String idExtractorName, String... messageHandlersMethodsNames)
    {
        WebsocketConnectionConfig connectionConfig = createWebsocketConnectionConfigInt(code, title,
                url, moduleName, initializerName,
                Objects.requireNonNullElse(idExtractorName, WsConstants.NOT_CONFIGURED),
                Arrays.asList(messageHandlersMethodsNames));

        if (connectionConfig == null)
        {
            throw new FxException(messages.getMessage("websocket.configCreationFailed"), true);
        }
        else
        {
            String message = messages.getMessage("websocket.configCreationSuccess", code);
            LOG.info(message);
            return message;
        }
    }

    @Override
    public void delete(String code, boolean forceClose)
    {
        boolean active = isActive(code);

        if (active && forceClose)
        {
            close(code);
        }
        else if (active)
        {
            throw new FxException(messages.getMessage("websocket.notDeletedConnectionIsActive", code), true);
        }

        connectionConfigService.delete(code);
    }

    @Override
    public void disableConfig(String code)
    {
        WebsocketConnectionConfig configToDisable = connectionConfigService.get(code);

        configToDisable.setActive(false);

        connectionConfigService.save(configToDisable);
    }

    @Override
    public void editConfigHandlersNames(String code, boolean saveOld, String... newHandlersNames)
    {
        WebsocketConnectionConfig configToEdit = connectionConfigService.get(code);

        if (saveOld)
        {
            addMessageHandlers(code, newHandlersNames);
        }
        else
        {
            configToEdit.setHandlersNames(Arrays.asList(newHandlersNames));

            connectionConfigService.save(configToEdit);

            if (helper.fullyConfiguredAndActive(configToEdit))
            {
                reconnect(code);
            }
        }
    }

    @Override
    public void editConfigModuleName(String code, String newModuleName)
    {
        WebsocketConnectionConfig configToEdit = connectionConfigService.get(code);

        configToEdit.setModuleName(newModuleName);

        connectionConfigService.save(configToEdit);

        if (helper.fullyConfiguredAndActive(configToEdit))
        {
            reconnect(code);
        }
    }

    @Override
    public void editConfigUrl(String code, String newUrl)
    {
        WebsocketConnectionConfig configToEdit = connectionConfigService.get(code);

        configToEdit.setUrl(newUrl);

        connectionConfigService.save(configToEdit);

        if (helper.fullyConfiguredAndActive(configToEdit))
        {
            reconnect(code);
        }
    }

    @Override
    public void enableConfig(String code)
    {
        WebsocketConnectionConfig configToEnable = connectionConfigService.get(code);

        configToEnable.setActive(true);

        connectionConfigService.save(configToEnable);
    }

    @Override
    public Set<String> getActiveConnectionCodes()
    {
        return endpointService.getActiveWebsocketCodes();
    }

    @Override
    public List<String> getAllConnectionCodes()
    {
        return connectionConfigService.list().stream()
                .map(WebsocketConnectionConfig::getCode)
                .toList();
    }

    @Override
    public String getConfigInfo(String code)
    {
        return helper.getConfigInfo(connectionConfigService.get(code)).replace("\n", "<br>");
    }

    @Override
    public boolean isActive(String code)
    {
        return endpointService.isActive(code);
    }

    @Override
    public boolean isEnabled(String code)
    {
        return connectionConfigService.get(code).getActive();
    }

    @Override
    public void reconnect(String code)
    {
        try
        {
            lock.lock();
            close(code);
            connect(code);
        }
        finally
        {
            lock.unlock();
        }
    }

    @Override
    public void reload(String code)
    {
        try
        {
            endpointService.reload(code);
        }
        catch (DeploymentException | IOException e)
        {
            throw new FxException(messages.getMessage("websocket.reloadError"), true, e);
        }
    }

    @Override
    public void reloadAll()
    {
        try
        {
            endpointService.reloadAll();
        }
        catch (DeploymentException | IOException e)
        {
            throw new FxException(messages.getMessage("websocket.reloadError"), true, e);
        }
    }

    @Override
    public void send(String code, String message)
    {
        if (!isActive(code))
        {
            throw new FxException(messages.getMessage("websocket.notSendedConnectionIsNotActive", code), true);
        }
        endpointService.get(code).send(message);
    }

    @Override
    public void sendSync(String code, String message)
    {
        if (!isActive(code))
        {
            throw new FxException(messages.getMessage("websocket.notSendedConnectionIsNotActive", code), true);
        }
        endpointService.get(code).sendSync(message);
    }

    @Override
    public void sendMessage(String destination, String message)
    {
        webSocketSTOMPService.sendMessageToSubscribers(API_WEBSOCKET_QUEUE_NAME + "." + destination, message);
    }

    /**
     * Создать конфигурацию подключения к вебсокету
     * @param code код конфигурации подключения
     * @param title название подключения
     * @param url URL вебсокета
     * @return конфигурация подключения к вебсокету
     */
    WebsocketConnectionConfig createWebsocketConnectionConfigInt(String code, String title, String url)
    {
        return createWebsocketConnectionConfigInt(code, title, url, WsConstants.NOT_CONFIGURED,
                WsConstants.NOT_CONFIGURED, WsConstants.NOT_CONFIGURED, new ArrayList<>());
    }

    WebsocketConnectionConfig createWebsocketConnectionConfigInt(String code, String title, String url,
            String moduleName, String initializerName, String idExtractorName, List<String> handlersNames)
    {
        WebsocketConnectionConfig connectionConfig = new WebsocketConnectionConfig();
        connectionConfig.setActive(true);
        connectionConfig.setCode(code);
        connectionConfig.setTitle(title);
        connectionConfig.setUrl(url);
        connectionConfig.setModuleName(moduleName);
        connectionConfig.setInitializerName(initializerName);
        connectionConfig.setMessageIdExtractorName(idExtractorName);
        connectionConfig.setHandlersNames(handlersNames);
        connectionConfigService.save(connectionConfig);
        return connectionConfig;
    }
}
