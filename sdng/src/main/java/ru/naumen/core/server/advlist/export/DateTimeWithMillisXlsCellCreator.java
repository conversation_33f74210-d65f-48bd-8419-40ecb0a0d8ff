package ru.naumen.core.server.advlist.export;

import static ru.naumen.metainfo.shared.Constants.Presentations.DATETIME_WITH_MILLIS_VIEW;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;

import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.usermodel.Workbook;

import ru.naumen.core.shared.attr.FormatterContext;
import ru.naumen.metainfo.shared.Constants.DateTimeAttributeType;

/**
 * <AUTHOR>
 * @since 29.03.2017
 *
 */
@CellCreatorComponent(codes = {
        DateTimeAttributeType.CODE + IXlsCellCreatorsRegistry.PRESENTATION_SEPARATOR + DATETIME_WITH_MILLIS_VIEW })
public class DateTimeWithMillisXlsCellCreator extends AbstractXlsCellCreator
{
    private static final String DATE_FORMAT_PATTERN = "dd.MM.yyyy HH:mm:ss.SSS";
    private static final ThreadLocal<SimpleDateFormat> DATE_FORMAT = ThreadLocal.withInitial(
            () -> new SimpleDateFormat(DATE_FORMAT_PATTERN));

    @Override
    public CellStyle createCellStyle(Workbook workbook)
    {
        CellStyle dateStyle = workbook.createCellStyle();
        dateStyle.setDataFormat(workbook.createDataFormat().getFormat(DATE_FORMAT_PATTERN));
        return dateStyle;
    }

    @Override
    protected void setCellValue(Cell cell, Object attributeValue, Workbook workbook, FormatterContext context)
    {
        if (null != attributeValue)
        {
            if (attributeValue instanceof Timestamp)
            {
                Date date = new Date(((Timestamp)attributeValue).getTime());

                cell.setCellValue(DATE_FORMAT.get().format(date));
            }
            else
            {
                cell.setCellValue((Date)attributeValue);
            }
        }
    }
}
