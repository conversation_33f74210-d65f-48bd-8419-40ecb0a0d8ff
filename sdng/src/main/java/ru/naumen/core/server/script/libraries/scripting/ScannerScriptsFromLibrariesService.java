package ru.naumen.core.server.script.libraries.scripting;

import java.nio.file.Path;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.Map.Entry;
import java.util.Set;
import java.util.function.Function;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import groovy.lang.GroovyClassLoader;
import io.github.classgraph.ClassGraph;
import io.github.classgraph.ScanResult;
import jakarta.annotation.Nullable;
import jakarta.annotation.PostConstruct;
import jakarta.inject.Inject;
import ru.naumen.core.server.script.ScriptService;
import ru.naumen.core.server.script.libraries.LibrariesClassifier;
import ru.naumen.core.server.script.libraries.LibrariesClassifier.LibrariesClassificationResult;
import ru.naumen.core.server.script.libraries.storage.LibraryStorage;
import ru.naumen.core.server.script.libraries.validation.ValidationException;
import ru.naumen.core.server.script.modules.compile.ModulesClassLoaderManager;
import ru.naumen.core.server.script.modules.compile.NauGroovyClassLoader;
import ru.naumen.core.server.script.spi.verifyAnnotations.AnnotationVerificationService;
import ru.naumen.core.shared.HasCode;
import ru.naumen.metainfo.server.libraries.ScriptLibrary;

/**
 * Сервис для сканирования библиотек на предмет аннотаций, указывающих на то, что класс используется в качестве скрипта
 *
 * @see ru.naumen.core.server.script.libraries.registration.role.InternalAutomationRole
 * @see ru.naumen.core.server.script.libraries.registration.modules.InternalAutomationScriptModule
 * @see ru.naumen.core.server.script.libraries.registration.scripts.InternalAutomationScript
 * <AUTHOR>
 * @since 22.06.2021
 */
@Service
public class ScannerScriptsFromLibrariesService
{
    private static final Logger LOG = LoggerFactory.getLogger(ScannerScriptsFromLibrariesService.class);

    private final List<StorageService<? extends HasCode, ? extends HasCode>> storageServices;
    private final ModulesClassLoaderManager modulesClassLoaderManager;
    private final LibraryStorage libraryStorage;
    private final AnnotationVerificationService annotationVerificationService;
    private final ScriptService scriptService;

    @Inject
    public ScannerScriptsFromLibrariesService(
            List<StorageService<? extends HasCode, ? extends HasCode>> storageServices,
            LibraryStorage libraryStorage,
            ModulesClassLoaderManager modulesClassLoaderManager,
            AnnotationVerificationService annotationVerificationService,
            ScriptService scriptService)
    {
        this.modulesClassLoaderManager = modulesClassLoaderManager;
        this.libraryStorage = libraryStorage;
        this.annotationVerificationService = annotationVerificationService;
        this.storageServices = storageServices;
        this.scriptService = scriptService;
    }

    @PostConstruct
    public void init()
    {
        final GroovyClassLoader modulesClassLoader = modulesClassLoaderManager.getModulesClassLoader();
        final Collection<Path> libs = getNonExpiredLibrariesJarsPaths();
        if (!libs.isEmpty())
        {
            registerScriptsFromLibraries(
                    modulesClassLoader,
                    modulesClassLoaderManager::getClassLoaderForScript,
                    libs,
                    false,
                    false);
        }
    }

    /**
     * Регистрирует модули и скрипты из библиотек в мете
     *
     * @param classLoader                             класслоадер для разрешения зависимостей;
     * @param embeddedApplicationsClassLoaderProvider средство получения класслоадера для разрешения зависимостей
     *                                                встроенных приложений;
     * @param libraries                               коллекция библиотек, которые нужно добавить к сканированию;
     * @param needVerification                        нужно ли верифицировать библиотеки;
     * @param refreshCache                            признак того, что выполняется перезагрузка кэша, а значит
     *                                                изменять метаинформацию нельзя;
     */
    private void registerScriptsFromLibraries(
            GroovyClassLoader classLoader,
            Function<String, NauGroovyClassLoader> embeddedApplicationsClassLoaderProvider,
            Collection<Path> libraries,
            boolean needVerification,
            boolean refreshCache)
    {
        final LibrariesClassificationResult classifiedLibraries = LibrariesClassifier.classify(libraries);
        final List<String> failures = new ArrayList<>(classifiedLibraries.failures());
        if (failures.isEmpty())
        {
            registerLibraries(
                    classLoader,
                    classifiedLibraries.genericLibraries(),
                    needVerification,
                    failures,
                    null,
                    refreshCache);
            for (Entry<String, String> entry : classifiedLibraries.embeddedApplicationsLibraries().entrySet())
            {
                final String embeddedApplicationCode = entry.getKey();
                final NauGroovyClassLoader embeddedAppClassLoader = embeddedApplicationsClassLoaderProvider
                        .apply(embeddedApplicationCode);
                registerLibraries(
                        embeddedAppClassLoader,
                        List.of(entry.getValue()),
                        needVerification,
                        failures,
                        embeddedApplicationCode,
                        refreshCache);
            }
        }
        if (needVerification && !failures.isEmpty())
        {
            throw new ValidationException(failures);
        }
    }

    private void registerLibraries(
            GroovyClassLoader classLoader,
            Collection<String> jarsFileNames,
            boolean needVerification,
            List<String> failures,
            @Nullable String embeddedApplicationCode,
            boolean refreshCache)
    {
        try (final ScanResult scanResult = getScanResult(classLoader, jarsFileNames))
        {
            if (needVerification)
            {
                failures.addAll(annotationVerificationService.verifyScanResult(scanResult));
            }
            if (failures.isEmpty())
            {
                storageServices.forEach(storageService ->
                        storageService.registerScriptsFromLibraries(
                                classLoader,
                                scanResult,
                                refreshCache,
                                embeddedApplicationCode));
            }
        }
        catch (Exception e)
        {
            LOG.atError()
                    .setCause(e)
                    .setMessage("There is an exception during scripts registration from the libraries: {}")
                    .addArgument(e::getMessage)
                    .log();
            failures.add(e.getMessage());
        }
    }

    /**
     * Перезагрузка в кластере информации, что содержится в загружаемых библиотеках
     * Этот метод должен вызываться только в методе синхронизации нод
     */
    public void resetLibraries(Set<Path> libraries)
    {
        scriptService.executeInNewClassLoader(classLoaderTemplate ->
        {
            final Set<Path> jarsPathsToAdd = getLibrariesJarsPathsForAddition(libraries, true);
            final GroovyClassLoader classLoader = classLoaderTemplate.getMainClassLoader();
            resetStorageServices();
            registerScriptsFromLibraries(
                    classLoader,
                    classLoaderTemplate::getChildClassLoader,
                    jarsPathsToAdd,
                    false,
                    true);
        });
    }

    /**
     * Регистрируем существующие библиотеки в новом класслоадере
     *
     * @param librariesToAdd    библиотеки для добавления
     * @param librariesToDelete имена библиотек для удаления
     * @param isReset           true - происходит полная перезагрузка библиотек, false - обычное изменение состава
     *                          библиотек;
     * @param needVerification  нужно ли верифицировать библиотеки;
     */
    public void registerNewLibraries(
            Set<Path> librariesToAdd,
            Set<String> librariesToDelete,
            boolean isReset,
            boolean needVerification)
    {
        scriptService.executeInNewClassLoader(classLoaderTemplate ->
        {
            final Set<String> jarsToAdd = getLibrariesJarsNamesForAddition(librariesToAdd, isReset);
            final Set<Path> jarsPathsToAdd = getLibrariesJarsPathsForAddition(librariesToAdd, isReset);
            if (isReset)
            {
                // полная перезагрузка библиотек
                resetStorageServices();
            }
            else
            {
                // обычное изменение состава библиотек
                storageServices.forEach(storageService ->
                        storageService.deleteScriptsFromChangingLibraries(jarsToAdd, librariesToDelete));
            }
            final GroovyClassLoader classLoader = classLoaderTemplate.getMainClassLoader();
            registerScriptsFromLibraries(
                    classLoader,
                    classLoaderTemplate::getChildClassLoader,
                    jarsPathsToAdd,
                    needVerification,
                    false);
        });

        LOG.info("Libraries' scripts has been reprocessed due to modules classloader recreation.");
    }

    private void resetStorageServices()
    {
        for (StorageService<? extends HasCode, ? extends HasCode> storageService : storageServices)
        {
            storageService.invalidateLibraryNameToScriptCodeCache();
            storageService.invalidateScriptCodeToScriptCache();
        }
    }

    private Set<Path> getNonExpiredLibrariesJarsPaths()
    {
        return libraryStorage.getNonExpiredLibraries()
                .map(ScriptLibrary::getPath)
                .collect(Collectors.toSet());
    }

    /**
     * Получить библиотеки, которые необходимо добавить
     *
     * @param librariesToAdd библиотеки для добавления;
     * @param isReset true - происходит полная перезагрузка библиотек, false - обычное изменение состава библиотек;
     * @return названия добавляемых библиотек;
     */
    private Set<Path> getLibrariesJarsPathsForAddition(Set<Path> librariesToAdd, boolean isReset)
    {
        if (!isReset)
        {
            return librariesToAdd.isEmpty() ? getNonExpiredLibrariesJarsPaths() : librariesToAdd;
        }
        return getNonExpiredLibrariesJarsPaths();
    }

    private Set<String> getLibrariesJarsNamesForAddition(Set<Path> librariesToAdd, boolean isReset)
    {
        return getLibrariesJarsPathsForAddition(librariesToAdd, isReset).stream()
                .map(Path::getFileName)
                .map(Path::toString)
                .collect(Collectors.toSet());
    }

    /**
     * Сканирование библиотек
     * @param classLoader класслоадер для резолвинга зависимостей
     * @param libraryJars имена библиотек для сканирования
     * @return результат сканирования
     */
    private static ScanResult getScanResult(GroovyClassLoader classLoader, Collection<String> libraryJars)
    {
        return new ClassGraph()
                .overrideClassLoaders(classLoader)
                .enableAnnotationInfo()
                .enableClassInfo()
                .enableFieldInfo()
                .ignoreClassVisibility() // нужно, чтобы смотреть на внутренние приватные классы, поля
                .ignoreFieldVisibility()
                .acceptJars(libraryJars.toArray(String[]::new))
                .scan();
    }
}
